<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(INFOPLIST_KEY_CFBundleDisplayName)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(APP_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>$(INFOPLIST_KEY_CFBundleDisplayName) want to use bluetooth to scan, connect and listen to BLE device events</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>$(INFOPLIST_KEY_CFBundleDisplayName) want to use bluetooth to scan, connect and listen to BLE device events</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_matter._tcp</string>
		<string>_matterc._udp</string>
		<string>_matterd._udp</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera and photo library access in order to read QR code to add the gateway, and to update the picture of your smart home in dashboard .</string>
    <key>NSLocalNetworkUsageDescription</key>
    <string>This App requires access to local network in order to control your devices locally .</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app need the location permission to scan and read BLE devices </string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app need the location permission to scan and read BLE devices </string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app need the location permission to scan the BLE devices </string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs camera and photo library access in order to read QR code to add the gateway, and to update the picture of your smart home in dashboard .</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
