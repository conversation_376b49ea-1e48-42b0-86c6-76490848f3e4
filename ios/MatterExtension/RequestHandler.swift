//
//  RequestHandler.swift
//  MatterExtension
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/16.
//

import MatterSupport
import os

// The extension is launched in response to `MatterAddDeviceRequest.perform()` and this class is the entry point
// for the extension operations.
class RequestHandler: MatterAddDeviceExtensionRequestHandler {
    
    private let logger = os.Logger(subsystem: "com.habi.eu.smarthome.MatterExtension", category: "debug")
    
    /// Use this function to perform additional attestation checks if that is useful for your ecosystem.
    /// - Parameter deviceCredential: cert declaration, attestation data, product Attestation Intermediate Certificate
    override func validateDeviceCredential(_ deviceCredential: MatterAddDeviceExtensionRequestHandler.DeviceCredential) async throws {
        logger.log("validateDeviceCredential() -> start...")
        DispatchQueue.main.async {
           MatterExtensionStorage.shared.saveCertDeclaration(certDeclaration: deviceCredential.certificationDeclaration)
           MatterExtensionStorage.shared.saveAttestationInfo(attestationInfo: deviceCredential.deviceAttestationCertificate)
        }
    }
    
    /// // Use this function to select a Wi-Fi network for the device if your ecosystem has special requirements.
    /// Or, return `.defaultSystemNetwork` to use the iOS device's current network.
    /// - Parameter wifiScanResults: wifi scan results
    /// - Returns: scan resilt
    override func selectWiFiNetwork(from wifiScanResults: [MatterAddDeviceExtensionRequestHandler.WiFiScanResult]) async throws -> MatterAddDeviceExtensionRequestHandler.WiFiNetworkAssociation {
        return .defaultSystemNetwork
    }
    
    /// Use this function to select a Thread network for the device if your ecosystem has special requirements.
    /// Or, return `.defaultSystemNetwork` to use the default Thread network.
    /// - Parameter threadScanResults: thread scan results
    /// - Returns: default system network
    override func selectThreadNetwork(from threadScanResults: [MatterAddDeviceExtensionRequestHandler.ThreadScanResult]) async throws -> MatterAddDeviceExtensionRequestHandler.ThreadNetworkAssociation {
        
        logger.log("selectThreadNetwork() -> start...")
        
        for threadScanResult in threadScanResults {
            if let borderAgentIdData = MatterExtensionStorage.shared.getBorderAgentId() {
                let borderAgentId = borderAgentIdData.hexadecimalString
                let id = threadScanResult.extendedAddress.hexadecimalString
                if id == borderAgentId {
                    logger.log("selectThreadNetwork() -> equal...")
                    return .network(extendedPANID: threadScanResult.extendedPANID)
                }
            }
        }
        
        logger.log("selectThreadNetwork() -> unequal...")
        return .defaultSystemNetwork
    }
    
    /// Commission device callback
    /// - Parameters:
    ///   - home: home
    ///   - onboardingPayload: onboarding payload
    ///   - commissioningID: commissioning id
    override func commissionDevice(in home: MatterAddDeviceRequest.Home?, onboardingPayload: String, commissioningID: UUID) async throws {
        // Use this function to commission the device with your Matter stack.
        logger.log("commissionDevice() -> start...")
        DispatchQueue.main.async {
            MatterExtensionStorage.shared.saveOnboardingPayload(onboardingPayload: onboardingPayload)
            MatterExtensionStorage.shared.saveCommissioningId(commissioningId: commissioningID.uuidString)
        }
    }

    override func rooms(in home: MatterAddDeviceRequest.Home?) async -> [MatterAddDeviceRequest.Room] {
        // Use this function to return the rooms your ecosystem manages.
        // If your ecosystem manages multiple homes, ensure you are returning rooms that belong to the provided home.
        logger.log("rooms() -> start...")
        return [.init(displayName: "Living Room")]
    }

    override func configureDevice(named name: String, in room: MatterAddDeviceRequest.Room?) async {
        // Use this function to configure the (now) commissioned device with the given name and room.
        logger.log("configureDevice() -> start...")
        DispatchQueue.main.async {
           MatterExtensionStorage.shared.saveDeviceName(deviceName: name)
        }
    }
}
