//
//  MatterExtensionStorage.swift
//  MatterExtension
//
//  Created by JieCao on 2024/12/16.
//

import Foundation

class MatterExtensionStorage {
    static var shared = MatterExtensionStorage()
    
    func removeCertDeclaration() {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: MatterExtensionConstants.certDeclarationKey)
    }
 
    func saveCertDeclaration(certDeclaration: Data) {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.set(certDeclaration, forKey: MatterExtensionConstants.certDeclarationKey)
    }
    
    func getCertDeclaration() -> Data? {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: MatterExtensionConstants.certDeclarationKey) {
            return value as? Data;
        }
        return nil
    }
    
    func removeAttestationInfo() {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: MatterExtensionConstants.attestationInfoKey)
    }

    func saveAttestationInfo(attestationInfo: Data) {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.set(attestationInfo, forKey: MatterExtensionConstants.attestationInfoKey)
    }

    func getAttestationInfo() -> Data? {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: MatterExtensionConstants.attestationInfoKey) {
            return value as? Data;
        }
        return nil
    }
    
    func removeBorderAgentId() {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: MatterExtensionConstants.borderAgentIdKey)
    }
    
    func saveBorderAgentId(borderAgentId: Data) {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.set(borderAgentId, forKey: MatterExtensionConstants.borderAgentIdKey)
    }
    
    func getBorderAgentId() -> Data? {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: MatterExtensionConstants.borderAgentIdKey) {
            return value as? Data;
        }
        return nil
    }
    
    func removeOnboardingPayload() {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: MatterExtensionConstants.onboardingPayloadKey)
    }

    func saveOnboardingPayload(onboardingPayload: String) {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.set(onboardingPayload, forKey: MatterExtensionConstants.onboardingPayloadKey)
    }
   
    func getOnboardingPayload() -> String? {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: MatterExtensionConstants.onboardingPayloadKey) {
            return value as? String;
        }
        return nil
    }
    
    func removeDeviceName() {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: MatterExtensionConstants.devicesName)
    }

    func saveDeviceName(deviceName: String) {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.set(deviceName, forKey: MatterExtensionConstants.devicesName)
    }

    func getDeviceName() -> String? {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: MatterExtensionConstants.devicesName) {
            return value as? String;
        }
        return nil
    }
    
    func removeCommissioningId() {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: MatterExtensionConstants.commissioningId)
    }

    func saveCommissioningId(commissioningId: String) {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        sharedUserDefaults?.set(commissioningId, forKey: MatterExtensionConstants.commissioningId)
    }

    func getCommissioningId() -> String? {
        let sharedUserDefaults = UserDefaults(suiteName: MatterExtensionConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: MatterExtensionConstants.commissioningId) {
            return value as? String;
        }
        return nil
    }
}
