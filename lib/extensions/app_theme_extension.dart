import 'package:flutter/material.dart';

@immutable
class AppThemeExtension extends ThemeExtension<AppThemeExtension> {

  // light: OxFF1C1C1C, dark: 0xFFFFFFFF
  final Color firstColor;
  // light: 0xFFFFFFFF, dark: 0xFF1C1C1C
  final Color secondColor;
  // light: 0xFFFAFAFA, dark: 0xFF333333
  final Color thirdColor;
  // light: 0xFF1C1C1C, dark: 0xFFF2FFFe
  final Color fourthColor;

  const AppThemeExtension({
    required this.firstColor,
    required this.secondColor,
    required this.thirdColor,
    required this.fourthColor,
  });

  @override
  ThemeExtension<AppThemeExtension> copyWith({
    Color? firstColor,
    Color? secondColor,
    Color? thirdColor,
    Color? fourthColor,
  }) {
    return AppThemeExtension(
      firstColor: firstColor ?? this.firstColor,
      secondColor: secondColor ?? this.secondColor,
      thirdColor: thirdColor ?? this.thirdColor,
      fourthColor: fourthColor ?? this.fourthColor,
    );
  }

  @override
  ThemeExtension<AppThemeExtension> lerp(covariant ThemeExtension<AppThemeExtension>? other,
      double t) {
    if (other is! AppThemeExtension){
      return this;
    }
    return AppThemeExtension(
      firstColor: Color.lerp(firstColor, other.firstColor, t) ?? firstColor,
      secondColor: Color.lerp(secondColor, other.secondColor, t) ?? secondColor,
      thirdColor: Color.lerp(thirdColor, other.thirdColor, t) ?? thirdColor,
      fourthColor: Color.lerp(fourthColor, other.fourthColor, t) ?? fourthColor,
    );
  }






}
