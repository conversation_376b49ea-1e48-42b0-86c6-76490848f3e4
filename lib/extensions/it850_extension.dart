import 'package:get/get.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/system_modes.dart';
import 'package:habi_app/helpers/app_number_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:habi_app/constants/app_enums.dart';

extension It850Extension on IT850 {

  void parseSchedules() {
    shadow?.state?.reported?.model?.properties?.schedules?.setScheduleMode(ScheduleMode.heatOnly);
    shadow?.state?.reported?.model?.properties?.schedules?.parseSchedules();
  }

  Future<void> setSchedule() async {
    try {
      await DeviceShadowService.to.updateDeviceProperties(
          thingName: thingName!,
          property: shadow?.state?.reported?.model?.properties?.schedules?.publishSetSchedule() ?? {},
          subId: IT850.subId
      );
      shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
      shadow?.state?.reported?.model?.properties?.schedules?.updateCopy();
      shadow?.state?.reported?.model?.properties?.schedules?.clearOtherTypeSchedules();
      log.i('setSchedule() -> 成功!');
    } catch (e, r) {
      shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: true);
      log.e('setSchedule() -> 失败: $e, $r');
    }
  }

  bool isConnected() {
    bool connected = (shadow?.state?.reported?.connected ?? 'false') == 'true';
    return connected;
  }

  String getDeviceName() {
    String deviceName = shadow?.state?.reported?.model?.properties?.sMdo?.deviceName ?? 'myThermostat'.tr;
    return deviceName;
  }

  String getNodeId() {
    String nodeId = shadow?.state?.reported?.model?.properties?.sMdo?.nodeID ?? '';
    return nodeId;
  }

  // 0: Off, 1: Auto, 3: Cool, 4: Heat, 5: Emeregncy Heating
  int getSystemMode() {
    int systemMode = shadow?.state?.reported?.model?.properties?.sTherS?.systemMode ?? SystemModes.off;
    return systemMode;
  }

  int getRunningState() {
    int runningState = shadow?.state?.reported?.model?.properties?.sTherS?.runningState ?? 0;
    return runningState;
  }

  int getLocalTemp() {
    int localTemp = shadow?.state?.reported?.model?.properties?.sTherS?.localTemp ?? 0;
    return localTemp;
  }

  double getLocalTempValue() {
    int localTemp = shadow?.state?.reported?.model?.properties?.sTherS?.localTemp ?? 0;
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      double value = AppNumberHelper.roundDouble(
          AppNumberHelper.degreeCtoFConversion(localTemp / 100),
          1
      );
      return value;
    }

    double value = AppNumberHelper.roundDouble(localTemp / 100, 1);
    return value;
  }

  int getSliderMaxHeatSp() {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return 9500;
    }
    return 3500;
  }

  int getSliderMinHeatSp() {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return 4100;
    }
    return 500;
  }

  int getSliderMaxCoolSp() {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return 9500;
    }
    return 3500;
  }

  int getSliderMinCoolSp() {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return 4100;
    }
    return 500;
  }

  double getSliderMaxHeatSpValue() {
    return getSliderMaxHeatSp() / 100;
  }

  double getSliderMinHeatSpValue() {
    return getSliderMinHeatSp() / 100;
  }

  double getSliderMaxCoolSpValue() {
    return getSliderMaxCoolSp() / 100;
  }

  double getSliderMinCoolSpValue() {
    return getSliderMinCoolSp() / 100;
  }

  int getMaxHeatSp() {
    return 3500;
  }

  int getMinHeatSp() {
    return 500;
  }

  int getMaxCoolSp() {
    return 3500;
  }

  int getMinCoolSp() {
    return 500;
  }

  double getMaxHeatSpValue() {
    return getMaxHeatSp() / 100;
  }

  double getMinHeatSpValue() {
    return getMinHeatSp() / 100;
  }

  double getMaxCoolSpValue() {
    return getMaxHeatSp() / 100;
  }

  double getMinCoolSpValue() {
    return getMinHeatSp() / 100;
  }

  int getHeatingSp() {
    int heatingSp = shadow?.state?.reported?.model?.properties?.sTherS?.heatingSp ?? 0;
    return heatingSp;
  }

  int getCoolingSp() {
    int coolingSp = shadow?.state?.reported?.model?.properties?.sTherS?.coolingSp ?? 0;
    return coolingSp;
  }

  void setHeatingSp(int value) {
    shadow?.state?.reported?.model?.properties?.sTherS?.heatingSp = value;
  }

  void setCoolingSp(int value) {
    shadow?.state?.reported?.model?.properties?.sTherS?.coolingSp = value;
  }

  void setHeatingSpValue(double value) {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      double newValue = AppNumberHelper.roundDouble(
          AppNumberHelper.degreeFtoCConversion(value),
          1
      );
      int thatHeatingSp = (newValue * 100).toInt();
      setHeatingSp(thatHeatingSp);
    } else {
      int thatHeatingSp = (value * 100).toInt();
      setHeatingSp(thatHeatingSp);
    }
  }

  double getHeatingSpValue() {
    int heatingSp = shadow?.state?.reported?.model?.properties?.sTherS?.heatingSp ?? 0;
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      double value = AppNumberHelper.roundDouble(
          AppNumberHelper.degreeCtoFConversion(heatingSp / 100),
          1
      );
      return value;
    }
    double value = AppNumberHelper.roundDouble(heatingSp / 100, 1);
    return value;
  }

  void setCoolingSpValue(double value) {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      double newValue = AppNumberHelper.roundDouble(
          AppNumberHelper.degreeFtoCConversion(value),
          1
      );
      int thatCoolingSp = (newValue * 100).toInt();
      setCoolingSp(thatCoolingSp);
    } else {
      int thatCoolingSp = (value * 100).toInt();
      setCoolingSp(thatCoolingSp);
    }
  }

  double getCoolingSpValue() {
    int coolingSp = shadow?.state?.reported?.model?.properties?.sTherS?.coolingSp ?? 0;
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      double value = AppNumberHelper.roundDouble(
          AppNumberHelper.degreeCtoFConversion(coolingSp / 100),
          1
      );
      return value;
    }
    double value = AppNumberHelper.roundDouble(coolingSp / 100, 1);
    return value;
  }

  double roundSliderValue(double value) {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      var newValue = AppNumberHelper.roundValueTo(value, to: 1);
      return newValue;
    } else {
      var newValue = AppNumberHelper.roundValueTo(value);
      return newValue;
    }
  }

  double getCardVectorValue() {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return 1.0;
    } else {
      return 0.5;
    }
  }

  bool isScheduleEnable() {
    int scheduleEn = shadow?.state?.reported?.model?.properties?.schedules?.sTimeH?.scheduleEn ?? 0;
    return scheduleEn == 1;
  }

  int getScheduleEnableValue() {
    int value = shadow?.state?.reported?.model?.properties?.schedules?.sTimeH?.scheduleEn ?? 0;
    return value;
  }

}