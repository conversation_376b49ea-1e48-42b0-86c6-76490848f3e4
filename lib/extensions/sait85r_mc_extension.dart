import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/device_shadow_service.dart';

extension Sait85rMCExtension on Sait85rMC {

  bool isConnected() {
    return shadow?.state?.reported?.connected == 'true';
  }

  String? getFabId() {
    String? fabID = shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID;
    return fabID;
  }

  bool isHotWaterEnable() {
    int enableDhw = shadow?.state?.reported?.model?.properties?.sBoiler?.enableDhw ?? 0;
    return enableDhw == 1;
  }

  bool isHotWater() {
    int dhwRelay = shadow?.state?.reported?.model?.properties?.sBoiler?.dhwRelay ?? 0;
    int holdType = shadow?.state?.reported?.model?.properties?.sBoiler?.holdType ?? 2;
    return dhwRelay == 1 && holdType == 2;
  }

  // 0x00: off
  // 0x01: on
  // 0x02: auto
  int getDHWSwitch() {
    int dhwSwitch = shadow?.state?.reported?.model?.properties?.sBoiler?.dhwSwitch ?? 0;
    return dhwSwitch;
  }

  int getDHWRelay() {
    int dhwRelay = shadow?.state?.reported?.model?.properties?.sBoiler?.dhwRelay ?? 0;
    return dhwRelay;
  }

  // 0x00: Schedule
  // 0x01: Temporary Hold (Hand)
  // 0x02: Permanent Hold
  int getHoldType() {
    int holdType = shadow?.state?.reported?.model?.properties?.sBoiler?.holdType ?? 2;
    return holdType;
  }

  int getBoostDHW() {
    int boostDhw = shadow?.state?.reported?.model?.properties?.sBoiler?.boostDhw ?? 0;
    return boostDhw;
  }

  Future<void> setHotWater(bool value) async {
    Map<String, dynamic> property;
    if (value) {
      property =  {
        "ep0:sBoiler:sDHWRelay": 1,
        "ep0:sBoiler:sHoldType": 2,
        "ep0:sBoiler:sBoostDHW": 0,
      };
    } else {
      property =  {
        "ep0:sBoiler:sDHWRelay": 0,
        "ep0:sBoiler:sHoldType": 2,
        "ep0:sBoiler:sBoostDHW": 0,
      };
    }

    try {
      await DeviceShadowService.to.updateDeviceProperties(
          thingName: thingName!,
          property: property,
          subId: Sait85rMC.subId
      );
      log.i('setHotWater() -> 成功!');
    } catch (e, r) {
      log.e('setHotWater() -> 失败: $e, $r');
    }
  }

  bool isBoostMode() {
    int holdType = shadow?.state?.reported?.model?.properties?.sBoiler?.holdType ?? 2;
    return holdType == 1;
  }

  Future<void> setBoostMode(int boostDhw) async {
    try {
      await DeviceShadowService.to.updateDeviceProperties(
          thingName: thingName!,
          property: {
            "ep0:sBoiler:sBoostDHW": boostDhw,
          },
          subId: Sait85rMC.subId
      );
      log.i('setBoostMode() -> 成功!');
    } catch (e, r) {
      log.e('setBoostMode() -> 失败: $e, $r');
    }
  }

  bool isScheduleMode() {
    int holdType = shadow?.state?.reported?.model?.properties?.sBoiler?.holdType ?? 2;
    return holdType == 0;
  }

  Future<void> setScheduleMode() async {
    try {
      await DeviceShadowService.to.updateDeviceProperties(
          thingName: thingName!,
          property: {
            "ep0:sBoiler:sHoldType": 0,
            "ep0:sBoiler:sBoostDHW": 0,
          },
          subId: Sait85rMC.subId
      );
      log.i('setScheduleMode() -> 成功!');
    } catch (e, r) {
      log.e('setScheduleMode() -> 失败: $e, $r');
    }
  }

  void parseSchedules() {
    shadow?.state?.reported?.model?.properties?.schedules?.setScheduleMode(ScheduleMode.onOrOff);
    shadow?.state?.reported?.model?.properties?.schedules?.parseSchedules();
  }

  Future<void> setSchedule() async {
    try {
      await DeviceShadowService.to.updateDeviceProperties(
          thingName: thingName!,
          property: shadow?.state?.reported?.model?.properties?.schedules?.publishSetSchedule() ?? {},
          subId: Sait85rMC.subId
      );
      shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
      shadow?.state?.reported?.model?.properties?.schedules?.updateCopy();
      shadow?.state?.reported?.model?.properties?.schedules?.clearOtherTypeSchedules();
      log.i('setSchedule() -> 成功!');
    } catch (e, r) {
      shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: true);
      log.e('setSchedule() -> 失败: $e, $r');
    }
  }
}