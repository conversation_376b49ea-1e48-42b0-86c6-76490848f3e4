import 'package:habi_app/models/device/sait85r_gw.dart';

extension Sait85rGWExtension on Sait85rGW {

  bool isConnected() {
    return shadow?.state?.reported?.connected == 'true';
  }

  String? getGatewaySoftwareVersion() {
    return shadow?.state?.reported?.model?.properties?.sGateway?.gatewaySoftwareVersion;
  }

  String? getGatewayHardwareVersion() {
    return shadow?.state?.reported?.model?.properties?.sGateway?.gatewayHardwareVersion;
  }


}