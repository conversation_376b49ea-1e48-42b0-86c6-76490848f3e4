class LogMessage {
  int? id;
  String level;
  String message;
  DateTime time;

  LogMessage({
    this.id,
    required this.level,
    required this.message,
    DateTime? time,
  }) : time = time ?? DateTime.now();

  // Factory constructor to create a Log instance from a map.
  // This is useful when querying the database and converting the results back into a Log object.
  factory LogMessage.fromMap(Map<dynamic, dynamic> map) {
    return LogMessage(
      id: map['id'] as int?,
      level: map['level'] as String,
      message: map['message'] as String,
      time: DateTime.parse(map['time'] as String),
    );
  }

  // Method to convert a Log instance into a map.
  // This is useful for inserting data into the database.
  Map<String, dynamic> toMap() {
    return {
      'id': id, // This will be null for new records and auto-incremented by SQLite.
      'level': level,
      'message': message,
      'time': time.toIso8601String(),
    };
  }
}
