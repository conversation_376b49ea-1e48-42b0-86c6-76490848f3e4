import 'package:flutter/material.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/helpers/app_number_helper.dart';
import 'package:habi_app/helpers/reg_exp_helper.dart';
import 'package:habi_app/utility/date_time_utils.dart';

class Schedule {
  Schedule({
    required this.scheduleMode,
    this.time,
    this.timeFormat24Hour = 0,
    this.dayIndex = 0,
    this.position,
    this.onOrOff,
    this.heating,
    this.isActive = 1,
    this.isModify = false,
    this.isNew = false,
    this.maxHeat,
    this.minHeat,
    this.temperatureDisplayMode,
  });

  bool isNew;
  ScheduleMode scheduleMode;
  int? timeFormat24Hour;
  String? time;
  int dayIndex; // 1...7    Mon-Sun
  int? position; // 0...6
  String? onOrOff;
  String? heating;
  int isActive = 1; //1 for active 0 for inactive
  bool isModify = false;
  double? maxHeat;
  double? minHeat;
  int? temperatureDisplayMode; // 0: "C"    1: "F"

  TimeOfDay getSelectedTimeOfDay() {
    if (time != null && time != "") {
      return TimeOfDay(
        hour: int.parse(time!.substring(0, 2)),
        minute: int.parse(time!.substring(2, 4)),
      );
    }
    return TimeOfDay.now();
  }

  bool is24HourFormat() {
    if (timeFormat24Hour == 1) {
      return true;
    }
    return false;
  }

  String getOnOffValue() {
    if (onOrOff == "0001") {
      return "On";
    } else {
      return "Off";
    }
  }

  void setOnOrOff(String value) {
    onOrOff = value;
    return;
  }

  String getHeatOnly() {
    var value = "";
    if (getHeating().isNotEmpty) {
      value = getHeating();
    }
    return value;
  }

  double getMinHeat() {
    if (temperatureDisplayMode == 1) {
      return AppNumberHelper.roundDouble(AppNumberHelper.degreeCtoFConversion(minHeat ?? 4100), 1);
    }
    return minHeat ?? 500;
  }

  double getMaxHeat() {
    if (temperatureDisplayMode == 1) {
      return AppNumberHelper.roundDouble(AppNumberHelper.degreeCtoFConversion(maxHeat ?? 9500), 1);
    }
    return maxHeat ?? 3500;
  }

  String getTime() {
    if (time != null && time != "" && time!.contains("f") == false) {
      if (timeFormat24Hour == 1) {
        return DateTimeUtils.dateConverter(
          "${time!.substring(0, 2)}:${time!.substring(2, 4)}",
          "HH:mm",
          "HH:mm",
        );
      } else {
        return DateTimeUtils.dateConverter(
          "${time!.substring(0, 2)}:${time!.substring(2, 4)}",
          "HH:mm",
          "hh:mm a",
        );
      }
    }
    return "";
  }

  void setTime(String time) {
    if (timeFormat24Hour == 1) {
      this.time = DateTimeUtils.dateConverter(time, "HH:mm", "HHmm")
          .replaceAll(":", "")
          .padLeft(4, "0");
      return;
    }
    if (time.split(" ").length == 2) {
      this.time = DateTimeUtils.dateConverter(time, "h:mm a", "HHmm")
          .replaceAll(":", "")
          .padLeft(4, "0");
      return;
    }
    this.time = DateTimeUtils.dateConverter(time, "HH:mm", "HHmm")
        .replaceAll(":", "")
        .padLeft(4, "0");
    return;
  }

  String convertTime(String time) {
    if (timeFormat24Hour == 1) {
      return DateTimeUtils.dateConverter(time, "HH:mm", "HHmm")
          .replaceAll(":", "")
          .padLeft(4, "0");
    }
    return DateTimeUtils.dateConverter(time, "h:mm a", "HHmm")
        .replaceAll(":", "")
        .padLeft(4, "0");
  }

  String getValue({ScheduleMode? mode}) {
    //DateTimeUtils.dateConverter(time, "HHmm", "HH:mm:ss");
    ScheduleMode m = scheduleMode;
    if (mode != null) {
      m = mode;
    }
    if (m == ScheduleMode.onOrOff) {
      return getOnOffValue();
    }
    if (m == ScheduleMode.heatOnly) {
      return getHeatOnly();
    }
    var value = "";
    return value;
  }

  String? validateTime(BuildContext context) {
    if (time != null) {
      return null;
    }
    return "validateTime".tr;
  }

  String? validateSetPoint(BuildContext context, String? value) {
    final double minValue = getMinHeat();
    final double maxValue = getMaxHeat();
    if (value == null || value.isEmpty) {
      if (temperatureDisplayMode != null && temperatureDisplayMode == 0) {
        return 'validateSetPoint'.trParams({
          'minValue': minValue.toString(), 'maxValue': maxValue.toString()
        });
      }
      return 'validateSetPoint2'.trParams({
        'minValue': minValue.toString(), 'maxValue': maxValue.toString()
      });
    }

    if (RegExp(RegExpHelper.scheduleValidation).hasMatch(value)) {
      if (value.isNotEmpty && double.parse(value) >= minValue && double.parse(value) <= maxValue) {
        var flag = false;
        if (temperatureDisplayMode == 0) {
          final split = value.split(".");
          if (split.length == 2) {
            if (split[1].length == 1 && (split[1] == "5" || split[1] == "0")) {
              flag = true;
            } else {
              flag = false;
            }
          } else {
            flag = true;
          }
        } else {
          final split = value.split(".");
          if (split.length == 2) {
            if (split[1].length == 1 && split[1] == "0") {
              flag = true;
            } else {
              flag = false;
            }
          } else {
            flag = true;
          }
        }
        if (flag == true) {
          setHeating(double.parse(value));
          return null;
        } else {
          setHeating(null);
        }
      } else {
        setHeating(null);
      }
    }
    if (temperatureDisplayMode != null && temperatureDisplayMode == 0) {
      return 'validateSetPoint'.trParams({
        'minValue': minValue.toString(), 'maxValue': maxValue.toString()
      });
    }
    return 'validateSetPoint2'.trParams({
      'minValue': minValue.toString(), 'maxValue': maxValue.toString()
    });
  }

  Map<String, String> buildMapForOnOrOff() {
    return {"time": getTime(), "value": getValue()};
  }

  String buildSTimeGhString() {
    //DateTimeUtils.dateConverter(time, "HH:mm:ss", "HHmm");
    final timeStr = time ?? "FFFF";
    var onOrOffStr = "FFFFFFFF";
    if (onOrOff == "0001") {
      onOrOffStr = "0001FFFF";
    } else {
      onOrOffStr = "0000FFFF";
    }
    return timeStr + onOrOffStr;
  }

  String buildSTimeHString() {
    final timeStr = time ?? "FFFF";
    var heatStr = "FFFF";
    if (getHeating().isNotEmpty && getHeating() != "-") {
      heatStr = heating ?? "2100";
    }
    var coolStr = "FFFF";
    return timeStr + heatStr + coolStr;
  }

  String getHeating() {
    if (heating != null && heating != "" && num.tryParse(heating!) != null) {
      double value;
      if (temperatureDisplayMode == 0) {
        value = AppNumberHelper.roundValueTo(int.parse(heating!).toDouble() / 100);
      } else {
        value = AppNumberHelper.roundDouble(
          AppNumberHelper.degreeCtoFConversion(int.parse(heating!).toDouble() / 100),
          1,
        );
      }
      return value.toString();
    }
    return "-";
  }

  void setHeating(double? value) {
    if (value == null) {
      heating = null;
      return;
    }
    if (temperatureDisplayMode == 0) {
      heating = (value * 100).toInt().toString().padLeft(4, '0');
    } else {
      heating = (AppNumberHelper.degreeFtoCConversion(value) * 100)
          .toInt()
          .toString()
          .padLeft(4, '0');
    }
  }

  Schedule copyWith({
    ScheduleMode? scheduleMode,
    int? temperatureDisplayMode,
    int? timeFormat24Hour,
    String? time,
    String? heating,
    int? dayIndex,
    int? position,
    double? minHeat,
    double? maxHeat,
    String? onOrOff,
    int? isActive,
    bool? isModify,
  }) {
    return Schedule(
      scheduleMode: scheduleMode ?? this.scheduleMode,
      temperatureDisplayMode:
          temperatureDisplayMode ?? this.temperatureDisplayMode,
      timeFormat24Hour: timeFormat24Hour ?? this.timeFormat24Hour,
      time: time ?? this.time,
      heating: heating ?? this.heating,
      dayIndex: dayIndex ?? this.dayIndex,
      position: position ?? this.position,
      minHeat: minHeat ?? this.minHeat,
      maxHeat: maxHeat ?? this.maxHeat,
      onOrOff: onOrOff ?? this.onOrOff,
      isActive: isActive ?? this.isActive,
      isModify: isModify ?? this.isModify,
    );
  }
}
