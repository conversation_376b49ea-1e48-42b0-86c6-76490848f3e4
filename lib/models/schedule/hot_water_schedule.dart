import 'package:habi_app/models/schedule/schedule.dart';

class HotWaterSchedule {
  final Schedule onSchedule;
  final Schedule offSchedule;

  HotWaterSchedule({
    required this.onSchedule,
    required this.offSchedule,
  });

  HotWaterSchedule copyWith({
    Schedule? onSchedule,
    Schedule? offSchedule,
  }) {
    return HotWaterSchedule(
      onSchedule: (onSchedule ?? this.onSchedule).copyWith(),
      offSchedule: (offSchedule ?? this.offSchedule).copyWith(),
    );
  }
}
