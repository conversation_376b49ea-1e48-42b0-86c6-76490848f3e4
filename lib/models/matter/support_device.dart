class SupportDevice {
  static const String capacityWiFi = 'WiFi';
  static const String capacityThread = 'Thread';
  static const String capacityUnknown = 'Unknown';

  final String deviceType;
  final String modelId;
  final String capacity;
  final int vid;
  final int pid;

  SupportDevice({
    required this.deviceType,
    required this.modelId,
    required this.capacity,
    required this.vid,
    required this.pid
  });

  factory SupportDevice.fromJson(Map<String, dynamic> json) {
    return SupportDevice(
      deviceType: json['deviceType'],
      modelId: json['modelId'],
      capacity: json['capacity'],
      vid: json['vid'],
      pid: json['pid']
    );
  }

  factory SupportDevice.unknown() {
    return SupportDevice(
        deviceType: '',
        modelId: '',
        capacity: capacityUnknown,
        vid: -1,
        pid: -1
    );
  }

  @override
  String toString() {
    return 'SupportDevice{deviceType: $deviceType, modelId: $modelId, capacity: $capacity, vid: $vid, pid: $pid}';
  }

}