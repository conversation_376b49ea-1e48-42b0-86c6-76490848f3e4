class SetupPayload {
  final int version;
  final int vendorId;
  final int productId;
  final int discriminator;
  final int setupPinCode;
  final int commissioningFlow;
  final bool isShortDiscriminator;
  final String discoveryCapabilities;

  SetupPayload({
    required this.version,
    required this.vendorId,
    required this.productId,
    required this.discriminator,
    required this.setupPinCode,
    required this.commissioningFlow,
    required this.isShortDiscriminator,
    required this.discoveryCapabilities,
  });

  factory SetupPayload.fromNativeMap(Map<Object?, Object?> json) {
    return SetupPayload(
      version: json['version'] as int,
      vendorId: json['vendorId'] as int,
      productId: json['productId'] as int,
      discriminator: json['discriminator'] as int,
      setupPinCode: json['setupPinCode'] as int,
      commissioningFlow: json['commissioningFlow'] as int,
      isShortDiscriminator: json['isShortDiscriminator'] as bool,
      discoveryCapabilities: json['discoveryCapabilities'] as String,
    );
  }

  @override
  String toString() {
    return 'SetupPayload(version: $version, vendorId: $vendorId, productId: $productId, discriminator: $discriminator, setupPinCode: $setupPinCode, commissioningFlow: $commissioningFlow, isShortDiscriminator: $isShortDiscriminator, discoveryCapabilities: $discoveryCapabilities)';
  }
}