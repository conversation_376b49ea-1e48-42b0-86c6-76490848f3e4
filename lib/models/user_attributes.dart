import 'dart:convert';

import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/user_attribute_keys.dart';

/// Model class representing user attributes from AWS Cognito
///
/// This class provides a type-safe way to handle user attributes stored in AWS Cognito.
/// It includes both standard Cognito attributes and custom attributes specific to our application.
class UserAttributes {
  // Standard attributes
  final String email;
  final String name;
  final String familyName;
  final String locale;

  // Custom attributes
  final String country;
  final String language;
  final String dataCollection;
  final String dataCollectionDateTime;
  final String privacyVersion;
  final String privacyAcceptDateTime;
  final String termsVersion;
  final String termsAcceptDateTime;
  final HourFormatStatus hourFormat;
  final TemperatureUnitStatus temperatureUnit;

  /// Creates a new [UserAttributes] instance
  const UserAttributes({
    required this.email,
    required this.name,
    required this.familyName,
    required this.locale,
    required this.country,
    required this.language,
    required this.dataCollection,
    required this.dataCollectionDateTime,
    required this.privacyVersion,
    required this.privacyAcceptDateTime,
    required this.termsVersion,
    required this.termsAcceptDateTime,
    required this.hourFormat,
    required this.temperatureUnit,
  });

  /// Creates a [UserAttributes] instance from a map of attributes
  factory UserAttributes.fromJson(Map<String, String> attributes) {
    return UserAttributes(
      email: attributes[UserAttributeKeys.email.key] ?? '',
      name: attributes[UserAttributeKeys.name.key] ?? '',
      familyName: attributes[UserAttributeKeys.familyName.key] ?? '',
      locale: attributes[UserAttributeKeys.locale.key] ?? '',
      country: attributes[UserAttributeKeys.country.key] ?? '',
      language: attributes[UserAttributeKeys.language.key] ?? '',
      dataCollection: attributes[UserAttributeKeys.dataCollection.key] ?? '',
      dataCollectionDateTime: attributes[UserAttributeKeys.dataCollectionDateTime.key] ?? '',
      privacyVersion: attributes[UserAttributeKeys.privacyVersion.key] ?? '',
      privacyAcceptDateTime: attributes[UserAttributeKeys.privacyAcceptDateTime.key] ?? '',
      termsVersion: attributes[UserAttributeKeys.termsVersion.key] ?? '',
      termsAcceptDateTime: attributes[UserAttributeKeys.termsAcceptDateTime.key] ?? '',
      hourFormat: HourFormatStatus.fromValue(
        attributes[UserAttributeKeys.hourFormat.key] ?? '',
      ),
      temperatureUnit: TemperatureUnitStatus.fromValue(
        attributes[UserAttributeKeys.temperatureUnit.key] ?? '',
      ),
    );
  }

  /// Converts the model to a map of attributes
  Map<String, String> toJson() => {
        UserAttributeKeys.email.key: email,
        UserAttributeKeys.name.key: name,
        UserAttributeKeys.familyName.key: familyName,
        UserAttributeKeys.locale.key: locale,
        UserAttributeKeys.country.key: country,
        UserAttributeKeys.language.key: language,
        UserAttributeKeys.dataCollection.key: dataCollection,
        UserAttributeKeys.dataCollectionDateTime.key: dataCollectionDateTime,
        UserAttributeKeys.privacyVersion.key: privacyVersion,
        UserAttributeKeys.privacyAcceptDateTime.key: privacyAcceptDateTime,
        UserAttributeKeys.termsVersion.key: termsVersion,
        UserAttributeKeys.termsAcceptDateTime.key: termsAcceptDateTime,
        UserAttributeKeys.hourFormat.key: hourFormat.value,
        UserAttributeKeys.temperatureUnit.key: temperatureUnit.value,
      };

  @override
  String toString() => json.encode(toJson());
}
