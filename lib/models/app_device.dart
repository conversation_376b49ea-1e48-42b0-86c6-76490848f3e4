import 'package:habi_app/helpers/sait85r_helper.dart';

class AppDevice {
  final String id;
  final String gwThingName;
  final String vid;
  final String pid;
  final String macAddress;

  AppDevice({
    required this.id,
    required this.gwThingName,
    required this.vid,
    required this.pid,
    required this.macAddress,
  });

  factory AppDevice.fromDeviceId(String deviceId) {
    var result = Sait85rHelper.parseThingName(deviceId);
    if (result.isEmpty) {
      return AppDevice(
        id: deviceId,
        gwThingName: '',
        vid: '',
        pid: '',
        macAddress: '',
      );
    }

    var gwThingName = result['gwThingName'] as String;
    var vid = result['vid'] as String;
    var pid = result['pid'] as String;
    var macAddress = result['macAddress'] as String;

    return AppDevice(
      id: deviceId,
      gwThingName: gwThingName,
      vid: vid,
      pid: pid,
      macAddress: macAddress,
    );
  }

}