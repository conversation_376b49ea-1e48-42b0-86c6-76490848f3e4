import 'dart:convert';

class AppShadow {
  final String thingName;
  final Map<String, dynamic> thingShadow;

  AppShadow({
    required this.thingName,
    required this.thingShadow,
  });

  factory AppShadow.empty(String thingName) {
    return AppShadow(
      thingName: thingName,
      thingShadow: {},
    );
  }

  AppShadow copy() {
    return AppShadow(
      thingName: thingName,
      thingShadow: json.decode(json.encode(thingShadow)),
    );
  }

  @override
  String toString() {
    return 'AppShadow(thingName: $thingName)';
  }
}
