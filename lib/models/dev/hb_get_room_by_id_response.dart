import 'package:habi_app/models/dev/hb_room.dart';

class HbGetRoomByDeviceIdResponse {
  bool? success;
  num? errorCode;
  HBRoom? room;

  HbGetRoomByDeviceIdResponse({
    this.success,
    this.errorCode,
    this.room,
  });

  factory HbGetRoomByDeviceIdResponse.fromJson(Map<String, dynamic> json) => HbGetRoomByDeviceIdResponse(
    success: json["success"],
    errorCode: json["error_code"],
    room: HBRoom.fromJson(json["room"]),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "room": room?.toJson(),
  };

}