import 'package:habi_app/models/dev/hb_home.dart';

class HbGetHomeByDeviceIdResponse {
  bool? success;
  num? errorCode;
  HBHome? home;

  HbGetHomeByDeviceIdResponse({
    this.success,
    this.errorCode,
    this.home,
  });

  factory HbGetHomeByDeviceIdResponse.fromJson(Map<String, dynamic> json) => HbGetHomeByDeviceIdResponse(
    success: json["success"],
    errorCode: json["error_code"],
    home: HBHome.fromJson(json["home"]),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "home": home?.toJson(),
  };

}