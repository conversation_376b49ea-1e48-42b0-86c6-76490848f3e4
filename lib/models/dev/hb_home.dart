import 'package:habi_app/models/dev/hb_attribute.dart';

class HBHome {
  String? userId;
  String? homeId;
  String? name;
  HBAttribute? attributes;
  List<String>? deviceList;
  List<String>? owner;
  List<String>? sharer;

  HBHome({
    this.userId,
    this.homeId,
    this.name,
    this.attributes,
    this.deviceList,
    this.owner,
    this.sharer,
  });

  factory HBHome.fromJson(Map<String, dynamic> json) {
    return HBHome(
      userId: json['UserID'],
      homeId: json['HomeID'],
      name: json['Name'],
      attributes: json['Attributes'] == null ? null : HBAttribute.fromJson(json['Attributes']),
      deviceList: json['DeviceList'] == null ? null : List<String>.from(json['DeviceList'].map((x) => x)),
      owner: json['Owner'] == null ? null : List<String>.from(json['Owner'].map((x) => x)),
      sharer: json['Sharer'] == null ? null : List<String>.from(json['Sharer'].map((x) => x)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'UserID': userId,
      'HomeID':homeId,
      'Name': name,
      'Attributes': attributes?.toJson(),
      'DeviceList': deviceList?.map((x) => x).toList(),
      'Owner': owner?.map((x) => x).toList(),
      'Sharer': sharer?.map((x) => x).toList(),
    };
  }
}