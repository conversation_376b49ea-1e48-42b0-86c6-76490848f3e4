class HBAddRoomResponse {
  bool? success;
  int? errorCode;
  String? roomId;

  HBAddRoomResponse({
    this.success,
    this.errorCode,
    this.roomId,
  });

  factory HBAddRoomResponse.fromJson(Map<String, dynamic> json) => HBAddRoomResponse(
    success: json["success"],
    errorCode: json["error_code"],
    roomId: json["roomId"],
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "roomId": roomId,
  };

}