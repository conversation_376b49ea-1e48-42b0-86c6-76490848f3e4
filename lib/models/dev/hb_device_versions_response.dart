import 'package:habi_app/models/dev/hb_version.dart';

class HbDeviceVersionsResponse {
  bool? success;
  int? errorCode;
  List<HbVersion>? versions;

  HbDeviceVersionsResponse({
    this.success,
    this.errorCode,
    this.versions,
  });

  factory HbDeviceVersionsResponse.fromJson(Map<String, dynamic> json) => HbDeviceVersionsResponse(
    success: json["success"],
    errorCode: json["error_code"],
    versions: json["versions"] == null ? null : List<HbVersion>
        .from(json["versions"].map((x) => HbVersion.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "versions": versions == null ? null : List<dynamic>.from(versions!.map((x) => x.toJson())),
  };

}