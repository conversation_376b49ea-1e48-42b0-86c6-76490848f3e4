import 'package:habi_app/models/dev/hb_home.dart';

class HbGetHomeByIdResponse {
  bool? success;
  num? errorCode;
  HBHome? home;

  HbGetHomeByIdResponse({
    this.success,
    this.errorCode,
    this.home,
  });

  factory HbGetHomeByIdResponse.fromJson(Map<String, dynamic> json) => HbGetHomeByIdResponse(
    success: json["success"],
    errorCode: json["error_code"],
    home: HBHome.fromJson(json["home"]),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "home": home?.toJson(),
  };

}