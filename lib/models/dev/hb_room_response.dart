import 'package:habi_app/models/dev/hb_room.dart';

class HBRoomResponse {
  bool? success;
  num? errorCode;
  bool? areMorePages;
  List<HBRoom>? rooms;

  HBRoomResponse({
    this.success,
    this.errorCode,
    this.areMorePages,
    this.rooms,
  });

  factory HBRoomResponse.fromJson(Map<String, dynamic> json) => HBRoomResponse(
    success: json["success"],
    errorCode: json["error_code"],
    areMorePages: json["areMorePages"],
    rooms: json["rooms"] == null ? null : List<HBRoom>.from(json["rooms"].map((x) => HBRoom.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "areMorePages": areMorePages,
    "rooms": rooms == null ? null : List<dynamic>.from(rooms!.map((x) => x.toJson())),
  };

}