
class HBUpdateDeviceResponse {
  bool? success;
  int? errorCode;
  Map<String, dynamic>? otaStatus;

  HBUpdateDeviceResponse({
    this.success,
    this.errorCode,
    this.otaStatus,
  });

  factory HBUpdateDeviceResponse.fromJson(Map<String, dynamic> json) => HBUpdateDeviceResponse(
    success: json["success"],
    errorCode: json["error_code"],
    otaStatus: json["ota_status"],
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "ota_status": otaStatus,
  };

}