import 'package:habi_app/models/dev/hb_home.dart';

class HBHomeResponse {
  bool? success;
  num? errorCode;
  bool? areMorePages;
  List<HBHome>? homes;

  HBHomeResponse({
    this.success,
    this.errorCode,
    this.areMorePages,
    this.homes,
  });

  factory HBHomeResponse.fromJson(Map<String, dynamic> json) => HBHomeResponse(
    success: json["success"],
    errorCode: json["error_code"],
    areMorePages: json["areMorePages"],
    homes: json["homes"] == null ? null : List<HBHome>.from(json["homes"].map((x) => HBHome.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "areMorePages": areMorePages,
    "homes": homes == null ? null : List<dynamic>.from(homes!.map((x) => x.toJson())),
  };

}