class HBAddHomeResponse {
  bool? success;
  int? errorCode;
  String? homeId;

  HBAddHomeResponse({
    this.success,
    this.errorCode,
    this.homeId,
  });

  factory HBAddHomeResponse.fromJson(Map<String, dynamic> json) => HBAddHomeResponse(
    success: json["success"],
    errorCode: json["error_code"],
    homeId: json["homeId"],
  );

  Map<String, dynamic> toJson() => {
    "success": success,
    "error_code": errorCode,
    "homeId": homeId,
  };

}