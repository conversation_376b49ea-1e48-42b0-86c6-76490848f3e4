
class HbVersion {
  final String model;
  final String version;

  HbVersion({
    required this.model,
    required this.version,
  });

  factory HbVersion.fromJson(Map<String, dynamic> json) {
    return HbVersion(
      model: json['model'],
      version: json['version'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'model': model,
      'version': version,
    };
  }

  @override
  String toString() {
    return 'HbVersion(model: $model, version: $version)';
  }
}