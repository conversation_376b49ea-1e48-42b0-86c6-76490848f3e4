import 'package:habi_app/models/dev/hb_attribute.dart';

class HBRoom {
  String? roomId;
  String? homeId;
  String? name;
  HBAttribute? attributes;
  List<String>? deviceList;

  HBRoom({
    this.roomId,
    this.homeId,
    this.name,
    this.attributes,
    this.deviceList,
  });

  factory HBRoom.fromJson(Map<String, dynamic> json) {
    return HBRoom(
      roomId: json['RoomID'],
      homeId: json['HomeID'],
      name: json['Name'],
      attributes: json['Attributes'] != null ? HBAttribute.fromJson(json['Attributes']) : null,
      deviceList: json['DeviceList'] != null ? List<String>.from(json['DeviceList'].map((x) => x)) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'RoomID': roomId,
      'HomeID': homeId,
      'Name': name,
      'Attributes': attributes?.toJson(),
      'DeviceList': deviceList?.map((x) => x).toList(),
    };
  }
}