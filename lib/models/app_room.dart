import 'package:habi_app/models/app_attribute.dart';
import 'package:habi_app/models/dev/hb_room.dart';

import 'app_device.dart';

class AppRoom {
  String? roomId;
  String? homeId;
  String? name;
  AppAttribute? attributes;
  List<String>? deviceList;

  AppRoom({
    this.roomId,
    this.homeId,
    this.name,
    this.attributes,
    this.deviceList,
  });

  factory AppRoom.fromHBRoom(HBRoom room) {
    AppRoom appRoom = AppRoom();
    appRoom = AppRoom();
    appRoom.roomId = room.roomId;
    appRoom.homeId = room.homeId;
    appRoom.name = room.name;
    appRoom.attributes = AppAttribute();
    appRoom.attributes!.address = room.attributes?.address;
    appRoom.deviceList = room.deviceList;
    return appRoom;
  }

  factory AppRoom.fromJson(Map<String, dynamic> json) {
    return AppRoom(
      roomId: json['roomId'],
      homeId: json['homeId'],
      name: json['name'],
      attributes: AppAttribute.fromJson(json['attributes']),
      deviceList: json['deviceList'] != null
          ? List<String>.from(json['deviceList'].map((x) => x)) : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'roomId': roomId,
    'homeId': homeId,
    'name': name,
    'attributes': attributes?.toJson(),
    'deviceList': deviceList,
  };

  @override
  String toString() {
    return 'AppRoom(roomId: $roomId, homeId: $homeId, name: $name, attributes: $attributes, deviceList: $deviceList)';
  }
}
