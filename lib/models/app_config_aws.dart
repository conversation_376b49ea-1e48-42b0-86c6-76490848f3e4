class AppConfigAws {
  String region;
  String endpointUrl;
  late String httpsEndpointUrl;

  String mqttScheme;
  String mqttUrlPath;
  int mqttPort;

  AppConfigAws({
    required this.region,
    required this.endpointUrl,
    required this.mqttScheme,
    required this.mqttUrlPath,
    required this.mqttPort,
  }) {
    httpsEndpointUrl = 'https://$endpointUrl';
  }

  factory AppConfigAws.fromJson(Map<String, dynamic> json) {
    return AppConfigAws(
      region: json['region'],
      endpointUrl: json['endpointUrl'],
      mqttScheme: json['mqttScheme'],
      mqttUrlPath: json['mqttUrlPath'],
      mqttPort: json['mqttPort'],
    );
  }

  @override
  String toString() {
    return 'AppConfigAws{'
        'region: $region, '
        'endpointUrl: $endpointUrl, '
        'httpsEndpointUrl: $httpsEndpointUrl, '
        'mqttScheme: $mqttScheme, '
        'mqttUrlPath: $mqttUrlPath, '
        'mqttPort: $mqttPort'
        '}';
  }
}
