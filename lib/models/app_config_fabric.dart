
class AppConfigFabric{

  final String scheme;
  final String host;
  final String path;

  AppConfigFabric({
    required this.scheme,
    required this.host,
    required this.path,
  });

  factory AppConfigFabric.fromJson(Map<String, dynamic> json) {
    return AppConfigFabric(
      scheme: json['scheme'],
      host: json['host'],
      path: json['path'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'scheme': scheme,
      'host': host,
      'path': path,
    };
  }

  @override
  String toString() {
    return 'AppConfigFabric{'
        'scheme: $scheme, '
        'host: $host, '
        'path: $path'
        '}';
  }

}
