import 'package:habi_app/models/aws_iot_template/s_base.dart';

class STherS extends SBase {
  int? localTemp;
  int? sHeatingSp;
  int? heatingSp;
  int? heatingSpA;
  int? coolingSp;
  int? coolingSpA;
  int? minHeatSp;
  int? maxHeatSp;
  int? minCoolSp;
  int? maxCoolSp;
  int? tempCalibr;
  int? sSystemMode;
  int? systemMode;
  int? systemModeA;
  int? runningState;

  STherS(super.epx, super.ctx,{
    this.localTemp,
    this.sHeatingSp,
    this.heatingSp,
    this.heatingSpA,
    this.coolingSp,
    this.coolingSpA,
    this.minHeatSp,
    this.maxHeatSp,
    this.minCoolSp,
    this.maxCoolSp,
    this.tempCalibr,
    this.sSystemMode,
    this.systemMode,
    this.systemModeA,
    this.runningState,
  });

  factory STherS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      STherS(
        epx,
        ctx,
        localTemp: json?['$epx:$ctx:LocalTemp'],
        sHeatingSp: json?['$epx:$ctx:sHeatingSp'],
        heatingSp: json?['$epx:$ctx:HeatingSp'],
        heatingSpA: json?['$epx:$ctx:HeatingSp_a'],
        coolingSp: json?['$epx:$ctx:CoolingSp'],
        coolingSpA: json?['$epx:$ctx:CoolingSp_a'],
        minHeatSp: json?['$epx:$ctx:MinHeatSp'],
        maxHeatSp: json?['$epx:$ctx:MaxHeatSp'],
        minCoolSp: json?['$epx:$ctx:MinCoolSp'],
        maxCoolSp: json?['$epx:$ctx:MaxCoolSp'],
        tempCalibr: json?['$epx:$ctx:TempCalibr'],
        sSystemMode: json?['$epx:$ctx:sSystemMode'],
        systemMode: json?['$epx:$ctx:SystemMode'],
        systemModeA: json?['$epx:$ctx:SystemMode_a'],
        runningState: json?['$epx:$ctx:RunningState'],
      );

  STherS mapJson(Map<String, dynamic>? json) {
    localTemp = json?['$epx:$ctx:LocalTemp'] ?? localTemp;
    sHeatingSp = json?['$epx:$ctx:sHeatingSp'] ?? sHeatingSp;
    heatingSp = json?['$epx:$ctx:HeatingSp'] ?? heatingSp;
    heatingSpA = json?['$epx:$ctx:HeatingSp_a'] ?? heatingSpA;
    coolingSp = json?['$epx:$ctx:CoolingSp'] ?? coolingSp;
    coolingSpA = json?['$epx:$ctx:CoolingSp_a'] ?? coolingSpA;
    minHeatSp = json?['$epx:$ctx:MinHeatSp'] ?? minHeatSp;
    maxHeatSp = json?['$epx:$ctx:MaxHeatSp'] ?? maxHeatSp;
    minCoolSp = json?['$epx:$ctx:MinCoolSp'] ?? minCoolSp;
    maxCoolSp = json?['$epx:$ctx:MaxCoolSp'] ?? maxCoolSp;
    tempCalibr = json?['$epx:$ctx:TempCalibr'] ?? tempCalibr;
    sSystemMode = json?['$epx:$ctx:sSystemMode'] ?? sSystemMode;
    systemMode = json?['$epx:$ctx:SystemMode'] ?? systemMode;
    systemModeA = json?['$epx:$ctx:SystemMode_a'] ?? systemModeA;
    runningState = json?['$epx:$ctx:RunningState'] ?? runningState;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:LocalTemp': localTemp,
    '$epx:$ctx:sHeatingSp': sHeatingSp,
    '$epx:$ctx:HeatingSp': heatingSp,
    '$epx:$ctx:HeatingSp_a': heatingSpA,
    '$epx:$ctx:CoolingSp': coolingSp,
    '$epx:$ctx:CoolingSp_a': coolingSpA,
    '$epx:$ctx:MinHeatSp': minHeatSp,
    '$epx:$ctx:MaxHeatSp': maxHeatSp,
    '$epx:$ctx:MinCoolSp': minCoolSp,
    '$epx:$ctx:MaxCoolSp': maxCoolSp,
    '$epx:$ctx:TempCalibr': tempCalibr,
    '$epx:$ctx:sSystemMode': sSystemMode,
    '$epx:$ctx:SystemMode': systemMode,
    '$epx:$ctx:SystemMode_a': systemModeA,
    '$epx:$ctx:RunningState': runningState,
  };

}