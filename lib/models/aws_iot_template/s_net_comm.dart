import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SNetComm extends SBase {
  int? rssi;

  SNetComm(
    super.epx,
    super.ctx, {
    this.rssi,
  });

  factory SNetComm.fromJson(
    String epx,
    String ctx,
    Map<String, dynamic>? json,
  ) =>
      SNetComm(
        epx,
        ctx,
        rssi: json?['$epx:$ctx:RSSI'],
      );

  SNetComm mapJson(Map<String, dynamic>? json) {
    rssi = json?['$epx:$ctx:RSSI'] ?? rssi;
    return this;
  }

  Map<String, dynamic> toJson() => {
        '$epx:$ctx:RSSI': rssi,
      };
}
