import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SOtaTS extends SBase {
  int? otaStatusD;
  int? otaFirmwareURLD;
  int? otaFirmwareVersionD;

  SOtaTS(super.epx, super.ctx,{
    this.otaStatusD,
    this.otaFirmwareURLD,
    this.otaFirmwareVersionD,
  });

  factory SOtaTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SOtaTS(
        epx,
        ctx,
        otaStatusD: json?['$epx:$ctx:OTAStatus_d']?["timestamp"],
        otaFirmwareURLD: json?['$epx:$ctx:OTAFirmwareURL_d']?["timestamp"],
        otaFirmwareVersionD: json?['$epx:$ctx:OTAFirmwareVersion_d']?["timestamp"],
      );

  SOtaTS mapJson(Map<String, dynamic>? json) {
    otaStatusD = json?['$epx:$ctx:OTAStatus_d']?["timestamp"] ?? otaStatusD;
    otaFirmwareURLD = json?['$epx:$ctx:OTAFirmwareURL_d']?["timestamp"] ?? otaFirmwareURLD;
    otaFirmwareVersionD = json?['$epx:$ctx:OTAFirmwareVersion_d']?["timestamp"] ?? otaFirmwareVersionD;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:OTAStatus_d': {
      "timestamp": otaStatusD
    },
    '$epx:$ctx:OTAFirmwareURL_d': {
      "timestamp": otaFirmwareURLD
    },
    '$epx:$ctx:OTAFirmwareVersion_d': {
      "timestamp": otaFirmwareVersionD
    },
  };

}