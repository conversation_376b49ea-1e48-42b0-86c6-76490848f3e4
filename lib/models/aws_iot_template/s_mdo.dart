import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SMdo extends SBase {
  String? euid;
  String? nodeID;
  String? deviceName;
  String? sDeviceName;

  SMdo(super.epx, super.ctx,{
    this.euid,
    this.nodeID,
    this.deviceName,
    this.sDeviceName,
  });

  factory SMdo.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SMdo(
        epx,
        ctx,
        euid: json?['$epx:$ctx:Euid'],
        nodeID: json?['$epx:$ctx:NodeID'],
        deviceName: json?['$epx:$ctx:DeviceName'],
        sDeviceName: json?['$epx:$ctx:sDeviceName'],
      );

  SMdo mapJson(Map<String, dynamic>? json) {
    euid = json?['$epx:$ctx:Euid'] ?? euid;
    nodeID = json?['$epx:$ctx:NodeID'] ?? nodeID;
    deviceName = json?['$epx:$ctx:DeviceName'] ?? deviceName;
    sDeviceName = json?['$epx:$ctx:sDeviceName'] ?? sDeviceName;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:Euid': euid,
    '$epx:$ctx:NodeID': nodeID,
    '$epx:$ctx:DeviceName': deviceName,
    '$epx:$ctx:sDeviceName': sDeviceName,
  };


}