import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SGatewayTS extends SBase {
  int? gatewaySoftwareVersion;
  int? gatewayHardwareVersion;
  int? networkWiFiMAC;
  int? modelIdentifier;
  int? deviceName;
  int? deviceFullList;
  int? networkSSID;
  int? networkPasswordF;
  int? timeZone;
  int? wifiRssi;

  SGatewayTS(super.epx, super.ctx, {
    this.gatewaySoftwareVersion,
    this.gatewayHardwareVersion,
    this.networkWiFiMAC,
    this.modelIdentifier,
    this.deviceName,
    this.deviceFullList,
    this.networkSSID,
    this.networkPasswordF,
    this.timeZone,
    this.wifiRssi,
  });

factory SGatewayTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SGatewayTS(
        epx,
        ctx,
        gatewaySoftwareVersion: json?['$epx:$ctx:GatewaySoftwareVersion']?["timestamp"],
        gatewayHardwareVersion: json?['$epx:$ctx:GatewayHardwareVersion']?["timestamp"],
        networkWiFiMAC: json?['$epx:$ctx:NetworkWiFiMAC']?["timestamp"],
        modelIdentifier: json?['$epx:$ctx:ModelIdentifier']?["timestamp"],
        deviceName: json?['$epx:$ctx:DeviceName']?["timestamp"],
        deviceFullList: json?['$epx:$ctx:DeviceFullList']?["timestamp"],
        networkSSID: json?['$epx:$ctx:NetworkSSID']?["timestamp"],
        networkPasswordF: json?['$epx:$ctx:NetworkPassword_f']?["timestamp"],
        timeZone: json?['$epx:$ctx:TimeZone']?["timestamp"],
        wifiRssi: json?['$epx:$ctx:WiFiRSSI']?["timestamp"],
      );

  SGatewayTS mapJson(Map<String, dynamic>? json) {
    gatewaySoftwareVersion = json?['$epx:$ctx:GatewaySoftwareVersion']?["timestamp"] ?? gatewaySoftwareVersion;
    gatewayHardwareVersion = json?['$epx:$ctx:GatewayHardwareVersion']?["timestamp"] ?? gatewayHardwareVersion;
    networkWiFiMAC = json?['$epx:$ctx:NetworkWiFiMAC']?["timestamp"] ?? networkWiFiMAC;
    modelIdentifier = json?['$epx:$ctx:ModelIdentifier']?["timestamp"] ?? modelIdentifier;
    deviceName = json?['$epx:$ctx:DeviceName']?["timestamp"] ?? deviceName;
    deviceFullList = json?['$epx:$ctx:DeviceFullList']?["timestamp"] ?? deviceFullList;
    networkSSID = json?['$epx:$ctx:NetworkSSID']?["timestamp"] ?? networkSSID;
    networkPasswordF = json?['$epx:$ctx:NetworkPassword_f']?["timestamp"] ?? networkPasswordF;
    timeZone = json?['$epx:$ctx:TimeZone']?["timestamp"] ?? timeZone;
    wifiRssi = json?['$epx:$ctx:WiFiRSSI']?["timestamp"] ?? wifiRssi;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:GatewaySoftwareVersion': {
      "timestamp": gatewaySoftwareVersion,
    },
    '$epx:$ctx:GatewayHardwareVersion': {
      "timestamp": gatewayHardwareVersion,
    },
    '$epx:$ctx:NetworkWiFiMAC': {
      "timestamp": networkWiFiMAC,
    },
    '$epx:$ctx:ModelIdentifier': {
      "timestamp": modelIdentifier,
    },
    '$epx:$ctx:DeviceName': {
      "timestamp": deviceName,
    },
    '$epx:$ctx:DeviceFullList' : {
      "timestamp": deviceFullList,
    },
    '$epx:$ctx:NetworkSSID': {
      "timestamp": networkSSID,
    },
    '$epx:$ctx:NetworkPassword_f': {
      "timestamp": networkPasswordF,
    },
    '$epx:$ctx:TimeZone': {
      "timestamp": timeZone,
    },
    '$epx:$ctx:WiFiRSSI': {
      "timestamp": wifiRssi,
    },
  };

}