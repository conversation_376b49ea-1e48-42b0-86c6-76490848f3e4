import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/constants/app_mode_type.dart';
import 'package:habi_app/models/aws_iot_template/s_time_gh.dart';
import 'package:habi_app/models/aws_iot_template/s_time_h.dart';
import 'package:habi_app/models/schedule/schedule.dart';

class Schedules {
  Schedules(
    this.epx, {
    this.sTimeGh,
    this.sTimeH,
    required this.sMode,
    required this.sTemplateMode,
  });

  factory Schedules.fromJson(
    String epx,
    Map<String, dynamic>? json, {
    required ScheduleMode sMode,
    required ScheduleTemplateMode sTemplateMode,
  }) {
    return Schedules(
      epx,
      sTimeGh: STimeGh.fromJson(epx, 'sTimeGH', sMode, json),
      sTimeH: STimeH.fromJson(epx, 'sTimeH', sMode, json),
      sMode: sMode,
      sTemplateMode: sTemplateMode,
    );
  }

  STimeGh? sTimeGh;
  STimeH? sTimeH;
  String epx;
  ScheduleMode sMode;
  ScheduleTemplateMode sTemplateMode;

  Schedules mapJson(Map<String, dynamic>? json) {
    if (sTimeGh != null) {
      sTimeGh = sTimeGh?.mapJson(json);
    }
    if (sTimeH != null) {
      sTimeH = sTimeH?.mapJson(json);
    }
    return this;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> value = {};
    if (sTimeGh != null) {
      value.addAll(sTimeGh!.toJson());
    }
    if (sTimeH != null) {
      value.addAll(sTimeH!.toJson());
    }
    return value;
  }

  String getEpXForSchedule() {
    return epx;
  }

  ScheduleTemplateMode getParseScheduleTemplateMode() {
    return sTemplateMode;
  }

  Future<void> parseSchedules() async {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.parseSchedules();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.parseSchedules();
        break;
    }
  }

  bool isScheduleEmpty() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        return false;
      case ScheduleTemplateMode.sTimeGh:
        return sTimeGh?.isScheduleEmpty() ?? true;
      case ScheduleTemplateMode.sTimeH:
        return sTimeH?.isScheduleEmpty() ?? true;
      default:
        return false;
    }
  }

  bool isAllActive() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        return false;
      case ScheduleTemplateMode.sTimeGh:
        return sTimeGh?.isAllActive() ?? false;
      case ScheduleTemplateMode.sTimeH:
        return sTimeH?.isAllActive() ?? false;
      default:
        return false;
    }
  }

  void setEnValue(int value) {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.setEnValue(value);
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.setEnValue(value);
        break;
      default:
        break;
    }
  }

  void buildScheduleStrings() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.buildScheduleStrings();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.buildScheduleStrings();
        break;
    }
  }

  List<Schedule> getMonToSun() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.monSun ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.monSun ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getMonToFri() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.monFir ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.monFir ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getSatToSun() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.satSun ?? [];
        break;
     case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.satSun ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getMonToFriHoliday() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.monFirHoliday ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.monFirHoliday ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getMon() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.mon ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.mon ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getTue() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.tue ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.tue ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getWed() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.wed ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.wed ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getThu() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.thu ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.thu ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getFri() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.fri ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.fri ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getSat() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.sat ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.sat ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getSun() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.sun ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.sun ?? [];
        break;
    }
    return list;
  }

  List<Schedule> getSunHoliday() {
    List<Schedule> list = [];
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        list = sTimeGh?.sunHoliday ?? [];
        break;
      case ScheduleTemplateMode.sTimeH:
        list = sTimeH?.sunHoliday ?? [];
        break;
    }
    return list;
  }

  void clearSchedulesWithScheduleUpdateFlag() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.clearSchedulesWithScheduleUpdateFlag();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.clearSchedulesWithScheduleUpdateFlag();
        break;
    }
  }

  void clearSchedules() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.clearSchedules();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.clearSchedules();
    }
  }

  void clearOtherTypeSchedules() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.clearOtherTypeSchedules();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.clearOtherTypeSchedules();
    }
  }

  void setCurrentSelectedDayIndex(int index) {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.currentSelectedDayIndex = index;
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.currentSelectedDayIndex = index;
        break;
    }
  }

  int getCurrentSelectedDayIndex() {
    int index = 0;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        index = sTimeGh?.currentSelectedDayIndex ?? 0;
        break;
      case ScheduleTemplateMode.sTimeH:
        index = sTimeH?.currentSelectedDayIndex ?? 0;
        break;
    }
    return index;
  }

  bool setIsScheduleUpdate({bool? isScheduleUpdate}) {
    isScheduleUpdate ??= false;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.isScheduleUpdate = isScheduleUpdate;
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.isScheduleUpdate = isScheduleUpdate;
        break;
    }
    return isScheduleUpdate;
  }

  void cancel() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.cancel();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.cancel();
    }
  }

  void changeBackScheduleType() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.changeBackScheduleType();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.changeBackScheduleType();
    }
  }

  void updateCopy() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.updateCopy();
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.updateCopy();
    }
  }

  bool getIsScheduleUpdate() {
    bool isScheduleUpdate = false;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        isScheduleUpdate = sTimeGh?.isScheduleUpdate ?? false;
        break;
      case ScheduleTemplateMode.sTimeH:
        isScheduleUpdate = sTimeH?.isScheduleUpdate ?? false;
        break;
    }
    return isScheduleUpdate;
  }

  void removeSchedule(Schedule objSchedule) {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.removeSchedule(objSchedule);
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.removeSchedule(objSchedule);
        break;
    }
  }

  void addUpdateSchedules(Schedule objSchedule) {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.addUpdateSchedules(objSchedule);
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.addUpdateSchedules(objSchedule);
        break;
    }
  }

  void setScheduleType(int value) {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.scheduleType = value;
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.scheduleType = value;
        break;
    }
  }

  String getUntilMessage() {
    String value = "";
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        value = sTimeGh?.getUntilMessage() ?? "";
        break;
      case ScheduleTemplateMode.sTimeH:
        value = sTimeH?.getUntilMessage() ?? "";
    }
    return value;
  }

  bool isDeleteAllInterval() {
    bool value = false;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        value = sTimeGh?.isDeleteAllInterval() ?? false;
        break;
      case ScheduleTemplateMode.sTimeH:
        value = sTimeH?.isDeleteAllInterval() ?? false;
        break;
    }
    return value;
  }

  bool isDeleteAllIntervalVisible() {
    bool value = false;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        value = sTimeGh?.isDeleteAllIntervalVisible() ?? false;
        break;
      case ScheduleTemplateMode.sTimeH:
        value = sTimeH?.isDeleteAllIntervalVisible() ?? false;
    }
    return value;
  }

  // List<Widget> getTabBarContainer(
  //     String emptyMessage,
  //     ScheduleTabBarHeader value, {
  //       ScheduleMode mode,
  //       SliderListItemDeviceModel item,
  //     }) {
  //   List<Widget> list = [];
  //   switch (getParseScheduleTemplateMode()) {
  //     case ScheduleTemplateMode.none:
  //       break;
  //     case ScheduleTemplateMode.sTimeGh:
  //       list = sTimeGh?.getTabBarContainer(emptyMessage, value) ?? [];
  //       break;
  //   }
  //   return list;
  // }

  Map<String, dynamic> publishSetSchedule() {
    Map<String, dynamic> value = {};
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        value = sTimeGh?.publishEpxsTimeGhSetSchedules() ?? {};
        break;
      case ScheduleTemplateMode.sTimeH:
        value = sTimeH?.publishEpxsTimeHSetSchedules() ?? {};
        break;
    }
    return value;
  }

  int limit() {
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        return 0;
      case ScheduleTemplateMode.sTimeGh:
        return sTimeGh?.limit() ?? 6;
      case ScheduleTemplateMode.sTimeH:
        return sTimeH?.limit() ?? 6;
    }
  }

  void setScheduleMode(ScheduleMode mode) {
    sMode = mode;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        sTimeGh?.scheduleMode = mode;
        break;
      case ScheduleTemplateMode.sTimeH:
        sTimeH?.scheduleMode = mode;
        break;
    }
  }

  // int getSelectedScheduleType() {
  //   int value;
  //   switch (getParseScheduleTemplateMode()) {
  //     case ScheduleTemplateMode.none:
  //       break;
  //     case ScheduleTemplateMode.sTimeGh:
  //       value = sTimeGh?.getSelectedScheduleType();
  //       break;
  //   }
  //   return value;
  // }

  int getScheduleType() {
    int value = ScheduleConfigurationTypes.WORKWEEK;
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        value = sTimeGh?.scheduleType ?? ScheduleConfigurationTypes.WORKWEEK;
        break;
      case ScheduleTemplateMode.sTimeH:
        value = sTimeH?.scheduleType ?? ScheduleConfigurationTypes.WORKWEEK;
        break;
    }
    return value;
  }

  Schedule getDefaultSchedule(int dayIndex) {
    Schedule schedule = Schedule(
      dayIndex: dayIndex,
      timeFormat24Hour: 0,
      scheduleMode: sMode,
      isNew: true,
    );
    switch (getParseScheduleTemplateMode()) {
      case ScheduleTemplateMode.none:
        break;
      case ScheduleTemplateMode.sTimeGh:
        schedule = sTimeGh?.getDefaultSchedule(dayIndex) ?? schedule;
        break;
      case ScheduleTemplateMode.sTimeH:
        schedule = sTimeH?.getDefaultSchedule(dayIndex) ?? schedule;
        break;
    }
    return schedule;
  }
}
