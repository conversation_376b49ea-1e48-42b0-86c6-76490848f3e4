import 'package:habi_app/models/aws_iot_template/s_base.dart';

class STherSTS extends SBase {
  int? localTemp;
  int? sHeatingSp;
  int? heatingSp;
  int? heatingSpA;
  int? coolingSp;
  int? coolingSpA;
  int? minHeatSp;
  int? maxHeatSp;
  int? minCoolSp;
  int? maxCoolSp;
  int? tempCalibr;
  int? sSystemMode;
  int? systemMode;
  int? systemModeA;
  int? runningState;

  STherSTS(super.epx, super.ctx,{
    this.localTemp,
    this.sHeatingSp,
    this.heatingSp,
    this.heatingSpA,
    this.coolingSp,
    this.coolingSpA,
    this.minHeatSp,
    this.maxHeatSp,
    this.minCoolSp,
    this.maxCoolSp,
    this.tempCalibr,
    this.sSystemMode,
    this.systemMode,
    this.systemModeA,
    this.runningState,
  });

  factory STherSTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      STherSTS(
        epx,
        ctx,
        localTemp: json?['$epx:$ctx:LocalTemp']?["timestamp"],
        sHeatingSp: json?['$epx:$ctx:sHeatingSp']?["timestamp"],
        heatingSp: json?['$epx:$ctx:HeatingSp']?["timestamp"],
        heatingSpA: json?['$epx:$ctx:HeatingSp_a']?["timestamp"],
        coolingSp: json?['$epx:$ctx:CoolingSp']?["timestamp"],
        coolingSpA: json?['$epx:$ctx:CoolingSp_a']?["timestamp"],
        minHeatSp: json?['$epx:$ctx:MinHeatSp']?["timestamp"],
        maxHeatSp: json?['$epx:$ctx:MaxHeatSp']?["timestamp"],
        minCoolSp: json?['$epx:$ctx:MinCoolSp']?["timestamp"],
        maxCoolSp: json?['$epx:$ctx:MaxCoolSp']?["timestamp"],
        tempCalibr: json?['$epx:$ctx:TempCalibr']?["timestamp"],
        sSystemMode: json?['$epx:$ctx:sSystemMode']?["timestamp"],
        systemMode: json?['$epx:$ctx:SystemMode']?["timestamp"],
        systemModeA: json?['$epx:$ctx:SystemMode_a']?["timestamp"],
        runningState: json?['$epx:$ctx:RunningState']?["timestamp"],
      );

  STherSTS mapJson(Map<String, dynamic>? json) {
    localTemp = json?['$epx:$ctx:LocalTemp']?["timestamp"] ?? localTemp;
    sHeatingSp = json?['$epx:$ctx:sHeatingSp']?["timestamp"] ?? sHeatingSp;
    heatingSp = json?['$epx:$ctx:HeatingSp']?["timestamp"] ?? heatingSp;
    heatingSpA = json?['$epx:$ctx:HeatingSp_a']?["timestamp"] ?? heatingSpA;
    coolingSp = json?['$epx:$ctx:CoolingSp']?["timestamp"] ?? coolingSp;
    coolingSpA = json?['$epx:$ctx:CoolingSp_a']?["timestamp"] ?? coolingSpA;
    minHeatSp = json?['$epx:$ctx:MinHeatSp']?["timestamp"] ?? minHeatSp;
    maxHeatSp = json?['$epx:$ctx:MaxHeatSp']?["timestamp"] ?? maxHeatSp;
    minCoolSp = json?['$epx:$ctx:MinCoolSp']?["timestamp"] ?? minCoolSp;
    maxCoolSp = json?['$epx:$ctx:MaxCoolSp']?["timestamp"] ?? maxCoolSp;
    tempCalibr = json?['$epx:$ctx:TempCalibr']?["timestamp"] ?? tempCalibr;
    sSystemMode = json?['$epx:$ctx:sSystemMode']?["timestamp"] ?? sSystemMode;
    systemMode = json?['$epx:$ctx:SystemMode']?["timestamp"] ?? systemMode;
    systemModeA = json?['$epx:$ctx:SystemMode_a']?["timestamp"] ?? systemModeA;
    runningState = json?['$epx:$ctx:RunningState']?["timestamp"] ?? runningState;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:LocalTemp': {
      "timestamp": localTemp,
    },
    '$epx:$ctx:sHeatingSp': {
      "timestamp": sHeatingSp,
    },
    '$epx:$ctx:HeatingSp': {
      "timestamp": heatingSp,
    },
    '$epx:$ctx:HeatingSp_a': {
      "timestamp": heatingSpA,
    },
    '$epx:$ctx:CoolingSp': {
      "timestamp": coolingSp,
    },
    '$epx:$ctx:CoolingSp_a': {
      "timestamp": coolingSpA,
    },
    '$epx:$ctx:MinHeatSp': {
      "timestamp": minHeatSp,
    },
    '$epx:$ctx:MaxHeatSp': {
      "timestamp": maxHeatSp,
    },
    '$epx:$ctx:MinCoolSp': {
      "timestamp": minCoolSp,
    },
    '$epx:$ctx:MaxCoolSp': {
      "timestamp": maxCoolSp,
    },
    '$epx:$ctx:TempCalibr': {
      "timestamp": tempCalibr,
    },
    '$epx:$ctx:sSystemMode': {
      "timestamp": sSystemMode,
    },
    '$epx:$ctx:SystemMode': {
      "timestamp": systemMode,
    },
    '$epx:$ctx:SystemMode_a': {
      "timestamp": systemModeA,
    },
    '$epx:$ctx:RunningState': {
      "timestamp": runningState,
    },
  };

}