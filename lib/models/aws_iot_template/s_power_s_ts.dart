import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SPowerSTS extends SBase {
  int? batChargeL;
  int? status;

  SPowerSTS(super.epx, super.ctx,{
    this.batChargeL,
    this.status,
  });

  factory SPowerSTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SPowerSTS(
        epx,
        ctx,
        batChargeL: json?['$epx:$ctx:BatChargeL']?["timestamp"],
        status: json?['$epx:$ctx:Status']?["timestamp"],
      );

  SPowerSTS mapJson(Map<String, dynamic>? json) {
    batChargeL = json?['$epx:$ctx:BatChargeL']?["timestamp"] ?? batChargeL;
    status = json?['$epx:$ctx:Status']?["timestamp"] ?? status;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:BatChargeL': {
      "timestamp": batChargeL
    },
    '$epx:$ctx:Status': {
      "timestamp": status
    },
  };

}