import 's_base.dart';

class STherUISTS extends SBase {
  int? tempDispM;
  int? lockKey;

  STherUISTS(super.epx, super.ctx,{
    this.tempDispM,
    this.lockKey,
  });

  factory STherUISTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      STherUISTS(
        epx,
        ctx,
        tempDispM: json?['$epx:$ctx:TempDispM']?["timestamp"],
        lockKey: json?['$epx:$ctx:LockKey']?["timestamp"],
      );

  STherUISTS mapJson(Map<String, dynamic>? json) {
    tempDispM = json?['$epx:$ctx:TempDispM']?["timestamp"] ?? tempDispM;
    lockKey = json?['$epx:$ctx:LockKey']?["timestamp"] ?? lockKey;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:TempDispM': {
      "timestamp": tempDispM,
    },
    '$epx:$ctx:LockKey': {
      "timestamp": lockKey,
    },
  };
}