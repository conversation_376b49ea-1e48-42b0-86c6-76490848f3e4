import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SPowerS extends SBase {
  int? batChargeL;
  int? status;

  SPowerS(super.epx, super.ctx,{
    this.batChargeL,
    this.status,
  });

  factory SPowerS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SPowerS(
        epx,
        ctx,
        batChargeL: json?['$epx:$ctx:BatChargeL'],
        status: json?['$epx:$ctx:Status'],
      );

  SPowerS mapJson(Map<String, dynamic>? json) {
    batChargeL = json?['$epx:$ctx:BatChargeL'] ?? batChargeL;
    status = json?['$epx:$ctx:Status'] ?? status;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:BatChargeL': batChargeL,
    '$epx:$ctx:Status': status,
  };

}