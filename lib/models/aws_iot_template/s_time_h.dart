import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/constants/app_mode_type.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/helpers/app_number_helper.dart';
import 'package:habi_app/models/aws_iot_template/s_base.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class STimeH extends SBase {
  ScheduleMode? scheduleMode;

  int? scheduleType;
  int? scheduleTypeCopy;

  String? schedule1;
  String? schedule2;
  String? schedule3;
  String? schedule4;
  String? schedule5;
  String? schedule6;
  String? schedule7;

  int? scheduleEn;

  String? schedule1Copy;
  String? schedule2Copy;
  String? schedule3Copy;
  String? schedule4Copy;
  String? schedule5Copy;
  String? schedule6Copy;
  String? schedule7Copy;

  String setSchedule1;
  String setSchedule2;
  String setSchedule3;
  String setSchedule4;
  String setSchedule5;
  String setSchedule6;
  String setSchedule7;
  String setScheduleType;
  String setScheduleEn;

  int sTherUisTempDispM;
  int sTherSMinHeatSp;
  int sTherSMaxHeatSp;

  List<Schedule> mon = [];
  List<Schedule> tue = [];
  List<Schedule> wed = [];
  List<Schedule> thu = [];
  List<Schedule> fri = [];
  List<Schedule> sat = [];
  List<Schedule> sun = [];
  List<Schedule> monSun = [];
  List<Schedule> monFir = [];
  List<Schedule> satSun = [];
  List<Schedule> monFirHoliday = [];
  List<Schedule> satHoliday = [];
  List<Schedule> sunHoliday = [];

  String lastSchedule = "";
  int currentSelectedDayIndex = 0;
  String scheduleSeparator =
      List<String>.filled(8, "F", growable: true).join("");
  bool isScheduleUpdate = false;

  STimeH(super.epx, super.ctx,
      {this.scheduleMode,
      this.scheduleType,
      this.scheduleTypeCopy,
      this.schedule1,
      this.schedule2,
      this.schedule3,
      this.schedule4,
      this.schedule5,
      this.schedule6,
      this.schedule7,
      this.scheduleEn,
      this.schedule1Copy,
      this.schedule2Copy,
      this.schedule3Copy,
      this.schedule4Copy,
      this.schedule5Copy,
      this.schedule6Copy,
      this.schedule7Copy,
      required this.sTherUisTempDispM,
      required this.sTherSMinHeatSp,
      required this.sTherSMaxHeatSp,
      this.setSchedule1 = '',
      this.setSchedule2 = '',
      this.setSchedule3 = '',
      this.setSchedule4 = '',
      this.setSchedule5 = '',
      this.setSchedule6 = '',
      this.setSchedule7 = '',
      this.setScheduleType = '',
      this.setScheduleEn = ''
      });

  factory STimeH.fromJson(String epx, String ctx, ScheduleMode sMode, Map<String, dynamic>? json) {
    return STimeH(
      epx,
      ctx,
      scheduleMode: sMode,
      scheduleType: json?['$epx:$ctx:ScheduleType'],
      scheduleTypeCopy: json?['$epx:$ctx:ScheduleType'],
      schedule1: json?['$epx:$ctx:Schedule1'],
      schedule2: json?['$epx:$ctx:Schedule2'],
      schedule3: json?['$epx:$ctx:Schedule3'],
      schedule4: json?['$epx:$ctx:Schedule4'],
      schedule5: json?['$epx:$ctx:Schedule5'],
      schedule6: json?['$epx:$ctx:Schedule6'],
      schedule7: json?['$epx:$ctx:Schedule7'],
      scheduleEn: json?['$epx:$ctx:ScheduleEn'],
      schedule1Copy: json?['$epx:$ctx:Schedule1'],
      schedule2Copy: json?['$epx:$ctx:Schedule2'],
      schedule3Copy: json?['$epx:$ctx:Schedule3'],
      schedule4Copy: json?['$epx:$ctx:Schedule4'],
      schedule5Copy: json?['$epx:$ctx:Schedule5'],
      schedule6Copy: json?['$epx:$ctx:Schedule6'],
      schedule7Copy: json?['$epx:$ctx:Schedule7'],
      setSchedule1: '$epx:$ctx:SetSchedule1',
      setSchedule2: '$epx:$ctx:SetSchedule2',
      setSchedule3: '$epx:$ctx:SetSchedule3',
      setSchedule4: '$epx:$ctx:SetSchedule4',
      setSchedule5: '$epx:$ctx:SetSchedule5',
      setSchedule6: '$epx:$ctx:SetSchedule6',
      setSchedule7: '$epx:$ctx:SetSchedule7',
      setScheduleType: '$epx:$ctx:SetScheduleType',
      setScheduleEn: '$epx:$ctx:sScheduleEn',
      sTherUisTempDispM: json?['$epx:sTherUIS:TempDispM'] ?? 0,
      sTherSMinHeatSp: json?['$epx:sTherS:MinHeatSp'] ?? 500,
      sTherSMaxHeatSp: json?['$epx:sTherS:MaxHeatSp'] ?? 3500,
    );
  }

  STimeH mapJson(Map<String, dynamic>? json) {
    scheduleType = json?['$epx:$ctx:ScheduleType'] ?? scheduleType;
    scheduleTypeCopy = json?['$epx:$ctx:ScheduleType'] ?? scheduleTypeCopy;
    schedule1 = json?['$epx:$ctx:Schedule1'] ?? schedule1;
    schedule2 = json?['$epx:$ctx:Schedule2'] ?? schedule2;
    schedule3 = json?['$epx:$ctx:Schedule3'] ?? schedule3;
    schedule4 = json?['$epx:$ctx:Schedule4'] ?? schedule4;
    schedule5 = json?['$epx:$ctx:Schedule5'] ?? schedule5;
    schedule6 = json?['$epx:$ctx:Schedule6'] ?? schedule6;
    schedule7 = json?['$epx:$ctx:Schedule7'] ?? schedule7;
    scheduleEn = json?['$epx:$ctx:ScheduleEn'] ?? scheduleEn;
    schedule1Copy = json?['$epx:$ctx:Schedule1'] ?? schedule1Copy;
    schedule2Copy = json?['$epx:$ctx:Schedule2'] ?? schedule2Copy;
    schedule3Copy = json?['$epx:$ctx:Schedule3'] ?? schedule3Copy;
    schedule4Copy = json?['$epx:$ctx:Schedule4'] ?? schedule4Copy;
    schedule5Copy = json?['$epx:$ctx:Schedule5'] ?? schedule5Copy;
    schedule6Copy = json?['$epx:$ctx:Schedule6'] ?? schedule6Copy;
    schedule7Copy = json?['$epx:$ctx:Schedule7'] ?? schedule7Copy;
    sTherUisTempDispM = json?['$epx:sTherUIS:TempDispM'] ?? sTherUisTempDispM;
    sTherSMinHeatSp = json?['$epx:sTherS:MinHeatSp'] ?? sTherSMinHeatSp;
    sTherSMaxHeatSp = json?['$epx:sTherS:MaxHeatSp'] ?? sTherSMaxHeatSp;
    return this;
  }

  Map<String, dynamic> toJson() {
    return {
      '$epx:$ctx:ScheduleType': scheduleType,
      '$epx:$ctx:Schedule1': schedule1,
      '$epx:$ctx:Schedule2': schedule2,
      '$epx:$ctx:Schedule3': schedule3,
      '$epx:$ctx:Schedule4': schedule4,
      '$epx:$ctx:Schedule5': schedule5,
      '$epx:$ctx:Schedule6': schedule6,
      '$epx:$ctx:Schedule7': schedule7,
      '$epx:$ctx:ScheduleEn': scheduleEn,
    };
  }

  Map<String, dynamic> publishEpxsTimeHSetSchedules() {
    return {
      setScheduleType: scheduleType,
      setSchedule1: schedule1,
      setSchedule2: schedule2,
      setSchedule3: schedule3,
      setSchedule4: schedule4,
      setSchedule5: schedule5,
      setSchedule6: schedule6,
      setSchedule7: schedule7,
    };
  }

  List<Schedule> getMonToSun() {
    return monSun;
  }

  List<Schedule> getMonToFri() {
    return monFir;
  }

  List<Schedule> getSatToSun() {
    return satSun;
  }

  void addUpdateSchedules(Schedule objSchedule) {
    isScheduleUpdate = true;
    scheduleType ??= ScheduleConfigurationTypes.WORKWEEK;
    switch (objSchedule.dayIndex) {
      case DayIndex.MON:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          if (objSchedule.position != null) {
            monSun[objSchedule.position!] = objSchedule;
          } else {
            monSun.add(objSchedule);
          }
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          if (objSchedule.position != null) {
            monFir[objSchedule.position!] = objSchedule;
          } else {
            monFir.add(objSchedule);
          }
          break;
        }
        if (objSchedule.position != null) {
          mon[objSchedule.position!] = objSchedule;
        } else {
          mon.add(objSchedule);
        }
        break;
      case DayIndex.TUE:
        if (objSchedule.position != null) {
          tue[objSchedule.position!] = objSchedule;
        } else {
          tue.add(objSchedule);
        }
        break;
      case DayIndex.WED:
        if (objSchedule.position != null) {
          wed[objSchedule.position!] = objSchedule;
        } else {
          wed.add(objSchedule);
        }
        break;
      case DayIndex.THU:
        if (objSchedule.position != null) {
          thu[objSchedule.position!] = objSchedule;
        } else {
          thu.add(objSchedule);
        }
        break;
      case DayIndex.FRI:
        if (objSchedule.position != null) {
          fri[objSchedule.position!] = objSchedule;
        } else {
          fri.add(objSchedule);
        }
        break;
      case DayIndex.SAT:
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          if (objSchedule.position != null) {
            satSun[objSchedule.position!] = objSchedule;
          } else {
            satSun.add(objSchedule);
          }
          break;
        }
        if (objSchedule.position != null) {
          sat[objSchedule.position!] = objSchedule;
        } else {
          sat.add(objSchedule);
        }
        break;
      case DayIndex.SUN:
        if (objSchedule.position != null) {
          sun[objSchedule.position!] = objSchedule;
        } else {
          sun.add(objSchedule);
        }
        break;
    }
    buildScheduleStrings();
    parseSchedules();
  }

  void removeSchedule(Schedule objSchedule) {
    isScheduleUpdate = true;
    switch (objSchedule.dayIndex) {
      case DayIndex.MON:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          monSun.removeAt(objSchedule.position!);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          monFir.removeAt(objSchedule.position!);
          break;
        }
        mon.removeAt(objSchedule.position!);
        break;
      case DayIndex.TUE:
        tue.removeAt(objSchedule.position!);
        break;
      case DayIndex.WED:
        wed.removeAt(objSchedule.position!);
        break;
      case DayIndex.THU:
        thu.removeAt(objSchedule.position!);
        break;
      case DayIndex.FRI:
        fri.removeAt(objSchedule.position!);
        break;
      case DayIndex.SAT:
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          satSun.removeAt(objSchedule.position!);
          break;
        }
        sat.removeAt(objSchedule.position!);
        break;
      case DayIndex.SUN:
        sun.removeAt(objSchedule.position!);
        break;
    }
    buildScheduleStrings();
    parseSchedules();
  }

  void clearSchedulesWithScheduleUpdateFlag() {
    isScheduleUpdate = true;
    clearSchedules();
    buildScheduleStrings();
    parseSchedules();
  }

  void clearSchedules() {
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK: //All Day
        {
          monSun.clear();
        }
        break;
      case ScheduleConfigurationTypes.WORKWEEK: // working week
        {
          monFir.clear();
          satSun.clear();
        }
        break;
      case ScheduleConfigurationTypes.DAILY: // 7 Days
        {
          mon.clear();
          tue.clear();
          wed.clear();
          thu.clear();
          fri.clear();
          sat.clear();
          sun.clear();
        }
        break;
      case ScheduleConfigurationTypes.HOLIDAY:
        {
          monFirHoliday.clear();
          satHoliday.clear();
          sunHoliday.clear();
        }
        break;

      default:
        break;
    }
  }

  void clearOtherTypeSchedules() {
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK: //All Day
        {
          monFir.clear();
          satSun.clear();
          mon.clear();
          tue.clear();
          wed.clear();
          thu.clear();
          fri.clear();
          sat.clear();
          sun.clear();
        }
        break;
      case ScheduleConfigurationTypes.WORKWEEK: // working week
        {
          monSun.clear();
          mon.clear();
          tue.clear();
          wed.clear();
          thu.clear();
          fri.clear();
          sat.clear();
          sun.clear();
        }
        break;
      case ScheduleConfigurationTypes.DAILY: // 7 Days
        {
          monSun.clear();
          monFir.clear();
          satSun.clear();
        }
        break;
      case ScheduleConfigurationTypes.HOLIDAY:
        {
          monFirHoliday.clear();
          satHoliday.clear();
          sunHoliday.clear();
        }
        break;
      default:
        break;
    }
  }

  String getUntilMessage() {
    /// 怎么获取 timeZone
    /// 当前 App 无法获取/设置 timeZone，默认 timeZone 是 "Europe/London"
    String timeZone = "Europe/London";
    // final String timeZone = AppDevices()
    //     .getCurrentIndexSlider()
    //     ?.keyGatewayBannerState
    //     ?.currentState
    //     ?.getTimeZoneRegion() ??
    //     "";
    if (timeZone != "") {
      return "";
    }
    Schedule? selectedSchedule;
    tz.initializeTimeZones();
    final detroit = tz.getLocation(timeZone);
    final DateTime date = tz.TZDateTime.now(detroit);
    final time = int.parse(
      date.hour.toString().padLeft(2, "0") +
          date.minute.toString().padLeft(2, "0"),
    );
    final int day = date.weekday;
    switch (day) {
      case DayIndex.MON:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(monFir, time);
          break;
        }
        selectedSchedule = findUntilSchedule(mon, time);
        break;
      case DayIndex.TUE:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(monFir, time);
          break;
        }
        selectedSchedule = findUntilSchedule(tue, time);
        break;
      case DayIndex.WED:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(monFir, time);
          break;
        }
        selectedSchedule = findUntilSchedule(wed, time);
        break;
      case DayIndex.THU:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(monFir, time);
          break;
        }
        selectedSchedule = findUntilSchedule(thu, time);
        break;
      case DayIndex.FRI:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(monFir, time);
          break;
        }
        selectedSchedule = findUntilSchedule(fri, time);
        break;
      case DayIndex.SAT:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(satSun, time);
          break;
        }
        selectedSchedule = findUntilSchedule(sat, time);
        break;
      case DayIndex.SUN:
        //All Day Home
        if (scheduleType == ScheduleConfigurationTypes.FULLWEEK) {
          selectedSchedule = findUntilSchedule(monSun, time);
          break;
        }
        //working
        if (scheduleType == ScheduleConfigurationTypes.WORKWEEK) {
          selectedSchedule = findUntilSchedule(satSun, time);
          break;
        }
        selectedSchedule = findUntilSchedule(sun, time);
        break;
    }
    if (selectedSchedule != null) {
      return "Until ${selectedSchedule.getTime()}";
    }
    return "";
  }

  Schedule? findUntilSchedule(List<Schedule> list, int time) {
    if (list.isEmpty) {
      return null;
    }
    final s = list.where((element) {
      if (int.parse(element.time!) > time) {
        return true;
      }
      return false;
    }).toList();
    if (s.isNotEmpty) {
      return s.first;
    }
    return list.first;
  }

  void buildScheduleStrings() {
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK: //All Day
        {
          final value = buildSchedulePropertyVal(monSun);
          schedule1 = value;
          schedule2 = value;
          schedule3 = value;
          schedule4 = value;
          schedule5 = value;
          schedule6 = value;
          schedule7 = value;
        }
        break;

      case ScheduleConfigurationTypes.WORKWEEK:
        {
          var value = buildSchedulePropertyVal(monFir);
          schedule1 = value;
          schedule2 = value;
          schedule3 = value;
          schedule4 = value;
          schedule5 = value;
          value = buildSchedulePropertyVal(satSun);
          schedule6 = value;
          schedule7 = value;
        }
        break;

      case ScheduleConfigurationTypes.DAILY: // 7 Days
        {
          schedule1 = buildSchedulePropertyVal(mon);
          schedule2 = buildSchedulePropertyVal(tue);
          schedule3 = buildSchedulePropertyVal(wed);
          schedule4 = buildSchedulePropertyVal(thu);
          schedule5 = buildSchedulePropertyVal(fri);
          schedule6 = buildSchedulePropertyVal(sat);
          schedule7 = buildSchedulePropertyVal(sun);
        }
        break;
      case ScheduleConfigurationTypes.HOLIDAY:
        {
          var value = buildSchedulePropertyVal(monFirHoliday);
          schedule1 = value;
          schedule2 = value;
          schedule3 = value;
          schedule4 = value;
          schedule5 = value;
          value = buildSchedulePropertyVal(satHoliday);
          schedule6 = value;
          value = buildSchedulePropertyVal(sunHoliday);
          schedule7 = value;
        }
        break;
      default:
        break;
    }
  }

  String buildSchedulePropertyVal(List<Schedule> list) {
    // const preFixStr = "07ffffff";
    final preFixStr = getPrefixString();
    final defaultActionData =
        List<String>.filled(6 * 20, "F", growable: true).join("");
    final shortlist = shortSchedule(list);
    var actionsStr = "";
    for (final element in shortlist) {
      final s = element.buildSTimeHString();
      actionsStr = actionsStr + s + scheduleSeparator;
    }
    final actionsData =
        defaultActionData.replaceRange(0, actionsStr.length, actionsStr);
    return preFixStr + actionsData;
  }

  String getPrefixString() {
    String val = "01";

    switch (scheduleMode) {
      case ScheduleMode.heatOnly:
        val = "01";
        break;
      case ScheduleMode.coolOnly:
        val = "02";
        break;
      case ScheduleMode.heatCool:
        val = "07";
        break;
      case ScheduleMode.onOrOff:
        val = "05";
        break;
      default:
        val = "05";
    }
    return "${val}FFFFFF";
  }

  List<Schedule> shortSchedule(List<Schedule>? list) {
    if (list == null) {
      return [];
    }
    if (list.isEmpty) {
      return [];
    }
    list.sort((action1, action2) {
      try {
        final timeA = int.parse(action1.time!);
        final timeB = int.parse(action2.time!);
        if (timeA < timeB) {
          return -1;
        } else if (timeA > timeB) {
          return 1;
        } else if (timeA == timeB) {
          return 0;
        }
        return 0;
      } on FormatException {
        return 0;
      }
    });
    return list;
  }

  void parseSchedules() {
    scheduleType ??= 2;
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK: //All Day
        {
          final value = parseRawScheduleValue(schedule1, 1);
          monSun = value;
        }
        break;

      case ScheduleConfigurationTypes.WORKWEEK:
        {
          var value = parseRawScheduleValue(schedule1, 1);
          monFir = value;
          value = parseRawScheduleValue(schedule6, 6);
          satSun = value;
        }
        break;

      case ScheduleConfigurationTypes.DAILY: // 7 Days
        {
          mon = parseRawScheduleValue(schedule1, 1);
          tue = parseRawScheduleValue(schedule2, 2);
          wed = parseRawScheduleValue(schedule3, 3);
          thu = parseRawScheduleValue(schedule4, 4);
          fri = parseRawScheduleValue(schedule5, 5);
          sat = parseRawScheduleValue(schedule6, 6);
          sun = parseRawScheduleValue(schedule7, 7);
        }
        break;

      case ScheduleConfigurationTypes.HOLIDAY:
        {
          var value = parseRawScheduleValue(schedule1, 1);
          monFirHoliday = value;
          value = parseRawScheduleValue(schedule6, 6);
          satHoliday = value;
          value = parseRawScheduleValue(schedule7, 7);
          sunHoliday = value;
        }
        break;

      default:
        break;
    }
  }

  List<Schedule> parseRawScheduleValue(String? rawScheduleValue, int dayIndex) {
    // parseSchedulePropertyActions
    final List<Schedule> result = [];
    if (rawScheduleValue == null || rawScheduleValue == "") {
      return result;
    }
    // const preFixStr = "07FFFFFF";
    final preFixStr = getPrefixString();
    final validScheduleVal = rawScheduleValue.substring(preFixStr.length);
    final Iterable<Match> matches =
        RegExp(r".{1,20}").allMatches(validScheduleVal);
    final actionStrArray = matches.map((m) => m.group(0));
    final min = (sTherSMinHeatSp) / 100;
    final max = (sTherSMaxHeatSp) / 100;
    for (int i = 0; i < actionStrArray.length; i++) {
      final value = actionStrArray.elementAt(i);
      if (value != null) {
        final scheduleTime = value.substring(0, 4);
        if (scheduleTime != "HHMM" &&
            scheduleTime.toUpperCase() != "FFFF" &&
            value.length == 20) {
          final Schedule action = Schedule(
            time: scheduleTime,
            heating: value.substring(4, 8),
            dayIndex: dayIndex,
            maxHeat: max,
            minHeat: min,
            position: i,
            timeFormat24Hour: getTimeFormat24Hour(),
            temperatureDisplayMode: getTemperatureDisplayMode(),
            scheduleMode: scheduleMode ?? ScheduleMode.heatOnly,
          );
          result.add(action);
        }
      }
    }
    return shortSchedule(result);
  }

  int getTimeFormat24Hour() {
    final userAttributesService = UserAttributesService.to;
    if (userAttributesService.hourFormat == HourFormatStatus.format24h) {
      return 1;
    }
    return 0;
  }

  int getTemperatureDisplayMode() {
    final userAttributesService = UserAttributesService.to;
    if (userAttributesService.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return 1;
    }
    return 0;
  }

  bool isDeleteAllInterval() {
    bool flag = false;
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK: //All Day
        if (monSun.isEmpty) {
          flag = true;
        }
        break;
      case ScheduleConfigurationTypes.WORKWEEK: // working week
        if (monFir.isEmpty && satSun.isEmpty) {
          flag = true;
        }
        break;
      case ScheduleConfigurationTypes.DAILY: // 7 Days
        if (mon.isEmpty &&
            tue.isEmpty &&
            wed.isEmpty &&
            thu.isEmpty &&
            fri.isEmpty &&
            sat.isEmpty &&
            sun.isEmpty) {
          flag = true;
        }
        break;
      case ScheduleConfigurationTypes.HOLIDAY:
        if (monFirHoliday.isEmpty && satHoliday.isEmpty && sunHoliday.isEmpty) {
          flag = true;
        }
        break;
      default:
        break;
    }
    return flag;
  }

  bool isDeleteAllIntervalVisible() {
    bool flag = false;
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK: //All Day
        if (monSun.isNotEmpty) {
          flag = true;
        }
        break;
      case ScheduleConfigurationTypes.WORKWEEK: // working week
        if (monFir.isNotEmpty || satSun.isNotEmpty) {
          flag = true;
        }
        break;
      case ScheduleConfigurationTypes.DAILY: // 7 Days
        if (mon.isNotEmpty ||
            tue.isNotEmpty ||
            wed.isNotEmpty ||
            thu.isNotEmpty ||
            fri.isNotEmpty ||
            sat.isNotEmpty ||
            sun.isNotEmpty) {
          flag = true;
        }
        break;
      case ScheduleConfigurationTypes.HOLIDAY:
        if (monFirHoliday.isNotEmpty ||
            satHoliday.isNotEmpty ||
            sunHoliday.isNotEmpty) {
          flag = true;
        }
        break;
      default:
        break;
    }
    return flag;
  }

  bool isScheduleEmpty() {
    scheduleType ??= 2;
    switch (scheduleType) {
      case ScheduleConfigurationTypes.FULLWEEK:
        return monSun.isEmpty;
      case ScheduleConfigurationTypes.WORKWEEK:
        return monFir.isEmpty && satSun.isEmpty;
      case ScheduleConfigurationTypes.DAILY:
        return sun.isEmpty &&
            mon.isEmpty &&
            tue.isEmpty &&
            wed.isEmpty &&
            thu.isEmpty &&
            fri.isEmpty &&
            sat.isEmpty;
      case ScheduleConfigurationTypes.HOLIDAY:
        return monFirHoliday.isEmpty &&
            sunHoliday.isEmpty &&
            satHoliday.isEmpty;

      default:
        return false;
    }
  }

  int limit() {
    return 6;
  }

  bool isAllActive() {
    return true;
  }

  void setEnValue(int value) {}

  void cancel() {
    scheduleType = scheduleTypeCopy;
    schedule1 = schedule1Copy;
    schedule2 = schedule2Copy;
    schedule3 = schedule3Copy;
    schedule4 = schedule4Copy;
    schedule5 = schedule5Copy;
    schedule6 = schedule6Copy;
    schedule7 = schedule7Copy;
    parseSchedules();
  }

  void changeBackScheduleType () {
    scheduleType = scheduleTypeCopy;
  }

  void updateCopy() {
    scheduleTypeCopy = scheduleType;
    schedule1Copy = schedule1;
    schedule2Copy = schedule2;
    schedule3Copy = schedule3;
    schedule4Copy = schedule4;
    schedule5Copy = schedule5;
    schedule6Copy = schedule6;
    schedule7Copy = schedule7;
  }

  Schedule getDefaultSchedule(int dayIndex) {
    final Schedule objSchedule = Schedule(
      dayIndex: dayIndex,
      timeFormat24Hour: getTimeFormat24Hour(),
      maxHeat: AppNumberHelper.roundValueTo((sTherSMaxHeatSp) / 100, to: 0.5),
      minHeat: AppNumberHelper.roundValueTo((sTherSMinHeatSp) / 100, to: 0.5),
      temperatureDisplayMode: getTemperatureDisplayMode(),
      scheduleMode: scheduleMode ?? ScheduleMode.heatOnly,
      isNew: true,
    );
    return objSchedule;
  }
}
