import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SNetCommTS extends SBase {
  int? rssi;

  SNetCommTS(
    super.epx,
    super.ctx, {
    this.rssi,
  });

  factory SNetCommTS.fromJson(
    String epx,
    String ctx,
    Map<String, dynamic>? json,
  ) =>
      SNetCommTS(
        epx,
        ctx,
        rssi: json?['$epx:$ctx:RSSI']?["timestamp"],
      );

  SNetCommTS mapJson(Map<String, dynamic>? json) {
    rssi = json?['$epx:$ctx:RSSI']?["timestamp"] ?? rssi;
    return this;
  }

  Map<String, dynamic> toJson() => {
        '$epx:$ctx:RSSI': {"timestamp": rssi},
      };
}
