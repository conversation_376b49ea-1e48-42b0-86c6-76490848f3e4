import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SMBasicSTS extends SBase {
  int? vendorID;
  int? productID;
  int? dmRevision;
  int? vendorName;
  int? productName;
  int? location;
  int? hwVersion;
  int? hwVersionStr;
  int? swVersion;
  int? swVersionStr;
  int? specVersion;
  int? maxPTHPerIvk;

  SMBasicSTS(super.epx, super.ctx,{
    this.vendorID,
    this.productID,
    this.dmRevision,
    this.vendorName,
    this.productName,
    this.location,
    this.hwVersion,
    this.hwVersionStr,
    this.swVersion,
    this.swVersionStr,
    this.specVersion,
    this.maxPTHPerIvk,
  });

  factory SMBasicSTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SMBasicSTS(
        epx,
        ctx,
        vendorID: json?['$epx:$ctx:VendorID']?["timestamp"],
        productID: json?['$epx:$ctx:ProductID']?["timestamp"],
        dmRevision: json?['$epx:$ctx:DMRevision']?["timestamp"],
        vendorName: json?['$epx:$ctx:VendorName']?["timestamp"],
        productName: json?['$epx:$ctx:ProductName']?["timestamp"],
        location: json?['$epx:$ctx:Location']?["timestamp"],
        hwVersion: json?['$epx:$ctx:HWVersion']?["timestamp"],
        hwVersionStr: json?['$epx:$ctx:HWVersionStr']?["timestamp"],
        swVersion: json?['$epx:$ctx:SWVersion']?["timestamp"],
        swVersionStr: json?['$epx:$ctx:SWVersionStr']?["timestamp"],
        specVersion: json?['$epx:$ctx:SpecVersion']?["timestamp"],
        maxPTHPerIvk: json?['$epx:$ctx:MaxPTHPerIvk']?["timestamp"],
      );

  SMBasicSTS mapJson(Map<String, dynamic>? json) {
    vendorID = json?['$epx:$ctx:VendorID']?["timestamp"] ?? vendorID;
    productID = json?['$epx:$ctx:ProductID']?["timestamp"] ?? productID;
    dmRevision = json?['$epx:$ctx:DMRevision']?["timestamp"] ?? dmRevision;
    vendorName = json?['$epx:$ctx:VendorName']?["timestamp"] ?? vendorName;
    productName = json?['$epx:$ctx:ProductName']?["timestamp"] ?? productName;
    location = json?['$epx:$ctx:Location']?["timestamp"] ?? location;
    hwVersion = json?['$epx:$ctx:HWVersion']?["timestamp"] ?? hwVersion;
    hwVersionStr = json?['$epx:$ctx:HWVersionStr']?["timestamp"] ?? hwVersionStr;
    swVersion = json?['$epx:$ctx:SWVersion']?["timestamp"] ?? swVersion;
    swVersionStr = json?['$epx:$ctx:SWVersionStr']?["timestamp"] ?? swVersionStr;
    specVersion = json?['$epx:$ctx:SpecVersion']?["timestamp"] ?? specVersion;
    maxPTHPerIvk = json?['$epx:$ctx:MaxPTHPerIvk']?["timestamp"] ?? maxPTHPerIvk;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:VendorID': {
      "timestamp": vendorID,
    },
    '$epx:$ctx:ProductID': {
      "timestamp": productID,
    },
    '$epx:$ctx:DMRevision': {
      "timestamp": dmRevision,
    },
    '$epx:$ctx:VendorName': {
      "timestamp": vendorName,
    },
    '$epx:$ctx:ProductName': {
      "timestamp": productName,
    },
    '$epx:$ctx:Location': {
      "timestamp": location,
    },
    '$epx:$ctx:HWVersion': {
      "timestamp": hwVersion,
    },
    '$epx:$ctx:HWVersionStr': {
      "timestamp": hwVersionStr,
    },
    '$epx:$ctx:SWVersion': {
      "timestamp": swVersion,
    },
    '$epx:$ctx:SWVersionStr': {
      "timestamp": swVersionStr,
    },
    '$epx:$ctx:SpecVersion': {
      "timestamp": specVersion,
    },
    '$epx:$ctx:MaxPTHPerIvk': {
      "timestamp": maxPTHPerIvk,
    },
  };

}