import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SBoiler extends SBase {
  int? holdType;
  int? lostThermState;
  int? lostOnOffState;
  int? chSwitch;
  int? chRelay;
  int? dhwSwitch;
  int? dhwRelay;
  int? enableDhw;
  int? boostDhw;

  SBoiler(super.epx, super.ctx, {
    this.holdType,
    this.lostThermState,
    this.lostOnOffState,
    this.chSwitch,
    this.chRelay,
    this.dhwSwitch,
    this.dhwRelay,
    this.enableDhw,
    this.boostDhw,
  });

  factory SBoiler.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SBoiler(
        epx,
        ctx,
        holdType: json?['$epx:$ctx:HoldType'],
        lostThermState: json?['$epx:$ctx:LostThermState'],
        lostOnOffState: json?['$epx:$ctx:LostOnoffState'],
        chSwitch: json?['$epx:$ctx:CHSwitch'],
        chRelay: json?['$epx:$ctx:CHRelay'],
        dhwSwitch: json?['$epx:$ctx:DHWSwitch'],
        dhwRelay: json?['$epx:$ctx:DHWRelay'],
        enableDhw: json?['$epx:$ctx:EnableDHW'],
        boostDhw: json?['$epx:$ctx:BoostDHW'],
      );

  SBoiler mapJson(Map<String, dynamic>? json) {
    holdType = json?['$epx:$ctx:HoldType'] ?? holdType;
    lostThermState = json?['$epx:$ctx:LostThermState'] ?? lostThermState;
    lostOnOffState = json?['$epx:$ctx:LostOnoffState'] ?? lostOnOffState;
    chSwitch = json?['$epx:$ctx:CHSwitch'] ?? chSwitch;
    chRelay = json?['$epx:$ctx:CHRelay'] ?? chRelay;
    dhwSwitch = json?['$epx:$ctx:DHWSwitch'] ?? dhwSwitch;
    dhwRelay = json?['$epx:$ctx:DHWRelay'] ?? dhwRelay;
    enableDhw = json?['$epx:$ctx:EnableDHW'] ?? enableDhw;
    boostDhw = json?['$epx:$ctx:BoostDHW'] ?? boostDhw;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:HoldType': holdType,
    '$epx:$ctx:LostThermState': lostThermState,
    '$epx:$ctx:LostOnoffState': lostOnOffState,
    '$epx:$ctx:CHSwitch': chSwitch,
    '$epx:$ctx:CHRelay': chRelay,
    '$epx:$ctx:DHWSwitch': dhwSwitch,
    '$epx:$ctx:DHWRelay': dhwRelay,
    '$epx:$ctx:EnableDHW': enableDhw,
    '$epx:$ctx:BoostDHW': boostDhw,
  };


}