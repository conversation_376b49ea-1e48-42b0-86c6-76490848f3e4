import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SMdoTS extends SBase {
  int? euid;
  int? nodeID;
  int? deviceName;
  int? sDeviceName;

  SMdoTS(super.epx, super.ctx,{
    this.euid,
    this.nodeID,
    this.deviceName,
    this.sDeviceName,
  });

  factory SMdoTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SMdoTS(
        epx,
        ctx,
        euid: json?['$epx:$ctx:Euid']?["timestamp"],
        nodeID: json?['$epx:$ctx:NodeID']?["timestamp"],
        deviceName: json?['$epx:$ctx:deviceName']?["timestamp"],
        sDeviceName: json?['$epx:$ctx:sDeviceName']?["timestamp"],
      );

  SMdoTS mapJson(Map<String, dynamic>? json) {
    euid = json?['$epx:$ctx:Euid']?["timestamp"] ?? euid;
    nodeID = json?['$epx:$ctx:NodeID']?["timestamp"] ?? nodeID;
    deviceName = json?['$epx:$ctx:deviceName']?["timestamp"] ?? deviceName;
    sDeviceName = json?['$epx:$ctx:sDeviceName']?["timestamp"] ?? sDeviceName;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:Euid': {
      "timestamp": euid,
    },
    '$epx:$ctx:NodeID': {
      "timestamp": nodeID,
    },
    '$epx:$ctx:deviceName': {
      "timestamp": deviceName,
    },
    '$epx:$ctx:sDeviceName': {
      "timestamp": sDeviceName,
    },
  };


}