import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SAwsIotTS extends SBase {
  int? certARN;
  int? cloudStatus;
  int? cloudID;

  SAwsIotTS(super.epx, super.ctx, {
    this.certARN,
    this.cloudStatus,
    this.cloudID,
  });

  factory SAwsIotTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SAwsIotTS(
        epx,
        ctx,
        certARN: json?['$epx:$ctx:CertARN']?["timestamp"],
        cloudStatus: json?['$epx:$ctx:CloudStatus']?["timestamp"],
        cloudID: json?['$epx:$ctx:CloudID']?["timestamp"],
      );

  SAwsIotTS mapJson(Map<String, dynamic>? json) {
    certARN = json?['$epx:$ctx:CertARN']?["timestamp"] ?? certARN;
    cloudStatus = json?['$epx:$ctx:CloudStatus']?["timestamp"] ?? cloudStatus;
    cloudID = json?['$epx:$ctx:CloudID']?["timestamp"] ?? cloudID;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:CertARN': {
      "timestamp": certARN
    },
    '$epx:$ctx:CloudStatus': {
      "timestamp": cloudStatus
    },
    '$epx:$ctx:CloudID': {
      "timestamp": cloudID
    },
  };


}