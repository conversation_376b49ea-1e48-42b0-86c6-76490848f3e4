import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SAwsIot extends SBase {
  String? certARN;
  int? cloudStatus;
  String? cloudID;

  SAwsIot(super.epx, super.ctx, {
    this.certARN,
    this.cloudStatus,
    this.cloudID,
  });

  factory SAwsIot.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SAwsIot(
        epx,
        ctx,
        certARN: json?['$epx:$ctx:CertARN'],
        cloudStatus: json?['$epx:$ctx:CloudStatus'],
        cloudID: json?['$epx:$ctx:CloudID'],
      );

  SAwsIot mapJson(Map<String, dynamic>? json) {
    certARN = json?['$epx:$ctx:CertARN'] ?? certARN;
    cloudStatus = json?['$epx:$ctx:CloudStatus'] ?? cloudStatus;
    cloudID = json?['$epx:$ctx:CloudID'] ?? cloudID;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:CertARN': certARN,
    '$epx:$ctx:CloudStatus': cloudStatus,
    '$epx:$ctx:CloudID': cloudID,
  };


}