import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SMcTlrTS extends SBase {
  int? fabID;
  int? fabRcAc;
  int? userCSR;
  int? userNOC;
  int? fabIPK;
  int? nodeID;
  int? thdDataset;
  int? thdChan;

  SMcTlrTS(super.epx, super.ctx, {
    this.fabID,
    this.fabRcAc,
    this.userCSR,
    this.userNOC,
    this.fabIPK,
    this.nodeID,
    this.thdDataset,
    this.thdChan,
  });

  factory SMcTlrTS.fromJson(String epx, String ctx, Map<String, dynamic> json) {
    return SMcTlrTS(
      epx,
      ctx,
      fabID: json['$epx:$ctx:FabID']?["timestamp"],
      fabRcAc: json['$epx:$ctx:FabRCAC']?["timestamp"],
      userCSR: json['$epx:$ctx:UserCSR']?["timestamp"],
      userNOC: json['$epx:$ctx:UserNOC']?["timestamp"],
      fabIPK: json['$epx:$ctx:FabIPK']?["timestamp"],
      nodeID: json['$epx:$ctx:NodeID']?["timestamp"],
      thdDataset: json['$epx:$ctx:ThdDataset']?["timestamp"],
      thdChan: json['$epx:$ctx:ThdChan']?["timestamp"],
    );
  }

  SMcTlrTS mapJson(Map<String, dynamic> json) {
    fabID = json['$epx:$ctx:FabID']?["timestamp"] ?? fabID;
    fabRcAc = json['$epx:$ctx:FabRCAC']?["timestamp"] ?? fabRcAc;
    userCSR = json['$epx:$ctx:UserCSR']?["timestamp"] ?? userCSR;
    userNOC = json['$epx:$ctx:UserNOC']?["timestamp"] ?? userNOC;
    fabIPK = json['$epx:$ctx:FabIPK']?["timestamp"] ?? fabIPK;
    nodeID = json['$epx:$ctx:NodeID']?["timestamp"] ?? nodeID;
    thdDataset = json['$epx:$ctx:ThdDataset']?["timestamp"] ?? thdDataset;
    thdChan = json['$epx:$ctx:ThdChan']?["timestamp"] ?? thdChan;
    return this;
  }

  Map<String, dynamic> toJson() {
    return {
      '$epx:$ctx:FabID': {
        "timestamp": fabID
      },
      '$epx:$ctx:FabRCAC': {
        "timestamp": fabRcAc
      },
      '$epx:$ctx:UserCSR': {
        "timestamp": userCSR
      },
      '$epx:$ctx:UserNOC': {
        "timestamp": userNOC
      },
      '$epx:$ctx:FabIPK': {
        "timestamp": fabIPK
      },
      '$epx:$ctx:NodeID': {
        "timestamp": nodeID
      },
      '$epx:$ctx:ThdDataset': {
        "timestamp": thdDataset
      },
      '$epx:$ctx:ThdChan': {
        "timestamp": thdChan
      }
    };
  }
}
