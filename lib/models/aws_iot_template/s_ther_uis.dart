import 's_base.dart';

class STherUIS extends SBase {
  int? tempDispM;
  int? lockKey;

  STherUIS(super.epx, super.ctx,{
    this.tempDispM,
    this.lockKey,
  });

  factory STherUIS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      STherUIS(
        epx,
        ctx,
        tempDispM: json?['$epx:$ctx:TempDispM'],
        lockKey: json?['$epx:$ctx:LockKey'],
      );

  STherUIS mapJson(Map<String, dynamic>? json) {
    tempDispM = json?['$epx:$ctx:TempDispM'] ?? tempDispM;
    lockKey = json?['$epx:$ctx:LockKey'] ?? lockKey;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:TempDispM': tempDispM,
    '$epx:$ctx:LockKey': lock<PERSON><PERSON>,
  };
}