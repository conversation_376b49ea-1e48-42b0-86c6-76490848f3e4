import 'package:habi_app/models/aws_iot_template/s_base.dart';

class STherOTS extends SBase {
  int? disHeatOD;

  STherOTS(super.epx, super.ctx,{
    this.disHeatOD,
  });

  factory STherOTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      STherOTS(
        epx,
        ctx,
        disHeatOD: json?['$epx:$ctx:DisHeatOD']?["timestamp"],
      );

  STherOTS mapJson(Map<String, dynamic>? json) {
    disHeatOD = json?['$epx:$ctx:DisHeatOD']?["timestamp"] ?? disHeatOD;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:DisHeatOD': {
      "timestamp": disHeatOD,
    },
  };

}