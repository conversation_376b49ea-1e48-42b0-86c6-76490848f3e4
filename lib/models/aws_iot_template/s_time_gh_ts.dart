import 'package:flutter/cupertino.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/models/aws_iot_template/s_base.dart';

class STimeGhTS extends SBase {
  ScheduleMode? scheduleMode;
  int? scheduleType;
  int? schedule1;
  int? schedule2;
  int? schedule3;
  int? schedule4;
  int? schedule5;
  int? schedule6;
  int? schedule7;

  STimeGhTS(super.epx, super.ctx, {
    this.scheduleMode,
    this.scheduleType,
    this.schedule1,
    this.schedule2,
    this.schedule3,
    this.schedule4,
    this.schedule5,
    this.schedule6,
    this.schedule7,
  });

  factory STimeGhTS.fromJson(String epx, String ctx, ScheduleMode sMode, Map<String, dynamic>? json) {
    return STimeGhTS(
      epx,
      ctx,
      scheduleMode: sMode,
      scheduleType: json?['$epx:$ctx:ScheduleType']?["timestamp"],
      schedule1: json?['$epx:$ctx:Schedule1']?["timestamp"],
      schedule2: json?['$epx:$ctx:Schedule2']?["timestamp"],
      schedule3: json?['$epx:$ctx:Schedule3']?["timestamp"],
      schedule4: json?['$epx:$ctx:Schedule4']?["timestamp"],
      schedule5: json?['$epx:$ctx:Schedule5']?["timestamp"],
      schedule6: json?['$epx:$ctx:Schedule6']?["timestamp"],
      schedule7: json?['$epx:$ctx:Schedule7']?["timestamp"],
    );
  }

  STimeGhTS mapJson(Map<String, dynamic>? json) {
    scheduleType = json?['$epx:$ctx:ScheduleType']?["timestamp"] ?? scheduleType;
    schedule1 = json?['$epx:$ctx:Schedule1']?["timestamp"] ?? schedule1;
    schedule2 = json?['$epx:$ctx:Schedule2']?["timestamp"] ?? schedule2;
    schedule3 = json?['$epx:$ctx:Schedule3']?["timestamp"] ?? schedule3;
    schedule4 = json?['$epx:$ctx:Schedule4']?["timestamp"] ?? schedule4;
    schedule5 = json?['$epx:$ctx:Schedule5']?["timestamp"] ?? schedule5;
    schedule6 = json?['$epx:$ctx:Schedule6']?["timestamp"] ?? schedule6;
    schedule7 = json?['$epx:$ctx:Schedule7']?["timestamp"] ?? schedule7;
    return this;
  }

  Map<String, dynamic> toJson() {
    return {
      '$epx:$ctx:ScheduleType': {
        "timestamp": scheduleType,
      },
      '$epx:$ctx:Schedule1': {
        "timestamp": schedule1,
      },
      '$epx:$ctx:Schedule2': {
        "timestamp": schedule2,
      },
      '$epx:$ctx:Schedule3': {
        "timestamp": schedule3,
      },
      '$epx:$ctx:Schedule4': {
        "timestamp": schedule4,
      },
      '$epx:$ctx:Schedule5': {
        "timestamp": schedule5,
      },
      '$epx:$ctx:Schedule6': {
        "timestamp": schedule6,
      },
      '$epx:$ctx:Schedule7': {
        "timestamp": schedule7,
      },
    };
  }
}
