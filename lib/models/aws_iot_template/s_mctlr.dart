
import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SMcTlr extends SBase {
  String? fabID;
  String? fabRcAc;
  String? userCSR;
  String? userNOC;
  String? fabIPK;
  String? nodeID;
  String? thdBaId;
  String? thdExtendedAddress;
  String? thdDataset;
  String? thdChan;

  SMcTlr(super.epx, super.ctx, {
    this.fabID,
    this.fabRcAc,
    this.userCSR,
    this.userNOC,
    this.fabIPK,
    this.nodeID,
    this.thdBaId,
    this.thdExtendedAddress,
    this.thdDataset,
    this.thdChan,
  });

  factory SMcTlr.fromJson(String epx, String ctx, Map<String, dynamic> json) {
    return SMcTlr(
      epx,
      ctx,
      fabID: json['$epx:$ctx:FabID'],
      fabRcAc: json['$epx:$ctx:FabRCAC'],
      userCSR: json['$epx:$ctx:UserCSR'],
      userNOC: json['$epx:$ctx:UserNOC'],
      fabIPK: json['$epx:$ctx:FabIPK'],
      nodeID: json['$epx:$ctx:NodeID'],
      thdBaId: json['$epx:$ctx:ThdBAID'],
      thdExtendedAddress: json['$epx:$ctx:Extaddr'],
      thdDataset: json['$epx:$ctx:ThdDataset'],
      thdChan: json['$epx:$ctx:ThdChan'],
    );
  }

  SMcTlr mapJson(Map<String, dynamic> json) {
    fabID = json['$epx:$ctx:FabID'] ?? fabID;
    fabRcAc = json['$epx:$ctx:FabRCAC'] ?? fabRcAc;
    userCSR = json['$epx:$ctx:UserCSR'] ?? userCSR;
    userNOC = json['$epx:$ctx:UserNOC'] ?? userNOC;
    fabIPK = json['$epx:$ctx:FabIPK'] ?? fabIPK;
    nodeID = json['$epx:$ctx:NodeID'] ?? nodeID;
    thdBaId = json['$epx:$ctx:ThdBAID'] ?? thdBaId;
    thdExtendedAddress = json['$epx:$ctx:Extaddr'] ?? thdExtendedAddress;
    thdDataset = json['$epx:$ctx:ThdDataset'] ?? thdDataset;
    thdChan = json['$epx:$ctx:ThdChan'] ?? thdChan;
    return this;
  }

  Map<String, dynamic> toJson() {
    return {
      '$epx:$ctx:FabID': fabID,
      '$epx:$ctx:FabRCAC': fabRcAc,
      '$epx:$ctx:UserCSR': userCSR,
      '$epx:$ctx:UserNOC': userNOC,
      '$epx:$ctx:FabIPK': fabIPK,
      '$epx:$ctx:NodeID': nodeID,
      '$epx:$ctx:ThdBAID': thdBaId,
      '$epx:$ctx:Extaddr': thdExtendedAddress,
      '$epx:$ctx:ThdDataset': thdDataset,
      '$epx:$ctx:ThdChan': thdChan
    };
  }
}
