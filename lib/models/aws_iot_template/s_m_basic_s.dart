import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SMBasicS extends SBase {
  int? vendorID;
  int? productID;
  int? dmRevision;
  String? vendorName;
  String? productName;
  String? location;
  int? hwVersion;
  String? hwVersionStr;
  int? swVersion;
  String? swVersionStr;
  int? specVersion;
  int? maxPTHPerIvk;

  SMBasicS(super.epx, super.ctx,{
    this.vendorID,
    this.productID,
    this.dmRevision,
    this.vendorName,
    this.productName,
    this.location,
    this.hwVersion,
    this.hwVersionStr,
    this.swVersion,
    this.swVersionStr,
    this.specVersion,
    this.maxPTHPerIvk,
  });

  factory SMBasicS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SMBasicS(
        epx,
        ctx,
        vendorID: json?['$epx:$ctx:VendorID'],
        productID: json?['$epx:$ctx:ProductID'],
        dmRevision: json?['$epx:$ctx:DMRevision'],
        vendorName: json?['$epx:$ctx:VendorName'],
        productName: json?['$epx:$ctx:ProductName'],
        location: json?['$epx:$ctx:Location'],
        hwVersion: json?['$epx:$ctx:HWVersion'],
        hwVersionStr: json?['$epx:$ctx:HWVersionStr'],
        swVersion: json?['$epx:$ctx:SWVersion'],
        swVersionStr: json?['$epx:$ctx:SWVersionStr'],
        specVersion: json?['$epx:$ctx:SpecVersion'],
        maxPTHPerIvk: json?['$epx:$ctx:MaxPTHPerIvk'],
      );

  SMBasicS mapJson(Map<String, dynamic>? json) {
    vendorID = json?['$epx:$ctx:VendorID'] ?? vendorID;
    productID = json?['$epx:$ctx:ProductID'] ?? productID;
    dmRevision = json?['$epx:$ctx:DMRevision'] ?? dmRevision;
    vendorName = json?['$epx:$ctx:VendorName'] ?? vendorName;
    productName = json?['$epx:$ctx:ProductName'] ?? productName;
    location = json?['$epx:$ctx:Location'] ?? location;
    hwVersion = json?['$epx:$ctx:HWVersion'] ?? hwVersion;
    hwVersionStr = json?['$epx:$ctx:HWVersionStr'] ?? hwVersionStr;
    swVersion = json?['$epx:$ctx:SWVersion'] ?? swVersion;
    swVersionStr = json?['$epx:$ctx:SWVersionStr'] ?? swVersionStr;
    specVersion = json?['$epx:$ctx:SpecVersion'] ?? specVersion;
    maxPTHPerIvk = json?['$epx:$ctx:MaxPTHPerIvk'] ?? maxPTHPerIvk;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:VendorID': vendorID,
    '$epx:$ctx:ProductID': productID,
    '$epx:$ctx:DMRevision': dmRevision,
    '$epx:$ctx:VendorName': vendorName,
    '$epx:$ctx:ProductName': productName,
    '$epx:$ctx:Location': location,
    '$epx:$ctx:HWVersion': hwVersion,
    '$epx:$ctx:HWVersionStr': hwVersionStr,
    '$epx:$ctx:SWVersion': swVersion,
    '$epx:$ctx:SWVersionStr': swVersionStr,
    '$epx:$ctx:SpecVersion': specVersion,
    '$epx:$ctx:MaxPTHPerIvk': maxPTHPerIvk,
  };

}