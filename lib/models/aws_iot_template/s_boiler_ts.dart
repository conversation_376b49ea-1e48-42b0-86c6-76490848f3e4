import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SBoilerTS extends SBase {
  int? holdType;
  int? lostThermState;
  int? lostOnOffState;
  int? chSwitch;
  int? chRelay;
  int? dhwSwitch;
  int? dhwRelay;
  int? enableDhw;
  int? boostDHW;

  SBoilerTS(super.epx, super.ctx, {
    this.holdType,
    this.lostThermState,
    this.lostOnOffState,
    this.chSwitch,
    this.chRelay,
    this.dhwSwitch,
    this.dhwRelay,
    this.enableDhw,
    this.boostDHW,
  });

  factory SBoilerTS.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SBoilerTS(
        epx,
        ctx,
        holdType: json?['$epx:$ctx:HoldType']?["timestamp"],
        lostThermState: json?['$epx:$ctx:LostThermState']?["timestamp"],
        lostOnOffState: json?['$epx:$ctx:LostOnoffState']?["timestamp"],
        chSwitch: json?['$epx:$ctx:CHSwitch']?["timestamp"],
        chRelay: json?['$epx:$ctx:CHRelay']?["timestamp"],
        dhwSwitch: json?['$epx:$ctx:DHWSwitch']?["timestamp"],
        dhwRelay: json?['$epx:$ctx:DHWRelay']?["timestamp"],
        enableDhw: json?['$epx:$ctx:EnableDHW']?["timestamp"],
        boostDHW: json?['$epx:$ctx:BoostDHW']?["timestamp"],
      );

  SBoilerTS mapJson(Map<String, dynamic>? json) {
    holdType = json?['$epx:$ctx:HoldType']?["timestamp"] ?? holdType;
    lostThermState = json?['$epx:$ctx:LostThermState']?["timestamp"] ?? lostThermState;
    lostOnOffState = json?['$epx:$ctx:LostOnoffState']?["timestamp"] ?? lostOnOffState;
    chSwitch = json?['$epx:$ctx:CHSwitch']?["timestamp"] ?? chSwitch;
    chRelay = json?['$epx:$ctx:CHRelay']?["timestamp"] ?? chRelay;
    dhwSwitch = json?['$epx:$ctx:DHWSwitch']?["timestamp"] ?? dhwSwitch;
    dhwRelay = json?['$epx:$ctx:DHWRelay']?["timestamp"] ?? dhwRelay;
    enableDhw = json?['$epx:$ctx:EnableDHW']?["timestamp"] ?? enableDhw;
    boostDHW = json?['$epx:$ctx:BoostDHW']?["timestamp"] ?? boostDHW;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:HoldType': {
      "timestamp": holdType,
    },
    '$epx:$ctx:LostThermState': {
      "timestamp": lostThermState,
    },
    '$epx:$ctx:LostOnoffState': {
      "timestamp": lostOnOffState,
    },
    '$epx:$ctx:CHSwitch': {
      "timestamp": chSwitch,
    },
    '$epx:$ctx:CHRelay': {
      "timestamp": chRelay,
    },
    '$epx:$ctx:DHWSwitch': {
      "timestamp": dhwSwitch,
    },
    '$epx:$ctx:DHWRelay': {
      "timestamp": dhwRelay,
    },
    '$epx:$ctx:EnableDHW': {
      "timestamp": enableDhw,
    },
    '$epx:$ctx:BoostDHW': {
      "timestamp": boostDHW,
    },
  };


}