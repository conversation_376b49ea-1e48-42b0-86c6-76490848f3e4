import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SOta extends SBase {
  int? otaStatusD;
  String? otaFirmwareURLD;
  String? otaFirmwareVersionD;

  SOta(super.epx, super.ctx,{
    this.otaStatusD,
    this.otaFirmwareURLD,
    this.otaFirmwareVersionD,
  });

  factory SOta.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      SOta(
        epx,
        ctx,
        otaStatusD: json?['$epx:$ctx:OTAStatus_d'],
        otaFirmwareURLD: json?['$epx:$ctx:OTAFirmwareURL_d'],
        otaFirmwareVersionD: json?['$epx:$ctx:OTAFirmwareVersion_d'],
      );

  SOta mapJson(Map<String, dynamic>? json) {
    otaStatusD = json?['$epx:$ctx:OTAStatus_d'] ?? otaStatusD;
    otaFirmwareURLD = json?['$epx:$ctx:OTAFirmwareURL_d'] ?? otaFirmwareURLD;
    otaFirmwareVersionD = json?['$epx:$ctx:OTAFirmwareVersion_d'] ?? otaFirmwareVersionD;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:OTAStatus_d': otaStatusD,
    '$epx:$ctx:OTAFirmwareURL_d': otaFirmwareURLD,
    '$epx:$ctx:OTAFirmwareVersion_d': otaFirmwareVersionD,
  };

}