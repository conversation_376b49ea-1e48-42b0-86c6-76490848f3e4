import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/aws_iot_template/s_base.dart';

class SGateway extends SBase {
  String? gatewaySoftwareVersion;
  String? gatewayHardwareVersion;
  String? networkWiFiMAC;
  String? modelIdentifier;
  String? deviceName;
  List<String>? deviceFullList;
  String? networkSSID;
  String? networkPasswordF;
  String? timeZone;
  int? wifiRssi;

  SGateway(super.epx, super.ctx, {
    this.gatewaySoftwareVersion,
    this.gatewayHardwareVersion,
    this.networkWiFiMAC,
    this.modelIdentifier,
    this.deviceName,
    this.deviceFullList,
    this.networkSSID,
    this.networkPasswordF,
    this.timeZone,
    this.wifiRssi,
  });

  factory SGateway.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) {
    return SGateway(
        epx,
        ctx,
        gatewaySoftwareVersion: json?['$epx:$ctx:GatewaySoftwareVersion'],
        gatewayHardwareVersion: json?['$epx:$ctx:GatewayHardwareVersion'],
        networkWiFiMAC: json?['$epx:$ctx:NetworkWiFiMAC'],
        modelIdentifier: json?['$epx:$ctx:ModelIdentifier'],
        deviceName: json?['$epx:$ctx:DeviceName'],
        deviceFullList: json?['$epx:$ctx:DeviceFullList'] != null
            ? List<String>.from(json?['$epx:$ctx:DeviceFullList'].map((x) => x)) : null,
        networkSSID: json?['$epx:$ctx:NetworkSSID'],
        networkPasswordF: json?['$epx:$ctx:NetworkPassword_f'],
        timeZone: json?['$epx:$ctx:TimeZone'],
        wifiRssi: json?['$epx:$ctx:WiFiRSSI'],
      );
  }

  SGateway mapJson(Map<String, dynamic>? json) {
    gatewaySoftwareVersion = json?['$epx:$ctx:GatewaySoftwareVersion'] ?? gatewaySoftwareVersion;
    gatewayHardwareVersion = json?['$epx:$ctx:GatewayHardwareVersion'] ?? gatewayHardwareVersion;
    networkWiFiMAC = json?['$epx:$ctx:NetworkWiFiMAC'] ?? networkWiFiMAC;
    modelIdentifier = json?['$epx:$ctx:ModelIdentifier'] ?? modelIdentifier;
    deviceName = json?['$epx:$ctx:DeviceName'] ?? deviceName;
    deviceFullList = json?['$epx:$ctx:DeviceFullList'] != null
        ? List<String>.from(json?['$epx:$ctx:DeviceFullList'].map((x) => x)) : deviceFullList;
    networkSSID = json?['$epx:$ctx:NetworkSSID'] ?? networkSSID;
    networkPasswordF = json?['$epx:$ctx:NetworkPassword_f'] ?? networkPasswordF;
    timeZone = json?['$epx:$ctx:TimeZone'] ?? timeZone;
    wifiRssi = json?['$epx:$ctx:WiFiRSSI'] ?? wifiRssi;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:GatewaySoftwareVersion': gatewaySoftwareVersion,
    '$epx:$ctx:GatewayHardwareVersion': gatewayHardwareVersion,
    '$epx:$ctx:NetworkWiFiMAC': networkWiFiMAC,
    '$epx:$ctx:ModelIdentifier': modelIdentifier,
    '$epx:$ctx:DeviceName': deviceName,
    '$epx:$ctx:DeviceFullList' : deviceFullList?.map((x) => x).toList(),
    '$epx:$ctx:NetworkSSID': networkSSID,
    '$epx:$ctx:NetworkPassword_f': networkPasswordF,
    '$epx:$ctx:TimeZone': timeZone,
    '$epx:$ctx:WiFiRSSI': wifiRssi,
  };

}