import 'package:habi_app/models/aws_iot_template/s_base.dart';

class STherO extends SBase {
  int? disHeatOD;

  STherO(super.epx, super.ctx,{
    this.disHeatOD,
  });

  factory STherO.fromJson(
      String epx,
      String ctx,
      Map<String, dynamic>? json,
      ) =>
      STherO(
        epx,
        ctx,
        disHeatOD: json?['$epx:$ctx:DisHeatOD'],
      );

  STherO mapJson(Map<String, dynamic>? json) {
    disHeatOD = json?['$epx:$ctx:DisHeatOD'] ?? disHeatOD;
    return this;
  }

  Map<String, dynamic> toJson() => {
    '$epx:$ctx:DisHeatOD': disHeatOD,
  };

}