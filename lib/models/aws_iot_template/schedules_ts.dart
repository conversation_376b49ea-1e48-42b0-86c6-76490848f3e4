import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/models/aws_iot_template/s_time_gh_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_time_h_ts.dart';

class SchedulesTS {
  SchedulesTS(
      this.epx, {
        this.sTimeGh,
        this.sTimeH,
        this.sMode = ScheduleMode.heatCool,
        this.sTemplateMode = ScheduleTemplateMode.sTimeGh,
      });

  factory SchedulesTS.fromJson(
      String epx,
      Map<String, dynamic>? json, {
        ScheduleMode sMode = ScheduleMode.heatCool,
        ScheduleTemplateMode sTemplateMode = ScheduleTemplateMode.sTimeGh,
      }) =>
      SchedulesTS(
        epx,
        sTimeGh: STimeGhTS.fromJson(epx, 'sTimeGH', sMode, json),
        sTimeH: STimeHTS.fromJson(epx, 'sTimeH', sMode, json),
      );

  STimeGhTS? sTimeGh;
  STimeHTS? sTimeH;
  String epx;
  ScheduleMode sMode;
  ScheduleTemplateMode sTemplateMode;

  SchedulesTS mapJson(Map<String, dynamic>? json) {
    sTimeGh = sTimeGh?.mapJson(json);
    sTimeH = sTimeH?.mapJson(json);
    return this;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> value = {};
    if (sTimeGh != null) {
      value.addAll(sTimeGh!.toJson());
    }
    if (sTimeH != null) {
      value.addAll(sTimeH!.toJson());
    }
    return value;
  }
}
