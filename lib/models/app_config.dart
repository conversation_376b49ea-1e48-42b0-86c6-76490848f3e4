import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:habi_app/models/app_config_fabric.dart';
import 'package:habi_app/models/matter/support_device.dart';
import 'app_config_aws.dart';

class AppConfig{

  final String clientCode;
  final String companyCode;
  final String baseUrl;
  final String amplify;
  final String termsUrl;
  final String privacyUrl;
  final String supportUrl;
  final String baseDeviceProvisionUrl;
  final bool factoryParingMode;
  final AppConfigAws aws;
  final AppConfigFabric fabric;
  final List<SupportDevice> supportDevices;
  bool amplifyConfigured = false;

  AppConfig({
    required this.clientCode,
    required this.companyCode,
    required this.baseUrl,
    required this.amplify,
    required this.termsUrl,
    required this.privacyUrl,
    required this.supportUrl,
    required this.baseDeviceProvisionUrl,
    required this.factoryParingMode,
    required this.aws,
    required this.fabric,
    required this.supportDevices,
  });

  factory AppConfig.fromJson(Map<String, dynamic> json) {
    return AppConfig(
      clientCode: json['clientCode'],
      companyCode: json['companyCode'],
      baseUrl: json['baseUrl'],
      termsUrl: json['termsUrl'],
      privacyUrl: json['privacyUrl'],
      supportUrl: json['supportUrl'],
      baseDeviceProvisionUrl: json['baseDeviceProvisionUrl'],
      factoryParingMode: json['factoryParingMode'],
      amplify: jsonEncode(json['amplify']),
      aws: AppConfigAws.fromJson(json['aws']),
      fabric: AppConfigFabric.fromJson(json['fabric']),
      supportDevices: List<SupportDevice>.from(
          json['supportDevices'].map((room) => SupportDevice.fromJson(room))),
    );
  }

  static Future<AppConfig> forEnvironment(String env) async {
    final contents = await rootBundle.loadString('assets/config/$env.json');
    final jsonResult = jsonDecode(contents);
    debugPrint('app-config: $jsonResult');
    return AppConfig.fromJson(jsonResult);
  }

  @override
  String toString() {
    return 'AppConfig{'
        'clientCode: $clientCode, '
        'companyCode: $companyCode, '
        'baseUrl: $baseUrl, '
        'termsUrl: $termsUrl, '
        'privacyUrl: $privacyUrl, '
        'supportUrl: $supportUrl, '
        'baseDeviceProvisionUrl: $baseDeviceProvisionUrl, '
        'factoryParingMode: $factoryParingMode, '
        'aws: $aws, '
        'fabric: $fabric'
        '}';
  }

}
