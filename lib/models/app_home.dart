import 'package:habi_app/models/app_attribute.dart';
import 'package:habi_app/models/dev/hb_home.dart';

import 'app_room.dart';

class AppHome {
  String? userId;
  String? homeId;
  String? name;
  List<AppRoom>? rooms;
  AppAttribute? attributes;
  List<String>? deviceList;
  List<String>? owner;
  List<String>? sharer;

  AppHome({
    this.userId,
    this.homeId,
    this.name,
    this.rooms,
    this.attributes,
    this.deviceList,
    this.owner,
    this.sharer,
  });

  factory AppHome.fromHBHome(HBHome home) {
    AppHome appHome = AppHome();
    appHome.userId = home.userId;
    appHome.homeId = home.homeId;
    appHome.name = home.name;
    appHome.attributes = AppAttribute();
    appHome.attributes!.address = home.attributes?.address;
    appHome.deviceList = home.deviceList;
    appHome.rooms = [];
    appHome.owner = home.owner;
    appHome.sharer = home.sharer;
    return appHome;
  }

  factory AppHome.fromJson(Map<String, dynamic> json) {
    return AppHome(
      userId: json['userId'],
      homeId: json['homeId'],
      name: json['name'],
      attributes: json['attributes'] != null
          ? AppAttribute.fromJson(json['attributes'])
          : null,
      rooms: json['rooms'] != null
          ? List<AppRoom>.from(
              json['rooms'].map((room) => AppRoom.fromJson(room)))
          : null,
      deviceList: json['deviceList'] != null
          ? List<String>.from(json['deviceList'].map((x) => x)) : null,
      owner: json['owner'] != null
          ? List<String>.from(json['owner'].map((x) => x)) : null,
      sharer: json['sharer'] != null
          ? List<String>.from(json['sharer'].map((x) => x)) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'homeId': homeId,
      'name': name,
      'attributes': attributes?.toJson(),
      'rooms': rooms?.map((room) => room.toJson()).toList(),
      'deviceList': deviceList?.map((x) => x).toList(),
      'owner': owner?.map((x) => x).toList(),
      'sharer': sharer?.map((x) => x).toList(),
    };
  }

  @override
  String toString() {
    return 'AppHome(userId: $userId, homeId: $homeId, name: $name, rooms: $rooms, attributes: $attributes, deviceList: $deviceList, owner: $owner, sharer: $sharer)';
  }

}
