class DemoHomes {
  List<DemoHome> homes;

  DemoHomes({
    required this.homes,
  });

  factory DemoHomes.fromJson(Map<String, dynamic> json) {
    return DemoHomes(
      homes: json['homes'] != null
          ? List<DemoHome>.from(
              (json['homes'] as List).map((home) => DemoHome.fromJson(home)),
            )
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'homes': homes.map((home) => home.toJson()).toList(),
    };
  }

  DemoHome getHomeById(String homeId) {
    for (final home in homes) {
      if (home.homeId == homeId) {
        return home;
      }
    }
    throw Exception('Home not found');
  }

  DemoHome getHomeByDeviceId(String deviceId) {
    for (final home in homes) {
      if (home.deviceList?.contains(deviceId) ?? false) {
        return home;
      }
    }
    throw Exception('Home not found');
  }

  DemoRoom getRoomById(String roomId) {
    for (final home in homes) {
      for (final room in home.rooms) {
        if (room.roomId == roomId) {
          return room;
        }
      }
    }
    throw Exception('Room not found');
  }

  Map<String, dynamic> buildGetHomesResponse() {
    return {
      'success': true,
      'error_code': 0,
      'homes': homes.map((home) => home.toJsonForGetHome()).toList(),
      'overFlowItems': [],
      'areMorePages': false,
    };
  }
}

class DemoHome {
  String homeId;
  List<String> owner;
  List<String> sharer;
  Map<String, dynamic>? attributes;
  String? name;
  List<String>? deviceList;
  List<DemoRoom> rooms;

  DemoHome({
    required this.homeId,
    required this.owner,
    required this.sharer,
    this.attributes,
    this.name,
    this.deviceList,
    required this.rooms,
  });

  factory DemoHome.fromJson(Map<String, dynamic> json) {
    return DemoHome(
      homeId: json['HomeID'] as String,
      owner: json['Owner'] != null ? List<String>.from(json['Owner']) : [],
      sharer: json['Sharer'] != null ? List<String>.from(json['Sharer']) : [],
      attributes: json['Attributes'] != null
          ? Map<String, dynamic>.from(json['Attributes'])
          : null,
      name: json['Name'] as String?,
      deviceList: json['DeviceList'] != null
          ? List<String>.from(json['DeviceList'])
          : null,
      rooms: json['rooms'] != null
          ? List<DemoRoom>.from(
              (json['rooms'] as List).map((room) => DemoRoom.fromJson(room)),
            )
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'HomeID': homeId,
      'Owner': owner,
      'Sharer': sharer,
      'Attributes': attributes,
      'Name': name,
      'DeviceList': deviceList,
      'rooms': rooms.map((room) => room.toJson()).toList(),
    };
  }

  Map<String, dynamic> toJsonForGetHome() {
    return {
      'HomeID': homeId,
      'Owner': owner,
      'Sharer': sharer,
      if (attributes != null) 'Attributes': attributes,
      if (name != null) 'Name': name,
      if (deviceList != null) 'DeviceList': deviceList,
    };
  }

  Map<String, dynamic> buildAddHomeResponse() {
    return {
      'success': true,
      'error_code': 0,
      'homeId': homeId,
    };
  }

  Map<String, dynamic> buildGetHomeResponse() {
    return {
      'success': true,
      'error_code': 0,
      'home': toJsonForGetHome(),
    };
  }

  Map<String, dynamic> buildGetHomeRoomsResponse() {
    return {
      'success': true,
      'error_code': 0,
      'areMorePages': false,
      'rooms': rooms.map((room) => room.toJsonForGetRoom()).toList(),
    };
  }
}

class DemoRoom {
  String homeId;
  String roomId;
  String? name;
  List<String>? deviceList;
  Map<String, dynamic>? attributes;

  DemoRoom({
    required this.homeId,
    required this.roomId,
    this.name,
    this.deviceList,
    this.attributes,
  });

  factory DemoRoom.fromJson(Map<String, dynamic> json) {
    return DemoRoom(
      homeId: json['HomeID'] as String,
      roomId: json['RoomID'] as String,
      name: json['Name'] as String?,
      deviceList: json['DeviceList'] != null
          ? List<String>.from(json['DeviceList'])
          : null,
      attributes: json['Attributes'] != null
          ? Map<String, dynamic>.from(json['Attributes'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'HomeID': homeId,
      'RoomID': roomId,
      'Name': name,
      'DeviceList': deviceList,
      'Attributes': attributes,
    };
  }

  Map<String, dynamic> toJsonForGetRoom() {
    return {
      'HomeID': homeId,
      'RoomID': roomId,
      if (name != null) 'Name': name,
      if (deviceList != null) 'DeviceList': deviceList,
      if (attributes != null) 'Attributes': attributes,
    };
  }

  Map<String, dynamic> buildAddRoomResponse() {
    return {
      'success': true,
      'error_code': 0,
      'roomId': roomId,
    };
  }

  Map<String, dynamic> buildGetRoomResponse() {
    return {
      'success': true,
      'error_code': 0,
      'room': toJsonForGetRoom(),
    };
  }
}
