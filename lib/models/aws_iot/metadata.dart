import 'package:habi_app/models/aws_iot/parse_properties.dart';

import 'metadata_desired.dart';
import 'metadata_reported.dart';

class Metadata<T extends ParseProperties>{

  Metadata({
    required this.desired,
    required this.reported,
  });

  MetadataDesired<T>? desired;
  MetadataReported<T>? reported;

  factory Metadata.fromJson(
      Map<String, dynamic> json,
      String baseKey,
      {
        required T desiredProperties,
        required T reportedProperties,
      }
      ) {
    return Metadata<T>(
      desired: MetadataDesired<T>.from<PERSON><PERSON>(
        json['desired'] ?? <String, dynamic>{},
        desiredProperties,
        baseKey,
      ),
      reported: MetadataReported<T>.from<PERSON>son(
        json['reported'] ?? <String, dynamic>{},
        reportedProperties,
        baseKey,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'desired': desired?.toJson(),
      'reported': reported?.toJson(),
    };
  }

  Metadata<T> mapJson(Map<String, dynamic> json) {
    desired = json['desired'] != null ? desired?.map<PERSON>son(json['desired']) : desired;
    reported = json['reported'] != null ? reported?.mapJson(json['reported']) : reported;
    return this;
  }

}