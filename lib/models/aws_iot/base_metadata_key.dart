
import 'parse_properties.dart';

class BaseMetadataKey<T extends ParseProperties> {

  int? model;
  T? properties;

  BaseMetadataKey({
    this.model,
    this.properties,
  });

  factory BaseMetadataKey.fromJson(
    Map<String, dynamic> json,
    T properties,
  ) {
    return BaseMetadataKey<T>(
      model: json['model']?["timestamp"],
      properties: properties.fromJson(json['properties'] ?? <String, dynamic>{}),
    );
  }

  BaseMetadataKey<T> mapJson(Map<String, dynamic> json) {
    model = json['model']?["timestamp"] ?? model;
    properties = properties?.mapJson(json['properties'] ?? <String, dynamic>{}) ?? properties;
    return this;
  }

  Map<String, dynamic> toJson() => {
    'model': {
      'timestamp': model,
    },
    'properties': properties?.toJson(),
  };

}
