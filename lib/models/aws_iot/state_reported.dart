import 'base_key.dart';
import 'parse_properties.dart';

class StateReported<T extends ParseProperties> {
  final String baseKey;
  String? connected;
  int? version;
  int? cloudConn;
  int? nodeConn;
  BaseKey<T>? model;
  String? createdAt;

  StateReported(
    this.baseKey, {
    this.connected,
    this.version,
    this.cloudConn,
    this.nodeConn,
    this.model,
    this.createdAt,
  });

  factory StateReported.fromJson(
    Map<String, dynamic> json,
    T properties,
    String baseKey,
  ) {
    return StateReported<T>(
      baseKey,
      connected: json['connected'],
      version: json['version'],
      cloudConn: json['cloud_conn'],
      nodeConn: json['node_conn'],
      model: BaseKey<T>.fromJson(
        json[baseKey] ?? <String, dynamic>{},
        properties,
      ),
      createdAt: json['createdAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'connected': connected,
      'version': version,
      'cloud_conn': cloudConn,
      'node_conn': nodeConn,
      baseKey: model?.toJson(),
      'createdAt': createdAt,
    };
  }

  StateReported<T> mapJson(Map<String, dynamic> json) {
    connected = json['connected'] ?? connected;
    version = json['version'] ?? version;
    cloudConn = json['cloud_conn'] ?? cloudConn;
    nodeConn = json['node_conn'] ?? nodeConn;
    model = json[baseKey] != null ? model?.mapJson(json[baseKey]) : model;
    createdAt = json['createdAt'] ?? createdAt;
    return this;
  }
}
