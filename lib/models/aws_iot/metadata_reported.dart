import 'base_metadata_key.dart';
import 'parse_properties.dart';

class MetadataReported<T extends ParseProperties> {

  final String baseKey;
  int? connected;
  int? version;
  int? cloudConn;
  int? nodeConn;
  BaseMetadataKey<T>? model;
  int? createdAt;

  MetadataReported(
    this.baseKey, {
    this.connected,
    this.version,
    this.cloudConn,
    this.nodeConn,
    this.model,
    this.createdAt,
  });

  factory MetadataReported.fromJson(
      Map<String, dynamic> json,
      T properties,
      String baseKey,
      ) {
    return MetadataReported<T>(
      baseKey,
      connected: json['connected']?["timestamp"],
      version: json['version']?["timestamp"],
      cloudConn: json['cloud_conn']?["timestamp"],
      nodeConn: json['node_conn']?["timestamp"],
      model: BaseMetadataKey<T>.fromJson(
        json[baseKey] ?? <String, dynamic>{},
        properties,
      ),
      createdAt: json['createdAt']?["timestamp"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'connected': {
        'timestamp': connected
      },
      'version': {
        'version': version,
      },
      'cloud_conn': {
        'timestamp': cloudConn
      },
      'node_conn': {
        'timestamp': nodeConn
      },
      baseKey: model?.toJson(),
      'createdAt': {
        'timestamp': createdAt
      },
    };
  }

  MetadataReported<T> mapJson(Map<String, dynamic> json) {
    connected = json['connected']?["timestamp"] ?? connected;
    version = json['version']?["timestamp"] ?? version;
    cloudConn = json['cloud_conn']?["timestamp"] ?? cloudConn;
    nodeConn = json['node_conn']?["timestamp"] ?? nodeConn;
    model = model?.mapJson(json) ?? model;
    createdAt = json['createdAt']?["timestamp"] ?? createdAt;
    return this;
  }
}
