import 'parse_properties.dart';
import 'base_key.dart';

class StateDelta<T extends ParseProperties> {
  final String baseKey;
  BaseKey<T>? model;

  StateDelta(
    this.baseKey, {
    this.model,
  });

  factory StateDelta.fromJson(
      Map<String, dynamic> json,
      T properties,
      String baseKey,
      ) {
    return StateDelta<T>(
      baseKey,
      model: BaseKey.fromJson(
        json[baseKey] ?? <String, dynamic>{},
        properties,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      baseKey: model?.toJson(),
    };
  }

  StateDelta<T> mapJson(Map<String, dynamic> json) {
    model = json[baseKey] != null ? model?.mapJson(json[baseKey]) : model;
    return this;
  }
}
