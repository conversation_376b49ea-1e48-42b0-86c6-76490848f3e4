import 'metadata.dart';
import 'parse_properties.dart';
import 'state.dart';

class Shadow<T1 extends ParseProperties, T2 extends ParseProperties> {
  Shadow({
    required this.state,
    required this.metadata,
    required this.version,
    required this.timestamp,
  });

  State<T1>? state;
  Metadata<T2>? metadata;
  int? version;
  int? timestamp;

  factory Shadow.fromJson(
      Map<String, dynamic> json,
      String baseKey,
      {
        required T1 stateDesiredProperties,
        required T1 stateReportedProperties,
        required T1 stateDeltaProperties,
        required T2 meteDataDesiredProperties,
        required T2 meteDataReportedProperties,
      }
  ){
    return Shadow<T1, T2>(
      state: json["state"] == null ? null : State<T1>.fromJson(
          json["state"] ?? <String, dynamic>{},
          baseKey,
          desiredProperties: stateDesiredProperties,
          reportedProperties: stateReportedProperties,
          deltaProperties: stateDeltaProperties,
      ),
      metadata: json["metadata"] == null ? null : Metadata<T2>.from<PERSON>son(
          json["metadata"] ?? <String, dynamic>{},
          baseKey,
          desiredProperties: meteDataDesiredProperties,
          reportedProperties: meteDataReportedProperties,
      ),
      version: json["version"] == null ? null : json["version"] as int,
      timestamp: json["timestamp"] == null ? null : json["timestamp"] as int,
    );
  }

  Map<String, dynamic> toJson() => {
    "state": state?.toJson(),
    "metadata": metadata?.toJson(),
    "version": version,
    "timestamp": timestamp,
  };

  Shadow<T1, T2> mapJson(Map<String, dynamic> json) {
    state = json["state"] != null ? state?.mapJson(json["state"]) : state;
    metadata = json["metadata"] != null ? metadata?.mapJson(json["metadata"]) : metadata;
    version = json["version"] ?? version;
    timestamp = json["timestamp"] ?? timestamp;
    return this;
  }

}