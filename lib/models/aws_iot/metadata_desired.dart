import 'base_metadata_key.dart';
import 'parse_properties.dart';

class MetadataDesired<T extends ParseProperties>  {

  final String baseKey;
  int? version;
  BaseMetadataKey<T>? model;

  MetadataDesired(
    this.baseKey, {
    this.version,
    this.model,
  });

  factory MetadataDesired.fromJson(
      Map<String, dynamic> json,
      T properties,
      String baseKey,
      ) {
    return MetadataDesired<T>(
      baseKey,
      version: json['version']?["timestamp"],
      model: BaseMetadataKey<T>.fromJson(
        json[baseKey] ?? <String, dynamic>{},
        properties,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': {
        'timestamp': version,
      },
      baseKey: model?.toJson(),
    };
  }

  MetadataDesired<T> mapJson(Map<String, dynamic> json) {
    version = json['version']?["timestamp"] ?? version;
    model = model?.mapJson(json) ?? model;
    return this;
  }

}