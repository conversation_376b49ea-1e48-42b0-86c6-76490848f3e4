import 'base_key.dart';
import 'parse_properties.dart';

class StateDesired<T extends ParseProperties> {
  final String baseKey;
  int? version;
  BaseKey<T>? model;

  StateDesired(
    this.baseKey, {
    this.version,
    this.model,
  });

  factory StateDesired.fromJson(
    Map<String, dynamic> json,
    T properties,
    String baseKey,
  ) {
    return StateDesired<T>(
      baseKey,
      version: json['version'],
      model: BaseKey.fromJson(
        json[baseKey] ?? <String, dynamic>{},
        properties,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      baseKey: model?.toJson(),
    };
  }

  StateDesired<T> mapJson(Map<String, dynamic> json) {
    version = json['version'] ?? version;
    model = json[baseKey] != null ? model?.mapJson(json[baseKey]) : model;
    return this;
  }
}
