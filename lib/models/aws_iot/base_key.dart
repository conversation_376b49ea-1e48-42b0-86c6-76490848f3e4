import 'parse_properties.dart';

class BaseKey<T extends ParseProperties> {

  String? model;
  T? properties;

  BaseKey({
    this.model,
    this.properties,
  });

  factory BaseKey.fromJson(
    Map<String, dynamic> json,
    T properties,
  ) {
    return BaseKey<T>(
      model: json['model'],
      properties: properties.fromJson(json['properties'] ?? <String, dynamic>{}),
    );
  }

  BaseKey<T> mapJson(Map<String, dynamic> json) {
    model = json['model'] ?? model;
    properties = json['properties'] != null ? properties?.mapJson(json['properties']) : properties;
    return this;
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    if (model != null) {
      allJson['model'] = model;
    }

    Map<String, dynamic>? propertiesJson = properties?.toJson();
    if (propertiesJson != null) {
      allJson['properties'] = propertiesJson;
    }

    return allJson;
  }

}
