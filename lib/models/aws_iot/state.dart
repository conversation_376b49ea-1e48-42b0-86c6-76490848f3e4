import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/aws_iot/parse_properties.dart';
import 'package:habi_app/models/aws_iot/state_delta.dart';
import 'package:habi_app/models/aws_iot/state_desired.dart';
import 'package:habi_app/models/aws_iot/state_reported.dart';

class State<T extends ParseProperties> {
  StateDesired<T>? desired;
  StateReported<T>? reported;
  StateDelta<T>? delta;

  State({
    this.desired,
    this.reported,
    this.delta,
  });

  factory State.fromJson(
    Map<String, dynamic> json,
    String baseKey,
      {
        required T desiredProperties,
        required T reportedProperties,
        required T deltaProperties,
      }
  ) {
    return State<T>(
      desired: StateDesired<T>.fromJson(
        json['desired'] ?? <String, dynamic>{},
        desiredProperties,
        baseKey,
      ),
      reported: StateReported<T>.fromJson(
        json['reported'] ?? <String, dynamic>{},
        reportedProperties,
        baseKey,
      ),
      delta: StateDelta<T>.fromJson(
        json['delta'] ?? <String, dynamic>{},
        deltaProperties,
        baseKey,
      ),
    );
  }

  Map<String, dynamic> toJson() => {
        'desired': desired?.toJson(),
        'reported': reported?.toJson(),
        'delta': delta?.toJson(),
      };

  State<T> mapJson(Map<String, dynamic> json) {
    desired = json['desired'] != null ? desired?.mapJson(json['desired']) : desired;
    reported = json['reported'] != null ? reported?.mapJson(json['reported']) : reported;
    delta = json['delta'] != null ? delta?.mapJson(json['delta']) : delta;
    return this;
  }
}
