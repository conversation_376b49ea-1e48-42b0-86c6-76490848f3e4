import 'dart:async';

class ValueCompleter<T, V> implements Completer<T> {
  final Completer<T> _completer;
  final V extraValue;

  ValueCompleter(this.extraValue) : _completer = Completer<T>();

  @override
  bool get isCompleted => _completer.isCompleted;

  @override
  Future<T> get future => _completer.future;

  @override
  void complete([FutureOr<T>? value]) => _completer.complete(value);

  @override
  void completeError(Object error, [StackTrace? stackTrace]) =>
      _completer.completeError(error, stackTrace);
}
