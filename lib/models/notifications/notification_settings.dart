class NotificationSettings {
  final bool? pushNotificationToggle;

  NotificationSettings({this.pushNotificationToggle});

  // Convert from JSON (Map) to NotificationSettings
  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      pushNotificationToggle: json['pushNotificationToggle'],
    );
  }

  // Convert NotificationSettings to JSON (Map)
  Map<String, dynamic> toJson() {
    return {
      'pushNotificationToggle': pushNotificationToggle,
    };
  }
}