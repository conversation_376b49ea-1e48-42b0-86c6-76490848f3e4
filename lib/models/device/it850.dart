import 'package:get/get.dart';
import 'package:habi_app/base/base_device.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/models/aws_iot/parse_properties.dart';
import 'package:habi_app/models/aws_iot/shadow.dart';
import 'package:habi_app/models/aws_iot_template/s_m_basic_s_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_mdo.dart';
import 'package:habi_app/models/aws_iot_template/s_mdo_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_net_comm.dart';
import 'package:habi_app/models/aws_iot_template/s_net_comm_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_power_s.dart';
import 'package:habi_app/models/aws_iot_template/s_power_s_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_ther_o.dart';
import 'package:habi_app/models/aws_iot_template/s_ther_o_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_ther_s.dart';
import 'package:habi_app/models/aws_iot_template/s_ther_s_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_m_basic_s.dart';
import 'package:habi_app/models/aws_iot_template/s_ther_uis.dart';
import 'package:habi_app/models/aws_iot_template/s_ther_uis_ts.dart';
import 'package:habi_app/models/aws_iot_template/schedules.dart';
import 'package:habi_app/models/aws_iot_template/schedules_ts.dart';

class IT850 extends BaseDevice {
  static const String subId = '11';
  Shadow<StateIT850Properties, MetaDataIT850PropertiesProperties> shadow;

  IT850({
    required this.shadow,
    required super.thingName,
  }) : super(baseKey: subId);

  factory IT850.fromJson(Map<String, dynamic> json, String thingName) {
    return IT850(
      shadow: Shadow<StateIT850Properties, MetaDataIT850PropertiesProperties>.fromJson(
        json,
        subId,
        stateDesiredProperties: StateIT850Properties(),
        stateReportedProperties: StateIT850Properties(),
        stateDeltaProperties: StateIT850Properties(),
        meteDataDesiredProperties: MetaDataIT850PropertiesProperties(),
        meteDataReportedProperties: MetaDataIT850PropertiesProperties(),
      ),
      thingName: thingName,
    );
  }

  IT850 mapJson(Map<String, dynamic> json) {
    shadow = shadow.mapJson(json);
    return this;
  }

  Map<String, dynamic> toJson() {
    return shadow.toJson();
  }

  @override
  String getDeviceName() {
    return shadow.state?.reported?.model?.properties?.sMdo?.deviceName ?? 'myThermostat'.tr;
  }

  @override
  Map<String, dynamic>? getLeaveNetworkPayload() {
    return {
      'ep1:sMDO:sLeavNetw': 1,
    };
  }
}

class StateIT850Properties extends ParseProperties<StateIT850Properties> {

  STherS? sTherS;
  STherO? sTherO;
  STherUIS? sTherUIS;
  SMBasicS? sMBasicS;
  SMdo? sMdo;
  Schedules? schedules;
  SPowerS? sPowerS;
  SNetComm? sNetComm;

  StateIT850Properties({
    this.sTherS,
    this.sTherO,
    this.sTherUIS,
    this.sMBasicS,
    this.sMdo,
    this.schedules,
    this.sPowerS,
    this.sNetComm,
  });

  @override
  StateIT850Properties fromJson(Map<String, dynamic> json) {
    return StateIT850Properties(
      sTherS: STherS.fromJson('ep1', 'sTherS', json),
      sTherO: STherO.fromJson('ep1', 'sTherO', json),
      sTherUIS: STherUIS.fromJson('ep1', 'sTherUIS', json),
      sMBasicS: SMBasicS.fromJson('ep1', 'sMBasicS', json),
      sMdo: SMdo.fromJson('ep1', 'sMDO', json),
      schedules: Schedules.fromJson('ep1', json, sMode: ScheduleMode.heatOnly,
          sTemplateMode: ScheduleTemplateMode.sTimeH),
      sPowerS: SPowerS.fromJson('ep1', 'sPowerS', json),
      sNetComm: SNetComm.fromJson('ep1', 'sNetComm', json),
    );
  }

  @override
  StateIT850Properties mapJson(Map<String, dynamic> json) {
    sTherS = sTherS?.mapJson(json);
    sTherO = sTherO?.mapJson(json);
    sTherUIS = sTherUIS?.mapJson(json);
    sMBasicS = sMBasicS?.mapJson(json);
    sMdo = sMdo?.mapJson(json);
    schedules = schedules?.mapJson(json);
    sPowerS = sPowerS?.mapJson(json);
    sNetComm = sNetComm?.mapJson(json);
    return this;
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    Map<String, dynamic>? sTherSJson = sTherS?.toJson();
    if (sTherSJson != null) {
      allJson.addAll(sTherSJson);
    }

    Map<String, dynamic>? sTherOJson = sTherO?.toJson();
    if (sTherOJson != null) {
      allJson.addAll(sTherOJson);
    }

    Map<String, dynamic>? sTherUISJson = sTherUIS?.toJson();
    if (sTherUISJson != null) {
      allJson.addAll(sTherUISJson);
    }

    Map<String, dynamic>? sMBasicSJson = sMBasicS?.toJson();
    if (sMBasicSJson != null) {
      allJson.addAll(sMBasicSJson);
    }

    Map<String, dynamic>? sMdoJson = sMdo?.toJson();
    if (sMdoJson != null) {
      allJson.addAll(sMdoJson);
    }

    Map<String, dynamic>? schedulesJson = schedules?.toJson();
    if (schedulesJson != null) {
      allJson.addAll(schedulesJson);
    }

    Map<String, dynamic>? sPowerSJson = sPowerS?.toJson();
    if (sPowerSJson != null) {
      allJson.addAll(sPowerSJson);
    }

    Map<String, dynamic>? sNetCommJson = sNetComm?.toJson();
    if (sNetCommJson != null) {
      allJson.addAll(sNetCommJson);
    }

    return allJson;
  }

}

class MetaDataIT850PropertiesProperties extends ParseProperties<MetaDataIT850PropertiesProperties> {

  STherSTS? sTherSTS;
  STherOTS? sTherOTS;
  STherUISTS? sTherUISTS;
  SMBasicSTS? sMBasicSTS;
  SMdoTS? sMdoTS;
  SchedulesTS? scheduleTS;
  SPowerSTS? sPowerSTS;
  SNetCommTS? sNetCommTS;

  MetaDataIT850PropertiesProperties({
    this.sTherSTS,
    this.sTherOTS,
    this.sTherUISTS,
    this.sMBasicSTS,
    this.sMdoTS,
    this.scheduleTS,
    this.sPowerSTS,
    this.sNetCommTS,
  });

  @override
  MetaDataIT850PropertiesProperties fromJson(Map<String, dynamic> json) {
    return MetaDataIT850PropertiesProperties(
      sTherSTS: STherSTS.fromJson('ep1', 'sTherS', json),
      sTherOTS: STherOTS.fromJson('ep1', 'sTherO', json),
      sTherUISTS: STherUISTS.fromJson('ep1', 'sTherUIS', json),
      sMBasicSTS: SMBasicSTS.fromJson('ep1', 'sMBasicS', json),
      sMdoTS: SMdoTS.fromJson('ep1', 'sMDO', json),
      scheduleTS: SchedulesTS.fromJson('ep1', json, sMode: ScheduleMode.heatOnly,
          sTemplateMode: ScheduleTemplateMode.sTimeH),
      sPowerSTS: SPowerSTS.fromJson('ep1', 'sPowerS', json),
      sNetCommTS: SNetCommTS.fromJson('ep1', 'sNetComm', json),
    );
  }

  @override
  MetaDataIT850PropertiesProperties mapJson(Map<String, dynamic> json) {
    sTherSTS = sTherSTS?.mapJson(json);
    sTherOTS = sTherOTS?.mapJson(json);
    sTherUISTS = sTherUISTS?.mapJson(json);
    sMBasicSTS = sMBasicSTS?.mapJson(json);
    sMdoTS = sMdoTS?.mapJson(json);
    scheduleTS = scheduleTS?.mapJson(json);
    sPowerSTS = sPowerSTS?.mapJson(json);
    sNetCommTS = sNetCommTS?.mapJson(json);
    return this;
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    Map<String, dynamic>? sTherSJson = sTherSTS?.toJson();
    if (sTherSJson != null) {
      allJson.addAll(sTherSJson);
    }

    Map<String, dynamic>? sTherOJson = sTherOTS?.toJson();
    if (sTherOJson != null) {
      allJson.addAll(sTherOJson);
    }

    Map<String, dynamic>? sTherUISJson = sTherUISTS?.toJson();
    if (sTherUISJson != null) {
      allJson.addAll(sTherUISJson);
    }

    Map<String, dynamic>? sMBasicSJson = sMBasicSTS?.toJson();
    if (sMBasicSJson != null) {
      allJson.addAll(sMBasicSJson);
    }

    Map<String, dynamic>? sMdoJson = sMdoTS?.toJson();
    if (sMdoJson != null) {
      allJson.addAll(sMdoJson);
    }

    Map<String, dynamic>? schedulesJson = scheduleTS?.toJson();
    if (schedulesJson != null) {
      allJson.addAll(schedulesJson);
    }

    Map<String, dynamic>? sPowerSJson = sPowerSTS?.toJson();
    if (sPowerSJson != null) {
      allJson.addAll(sPowerSJson);
    }

    Map<String, dynamic>? sNetCommJson = sNetCommTS?.toJson();
    if (sNetCommJson != null) {
      allJson.addAll(sNetCommJson);
    }

    return allJson;
  }

}
