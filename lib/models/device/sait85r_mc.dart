import 'package:habi_app/base/base_device.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/models/aws_iot/parse_properties.dart';
import 'package:habi_app/models/aws_iot/shadow.dart';
import 'package:habi_app/models/aws_iot_template/s_boiler.dart';
import 'package:habi_app/models/aws_iot_template/s_boiler_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_mctlr.dart';
import 'package:habi_app/models/aws_iot_template/s_mctlr_ts.dart';
import 'package:habi_app/models/aws_iot_template/schedules.dart';
import 'package:habi_app/models/aws_iot_template/schedules_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_ota.dart';
import 'package:habi_app/models/aws_iot_template/s_ota_ts.dart';

class Sait85rMC extends BaseDevice {
  static const String subId = '11';
  Shadow<StateSait85rMCProperties, MetaDataSait85rMCProperties> shadow;

  Sait85rMC({
    required this.shadow,
    required super.thingName,
  }) : super(baseKey: subId);

  factory Sait85rMC.fromJson(Map<String, dynamic> json, String thingName) {
    return Sait85rMC(
      shadow: Shadow<StateSait85rMCProperties, MetaDataSait85rMCProperties>.fromJson(
        json,
        subId,
        stateDesiredProperties: StateSait85rMCProperties(),
        stateReportedProperties: StateSait85rMCProperties(),
        stateDeltaProperties: StateSait85rMCProperties(),
        meteDataDesiredProperties: MetaDataSait85rMCProperties(),
        meteDataReportedProperties: MetaDataSait85rMCProperties(),
      ),
      thingName: thingName,
    );
  }

  Sait85rMC mapJson(Map<String, dynamic> json) {
    shadow = shadow.mapJson(json);
    return this;
  }

  Map<String, dynamic> toJson() {
    return shadow.toJson();
  }

  @override
  String getDeviceName() {
    return thingName;
  }

  @override
  Map<String, dynamic>? getLeaveNetworkPayload() {
    return null;
  }
}

class StateSait85rMCProperties extends ParseProperties<StateSait85rMCProperties> {

  SMcTlr? sMcTlr;
  SBoiler? sBoiler;
  SOta? sOta;
  Schedules? schedules;

  StateSait85rMCProperties({
    this.sMcTlr,
    this.sBoiler,
    this.sOta,
    this.schedules,
  });

  @override
  StateSait85rMCProperties fromJson(Map<String, dynamic> json) {
    return StateSait85rMCProperties(
      sMcTlr: SMcTlr.fromJson('ep0', 'sMCtlr', json),
      sBoiler: SBoiler.fromJson('ep0', 'sBoiler', json),
      sOta: SOta.fromJson('ep0', 'sOTA', json),
      schedules: Schedules.fromJson('ep0', json, sMode: ScheduleMode.onOrOff,
          sTemplateMode: ScheduleTemplateMode.sTimeGh),
    );
  }

  @override
  StateSait85rMCProperties mapJson(Map<String, dynamic> json) {
    sMcTlr = sMcTlr?.mapJson(json);
    sBoiler = sBoiler?.mapJson(json);
    sOta = sOta?.mapJson(json);
    schedules = schedules?.mapJson(json);
    return this;
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    Map<String, dynamic>? sMcTlrJson = sMcTlr?.toJson();
    if (sMcTlrJson != null) {
      allJson.addAll(sMcTlrJson);
    }

    Map<String, dynamic>? sBoilerJson = sBoiler?.toJson();
    if (sBoilerJson != null) {
      allJson.addAll(sBoilerJson);
    }

    Map<String, dynamic>? sOtaJson = sOta?.toJson();
    if (sOtaJson != null) {
      allJson.addAll(sOtaJson);
    }

    Map<String, dynamic>? schedulesJson = schedules?.toJson();
    if (schedulesJson != null) {
      allJson.addAll(schedulesJson);
    }

    return allJson;
  }

}

class MetaDataSait85rMCProperties extends ParseProperties<MetaDataSait85rMCProperties> {

  SMcTlrTS? sMcTlr;
  SBoilerTS? sBoiler;
  SOtaTS? sOta;
  SchedulesTS? schedules;

  MetaDataSait85rMCProperties({
    this.sMcTlr,
    this.sBoiler,
    this.sOta,
    this.schedules,
  });

  @override
  MetaDataSait85rMCProperties fromJson(Map<String, dynamic> json) {
    return MetaDataSait85rMCProperties(
      sMcTlr: SMcTlrTS.fromJson('ep0', 'sMCtlr', json),
      sBoiler: SBoilerTS.fromJson('ep0', 'sBoiler', json),
      sOta: SOtaTS.fromJson('ep0', 'sOTA', json),
      schedules: SchedulesTS.fromJson('ep0', json, sMode: ScheduleMode.onOrOff,
        sTemplateMode: ScheduleTemplateMode.sTimeGh),
    );
  }

  @override
  MetaDataSait85rMCProperties mapJson(Map<String, dynamic> json) {
    sMcTlr = sMcTlr?.mapJson(json);
    sBoiler = sBoiler?.mapJson(json);
    sOta = sOta?.mapJson(json);
    schedules = schedules?.mapJson(json);
    return this;
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    Map<String, dynamic>? sMcTlrJson = sMcTlr?.toJson();
    if (sMcTlrJson != null) {
      allJson.addAll(sMcTlrJson);
    }

    Map<String, dynamic>? sBoilerJson = sBoiler?.toJson();
    if (sBoilerJson != null) {
      allJson.addAll(sBoilerJson);
    }

    Map<String, dynamic>? sOtaJson = sOta?.toJson();
    if (sOtaJson != null) {
      allJson.addAll(sOtaJson);
    }

    Map<String, dynamic>? schedulesJson = schedules?.toJson();
    if (schedulesJson != null) {
      allJson.addAll(schedulesJson);
    }

    return allJson;
  }

}


