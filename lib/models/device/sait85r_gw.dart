import 'package:habi_app/base/base_device.dart';
import 'package:habi_app/models/aws_iot/parse_properties.dart';
import 'package:habi_app/models/aws_iot/shadow.dart';
import 'package:habi_app/models/aws_iot_template/s_aws_iot.dart';
import 'package:habi_app/models/aws_iot_template/s_gateway.dart';
import 'package:habi_app/models/aws_iot_template/s_ota.dart';
import 'package:habi_app/models/aws_iot_template/s_aws_iot_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_gateway_ts.dart';
import 'package:habi_app/models/aws_iot_template/s_ota_ts.dart';

class Sait85rGW extends BaseDevice {
  static const String subId = '000000000001';
  Shadow<StateSait85rGWProperties, MetaDataSait85rGWProperties> shadow;

  Sait85rGW({
    required this.shadow,
    required super.thingName,
  }) : super(baseKey: subId);

  factory Sait85rGW.fromJson(Map<String, dynamic> json, String thingName) {
    return Sait85rGW(
      shadow: Shadow<StateSait85rGWProperties, MetaDataSait85rGWProperties>.fromJson(
        json,
        subId,
        stateDesiredProperties: StateSait85rGWProperties(),
        stateReportedProperties: StateSait85rGWProperties(),
        stateDeltaProperties: StateSait85rGWProperties(),
        meteDataDesiredProperties: MetaDataSait85rGWProperties(),
        meteDataReportedProperties: MetaDataSait85rGWProperties(),
      ),
      thingName: thingName,
    );
  }

  Sait85rGW mapJson(Map<String, dynamic> json) {
    shadow = shadow.mapJson(json);
    return this;
  }

  Map<String, dynamic> toJson() {
    return shadow.toJson();
  }

  @override
  String getDeviceName() {
    return shadow.state?.reported?.model?.properties?.sGateway?.deviceName ?? thingName;
  }

  @override
  Map<String, dynamic>? getLeaveNetworkPayload() {
    return null;
  }
}

class StateSait85rGWProperties extends ParseProperties<StateSait85rGWProperties> {

  SGateway? sGateway;
  SAwsIot? sAwsIot;
  SOta? sOta;

  StateSait85rGWProperties({
    this.sGateway,
    this.sAwsIot,
    this.sOta,
  });

  @override
  StateSait85rGWProperties fromJson(Map<String, dynamic> json) {
    return StateSait85rGWProperties(
      sGateway: SGateway.fromJson('ep0', 'sGateway', json),
      sAwsIot: SAwsIot.fromJson('ep0', 'sAWSIoT', json),
      sOta: SOta.fromJson('ep0', 'sOTA', json),
    );
  }

  @override
  StateSait85rGWProperties mapJson(Map<String, dynamic> json) {
    sGateway = sGateway?.mapJson(json);
    sAwsIot = sAwsIot?.mapJson(json);
    sOta = sOta?.mapJson(json);
    return this;
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    Map<String, dynamic>? sGatewayJson = sGateway?.toJson();
    if (sGatewayJson != null) {
      allJson.addAll(sGatewayJson);
    }

    Map<String, dynamic>? sAwsIotJson = sAwsIot?.toJson();
    if (sAwsIotJson != null) {
      allJson.addAll(sAwsIotJson);
    }

    Map<String, dynamic>? sOtaJson = sOta?.toJson();
    if (sOtaJson != null) {
      allJson.addAll(sOtaJson);
    }

    return allJson;
  }

}

class MetaDataSait85rGWProperties extends ParseProperties<MetaDataSait85rGWProperties> {

  SGatewayTS? sGateway;
  SAwsIotTS? sAwsIot;
  SOtaTS? sOta;

  MetaDataSait85rGWProperties({
    this.sGateway,
    this.sAwsIot,
    this.sOta,
  });

  @override
  MetaDataSait85rGWProperties fromJson(Map<String, dynamic> json) {
    return MetaDataSait85rGWProperties(
      sGateway: SGatewayTS.fromJson('ep0', 'sGateway', json),
      sAwsIot: SAwsIotTS.fromJson('ep0', 'sAWSIoT', json),
      sOta: SOtaTS.fromJson('ep0', 'sOTA', json),
    );
  }

  @override
  MetaDataSait85rGWProperties mapJson(Map<String, dynamic> json) {
    sGateway = sGateway?.mapJson(json);
    sAwsIot = sAwsIot?.mapJson(json);
    sOta = sOta?.mapJson(json);
    return this;
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> allJson = {};

    Map<String, dynamic>? sGatewayJson = sGateway?.toJson();
    if (sGatewayJson != null) {
      allJson.addAll(sGatewayJson);
    }

    Map<String, dynamic>? sAwsIotJson = sAwsIot?.toJson();
    if (sAwsIotJson != null) {
      allJson.addAll(sAwsIotJson);
    }

    Map<String, dynamic>? sOtaJson = sOta?.toJson();
    if (sOtaJson != null) {
      allJson.addAll(sOtaJson);
    }

    return allJson;
  }

}


