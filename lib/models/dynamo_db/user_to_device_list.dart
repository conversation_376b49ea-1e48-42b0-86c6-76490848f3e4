import 'dart:convert';

import 'package:aws_dynamodb_api/dynamodb-2012-08-10.dart';
import 'package:flutter/foundation.dart';

class UserToDeviceList {
  String? userId;
  String? username;
  List<String>? ownNames;
  List<String>? shareNames;

  UserToDeviceList({
    this.userId,
    this.ownNames,
    this.shareNames,
  });

  factory UserToDeviceList.fromDatabase(Map<String, AttributeValue> map) {
    if (kDebugMode) {
      debugPrint('fromDatabase() -> map=$map');
    }
    final userid = map['userid']!.s!;

    final ownNames = (jsonDecode(map['Own']!.s!)['list'] as List)
        .map((e) => e.toString())
        .toList();
    final shareNames = (jsonDecode(map['Sharer']!.s!)['list'] as List)
        .map((e) => e.toString())
        .toList();

    return UserToDeviceList(
      userId: userid,
      ownNames: ownNames,
      shareNames: shareNames,
    );
  }

  @override
  String toString() {
    return 'UserToDeviceList{userId: $userId, ownNames: $ownNames, shareNames: $shareNames}';
  }

  List<String> getAllNames() {
    List<String> allNames = [];

    if (ownNames != null && ownNames!.isNotEmpty){
      allNames.addAll(ownNames!);
    }
    if (shareNames != null && shareNames!.isNotEmpty){
      allNames.addAll(shareNames!);
    }

    return allNames;
  }

}