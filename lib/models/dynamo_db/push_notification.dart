import 'dart:convert';

import 'package:aws_dynamodb_api/dynamodb-2012-08-10.dart';
import 'package:flutter/foundation.dart';

class PushNotification {
  String? deviceId;
  int? timestamp;
  String? userId;
  String? type;
  String? title;
  String? body;
  bool? isRead;

  PushNotification({
    this.deviceId,
    this.timestamp,
    this.userId,
    this.type,
    this.title,
    this.body,
    this.isRead,
  });

  factory PushNotification.fromDatabase(Map<String, AttributeValue> map) {
    if (kDebugMode) {
      debugPrint('fromDatabase() -> map=$map');
    }

    final deviceId = map['DeviceID']!.s!;
    final timestamp = int.parse(map['Timestamp']!.n!);
    final userId = map['UserID']!.s!;
    final type = map['Type']!.s!;
    final title = map['Title']!.s!;
    final body = map['Body']!.s!;
    final isRead = map['IsRead']!.boolValue!;

    return PushNotification(
      deviceId: deviceId,
      timestamp: timestamp,
      userId: userId,
      type: type,
      title: title,
      body: body,
      isRead: isRead,
    );
  }

  @override
  String toString() {
    return jsonEncode({
      'deviceId': deviceId,
      'timestamp': timestamp,
      'userId': userId,
      'type': type,
      'title': title,
      'body': body,
      'isRead': isRead,
    });
  }
}