import 'dart:convert';

import 'package:aws_dynamodb_api/dynamodb-2012-08-10.dart';
import 'package:flutter/foundation.dart';

class ControllerFabricProperty {
  String? deviceId;
  String? fabricId;
  String? ipk;
  String? privateKeyArn;
  String? rootCertArn;

  ControllerFabricProperty({
    this.deviceId,
    this.fabricId,
    this.ipk,
    this.privateKeyArn,
    this.rootCertArn,
  });

  factory ControllerFabricProperty.fromDatabase(Map<String, AttributeValue> map) {
    if (kDebugMode) {
      debugPrint('fromDatabase() -> map=$map');
    }

    final deviceId = map['DeviceID']!.s!;
    final fabricId = map['FabricID']!.s!;
    final ipk = map['IPK']!.s!;
    final privateKeyArn = map['PrivateKeyArn']!.s!;
    final rootCertArn = map['RootCertArn']!.s!;

    return ControllerFabricProperty(
      deviceId: deviceId,
      fabricId: fabricId,
      ipk: ipk,
      privateKeyArn: privateKeyArn,
      rootCertArn: rootCertArn,
    );
  }

  @override
  String toString() {
    return jsonEncode({
      'deviceId': deviceId,
      'fabricId': fabricId,
      'ipk': ipk,
      'privateKeyArn': privateKeyArn,
      'rootCertArn': rootCertArn,
    });
  }

}