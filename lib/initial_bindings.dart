import 'package:get/get.dart';
import 'package:habi_app/services/Iot_service.dart';
import 'package:habi_app/services/api_service.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/database_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/device_provision_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/home_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/push_notification_service.dart';
import 'package:habi_app/services/thing_shadow_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'models/app_config.dart';

class InitialBindings extends Bindings {
  final AppConfig config;

  InitialBindings(this.config);

  @override
  void dependencies() {
    final globalService = Get.put(GlobalService(config: config));
    final demoModeService = Get.put(DemoModeService());
    final authService = Get.put(AuthService(
      demoModeService: demoModeService,
    ));
    final apiService = Get.put(ApiService());
    Get.put(UserAttributesService(
      demoModeService: demoModeService,
    ));
    final devService = Get.put(DevService(
      apiService: apiService,
      globalService: globalService,
      demoModeService: demoModeService,
    ));
    final dynamoDBService = Get.put(DynamoDBService(
      demoModeService: demoModeService,
    ));
    Get.put(DeviceProvisionService(
      apiService: apiService,
      globalService: globalService,
    ));
    final thingShadowService = Get.put(ThingShadowService(
      demoModeService: demoModeService,
    ));
    final iotService = Get.put(IotService(
      demoModeService: demoModeService,
    ));
    final localStorageService = Get.put(LocalStorageService());
    Get.put(DatabaseService());
    Get.put(PushNotificationService());
    final deviceListService = Get.put(DeviceListService(
      iotService: iotService,
      dynamoDBService: dynamoDBService,
    ));
    Get.put(HomeService(
      devService: devService,
      deviceListService: deviceListService,
      localStorageService: localStorageService,
    ));
    Get.put(DeviceShadowService(
      authService: authService,
      globalService: globalService,
      demoModeService: demoModeService,
      deviceListService: deviceListService,
      thingShadowService: thingShadowService,
      localStorageService: localStorageService,
    ));
  }
}
