import 'dart:async';
import 'dart:convert';
import 'package:aws_iot_data_api/iot-data-2015-05-28.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/global_service.dart';

class ThingShadowService extends GetxService {
  static ThingShadowService get to => Get.find();

  final DemoModeService demoModeService;

  ThingShadowService({required this.demoModeService});

  Future<Map<String, dynamic>> getShadow(thingName) async {
    if (demoModeService.isDemo) {
      final shadowObject = await demoModeService.getDemoDeviceShadow(
        thingName: thingName,
      );
      return shadowObject;
    }

    final session = await AuthService.to.fetchCognitoAuthSession();
    final ioTDataPlane = IoTDataPlane(
      region: GlobalService.to.config.aws.region,
      endpointUrl: GlobalService.to.config.aws.httpsEndpointUrl,
      credentials: AwsClientCredentials(
        accessKey: session.credentialsResult.value.accessKeyId,
        secretKey: session.credentialsResult.value.secretAccessKey,
        sessionToken: session.credentialsResult.value.sessionToken,
      ),
    );
    final response = await ioTDataPlane.getThingShadow(thingName: thingName);
    final shadowObject =
        jsonDecode(utf8.decode(Uint8List.fromList(response.payload!)));
    return shadowObject;
  }

  // Future Configuration Optimization:
  // thingName, subId, region, endpointURL, QoS, property
  Future<void> publishProperty({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
    bool desired = true,
  }) async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final ioTDataPlane = IoTDataPlane(
      region: GlobalService.to.config.aws.region,
      endpointUrl: GlobalService.to.config.aws.httpsEndpointUrl,
      credentials: AwsClientCredentials(
        accessKey: session.credentialsResult.value.accessKeyId,
        secretKey: session.credentialsResult.value.secretAccessKey,
        sessionToken: session.credentialsResult.value.sessionToken,
      ),
    );
    await ioTDataPlane.publish(
      topic: getPublishTopic(thingName),
      payload: getPublishPayload(
        subId: subId,
        property: property,
        desired: desired,
      ),
      qos: 1,
    );
  }

  String getPublishTopic(String thingName) {
    var topic = '\$aws/things/$thingName/shadow/update';
    return topic;
  }

  Uint8List getPublishPayload({
    required String subId,
    required bool desired,
    required Map<String, dynamic> property,
  }) {
    final stateType = desired ? 'desired' : 'reported';
    final Map<String, dynamic> payload = {
      'state': {
        stateType: {
          subId: {
            'properties': property,
          },
        },
      },
    };
    log.t('PublishPayload: $payload');
    return Uint8List.fromList(utf8.encode(json.encode(payload)));
  }
}
