import 'dart:async';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_config.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_route.dart';
import 'package:habi_app/models/app_shadow.dart';

class GlobalService extends GetxService {
  static GlobalService get to => Get.find();

  final AppConfig config;

  GlobalService({
    required this.config,
  });

  late StreamController<AppEvent> _eventStreamController;
  late Stream<AppEvent> _eventStream;
  late StreamController<AppRoute> _routeStreamController;
  late Stream<AppRoute> _routeStream;
  late StreamController<AppShadow> _thingShadowStreamController;
  late Stream<AppShadow> _thingShadowStream;
  late StreamController<AppShadow> _mqttStreamController;
  late Stream<AppShadow> _mqttStream;
  late StreamController<AppShadow> _realTimeMqttStreamController;
  late Stream<AppShadow> _realTimeMqttStream;

  @override
  void onInit() {
    super.onInit();
    _eventStreamController = StreamController.broadcast();
    _eventStream = _eventStreamController.stream;

    _routeStreamController = StreamController.broadcast();
    _routeStream = _routeStreamController.stream;

    _thingShadowStreamController = StreamController.broadcast();
    _thingShadowStream = _thingShadowStreamController.stream;

    _mqttStreamController = StreamController.broadcast();
    _mqttStream = _mqttStreamController.stream;

    _realTimeMqttStreamController = StreamController.broadcast();
    _realTimeMqttStream = _realTimeMqttStreamController.stream;
  }

  @override
  void onClose() {
    log.i('GlobalService{} -> onClose()');
    _eventStreamController.close();
    _routeStreamController.close();
    _thingShadowStreamController.close();
    _mqttStreamController.close();
    _realTimeMqttStreamController.close();
    super.onClose();
  }

  StreamController<AppEvent> getEventStreamController() => _eventStreamController;
  Stream<AppEvent> getEventStream() => _eventStream;

  StreamController<AppRoute> getRouteStreamController() => _routeStreamController;
  Stream<AppRoute> getRouteStream() => _routeStream;

  StreamController<AppShadow> getThingShadowController() => _thingShadowStreamController;
  Stream<AppShadow> getThingShadowStream() => _thingShadowStream;

  StreamController<AppShadow> getMqttStreamController() => _mqttStreamController;
  Stream<AppShadow> getMqttStream() => _mqttStream;

  StreamController<AppShadow> getRealTimeMqttStreamController() => _realTimeMqttStreamController;
  Stream<AppShadow> getRealTimeMqttStream() => _realTimeMqttStream;
}