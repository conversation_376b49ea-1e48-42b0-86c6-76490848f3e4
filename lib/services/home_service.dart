import 'package:get/get.dart';
import 'package:habi_app/helpers/retry_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/models/app_room.dart';
import 'package:habi_app/models/dev/hb_home.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/local_storage_service.dart';

class HomeService extends GetxService {
  static HomeService get to => Get.find();

  final DevService devService;
  final DeviceListService deviceListService;
  final LocalStorageService localStorageService;

  List<AppHome> homes = [];

  HomeService({
    required this.devService,
    required this.deviceListService,
    required this.localStorageService,
  });

  Future<List<AppHome>> loadList({bool isLoadRooms = true}) async {
    List<AppHome> appHomes = await _loadList(isLoadRooms);
    homes = appHomes;
    return appHomes;
  }

  Future<List<AppHome>> _loadList(bool isLoadRooms) async {
    final hbHomes = await _fetchHomes();
    if (hbHomes.isEmpty) {
      return <AppHome>[];
    }

    final appHomes = await Future.wait(
      hbHomes.map((hbHome) async {
        final appHome = AppHome.fromHBHome(hbHome);

        if (isLoadRooms) {
          final hbRooms = await _fetchRooms(hbHome);
          appHome.rooms = hbRooms.map(AppRoom.fromHBRoom).toList();
        }

        return appHome;
      }),
    );

    return appHomes;
  }

  Future<List<HBHome>> _fetchHomes() async {
    final response = await devService.getUserHomes();

    if (response.success != true) {
      throw Exception('Failed to fetch homes: ${response.errorCode}');
    }

    final List<HBHome> homes = response.homes ?? [];
    final List<HBHome> validHomes = [];

    if (homes.isEmpty) {
      return validHomes;
    }

    final List<String> thingsGroupList = deviceListService.gatewayIds;

    for (final home in homes) {
      if (home.homeId != null) {
        home.deviceList = home.deviceList
            ?.where((device) => thingsGroupList.contains(device))
            .toSet()
            .toList();

        validHomes.add(home);
      }
    }

    return validHomes;
  }

  Future<List<HBRoom>> _fetchRooms(HBHome home) async {
    final response = await devService.getHomeRooms(home.homeId!);

    if (response.success != true) {
      throw Exception(
        'Failed to fetch rooms for home ${home.homeId}: ${response.errorCode}',
      );
    }

    final List<HBRoom> rooms = response.rooms ?? [];
    final List<HBRoom> validRooms = [];

    if (rooms.isEmpty) {
      return validRooms;
    }

    final List<String> allThings = [];
    for (final device in home.deviceList ?? <String>[]) {
      final thingsList = deviceListService.getDevicesByGatewayId(device);
      allThings.addAll(thingsList ?? []);
    }

    for (final room in rooms) {
      room.deviceList = room.deviceList
          ?.where((device) => allThings.contains(device))
          .toSet()
          .toList();

      validRooms.add(room);
    }

    return validRooms;
  }

  Future<void> removeSingleDeviceRoom({
    required String roomId,
    required String thingName,
  }) async {
    try {
      log.i('removeSingleDeviceRoom() -> start');
      final room = await RetryHelper.execute(
        operation: () async {
          final result = await devService.getRoomByID(roomId);
          if (!(result.success ?? false)) {
            throw Exception('Failed to get room by ID: ${result.errorCode}');
          }
          return result.room;
        },
        operationName: 'getRoomByID',
      );

      if (room == null) {
        log.i('removeSingleDeviceRoom() -> room is null');
        return;
      }

      log.i('removeSingleDeviceRoom() -> room=${room.toJson()}');

      if (!(room.deviceList ?? []).any((item) => item != thingName)) {
        log.i('removeSingleDeviceRoom() -> delete room: $roomId');
        await devService.deleteRoom(roomId);
      }
    } catch (e) {
      log.e('removeSingleDeviceRoom() -> failed: $e');
    }
  }
}
