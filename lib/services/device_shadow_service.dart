import 'dart:async';
import 'package:get/get.dart';
import 'package:habi_app/helpers/app_map_helper.dart';
import 'package:habi_app/helpers/retry_helper.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/mqtt/mqtt_connection.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/thing_shadow_service.dart';

/// 设备 Shadow 服务，用于管理和同步设备状态
///
/// 该服务负责：
///   - 维护设备 `Shadow` 的本地缓存
///   - 通过 MQTT 或 HTTP 与 AWS IoT Core 同步设备 `Shadow`
///   - 处理设备列表的更新，并管理 MQTT 连接
class DeviceShadowService extends GetxService {
  static DeviceShadowService get to => Get.find();

  final AuthService authService;
  final GlobalService globalService;
  final DemoModeService demoModeService;
  final DeviceListService deviceListService;
  final ThingShadowService thingShadowService;
  final LocalStorageService localStorageService;

  DeviceShadowService({
    required this.authService,
    required this.globalService,
    required this.demoModeService,
    required this.deviceListService,
    required this.thingShadowService,
    required this.localStorageService,
  });

  static const int _maxDevicesPerConnection = 30;

  static const String _logTag = '[DeviceShadowService]';

  /// 设备 `Shadow` 缓存，key 为 deviceId，value 为 `AppShadow` 的 `Rx` 对象
  final _deviceShadows = <String, Rx<AppShadow>>{};

  // 任意设备 `Shadow` 发生变化时，触发通知
  final deviceShadowChangedEvent = 0.obs;

  /// 网关 MQTT 连接，key 为 gatewayId，value 为 `MqttConnection` 列表
  final _gatewayConnections = <String, List<MqttConnection>>{};

  /// 监听设备列表的 `Worker`
  Worker? _watchDeviceListWorker;

  /// 监听 Demo 设备 Shadow 更新的 `Worker`
  Worker? _demoDeviceShadowUpdateEventWorker;

  /// 通知设备 `Shadow` 发生了变化
  void _notifyDeviceShadowChanged() {
    deviceShadowChangedEvent.refresh();
  }

  /// 获取指定设备的 `Shadow`
  ///
  /// 如果本地缓存不存在，则返回一个空的 `AppShadow`
  AppShadow getDeviceShadow(String deviceId) {
    Rx<AppShadow>? shadow = _deviceShadows[deviceId];
    if (shadow == null) {
      shadow = AppShadow.empty(deviceId).obs;
      _deviceShadows[deviceId] = shadow;
      _notifyDeviceShadowChanged();
    }
    return shadow.value.copy();
  }

  Worker watchDeviceShadow(String deviceId, void Function(AppShadow) callback) {
    Rx<AppShadow>? shadow = _deviceShadows[deviceId];
    if (shadow == null) {
      shadow = AppShadow.empty(deviceId).obs;
      _deviceShadows[deviceId] = shadow;
      _notifyDeviceShadowChanged();
    }
    return ever<AppShadow>(
      shadow,
      (_) => callback(getDeviceShadow(deviceId)),
    );
  }

  /// 设置指定设备的 `Shadow`
  void _setDeviceShadow(String deviceId, AppShadow newShadow) {
    Rx<AppShadow>? shadow = _deviceShadows[deviceId];
    if (shadow != null) {
      shadow.value = newShadow;
    } else {
      _deviceShadows[deviceId] = newShadow.obs;
    }
    _notifyDeviceShadowChanged();
  }

  /// 开始根据设备列表更新设备 Shadow
  void startSync() {
    log.i('$_logTag 开始根据设备列表更新设备 Shadow');
    _startWatchDeviceList();
    _startWatchDemoDeviceShadowUpdate();
  }

  /// 停止根据设备列表更新设备 Shadow
  void stopSync() {
    log.i('$_logTag 停止根据设备列表更新设备 Shadow');
    _stopWatchDeviceList();
    _stopWatchDemoDeviceShadowUpdate();
    _cleanupDeviceList();
  }

  /// 开始监听设备列表
  void _startWatchDeviceList() {
    _watchDeviceListWorker?.dispose();
    _watchDeviceListWorker = ever(
      deviceListService.deviceListChangedEvent,
      (_) => _handleDeviceListUpdated(),
    );
    _handleDeviceListUpdated();
  }

  /// 停止监听设备列表
  void _stopWatchDeviceList() {
    _watchDeviceListWorker?.dispose();
    _watchDeviceListWorker = null;
  }

  void _startWatchDemoDeviceShadowUpdate() {
    _demoDeviceShadowUpdateEventWorker?.dispose();
    _demoDeviceShadowUpdateEventWorker = ever<DemoDeviceShadowUpdateEvent?>(
      demoModeService.lastDemoDeviceShadowUpdateEvent,
      (DemoDeviceShadowUpdateEvent? event) {
        if (event != null) {
          log.i(
              '$_logTag 处理设备 Shadow 更新 - Demo 模式，thingName: ${event.thingName}, shadow: ${event.shadow}');
          _handleShadowUpdated(event.thingName, event.shadow);
          final appShadow = AppShadow(
            thingName: event.thingName,
            thingShadow: event.shadow,
          );
          globalService.getRealTimeMqttStreamController().add(appShadow);
        }
      },
    );
  }

  void _stopWatchDemoDeviceShadowUpdate() {
    _demoDeviceShadowUpdateEventWorker?.dispose();
    _demoDeviceShadowUpdateEventWorker = null;
  }

  /// 处理设备列表更新
  Future<void> _handleDeviceListUpdated() async {
    try {
      final gatewayDevices = deviceListService.gatewayDevices;
      log.i('$_logTag 处理设备列表更新 - 网关列表: ${gatewayDevices.keys}');

      final currentGatewayId = await localStorageService.getThingGroup();
      log.i('$_logTag 处理设备列表更新 - 当前网关ID: $currentGatewayId');

      if (currentGatewayId == null ||
          !gatewayDevices.containsKey(currentGatewayId)) {
        _cleanupDeviceList();
        return;
      }

      if (demoModeService.isDemo) {
        await _handleDemoDeviceListUpdated(currentGatewayId, gatewayDevices);
        return;
      }

      final currentGateways = {currentGatewayId};
      final existingGateways = _gatewayConnections.keys.toSet();
      log.i('$_logTag 处理设备列表更新 - 已存在的网关列表: $existingGateways');

      for (final gatewayId in existingGateways) {
        if (!currentGateways.contains(gatewayId)) {
          _handleGatewayRemoved(gatewayId);
        }
      }

      final devices = gatewayDevices[currentGatewayId] ?? [];
      if (!existingGateways.contains(currentGatewayId)) {
        await _handleGatewayAdded(currentGatewayId, devices);
      } else {
        await _handleDevicesUpdated(currentGatewayId, devices);
      }

      // await Future.wait(
      //   gatewayDevices.entries.map((entry) async {
      //     final gatewayId = entry.key;
      //     final devices = entry.value;
      //
      //     if (!existingGateways.contains(gatewayId)) {
      //       await _handleGatewayAdded(gatewayId, devices);
      //     } else {
      //       await _handleDevicesUpdated(gatewayId, devices);
      //     }
      //   }),
      // );
    } catch (e, stackTrace) {
      log.e('$_logTag 处理设备列表更新失败', error: e, stackTrace: stackTrace);
    }
  }

  Future<void> _handleDemoDeviceListUpdated(
    String currentGatewayId,
    Map<String, List<String>> gatewayDevices,
  ) async {
    final gatewayDeviceIds = gatewayDevices[currentGatewayId] ?? [];
    final allDeviceIds = {currentGatewayId, ...gatewayDeviceIds};

    _deviceShadows.removeWhere((key, value) => !allDeviceIds.contains(key));
    await Future.wait(allDeviceIds.map(fetchDeviceShadow));
  }

  /// 处理网关设备更新
  Future<void> _handleDevicesUpdated(
    String gatewayId,
    List<String> deviceIds,
  ) async {
    try {
      log.i(
        '$_logTag 处理网关的设备更新 - gatewayId: $gatewayId, 设备数量: ${deviceIds.length}',
      );

      final subscribedDeviceIds = _getSubscribedDeviceIds(gatewayId);
      final allDeviceIds = {gatewayId, ...deviceIds};

      for (final deviceId in allDeviceIds) {
        if (!subscribedDeviceIds.contains(deviceId)) {
          await _handleDeviceAdded(gatewayId, deviceId);
        }
      }

      for (final deviceId in subscribedDeviceIds) {
        if (!allDeviceIds.contains(deviceId)) {
          await _handleDeviceRemoved(gatewayId, deviceId);
        }
      }
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 处理网关的设备更新失败 - gatewayId: $gatewayId',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 处理网关新增
  Future<void> _handleGatewayAdded(
    String gatewayId,
    List<String> devices,
  ) async {
    try {
      log.i(
        '$_logTag 处理网关新增，建立 MQTT 连接 - gatewayId: $gatewayId, 设备数量: ${devices.length}',
      );
      await _addGatewayConnections(gatewayId, {gatewayId, ...devices});
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 处理网关新增，建立 MQTT 连接失败 - gatewayId: $gatewayId',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 添加网关连接
  Future<void> _addGatewayConnections(
    String gatewayId,
    Set<String> allDeviceIds,
  ) async {
    log.i(
      '$_logTag 根据设备数量建立多个 MQTT 连接 - gatewayId: $gatewayId, 设备数量: ${allDeviceIds.length}',
    );

    final mqttConnections = <MqttConnection>[];
    Set<String> deviceIds = {};

    for (final deviceId in allDeviceIds) {
      deviceIds.add(deviceId);

      if (deviceIds.length == _maxDevicesPerConnection) {
        mqttConnections.add(_createMqttConnection(gatewayId, deviceIds));
        deviceIds = {};
      }
    }

    if (deviceIds.isNotEmpty) {
      mqttConnections.add(_createMqttConnection(gatewayId, deviceIds));
    }

    if (_gatewayConnections.containsKey(gatewayId)) {
      _gatewayConnections[gatewayId]!.addAll(mqttConnections);
    } else {
      _gatewayConnections[gatewayId] = mqttConnections;
    }

    await Future.wait(mqttConnections.map((connection) => connection.create()));
  }

  /// 处理网关移除
  void _handleGatewayRemoved(String gatewayId) async {
    log.i('$_logTag 处理网关移除，移除网关的 MQTT 连接 - gatewayId: $gatewayId');

    final connections = _gatewayConnections[gatewayId];

    if (connections == null) {
      log.w('$_logTag 网关没有 MQTT 连接 - gatewayId: $gatewayId');
      return;
    }

    for (final connection in connections) {
      connection.cleanup();
    }

    _gatewayConnections.remove(gatewayId);
  }

  /// 处理设备新增
  Future<void> _handleDeviceAdded(String gatewayId, String deviceId) async {
    try {
      log.i(
        '$_logTag 处理设备新增，添加设备到网关的 MQTT 连接 - gatewayId: $gatewayId, deviceId: $deviceId',
      );

      final connection = _findBestConnection(gatewayId);

      if (connection == null) {
        log.i(
          '$_logTag 未找到合适的 MQTT 连接，创建新的 MQTT 连接 - gatewayId: $gatewayId, deviceId: $deviceId',
        );
        await _addGatewayConnections(gatewayId, {deviceId});
      } else {
        connection.addDevice(deviceId);
      }
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 处理设备新增，添加设备到网关的 MQTT 连接失败 - gatewayId: $gatewayId, deviceId: $deviceId',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 处理设备移除
  Future<void> _handleDeviceRemoved(String gatewayId, String deviceId) async {
    log.i('$_logTag 处理设备删除，从网关的 MQTT 连接中移除设备 - deviceId: $deviceId');

    final connection = _findDeviceConnection(deviceId, gatewayId: gatewayId);

    if (connection != null) {
      connection.removeDevice(deviceId);

      if (connection.deviceCount == 0) {
        connection.cleanup();
        _gatewayConnections[gatewayId]?.remove(connection);
      }
    }

    _deviceShadows.remove(deviceId);
    _notifyDeviceShadowChanged();
  }

  /// 创建 MQTT 连接
  MqttConnection _createMqttConnection(String gatewayId, Set<String> devices) {
    return MqttConnection(
      gatewayId,
      devices,
      deviceShadowService: this,
      authService: authService,
      globalService: globalService,
      thingShadowService: thingShadowService,
    );
  }

  /// 查找设备的 MQTT 连接
  MqttConnection? _findDeviceConnection(String deviceId, {String? gatewayId}) {
    if (gatewayId != null) {
      return _gatewayConnections[gatewayId]?.firstWhereOrNull(
        (connection) => connection.devices.contains(deviceId),
      );
    }

    for (final connections in _gatewayConnections.values) {
      for (final connection in connections) {
        if (connection.devices.contains(deviceId)) {
          return connection;
        }
      }
    }

    return null;
  }

  /// 查找网关最佳的 MQTT 连接
  MqttConnection? _findBestConnection(String gatewayId) {
    log.d('$_logTag 查找网关最佳的 MQTT 连接 - gatewayId: $gatewayId');
    int minCount = _maxDevicesPerConnection;
    MqttConnection? selectedConnection;

    final connections = _gatewayConnections[gatewayId];

    if (connections == null) {
      log.d('$_logTag 未找到网关的 MQTT 连接 - gatewayId: $gatewayId');
      return null;
    }

    for (final connection in connections) {
      if (connection.deviceCount < minCount) {
        if (connection.isConnected) {
          minCount = connection.deviceCount;
          selectedConnection = connection;
        } else {
          selectedConnection ??= connection;
        }
      }
    }

    return selectedConnection;
  }

  /// 获取网关已订阅的设备列表
  Set<String> _getSubscribedDeviceIds(String gatewayId) {
    final subscribedDevices = <String>{};

    _gatewayConnections[gatewayId]?.forEach((connection) {
      subscribedDevices.addAll(connection.devices);
    });

    return subscribedDevices;
  }

  /// 清理设备列表
  Future<void> _cleanupDeviceList() async {
    log.i('$_logTag 清理设备列表');

    _gatewayConnections.forEach((gatewayId, connections) {
      for (final connection in connections) {
        connection.cleanup();
      }
    });
    _gatewayConnections.clear();

    _deviceShadows.clear();
    _notifyDeviceShadowChanged();
  }

  /// 获取设备 Shadow
  Future<Map<String, dynamic>> fetchDeviceShadow(
    String deviceId, {
    int maxRetries = 0,
  }) async {
    log.i('$_logTag 获取设备 Shadow - deviceId: $deviceId');
    final shadow = await RetryHelper.execute<Map<String, dynamic>>(
      operation: () => thingShadowService.getShadow(deviceId),
      operationName: "$_logTag 获取设备 Shadow - deviceId: $deviceId ",
      maxRetries: maxRetries,
    );
    _handleShadowUpdated(deviceId, shadow, fromHttp: true);
    return shadow;
  }

  void handleMqttShadowUpdated(String deviceId, Map<String, dynamic> updates) {
    _handleShadowUpdated(deviceId, updates);
  }

  /// 处理 Shadow 更新
  void _handleShadowUpdated(
    String deviceId,
    Map<String, dynamic> updates, {
    bool fromHttp = false,
  }) {
    try {
      final appShadow = getDeviceShadow(deviceId);
      final oldShadow = appShadow.thingShadow;

      final newTimestamp = updates['timestamp'] as int?;
      final oldTimestamp = oldShadow['timestamp'] as int?;

      if (newTimestamp != null &&
          oldTimestamp != null &&
          newTimestamp < oldTimestamp) {
        log.d(
          '$_logTag 忽略旧 Shadow 数据 - deviceId: $deviceId, fromHttp: $fromHttp, newTimestamp: $newTimestamp, oldTimestamp: $oldTimestamp',
        );
        _findDeviceConnection(deviceId)?.reFetchDeviceShadow(deviceId);
        return;
      }

      final newShadow = AppShadow(
        thingName: deviceId,
        thingShadow:
            fromHttp ? updates : AppMapHelper.deepMerge(oldShadow, updates),
      );

      _setDeviceShadow(deviceId, newShadow);

      globalService.getMqttStreamController().add(newShadow);

      log.d(
        '$_logTag 处理 Shadow 更新完成 - deviceId: $deviceId, fromHttp: $fromHttp',
      );
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 处理 Shadow 更新失败 - deviceId: $deviceId, fromHttp: $fromHttp',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 获取发布 payload
  Map<String, dynamic> _getPublishPayload({
    required String subId,
    required bool desired,
    required Map<String, dynamic> property,
  }) {
    final stateType = desired ? 'desired' : 'reported';
    return {
      'state': {
        stateType: {
          subId: {
            'properties': property,
          },
        },
      },
    };
  }

  /// 更新设备属性
  Future<void> updateDeviceProperties({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
    bool desired = true,
  }) async {
    log.i('$_logTag 更新设备属性 - deviceId: $thingName, properties: $property');

    if (demoModeService.isDemo) {
      await _updateDemoDeviceProperties(
        thingName: thingName,
        property: property,
        subId: subId,
        desired: desired,
      );
      return;
    }

    try {
      await _updatePropertiesViaMqtt(
        thingName: thingName,
        property: property,
        subId: subId,
        desired: desired,
      );
    } catch (e) {
      await _updatePropertiesViaHttp(
        thingName: thingName,
        property: property,
        subId: subId,
        desired: desired,
      );
    }
  }

  /// 更新 Demo 模式设备属性
  Future<void> _updateDemoDeviceProperties({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
    required bool desired,
  }) async {
    await demoModeService.updateDemoDeviceProperties(
      thingName: thingName,
      property: property,
      subId: subId,
      desired: desired,
    );
  }

  /// 通过 MQTT 更新设备属性
  Future<void> _updatePropertiesViaMqtt({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
    required bool desired,
  }) async {
    try {
      final connection = _findDeviceConnection(thingName);

      if (connection == null) {
        throw Exception('MQTT 连接不存在');
      }

      if (!connection.isConnected) {
        throw Exception('MQTT 连接未就绪');
      }

      final payload = _getPublishPayload(
        subId: subId,
        desired: desired,
        property: property,
      );

      await connection.updateProperties(thingName, payload);

      log.i(
        '$_logTag 通过 MQTT 更新设备属性成功 - deviceId: $thingName, payload: $payload',
      );
    } catch (e) {
      log.w('$_logTag 通过 MQTT 更新设备属性失败 - deviceId: $thingName', error: e);
      rethrow;
    }
  }

  /// 通过 HTTP 更新设备属性
  Future<void> _updatePropertiesViaHttp({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
    required bool desired,
  }) async {
    try {
      await thingShadowService.publishProperty(
        thingName: thingName,
        property: property,
        subId: subId,
        desired: desired,
      );

      log.i(
        '$_logTag 通过 HTTP 更新设备属性成功 - deviceId: $thingName, properties: $property',
      );
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 通过 HTTP 更新设备属性失败 - deviceId: $thingName',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
