import 'dart:convert';

import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/user_attribute_keys.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/user_attributes.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:intl/intl.dart';

/// Service for managing user attributes in AWS Cognito
class UserAttributesService extends GetxService {
  static UserAttributesService get to => Get.find();

  final DemoModeService demoModeService;

  UserAttributesService({required this.demoModeService});

  /// Current user attributes
  final attributes = Rx<UserAttributes?>(null);

  bool get isDataCollectionOn =>
      attributes.value?.dataCollection == DataCollectionStatus.on.value;

  String? get email => attributes.value?.email;

  String? get name => attributes.value?.name;

  String? get familyName => attributes.value?.familyName;

  String? get country => attributes.value?.country;

  HourFormatStatus? get hourFormat => attributes.value?.hourFormat;

  TemperatureUnitStatus? get temperatureUnit =>
      attributes.value?.temperatureUnit;

  /// Fetches and updates the current user attributes
  Future<void> fetchAttributes() async {
    if (demoModeService.isDemo) {
      final demoUserAttributes = await demoModeService.getDemoUserAttributes();
      attributes.value = UserAttributes.fromJson(demoUserAttributes);
      return;
    }

    try {
      final List<AuthUserAttribute> authAttributes =
          await AuthService.to.fetchUserAttributes();

      final attributesMap = Map<String, String>.fromEntries(
        authAttributes.map(
          (attr) => MapEntry(attr.userAttributeKey.key, attr.value),
        ),
      );

      attributes.value = UserAttributes.fromJson(attributesMap);

      log.i('User attributes: ${attributes.value}');
    } catch (e, stackTrace) {
      log.e('Error fetching user attributes', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Updates user attributes
  Future<void> updateAttributes(
    Map<AuthUserAttributeKey, String> updates,
  ) async {
    if (updates.isEmpty) return;

    try {
      if (demoModeService.isDemo) {
        final updatesMap = updates.map(
          (key, value) => MapEntry(key.key, value),
        );
        await demoModeService.updateDemoUserAttributes(updatesMap);
        await fetchAttributes();
        return;
      }

      final attributes = updates.entries
          .map(
            (entry) => AuthUserAttribute(
              userAttributeKey: entry.key,
              value: entry.value,
            ),
          )
          .toList();

      final result = await Amplify.Auth.updateUserAttributes(
        attributes: attributes,
      );

      result.forEach((key, value) {
        switch (value.nextStep.updateAttributeStep) {
          case AuthUpdateAttributeStep.confirmAttributeWithCode:
            final destination = value.nextStep.codeDeliveryDetails?.destination;
            log.w('Confirmation code sent to $destination for $key');
          case AuthUpdateAttributeStep.done:
            log.i('Update completed for $key');
        }
      });

      await fetchAttributes();
    } catch (e, stackTrace) {
      final keys = updates.keys.map((key) => key.key).join(', ');
      log.e(
        'Error updating user attributes: $keys',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // Standard attribute update methods
  Future<void> updateEmail(String value) async {
    return updateAttributes({UserAttributeKeys.email: value});
  }

  Future<void> updateFamilyName(String value) async {
    return updateAttributes({UserAttributeKeys.familyName: value});
  }

  Future<void> updateLocale(String value) async {
    return updateAttributes({UserAttributeKeys.locale: value});
  }

  Future<void> updateName(String value) async {
    return updateAttributes({UserAttributeKeys.name: value});
  }

  // Custom attribute update methods
  Future<void> updateCountry(String value) async {
    return updateAttributes({UserAttributeKeys.country: value});
  }

  Future<void> updateLanguage(String value) async {
    return updateAttributes({UserAttributeKeys.language: value});
  }

  Future<void> updateDataCollection(String value) async {
    return updateAttributes({
      UserAttributeKeys.dataCollection: value,
      UserAttributeKeys.dataCollectionDateTime: _getCurrentUtcTime(),
    });
  }

  Future<void> updatePrivacyVersion(String value) async {
    return updateAttributes({UserAttributeKeys.privacyVersion: value});
  }

  Future<void> updateTermsVersion(String value) async {
    return updateAttributes({UserAttributeKeys.termsVersion: value});
  }

  Future<void> updateHourFormat(String value) async {
    return updateAttributes({UserAttributeKeys.hourFormat: value});
  }

  Future<void> updateTemperatureUnit(String value) async {
    return updateAttributes({UserAttributeKeys.temperatureUnit: value});
  }

  String _getCurrentUtcTime() {
    final formatter = DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
    return formatter.format(DateTime.now().toUtc());
  }
}
