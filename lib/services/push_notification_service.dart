import 'dart:async';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/notifications/notification_settings.dart';
import 'package:habi_app/services/local_notifications_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/auth_service.dart';

class PushNotificationService extends GetxService {
  static PushNotificationService get to => Get.find();

  late StreamSubscription<String> tokenReceived;
  late StreamSubscription<PushNotificationMessage> notificationReceivedInForeground;
  late StreamSubscription<PushNotificationMessage> notificationOpened;

  bool isInitPushNotificationListeners = false;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    isInitPushNotificationListeners = false;
    tokenReceived.cancel();
    notificationReceivedInForeground.cancel();
    notificationOpened.cancel();
  }

  void initializePushNotificationListeners() {
    if (isInitPushNotificationListeners) {
      log.d('Push notification listeners already initialized');
      return;
    }

    isInitPushNotificationListeners = true;

    try {
      tokenReceived = Amplify.Notifications.Push.onTokenReceived
          .listen(_tokenReceivedHandler);
      notificationReceivedInForeground = Amplify
          .Notifications.Push.onNotificationReceivedInForeground
          .listen(_notificationReceivedInForegroundHandler);
      notificationOpened = Amplify.Notifications.Push.onNotificationOpened
          .listen(_notificationOpenedHandler);
      log.i('Push notification listeners initialized');
    } catch (e, s) {
      log.e('Failed to initialize push notification listeners', error: e, stackTrace: s);
    }
  }

  Future<bool> get areAppNotificationEnabled async {
    final status = await Amplify.Notifications.Push.getPermissionStatus();
    return status == PushNotificationPermissionStatus.granted;
  }

  Future<NotificationSettings?> get notificationSettings async {
    final userId = await LocalStorageService.to.getUserId();
    var userIdToSettingsMap =
        await LocalStorageService.to.getNotificationSettings();
    if (userIdToSettingsMap != null &&
        userIdToSettingsMap.containsKey(userId)) {
      return NotificationSettings.fromJson(userIdToSettingsMap[userId]);
    }
    return null;
  }

  Future<void> setNotificationSettings(NotificationSettings settings) async {
    final userId = await LocalStorageService.to.getUserId();
    if (userId == null) {
      log.d('Cannot save notification state. User is not signed in.');
      return;
    }

    var userIdToSettingsMap =
        await LocalStorageService.to.getNotificationSettings() ?? {};
    userIdToSettingsMap[userId] = settings.toJson();
    await LocalStorageService.to.setNotificationSettings(userIdToSettingsMap);
  }

  Future<bool> enableAppNotifications() async {
    try {
      final permissionStatus = await Amplify.Notifications.Push.getPermissionStatus();
      switch (permissionStatus) {
        case PushNotificationPermissionStatus.granted:
          log.d('User has granted permissions for push notifications');
          return true;
        case PushNotificationPermissionStatus.denied:
          log.d('User has denied permissions for push notifications. Opening settings...');
          // await AppSettings.openAppSettings(type: AppSettingsType.notification);
          return false;
        case PushNotificationPermissionStatus.shouldRequest:
        case PushNotificationPermissionStatus.shouldExplainThenRequest:
          log.d('User should request permissions');
          return await Amplify.Notifications.Push.requestPermissions();
      }
    } catch (e, s) {
      log.e('Failed to enable app notifications', error: e, stackTrace: s);
      return false;
    }
  }

  Future<void> updatePinpointNotifications() async {
    try {
      final userId = await AuthService.to.currentUserId;
      log.i("===== Starting identifyUser($userId) =====");
      await Amplify.Notifications.Push.identifyUser(userId: userId);
      log.i("===== Finished identifyUser($userId) =====");
    } catch (e, s) {
      log.e('Failed to enable app notifications: $e, $s');
    }
  }

  Future<void> disablePinpointNotifications() async {
    try {
      log.i('Disabling pinpoint notifications...');
      await Amplify.Notifications.Push.identifyUser(userId: '');
      log.i('Pinpoint notifications disabled');
    } catch (e) {
      log.e('Failed to disable pinpoint notifications: $e');
    }
  }

  void _notificationReceivedInForegroundHandler(
    PushNotificationMessage notification,
  ) {
    log.i(
      'Notification received in foreground: ${notification.toString()}',
    );
    if (notification.title != null && notification.body != null) {
      LocalNotificationsService().showNotification(
        title: notification.title!,
        body: notification.body!,
      );
    }
  }

  void _notificationOpenedHandler(PushNotificationMessage notification) {
    log.i('Notification opened: ${notification.toString()}');
    if (notification.title != null && notification.body != null) {
      LocalNotificationsService().showNotification(
        title: notification.title!,
        body: notification.body!,
      );
    }
  }

  void _tokenReceivedHandler(String token) {
    log.i("Token received: $token");
  }

}
