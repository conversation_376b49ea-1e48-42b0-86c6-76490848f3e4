import 'dart:convert';
import 'dart:async';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:habi_app/constants/demo_mode.dart';
import 'package:habi_app/constants/storage_keys.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/demo/demo_homes.dart';
import 'package:habi_app/models/dynamo_db/user_to_device_list.dart';
import 'package:uuid/uuid.dart';

class DemoModeService extends GetxService {
  static DemoModeService get to => Get.find();

  // Storage keys
  static const String _userAttributes = 'user_attributes';
  static const String _userHomes = 'user_homes';
  static const String _thingShadows = 'thing_shadows';

  // File paths
  static const String _userHomesPath = 'user_homes';
  static const String _thingGroupsPath = 'thing_groups';
  static const String _thingShadowsPath = 'thing_shadows';
  static const String _userDeviceListPath = 'user_device_list';
  static const String _userAttributesPath = 'user_attributes';
  static const String _userSignInDetailsPath = 'user_sign_in_details';

  late final GetStorage _demoDataStorage;

  bool isDemo = false;
  bool _isInitialized = false;
  final _isSyncing = <String, bool>{};
  final _pendingSyncProps = <String, Set<String>>{};
  final lastDemoDeviceShadowUpdateEvent = Rxn<DemoDeviceShadowUpdateEvent>();

  Future<void> init() async {
    if (_isInitialized) {
      return;
    }
    await GetStorage.init(StorageKeys.demoData);
    _demoDataStorage = GetStorage(StorageKeys.demoData);
    _isInitialized = true;
  }

  Future<Map<String, dynamic>> _loadLocalJson(String fileName) async {
    try {
      final data = await rootBundle.loadString('assets/demo/$fileName.json');
      return json.decode(data) as Map<String, dynamic>;
    } catch (e) {
      log.e(
        'Demo: Error reading demo data from local - fileName: $fileName',
        error: e,
      );
      rethrow;
    }
  }

  Map<String, dynamic>? _readDemoData(String key) {
    try {
      final jsonString = _demoDataStorage.read<String>(key);
      return jsonString != null ? json.decode(jsonString) : null;
    } catch (e) {
      log.e(
        'Demo: Error reading demo data from storage - key: $key',
        error: e,
      );
      return null;
    }
  }

  Future<void> _writeDemoData(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = json.encode(data);
      await _demoDataStorage.write(key, jsonString);
    } catch (e) {
      log.e(
        'Demo: Error writing demo data to storage - key: $key',
        error: e,
      );
      rethrow;
    }
  }

  Future<AuthUser> getDemoAuthUser() async {
    final data = await _loadLocalJson(_userSignInDetailsPath);
    return CognitoAuthUser.fromJson(data);
  }

  Future<void> _writeDemoUserAttributes(Map<String, String> attributes) async {
    await _writeDemoData(_userAttributes, attributes);
  }

  Future<Map<String, String>> _readDemoUserAttributes() async {
    final data = _readDemoData(_userAttributes);
    return data?.cast<String, String>() ?? _readDefaultDemoUserAttributes();
  }

  Future<Map<String, String>> _readDefaultDemoUserAttributes() async {
    final data = await _loadLocalJson(_userAttributesPath);
    final attributes = data.cast<String, String>();
    await _writeDemoUserAttributes(attributes);
    return attributes;
  }

  Future<Map<String, String>> getDemoUserAttributes() async {
    final attributes = await _readDemoUserAttributes();
    return attributes;
  }

  Future<void> updateDemoUserAttributes(Map<String, String> updates) async {
    final attributes = await getDemoUserAttributes();
    attributes.addAll(updates);
    await _writeDemoUserAttributes(attributes);
  }

  Future<UserToDeviceList> getDemoUserToDeviceList() async {
    final data = await _loadLocalJson(_userDeviceListPath);
    final ownNames = (data['Own'] as List).cast<String>();
    final shareNames = (data['Sharer'] as List).cast<String>();

    return UserToDeviceList(
      ownNames: ownNames,
      shareNames: shareNames,
    );
  }

  Future<List<String>> getDemoThingGroupList(String thingGroupName) async {
    final data = await _loadLocalJson('$_thingGroupsPath/$thingGroupName');
    return (data['things'] as List).cast<String>();
  }

  Future<void> _writeDemoUserHomes(DemoHomes homes) async {
    await _writeDemoData(_userHomes, homes.toJson());
  }

  Future<DemoHomes> _readDemoUserHomes() async {
    final homesJson = _readDemoData(_userHomes);
    return homesJson != null
        ? DemoHomes.fromJson(homesJson)
        : _readDefaultDemoUserHomes();
  }

  Future<DemoHomes> _readDefaultDemoUserHomes() async {
    final data = await _loadLocalJson(_userHomesPath);
    final homes = DemoHomes.fromJson(data);
    await _writeDemoUserHomes(homes);
    return homes;
  }

  Future<Map<String, dynamic>> addDemoHome(String homeName) async {
    final homes = await _readDemoUserHomes();
    final homeId = const Uuid().v4();
    final home = DemoHome(
      homeId: homeId,
      name: homeName,
      owner: [demoUserId],
      sharer: [],
      rooms: [],
    );
    homes.homes.add(home);
    await _writeDemoUserHomes(homes);
    final response = home.buildAddHomeResponse();
    log.i('addDemoHome() -> response=${json.encode(response)}');
    return response;
  }

  Future<void> deleteDemoHome(String homeId) async {
    final homes = await _readDemoUserHomes();
    homes.homes.removeWhere((home) => home.homeId == homeId);
    await _writeDemoUserHomes(homes);
  }

  Future<Map<String, dynamic>> getDemoUserHomes() async {
    final homes = await _readDemoUserHomes();
    final response = homes.buildGetHomesResponse();
    log.i('getDemoUserHomes() -> response=${json.encode(response)}');
    return response;
  }

  Future<Map<String, dynamic>> getDemoHomeRooms(String homeId) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    final response = home.buildGetHomeRoomsResponse();
    log.i('getDemoHomeRooms() -> response=${json.encode(response)}');
    return response;
  }

  Future<void> changeDemoHomeName(String homeId, String name) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    home.name = name;
    await _writeDemoUserHomes(homes);
  }

  Future<void> addDemoPropertyToHome(
    String homeId,
    String name,
    String value,
  ) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    home.attributes ??= <String, dynamic>{};
    home.attributes![name] = value;
    await _writeDemoUserHomes(homes);
  }

  Future<void> removeDemoPropertyFromHome(String homeId, String name) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    if (home.attributes != null) {
      home.attributes!.remove(name);
      await _writeDemoUserHomes(homes);
    }
  }

  Future<void> addDemoDeviceToHome(String homeId, String deviceId) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    home.deviceList ??= <String>[];
    home.deviceList!.add(deviceId);
    await _writeDemoUserHomes(homes);
  }

  Future<void> removeDemoDeviceFromHome(String homeId, String deviceId) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    if (home.deviceList != null) {
      home.deviceList!.remove(deviceId);
      await _writeDemoUserHomes(homes);
    }
  }

  Future<void> addDemoUserToHome(
    String homeId,
    List<String> userList,
    bool isOwner,
  ) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    if (isOwner) {
      home.owner.addAll(userList);
    } else {
      home.sharer.addAll(userList);
    }
    await _writeDemoUserHomes(homes);
  }

  Future<void> removeDemoUserFromHome(
    String homeId,
    List<String> userList,
    bool isOwner,
  ) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    if (isOwner) {
      home.owner.removeWhere((element) => userList.contains(element));
    } else {
      home.sharer.removeWhere((element) => userList.contains(element));
    }
    await _writeDemoUserHomes(homes);
  }

  Future<void> changeDemoRoomName(String roomId, String name) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    room.name = name;
    await _writeDemoUserHomes(homes);
  }

  Future<Map<String, dynamic>> addDemoRoom(
    String homeId,
    String roomName,
  ) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);

    final roomId = const Uuid().v4();
    final room = DemoRoom(
      homeId: homeId,
      roomId: roomId,
      name: roomName,
    );
    home.rooms.add(room);
    await _writeDemoUserHomes(homes);
    final response = room.buildAddRoomResponse();
    log.i('addDemoRoom() -> response=${json.encode(response)}');
    return response;
  }

  Future<void> deleteDemoRoom(String roomId) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    final home = homes.getHomeById(room.homeId);
    home.rooms.remove(room);
    await _writeDemoUserHomes(homes);
  }

  Future<void> addDemoPropertyToRoom(
    String roomId,
    String name,
    String value,
  ) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    room.attributes ??= <String, dynamic>{};
    room.attributes![name] = value;
    await _writeDemoUserHomes(homes);
  }

  Future<void> removeDemoPropertyFromRoom(String roomId, String name) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    if (room.attributes != null) {
      room.attributes!.remove(name);
      await _writeDemoUserHomes(homes);
    }
  }

  Future<void> addDemoDeviceToRoom(String roomId, String deviceId) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    if (room.deviceList == null) {
      room.deviceList = [deviceId];
    } else {
      room.deviceList!.add(deviceId);
    }
    await _writeDemoUserHomes(homes);
  }

  Future<void> removeDemoDeviceFromRoom(String roomId, String deviceId) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    if (room.deviceList != null) {
      room.deviceList!.remove(deviceId);
      await _writeDemoUserHomes(homes);
    }
  }

  Future<Map<String, dynamic>> getDemoHomeByID(String homeId) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeById(homeId);
    final response = home.buildGetHomeResponse();
    log.i('getDemoHomeByID() -> response=${json.encode(response)}');
    return response;
  }

  Future<Map<String, dynamic>> getDemoHomeByDeviceID(String deviceId) async {
    final homes = await _readDemoUserHomes();
    final home = homes.getHomeByDeviceId(deviceId);
    final response = home.buildGetHomeResponse();
    log.i('getDemoHomeByDeviceID() -> response=${json.encode(response)}');
    return response;
  }

  Future<Map<String, dynamic>> getDemoRoomByID(String roomId) async {
    final homes = await _readDemoUserHomes();
    final room = homes.getRoomById(roomId);
    final response = room.buildGetRoomResponse();
    log.i('getDemoRoomByID() -> response=${json.encode(response)}');
    return response;
  }

  Future<void> _writeDemoDeviceShadow({
    required String thingName,
    required Map<String, dynamic> shadowObject,
  }) async {
    final key = '$_thingShadows/$thingName';
    await _writeDemoData(key, shadowObject);
  }

  Future<Map<String, dynamic>> _readDemoDeviceShadow({
    required String thingName,
  }) async {
    final key = '$_thingShadows/$thingName';
    final shadowObject = _readDemoData(key);
    return shadowObject ?? _readDefaultDemoDeviceShadow(thingName: thingName);
  }

  Future<Map<String, dynamic>> _readDefaultDemoDeviceShadow({
    required String thingName,
  }) async {
    final path = '$_thingShadowsPath/$thingName';
    final shadowObject = await _loadLocalJson(path);
    await _writeDemoDeviceShadow(
      thingName: thingName,
      shadowObject: shadowObject,
    );
    return shadowObject;
  }

  Future<Map<String, dynamic>> getDemoDeviceShadow({
    required String thingName,
  }) async {
    return await _readDemoDeviceShadow(thingName: thingName);
  }

  Future<void> updateDemoDeviceProperties({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
    required bool desired,
  }) async {
    Future.delayed(const Duration(seconds: 1), () async {
      if (desired) {
        await _updateDemoDeviceShadowDesired(
          thingName: thingName,
          property: property,
          subId: subId,
        );
      } else {
        await _updateDemoDeviceShadowReported(
          thingName: thingName,
          property: property,
          subId: subId,
        );
      }
    });
  }

  Future<void> _updateDemoDeviceShadowDesired({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
  }) async {
    try {
      final shadowObject = await _readDemoDeviceShadow(thingName: thingName);
      final state = shadowObject['state'] as Map<String, dynamic>;
      final desired = state['desired'] as Map<String, dynamic>;
      final desiredSub = desired[subId] as Map<String, dynamic>;
      final desiredProps = desiredSub['properties'] as Map<String, dynamic>;

      desiredProps.addAll(property);

      await _writeDemoDeviceShadow(
        thingName: thingName,
        shadowObject: shadowObject,
      );

      lastDemoDeviceShadowUpdateEvent.value = DemoDeviceShadowUpdateEvent(
        thingName: thingName,
        shadow: _buildShadowUpdatePayload(
          subId: subId,
          property: property,
          desired: true,
        ),
      );

      Future.delayed(const Duration(seconds: 1), () async {
        await _syncDesiredToReported(
          thingName: thingName,
          subId: subId,
          propKeys: property.keys.toSet(),
        );
      });
    } catch (e) {
      log.e('Demo: Error updating demo device shadow desired', error: e);
    }
  }

  Future<void> _updateDemoDeviceShadowReported({
    required String thingName,
    required Map<String, dynamic> property,
    required String subId,
  }) async {
    try {
      final shadowObject = await _readDemoDeviceShadow(thingName: thingName);
      final state = shadowObject['state'] as Map<String, dynamic>;
      final reported = state['reported'] as Map<String, dynamic>;
      final reportedSub = reported[subId] as Map<String, dynamic>;
      final reportedProps = reportedSub['properties'] as Map<String, dynamic>;

      reportedProps.addAll(property);

      await _writeDemoDeviceShadow(
        thingName: thingName,
        shadowObject: shadowObject,
      );

      lastDemoDeviceShadowUpdateEvent.value = DemoDeviceShadowUpdateEvent(
        thingName: thingName,
        shadow: _buildShadowUpdatePayload(
          subId: subId,
          property: property,
          desired: false,
        ),
      );
    } catch (e) {
      log.e('Demo: Error updating demo device shadow reported', error: e);
    }
  }

  Future<void> _syncDesiredToReported({
    required String thingName,
    required String subId,
    required Set<String> propKeys,
  }) async {
    final key = '$thingName|$subId';
    _pendingSyncProps.putIfAbsent(key, () => <String>{});
    _pendingSyncProps[key]!.addAll(propKeys);

    if (_isSyncing[key] == true) {
      return;
    }
    _isSyncing[key] = true;

    final pendingProps = _pendingSyncProps[key]!;
    while (pendingProps.isNotEmpty) {
      final propKeysToSync = Set<String>.from(pendingProps);
      pendingProps.clear();
      await _doSync(
        thingName: thingName,
        subId: subId,
        propKeys: propKeysToSync,
      );
    }
    _isSyncing.remove(key);
    _pendingSyncProps.remove(key);
  }

  Future<void> _doSync({
    required String thingName,
    required String subId,
    required Set<String> propKeys,
  }) async {
    try {
      await Future.sync(() async {
        final shadowObject = await _readDemoDeviceShadow(thingName: thingName);
        final state = shadowObject['state'] as Map<String, dynamic>;
        final desired = state['desired'] as Map<String, dynamic>;
        final reported = state['reported'] as Map<String, dynamic>;
        final desiredSub = desired[subId] as Map<String, dynamic>;
        final reportedSub = reported[subId] as Map<String, dynamic>;
        final desiredProps = desiredSub['properties'] as Map<String, dynamic>;
        final reportedProps = reportedSub['properties'] as Map<String, dynamic>;
        final syncReportedProps = <String, dynamic>{};

        desiredProps.forEach((desiredPropKey, desiredPropValue) {
          if (propKeys.contains(desiredPropKey)) {
            final parts = desiredPropKey.split(':');
            if (parts.length > 1) {
              final epPart = parts[0];
              final desiredPropName = parts.sublist(1).join(':');
              final reportedPropNames = desiredToReportedMap[desiredPropName];
              if (reportedPropNames != null) {
                for (final propName in reportedPropNames) {
                  final reportedPropKey = '$epPart:$propName';
                  syncReportedProps[reportedPropKey] = desiredPropValue;
                }
              }
            }
          }
        });

        reportedProps.addAll(syncReportedProps);

        await _writeDemoDeviceShadow(
          thingName: thingName,
          shadowObject: shadowObject,
        );

        lastDemoDeviceShadowUpdateEvent.value = DemoDeviceShadowUpdateEvent(
          thingName: thingName,
          shadow: _buildShadowUpdatePayload(
            subId: subId,
            property: syncReportedProps,
            desired: false,
          ),
        );
      }).timeout(const Duration(seconds: 10));
    } catch (e, stack) {
      log.e(
        'Demo: Sync desired to reported error or timeout => thingName: $thingName, subId: $subId',
        error: e,
        stackTrace: stack,
      );
    }
  }

  Map<String, dynamic> _buildShadowUpdatePayload({
    required String subId,
    required Map<String, dynamic> property,
    required bool desired,
  }) {
    final stateType = desired ? 'desired' : 'reported';
    final timestamp = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final result = {
      "state": {
        stateType: {
          subId: {
            "properties": property,
          }
        }
      },
      "metadata": {
        stateType: {
          subId: {
            "properties": property.map(
              (key, value) => MapEntry(key, {
                "timestamp": timestamp,
              }),
            ),
          }
        }
      },
      // "version": 5668,
      "timestamp": timestamp
    };

    log.i('Demo: _buildShadowUpdatePayload result: ${json.encode(result)}');

    return result;
  }

  Future<void> deleteDemoUserProfile() async {
    log.i('deleteDemoUserProfile() -> erase demo data storage...');
    await _demoDataStorage.erase();
  }
}

class DemoDeviceShadowUpdateEvent {
  final String thingName;
  final Map<String, dynamic> shadow;

  DemoDeviceShadowUpdateEvent({
    required this.thingName,
    required this.shadow,
  });
}
