import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/retry_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/dynamo_db/user_to_device_list.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/Iot_service.dart';

/// 管理设备列表，从 DynamoDB 获取网关，从 AWS IoT 获取设备
class DeviceListService extends GetxService {
  static DeviceListService get to => Get.find();

  static const String _logTag = '[DeviceListService]';

  final IotService iotService;
  final DynamoDBService dynamoDBService;

  DeviceListService({
    required this.iotService,
    required this.dynamoDBService,
  });

  var _gatewayDevices = <String, List<String>>{};

  final deviceListChangedEvent = 0.obs;

  List<String> get gatewayIds => _gatewayDevices.keys.toList();

  Map<String, List<String>> get gatewayDevices =>
      _gatewayDevices.map((key, value) => MapEntry(key, value.toList()));

  List<String>? getDevicesByGatewayId(String gatewayId) {
    return _gatewayDevices[gatewayId]?.toList();
  }

  String? findDeviceGateway(String deviceId) {
    for (final entry in _gatewayDevices.entries) {
      if (entry.value.contains(deviceId)) {
        return entry.key;
      }
    }
    return null;
  }

  /// 通知 `gatewayDevices` 发生变化
  void _notifyDeviceListChanged() {
    deviceListChangedEvent.refresh();
  }

  /// 清空所有网关和设备数据
  void clearAll() {
    log.i('$_logTag 清空所有网关和设备数据');
    _gatewayDevices.clear();
    _notifyDeviceListChanged();
  }

  /// 刷新所有网关和设备数据
  Future<void> refresh({bool skipFetchDevices = true}) async {
    try {
      log.i('$_logTag 开始刷新所有网关和设备数据');
      final gateways = await _fetchGateways();
      if (gateways.isEmpty) {
        log.w('$_logTag 用户没有任何网关');
        clearAll();
        return;
      }

      final copyGatewayDevices = {..._gatewayDevices};

      copyGatewayDevices.removeWhere((gatewayId, _) {
        bool isRemoved = !gateways.contains(gatewayId);
        if (isRemoved) {
          log.i('$_logTag 删除不存在的网关：$gatewayId');
        }
        return isRemoved;
      });

      for (var gatewayId in gateways) {
        copyGatewayDevices[gatewayId] = <String>[];
      }

      _gatewayDevices = copyGatewayDevices;
      _notifyDeviceListChanged();

      if (skipFetchDevices) {
        log.i('$_logTag 跳过获取网关下所有设备数据');
      } else {
        log.i('$_logTag 获取网关下所有设备数据');
        await Future.wait(gateways.map(refreshGatewayDevices));
      }

      log.i('$_logTag 成功刷新所有网关和设备数据');
    } catch (e, stackTrace) {
      log.e('$_logTag 刷新网关和设备数据失败', error: e, stackTrace: stackTrace);
      clearAll();
      rethrow;
    }
  }

  /// 刷新指定网关下的设备列表
  Future<void> refreshGatewayDevices(String gatewayId) async {
    if (gatewayId.isEmpty) {
      log.w('$_logTag 网关ID不能为空');
      return;
    }

    try {
      log.i('$_logTag 开始刷新网关 $gatewayId 的设备列表');
      final deviceList = await _fetchGatewayDevices(gatewayId);
      if (!_gatewayDevices.containsKey(gatewayId)) {
        log.i('$_logTag 新网关 $gatewayId, 直接初始化设备列表');
        _gatewayDevices[gatewayId] = deviceList;
        _notifyDeviceListChanged();
      } else {
        log.i('$_logTag 网关 $gatewayId 已存在, 检查设备列表是否有变化');

        final currentList = _gatewayDevices[gatewayId]!;
        if (!listEquals(currentList, deviceList)) {
          log.i('$_logTag 网关 $gatewayId 的设备列表有变化');
          _gatewayDevices[gatewayId] = deviceList;
          _notifyDeviceListChanged();
        } else {
          log.i('$_logTag 网关 $gatewayId 的设备列表无变化');
        }
      }

      log.i('$_logTag 成功刷新网关 $gatewayId 的设备列表：$deviceList ');
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 刷新网关 $gatewayId 的设备列表失败',
        error: e,
        stackTrace: stackTrace,
      );
      if (!_gatewayDevices.containsKey(gatewayId)) {
        _gatewayDevices[gatewayId] = <String>[];
        _notifyDeviceListChanged();
      }
      rethrow;
    }
  }

  /// 从 DynamoDB 获取用户的所有网关
  Future<List<String>> _fetchGateways() async {
    final userDevices = await RetryHelper.execute<UserToDeviceList?>(
      operation: () => dynamoDBService.fetchUserToDeviceList(),
      operationName: "$_logTag 从 DynamoDB 获取 UserToDeviceList ",
      maxRetries: 3,
      delayMs: 1000,
    );
    log.i('$_logTag 从 DynamoDB 获取 UserToDeviceList: $userDevices');
    return userDevices?.getAllNames() ?? [];
  }

  /// 从 AWS IoT 获取指定网关下的设备列表
  Future<List<String>> _fetchGatewayDevices(String gatewayId) async {
    final thingGroupName = Sait85rHelper.getThingGroupName(gatewayId);
    final devices = await RetryHelper.execute<List<String>?>(
      operation: () => iotService.listThingsInThingGroup(thingGroupName),
      operationName: "$_logTag 从 AWS IoT 获取网关 $gatewayId 的设备列表",
      maxRetries: 3,
      delayMs: 1000,
    );
    log.i(
      '$_logTag 从 AWS IoT 获取网关 $gatewayId 的设备列表: $devices',
    );
    return devices ?? [];
  }
}
