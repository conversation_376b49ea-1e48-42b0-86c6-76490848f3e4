import 'package:get/get.dart';
import 'package:habi_app/constants/api_endpoints.dart';
import 'package:habi_app/constants/device_provision_command.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/device_provision/device_provision_result.dart';
import 'package:habi_app/services/api_service.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/global_service.dart';

class DeviceProvisionService extends GetxService {
  static DeviceProvisionService get to => Get.find();

  final ApiService apiService;
  final GlobalService globalService;

  DeviceProvisionService({
    required this.apiService,
    required this.globalService,
  });

  String get _baseDeviceProvisionUrl => globalService.config.baseDeviceProvisionUrl;

  Future<DeviceProvisionResult> createUserRecord() async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final user = await AuthService.to.getCurrentUser();
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deviceProvision}',
      data: {
        'UserID': session.identityIdResult.value,
        'Username': user.username,
        'Command': DeviceProvisionCommand.createUserRecord,
      },
    );
    final jsonResponse = response.data!;
    final DeviceProvisionResult result = DeviceProvisionResult.fromJson(jsonResponse);
    log.i('createUserRecord() -> jsonResponse=$jsonResponse');
    return result;
  }

  Future<DeviceProvisionResult> registerDeviceOwner({
    required String bluetoothID,
  }) async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final user = await AuthService.to.getCurrentUser();
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deviceProvision}',
      data: {
        'UserID': session.identityIdResult.value,
        'Username': user.username,
        'Command': DeviceProvisionCommand.registerDeviceOwner,
        'BluetoothID': bluetoothID,
      },
    );
    final jsonResponse = response.data!;
    final DeviceProvisionResult result = DeviceProvisionResult.fromJson(jsonResponse);
    log.i('registerDeviceOwner() -> jsonResponse=$jsonResponse');
    return result;
  }

  Future<DeviceProvisionResult> removeDeviceRegistration({
    required String deviceId,
  }) async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final user = await AuthService.to.getCurrentUser();
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deviceProvision}',
      data: {
        'UserID': session.identityIdResult.value,
        'Username': user.username,
        'Command': DeviceProvisionCommand.removeDeviceRegistration,
        'DeviceID': deviceId,
      },
    );
    final jsonResponse = response.data!;
    final DeviceProvisionResult result = DeviceProvisionResult.fromJson(jsonResponse);
    log.i('removeDeviceRegistration() -> jsonResponse=$jsonResponse');
    return result;
  }

  Future<DeviceProvisionResult> shareDeviceByOwner({
    required String deviceId,
    required String username,
  }) async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deviceProvision}',
      data: {
        'UserID': session.identityIdResult.value,
        'Username': username,
        'Command': DeviceProvisionCommand.shareDevice,
        'DeviceID': deviceId,
      },
    );
    final jsonResponse = response.data!;
    final DeviceProvisionResult result = DeviceProvisionResult.fromJson(jsonResponse);
    log.i('shareDeviceByOwner() -> jsonResponse=$jsonResponse');
    return result;
  }

  Future<DeviceProvisionResult> removeShareDeviceByOwner({
    required String deviceId,
    required String username,
  }) async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deviceProvision}',
      data: {
        'UserID': session.identityIdResult.value,
        'Username': username,
        'Command': DeviceProvisionCommand.removeSharingUser,
        'DeviceID': deviceId,
      },
    );
    final jsonResponse = response.data!;
    final DeviceProvisionResult result = DeviceProvisionResult.fromJson(jsonResponse);
    log.i('removeShareDeviceByOwner() -> jsonResponse=$jsonResponse');
    return result;
  }

  Future<DeviceProvisionResult> removeUserRecord() async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final user = await AuthService.to.getCurrentUser();
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deviceProvision}',
      data: {
        'UserID': session.identityIdResult.value,
        'Username': user.username,
        'Command': DeviceProvisionCommand.removeUserRecord,
      },
    );
    final jsonResponse = response.data!;
    final DeviceProvisionResult result = DeviceProvisionResult.fromJson(jsonResponse);
    log.i('removeUserRecord() -> jsonResponse=$jsonResponse');
    return result;
  }
}
