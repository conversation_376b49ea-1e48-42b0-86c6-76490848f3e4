import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class LocalNotificationsService {
  LocalNotificationsService._internal();

  static final _instance = LocalNotificationsService._internal();

  factory LocalNotificationsService() => _instance;

  final _notificationsPlugin = FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    const initializationSettingsAndroid = AndroidInitializationSettings(
      'ic_notification',
    );

    const initializationSettingsDarwin = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      defaultPresentAlert: true,
      defaultPresentBadge: true,
      defaultPresentSound: true,
      defaultPresentBanner: true,
      defaultPresentList: true,
    );

    const initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        debugPrint('点击通知: ${response.payload}');
        if (response.payload != null) {
          debugPrint('Notification payload: ${response.payload}');
          // 这里可以根据 payload 进行页面跳转或其他操作
        }
      },
      // 如果需要处理后台点击通知，请注册 onDidReceiveBackgroundNotificationResponse 回调
    );
  }

  // @pragma('vm:entry-point')
  // static void notificationTapBackground(
  //     NotificationResponse notificationResponse) {
  //   // handle action
  //   debugPrint('后台点击通知: ${notificationResponse.payload}');
  // }

  Future<void> showNotification({
    required String title,
    required String body,
  }) async {
    try {
      debugPrint('Show local notification: $title, $body');
      await _notificationsPlugin.show(
        _generateNotificationId(),
        title,
        body,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'alert_notification_channel',
            'Alert Notification',
            importance: Importance.max,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
            presentBanner: true,
            presentList: true,
          ),
        ),
      );
    } catch (e) {
      debugPrint('Failed to show local notification: $e');
    }
  }

  int _generateNotificationId() {
    return Random().nextInt(100000);
  }
}
