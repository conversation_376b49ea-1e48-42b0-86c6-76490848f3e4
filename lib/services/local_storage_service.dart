import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:habi_app/constants/storage_keys.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/notifications/notification_settings.dart';

class LocalStorageService extends GetxService {
  static LocalStorageService get to => Get.find();
  late GetStorage _appBox;
  late GetStorage _userBox;
  late GetStorage _thingShadowBox;
  late GetStorage _notificationBox;

  Future<void> initApp() async {
    await GetStorage.init(StorageKeys.appData);
    _appBox = GetStorage(StorageKeys.appData);
  }

  Future<void> initUser(String email) async {
    String name = '${StorageKeys.userData}_$email';
    await GetStorage.init(name);
    _userBox = GetStorage(name);
  }

  Future<void> initThingShadow(String email) async {
    String name = '${StorageKeys.thingShadowData}_$email';
    await GetStorage.init(name);
    _thingShadowBox = GetStorage(name);
  }

  Future<void> initNotification() async {
    await GetStorage.init(StorageKeys.pinpointNotificationEnabled);
    _notificationBox = GetStorage(StorageKeys.pinpointNotificationEnabled);
  }

  Future<void> printAllData() async {
    log.i('--------- appData ---------');
    for (var key in _appBox.getKeys()) {
      var value = _appBox.read(key as String);
      log.i('key=$key, value=$value');
    }

    log.i('--------- userData ---------');
    for (var key in _userBox.getKeys()) {
      var value = _userBox.read(key as String);
      log.i('key=$key, value=$value');
    }

    log.i('--------- thingShadowData ---------');
    for (var key in _thingShadowBox.getKeys()) {
      var value = _thingShadowBox.read(key as String);
      log.i('key=$key, value=$value');
    }
  }

  Future<void> eraseAllData() async {
    await _appBox.erase();
    await _userBox.erase();
    await _thingShadowBox.erase();
  }

  Future<void> clearThingShadow(String thingName) async {
    var keys = _thingShadowBox.getKeys();
    if (keys != null) {
      var copyKeys = [...keys];
      for (var key in copyKeys) {
        if (key.contains(thingName)) {
          _thingShadowBox.remove(key);
          log.i('clearThingShadow() -> remove key=$key');
        }
      }
    }
  }

  // app data

  Future<String?> getEmail() async {
    return await _appBox.read(StorageKeys.appDataEmail);
  }

  Future<void> setEmail(String value) async {
    await _appBox.write(StorageKeys.appDataEmail, value);
  }

  Future<String?> getPassword() async {
    return await _appBox.read(StorageKeys.appDataPassword);
  }

  Future<void> setPassword(String value) async {
    await _appBox.write(StorageKeys.appDataPassword, value);
  }

  Future<String?> getUserId() async {
    return await _appBox.read(StorageKeys.appDataUserId);
  }

  Future<void> setUserId(String value) async {
    await _appBox.write(StorageKeys.appDataUserId, value);
  }

  Future<bool?> isFirstStartUp() async {
    return await _appBox.read(StorageKeys.appDataFirstStartUp);
  }

  Future<void> setFirstStartUp(bool value) async {
    await _appBox.write(StorageKeys.appDataFirstStartUp, value);
  }

  String? getKeypadMode() {
    return _appBox.read(StorageKeys.keypadMode);
  }

  Future<void> setKeypadMode(String value) async {
    await _appBox.write(StorageKeys.keypadMode, value);
  }

  Future<bool?> getKeepMeLoggedIn() async {
    return await _appBox.read(StorageKeys.appDataKeepMeLoggedIn);
  }

  Future<void> setKeepMeLoggedIn(bool value) async {
    await _appBox.write(StorageKeys.appDataKeepMeLoggedIn, value);
  }

  // user data

  Future<String?> getTheme() async {
    return await _userBox.read(StorageKeys.userDataTheme);
  }

  Future<void> setTheme(String value) async {
    await _userBox.write(StorageKeys.userDataTheme, value);
  }

  Future<String?> getLanguage() async {
    return await _userBox.read(StorageKeys.userDataLanguage);
  }

  Future<void> setLanguage(String value) async {
    await _userBox.write(StorageKeys.userDataLanguage, value);
  }

  Future<String?> getThingGroup() async {
    return await _userBox.read(StorageKeys.userDataThingGroup);
  }

  Future<void> setThingGroup(String value) async {
    await _userBox.write(StorageKeys.userDataThingGroup, value);
  }

  // Future<List<String>?> getThingGroupList() async {
  //   return await _userBox.read(StorageKeys.userDataThingGroupList);
  // }

  // Future<void> setThingGroupList(List<String> value) async {
  //   await _userBox.write(StorageKeys.userDataThingGroupList, value);
  // }

  // Future<List<String>?> getThingList(String thingGroupName) async {
  //   String name = '${StorageKeys.userDataThingList}_$thingGroupName';
  //   return await _userBox.read(name);
  // }

  // Future<void> setThingList(String thingGroupName, List<String> value) async {
  //   String name = '${StorageKeys.userDataThingList}_$thingGroupName';
  //   await _userBox.write(name, value);
  // }

  Future<String?> getHomeList() async {
    return await _userBox.read(StorageKeys.userDataHomeList);
  }

  Future<void> setHomeList(String value) async {
    await _userBox.write(StorageKeys.userDataHomeList, value);
  }

  Future<Map<String, dynamic>?> getFabricMap() async {
    return await _userBox.read(StorageKeys.userDataFabricMap);
  }

  Future<void> setFabricMap(Map<String, dynamic> value) async {
    await _userBox.write(StorageKeys.userDataFabricMap, value);
  }

  Future<String?> getNodeId() async {
    return await _userBox.read(StorageKeys.userDataNodeId);
  }

  Future<void> setNodeId(String value) async {
    await _userBox.write(StorageKeys.userDataNodeId, value);
  }

  Future<Map<String, dynamic>?> getWiFiMap() async {
    return await _userBox.read(StorageKeys.userDataWiFiMap);
  }

  Future<void> setWiFiMap(Map<String, dynamic> value) async {
    await _userBox.write(StorageKeys.userDataWiFiMap, value);
  }

  Future<String?> getFactoryPairingWiFiPassword() async {
    return await _userBox.read(StorageKeys.userDataFactoryPairingWiFiPassword);
  }

  Future<void> setFactoryPairingWiFiPassword(String value) async {
    await _userBox.write(StorageKeys.userDataFactoryPairingWiFiPassword, value);
  }

  Future<int?> getControlMode() async {
    return await _userBox.read(StorageKeys.userDataControlMode);
  }

  Future<void> setControlMode(int value) async {
    await _userBox.write(StorageKeys.userDataControlMode, value);
  }

  // thing shadow data
  Future<Map<String, dynamic>?> getThingShadow(String thingName) async {
    return await _thingShadowBox.read(thingName);
  }

  Future<void> setThingShadow(String thingName, Map<String, dynamic> value) async {
    await _thingShadowBox.write(thingName, value);
  }

  // pinpoint notification
  Future<Map<String, dynamic>?> getNotificationSettings() async {
    return await _notificationBox.read(StorageKeys.pinpointNotificationEnabled);
  }

  Future<void> setNotificationSettings(Map<String, dynamic> userIdToSettingsMap) async {
    await _notificationBox.write(StorageKeys.pinpointNotificationEnabled, userIdToSettingsMap);
  }  
}
