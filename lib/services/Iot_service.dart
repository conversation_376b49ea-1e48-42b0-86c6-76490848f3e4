import 'package:get/get.dart';
import 'package:aws_iot_api/iot-2015-05-28.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/global_service.dart';

class IotService extends GetxService {
  static IotService get to => Get.find();

  final DemoModeService demoModeService;

  IotService({required this.demoModeService});

  String _buildWithOutHost(String region) {
    return 'iot.$region.amazonaws.com';
  }

  Future<List<String>?> listThingsInThingGroup(String thingGroupName) async {
    if (demoModeService.isDemo) {
      return demoModeService.getDemoThingGroupList(thingGroupName);
    }

    final session = await AuthService.to.fetchCognitoAuthSession();
    final service = IoT(
      region: GlobalService.to.config.aws.region,
      endpointUrl: 'https://${_buildWithOutHost(GlobalService.to.config.aws.region)}',
      credentials: AwsClientCredentials(
        accessKey: session.credentialsResult.value.accessKeyId,
        secretKey: session.credentialsResult.value.secretAccessKey,
        sessionToken: session.credentialsResult.value.sessionToken,
      ),
    );

    final response = await service.listThingsInThingGroup(
        thingGroupName: thingGroupName,
        maxResults: 250,
        recursive: true
    );
    log.i('things: ${response.things}');
    return response.things;
  }

  Future<List<GroupNameAndArn>?> listThingGroups() async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final service = IoT(
      region: GlobalService.to.config.aws.region,
      endpointUrl: 'https://${_buildWithOutHost(GlobalService.to.config.aws.region)}',
      credentials: AwsClientCredentials(
        accessKey: session.credentialsResult.value.accessKeyId,
        secretKey: session.credentialsResult.value.secretAccessKey,
        sessionToken: session.credentialsResult.value.sessionToken,
      ),
    );

    final response = await service.listThingGroups(
        maxResults: 250,
        recursive: true
    );
    log.i('things: ${response.thingGroups}');
    return response.thingGroups;
  }

}