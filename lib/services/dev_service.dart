import 'package:get/get.dart';
import 'package:habi_app/constants/api_endpoints.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/dev/hb_add_home_response.dart';
import 'package:habi_app/models/dev/hb_add_room_response.dart';
import 'package:habi_app/models/dev/hb_device_versions_response.dart';
import 'package:habi_app/models/dev/hb_get_home_by_device_id_response.dart';
import 'package:habi_app/models/dev/hb_get_home_by_id_response.dart';
import 'package:habi_app/models/dev/hb_get_room_by_id_response.dart';
import 'package:habi_app/models/dev/hb_home_response.dart';
import 'package:habi_app/models/dev/hb_room_response.dart';
import 'package:habi_app/models/dev/hb_update_device_response.dart';
import 'package:habi_app/services/api_service.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/global_service.dart';

class DevService extends GetxService {
  static DevService get to => Get.find();

  final ApiService apiService;
  final GlobalService globalService;
  final DemoModeService demoModeService;

  DevService({
    required this.apiService,
    required this.globalService,
    required this.demoModeService,
  });

  String get _baseDeviceProvisionUrl => globalService.config.baseDeviceProvisionUrl;

  Future<HbGetHomeByDeviceIdResponse> getHomeByDeviceID(String deviceId) async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.getDemoHomeByDeviceID(deviceId);
      return HbGetHomeByDeviceIdResponse.fromJson(response);
    }

    final response = await apiService.getJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.getHomeByDeviceID}',
      queryParameters: {
        "id": deviceId,
      },
    );
    log.i('getHomeByDeviceID() -> response=$response');

    final responseJson = response.data!;
    return HbGetHomeByDeviceIdResponse.fromJson(responseJson);
  }

  Future<HbGetHomeByIdResponse> getHomeByID(String homeId) async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.getDemoHomeByID(homeId);
      return HbGetHomeByIdResponse.fromJson(response);
    }

    final response = await apiService.getJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.getHomeById}',
      queryParameters: {
        "id": homeId,
      },
    );
    log.i('getHomeByID() -> response=$response');

    final responseJson = response.data!;
    return HbGetHomeByIdResponse.fromJson(responseJson);
  }

  Future<HBHomeResponse> listHomes(int pageSize) async {
    final session = await AuthService.to.fetchCognitoAuthSession();
    final userId = session.identityIdResult.value;

    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.listHomes}',
      queryParameters: {
        "id": userId,
      },
      data: {
        "page": {
          "pageSize": pageSize,
          "startKey": {
            // "HomeID": "586c234a-8f20-464c-b8ef-dc038440063f",
            // "UserID": "eu-central-1:31280ebc-c2f2-c0db-99cc-428b0a97a72d"
          },
        }
      },
    );
    log.i('listHomes() -> response=$response');

    final responseJson = response.data!;
    return HBHomeResponse.fromJson(responseJson);
  }

  Future<HBHomeResponse> getUserHomes() async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.getDemoUserHomes();
      return HBHomeResponse.fromJson(response);
    }

    final session = await AuthService.to.fetchCognitoAuthSession();
    final userId = session.identityIdResult.value;

    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.getUserHomes}',
      queryParameters: {
        "id": userId,
      },
      data: {
        "page": {
          "pageSize": 100,
          // "startKey": {
          //   "HomeID": "607b1420-0484-49fc-8984-a2ae0b37e9ed",
          //   "UserID": userId
          // },
        }
      },
    );
    log.i('getUserHomes() -> response=$response');

    final responseJson = response.data!;
    return HBHomeResponse.fromJson(responseJson);
  }

  Future<HBAddHomeResponse> addHome(String homeName) async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.addDemoHome(homeName);
      return HBAddHomeResponse.fromJson(response);
    }

    final session = await AuthService.to.fetchCognitoAuthSession();
    final userId = session.identityIdResult.value;
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addHome}',
      queryParameters: {
        "id": userId,
      },
      data: {
        "homeName": homeName,
      },
    );
    log.i('addHome() -> response=$response');

    final responseJson = response.data!;
    return HBAddHomeResponse.fromJson(responseJson);
  }

  Future<void> changeHomeName(String homeId, String name) async {
    if (demoModeService.isDemo) {
      await demoModeService.changeDemoHomeName(homeId, name);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.changeHomeName}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "name": name,
      },
    );
    log.i('changeHomeName() -> response=$response');
  }

  Future<void> addPropertyToHome(String homeId, String name, String value) async {
    if (demoModeService.isDemo) {
      await demoModeService.addDemoPropertyToHome(homeId, name, value);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addPropertyToHome}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "property": {
          "name": name,
          "value": value,
        },
      },
    );
    log.i('addPropertyToHome() -> response=$response');
  }

  Future<void> removePropertyFromHome(String homeId, String name) async {
    if (demoModeService.isDemo) {
      await demoModeService.removeDemoPropertyFromHome(homeId, name);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.removePropertyFromHome}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "property": name,
      },
    );
    log.i('removePropertyFromHome() -> response=$response');
  }

  Future<void> deleteHome(String homeId) async {
    if (demoModeService.isDemo) {
      await demoModeService.deleteDemoHome(homeId);
      return;
    }

    final response = await apiService.deleteJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deleteHome}',
      queryParameters: {
        "id": homeId,
      },
    );
    log.i('deleteHome() -> response=$response');
  }

  Future<void> addDeviceToHome(String homeId, String deviceId) async {
    if (demoModeService.isDemo) {
      await demoModeService.addDemoDeviceToHome(homeId, deviceId);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addDeviceToHome}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "device": deviceId,
      },
    );
    log.i('addDeviceToHome() -> response=$response');
  }

  Future<void> removeDeviceFromHome(String homeId, String deviceId) async {
    if (demoModeService.isDemo) {
      await demoModeService.removeDemoDeviceFromHome(homeId, deviceId);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.removeDeviceFromHome}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "device": deviceId,
      },
    );
    log.i('removeDeviceFromHome() -> response=$response');
  }

  Future<HBRoomResponse> listRooms(int pageSize, String homeId) async {
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.listRooms}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "page": {
          "pageSize": pageSize,
        },
      },
    );
    log.i('listRooms() -> response=$response');

    final responseJson = response.data!;
    return HBRoomResponse.fromJson(responseJson);
  }

  Future<HBRoomResponse> getHomeRooms(String homeId) async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.getDemoHomeRooms(homeId);
      return HBRoomResponse.fromJson(response);
    }

    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.getHomeRooms}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "page": {
          "pageSize": 100,
        },
      },
    );
    log.i('getHomeRooms() -> response=$response');

    final responseJson = response.data!;
    return HBRoomResponse.fromJson(responseJson);
  }

  Future<HbGetRoomByDeviceIdResponse> getRoomByID(String roomId) async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.getDemoRoomByID(roomId);
      return HbGetRoomByDeviceIdResponse.fromJson(response);
    }

    final response = await apiService.getJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.getRoomById}',
      queryParameters: {
        "id": roomId,
      },
    );
    log.i('getRoomById() -> response=$response');

    final responseJson = response.data!;
    return HbGetRoomByDeviceIdResponse.fromJson(responseJson);
  }

  Future<HBAddRoomResponse> addRoom(String homeId, String roomName) async {
    if (demoModeService.isDemo) {
      final response = await demoModeService.addDemoRoom(homeId, roomName);
      return HBAddRoomResponse.fromJson(response);
    }

    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addRoom}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "roomName": roomName,
      },
    );
    log.i('addRoom() -> response=$response');

    final responseJson = response.data!;
    return HBAddRoomResponse.fromJson(responseJson);
  }

  Future<void> changeRoomName(String roomId, String name) async {
    if (demoModeService.isDemo) {
      await demoModeService.changeDemoRoomName(roomId, name);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.changeRoomName}',
      queryParameters: {
        "id": roomId,
      },
      data: {
        "name": name,
      },
    );
    log.i('changeRoomName() -> response=$response');
  }

  Future<void> addPropertyToRoom(String roomId, String name, String value) async {
    if (demoModeService.isDemo) {
      await demoModeService.addDemoPropertyToRoom(roomId, name, value);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addPropertyToRoom}',
      queryParameters: {
        "id": roomId,
      },
      data: {
        "property": {
          "name": name,
          "value": value,
        },
      },
    );
    log.i('addPropertyToRoom() -> response=$response');
  }

  Future<void> removePropertyFromRoom(String roomId, String name) async {
    if (demoModeService.isDemo) {
      await demoModeService.removeDemoPropertyFromRoom(roomId, name);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.removePropertyFromRoom}',
      queryParameters: {
        "id": roomId,
      },
      data: {
        "property": name,
      },
    );
    log.i('removePropertyFromRoom() -> response=$response');
  }

  Future<void> deleteRoom(String roomId) async {
    if (demoModeService.isDemo) {
      await demoModeService.deleteDemoRoom(roomId);
      return;
    }

    final response = await apiService.deleteJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.deleteRoom}',
      queryParameters: {
        "id": roomId,
      },
    );
    log.i('deleteRoom() -> response=$response');
  }

  Future<void> addDeviceToRoom(String roomId, String deviceId) async {
    if (demoModeService.isDemo) {
      await demoModeService.addDemoDeviceToRoom(roomId, deviceId);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addDeviceToRoom}',
      queryParameters: {
        "id": roomId,
      },
      data: {
        "device": deviceId,
      },
    );
    log.i('addDeviceToRoom() -> response=$response');
  }

  Future<void> removeDeviceFromRoom(String roomId, String deviceId) async {
    if (demoModeService.isDemo) {
      await demoModeService.removeDemoDeviceFromRoom(roomId, deviceId);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.removeDeviceFromRoom}',
      queryParameters: {
        "id": roomId,
      },
      data: {
        "device": deviceId,
      },
    );
    log.i('removeDeviceFromRoom() -> response=$response');
  }

  Future<void> addUserToHome(String homeId, List<String> userList, bool isOwner) async {
    if (demoModeService.isDemo) {
      await demoModeService.addDemoUserToHome(homeId, userList, isOwner);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.addUserToHome}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "userList": userList,
        "isOwnerOrSharer": isOwner ? 'owner' : 'sharer',
      },
    );
    log.i('addUserToHome() -> response=$response');
  }

  Future<void> removeUserFromHome(String homeId, List<String> userList, bool isOwner) async {
    if (demoModeService.isDemo) {
      await demoModeService.removeDemoUserFromHome(homeId, userList, isOwner);
      return;
    }

    final response = await apiService.putJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.removeUserFromHome}',
      queryParameters: {
        "id": homeId,
      },
      data: {
        "userList": userList,
        "isOwnerOrSharer": isOwner ? 'owner' : 'sharer',
      },
    );
    log.i('removeUserFromHome() -> response=$response');
  }

  Future<HbDeviceVersionsResponse> getDeviceVersions() async {
    final response = await apiService.getJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.version}',
    );
    log.i('getDeviceVersions() -> response=$response');

    final responseJson = response.data!;
    return HbDeviceVersionsResponse.fromJson(responseJson);
  }

  Future<HBUpdateDeviceResponse> updateDevice(List<String> deviceIdList) async {
    final response = await apiService.postJson(
      '$_baseDeviceProvisionUrl${ApiEndpoints.update}',
      data: {
        "deviceId": deviceIdList,
      },
    );
    log.i('updateDevice() -> response=$response');

    final responseJson = response.data!;
    return HBUpdateDeviceResponse.fromJson(responseJson);
  }
}
