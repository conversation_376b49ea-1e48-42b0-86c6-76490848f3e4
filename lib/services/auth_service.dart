import 'dart:io';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:get/get.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:dio/dio.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/user_attribute_keys.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:intl/intl.dart';

class AuthService extends GetxService {
  static AuthService get to => Get.find();

  final Dio _dio = Dio();
  final DemoModeService demoModeService;

  AuthService({required this.demoModeService});

  Future<AuthUser> getCurrentUser() async {
    if (demoModeService.isDemo) {
      final result = await demoModeService.getDemoAuthUser();
      return result;
    }

    AuthUser res = await Amplify.Auth.getCurrentUser();
    return res;
  }

  Future<String> get currentUserId async {
    final cognitoData = await fetchCognitoAuthSession();
    return cognitoData.identityIdResult.value;
  }

  Future<CognitoAuthSession> fetchCognitoAuthSession() async {
    if (demoModeService.isDemo) {
      try {
        throw Exception('Demo Mode not implemented');
      } catch (error, stackTrace) {
        log.e(
          'Error fetching Cognito Auth Session',
          error: error,
          stackTrace: stackTrace,
        );
        rethrow;
      }
    }

    AmplifyAuthCognito amplifyAuthCognito =
        Amplify.Auth.getPlugin(AmplifyAuthCognito.pluginKey);
    CognitoAuthSession cognitoAuthSession =
        await amplifyAuthCognito.fetchAuthSession();
    return cognitoAuthSession;
  }

  Future<bool> isUserSignedIn() async {
    AuthSession session = await Amplify.Auth.fetchAuthSession();
    return session.isSignedIn;
  }

  Future<SignUpResult> signUpUser({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String language,
    required String country,
  }) async {

    // 获取当前的UTC时间
    DateTime utcTime = DateTime.now().toUtc();
    // 格式化UTC时间为字符串
    String formattedUtcTime = DateFormat("yyyy-MM-ddTHH:mm:ss'Z'").format(utcTime);

    SignUpResult result = await Amplify.Auth.signUp(
      username: email,
      password: password,
      options: SignUpOptions(
        userAttributes: {
          UserAttributeKeys.email: email,
          UserAttributeKeys.locale: language,
          UserAttributeKeys.name: firstName,
          UserAttributeKeys.familyName: lastName,
          UserAttributeKeys.country: country,
          UserAttributeKeys.language: language,
          UserAttributeKeys.dataCollection: DataCollectionStatus.on.value,
          UserAttributeKeys.dataCollectionDateTime: formattedUtcTime,
          UserAttributeKeys.privacyVersion: '1.0.0',
          UserAttributeKeys.privacyAcceptDateTime: formattedUtcTime,
          UserAttributeKeys.termsVersion: '1.0.0',
          UserAttributeKeys.termsAcceptDateTime: formattedUtcTime,
          UserAttributeKeys.hourFormat: HourFormatStatus.format24h.value,
          UserAttributeKeys.temperatureUnit: TemperatureUnitStatus.celsius.value,
        },
      ),
    );
    return result;
  }

  Future<SignInResult> signIn({
    required String username,
    required String password,
  }) async {
    demoModeService.isDemo = false;
    SignInResult result = await Amplify.Auth.signIn(
      username: username,
      password: password,
    );
    return result;
  }

  Future<SignOutResult> signOut() async {
    if (demoModeService.isDemo) {
      log.i("Demo: signOut");
      demoModeService.isDemo = false;
      return const CognitoSignOutResult.complete();
    }

    final SignOutResult result = await Amplify.Auth.signOut();
    return result;
  }

  Future<SignUpResult> confirmUser({
    required String username,
    required String confirmationCode,
  }) async {
    SignUpResult result = await Amplify.Auth.confirmSignUp(
      username: username,
      confirmationCode: confirmationCode,
    );
    return result;
  }

  Future<ResendSignUpCodeResult> resendSignUpCode(
      {required String username}) async {
    ResendSignUpCodeResult result = await Amplify.Auth.resendSignUpCode(
      username: username,
    );
    return result;
  }

  Future<ResetPasswordResult> forgotPassword(String username) async {
    ResetPasswordResult result = await Amplify.Auth.resetPassword(
      username: username,
    );
    return result;
  }

  Future<ResetPasswordResult> confirmResetPassword({
    required String username,
    required String newPassword,
    required String confirmationCode,
  }) async {
    ResetPasswordResult result = await Amplify.Auth.confirmResetPassword(
      username: username,
      newPassword: newPassword,
      confirmationCode: confirmationCode,
    );
    return result;
  }

  Future<UpdatePasswordResult> updatePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    UpdatePasswordResult result = await Amplify.Auth.updatePassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
    );
    return result;
  }

  Future<void> sendConfirmationCode({required String email}) async {
    var response = await _dio.request(
      '${GlobalService.to.config.baseUrl}/occupants/forgot_password',
      options: Options(
        method: 'POST',
        headers: await _getAuthHeaders(),
      ),
      data: {"email": email},
    );

    if (response.statusCode != HttpStatus.ok) {
      throw Exception(response.statusMessage);
    }
  }

  Future<List<AuthUserAttribute>> fetchUserAttributes() async {
    List<AuthUserAttribute> results = await Amplify.Auth.fetchUserAttributes();
    return results;
  }

  Future<Map<AuthUserAttributeKey, UpdateUserAttributeResult>>
      updateUserAttributes({required Map<String, dynamic> attributes}) async {
    List<AuthUserAttribute> attributesList = [];
    attributes.forEach((key, value) {
      AuthUserAttribute attribute = AuthUserAttribute(
        userAttributeKey: CognitoUserAttributeKey.custom(key),
        value: value,
      );
      attributesList.add(attribute);
    });

    Map<AuthUserAttributeKey, UpdateUserAttributeResult> results =
        await Amplify.Auth.updateUserAttributes(attributes: attributesList);

    return results;
  }

  Future<SignInResult> signInDemo() async {
    demoModeService.isDemo = true;
    await demoModeService.init();
    const result = CognitoSignInResult(
      isSignedIn: true,
      nextStep: AuthNextSignInStep(
        signInStep: AuthSignInStep.done,
      ),
    );
    return result;
  }

  Future<SignInResult> signInWithGoogle() async {
    demoModeService.isDemo = false;
    final result = await Amplify.Auth.signInWithWebUI(
      provider: AuthProvider.google,
    );
    return result;
  }

  Future<SignInResult> signInWithApple() async {
    demoModeService.isDemo = false;
    final result = await Amplify.Auth.signInWithWebUI(
      provider: AuthProvider.apple,
    );
    return result;
  }

  Future<Map<String, String>> _getAuthHeaders() async {
    final session = await fetchCognitoAuthSession();
    final idToken = session.userPoolTokensResult.value.idToken.raw;
    final accessToken = session.userPoolTokensResult.value.accessToken.raw;
    final company = GlobalService.to.config.companyCode;

    return {
      'X-Auth-Token': idToken,
      'X-Access-Token': accessToken,
      'X-Company-Code': company,
      'Content-Type': 'application/json',
    };
  }
}
