import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import 'package:habi_app/exceptions/api_exceptions.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';

enum ApiAuthorizationType {
  auth,
  none,
}

class ApiService extends GetxService {
  static ApiService get to => Get.find();

  late Dio _dio;

  @override
  void onInit() {
    super.onInit();
    _setupDio();
  }

  @override
  void onClose() {
    _dio.close();
    super.onClose();
  }

  void _setupDio() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        Headers.contentTypeHeader: Headers.jsonContentType,
        Headers.acceptHeader: Headers.jsonContentType,
      },
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: _onRequest,
      onError: _onError,
    ));
  }

  Future<void> _onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      final authorizationType =
          options.extra['authorizationType'] as ApiAuthorizationType?;

      switch (authorizationType) {
        case ApiAuthorizationType.auth:
          await _addAuthHeaders(options);
          break;
        default:
          break;
      }

      handler.next(options);
    } catch (e) {
      handler.reject(DioException(
        requestOptions: options,
        error: e,
        type: DioExceptionType.unknown,
      ));
    }
  }

  void _onError(DioException error, ErrorInterceptorHandler handler) {
    log.e('API Error: ${error.type} ${error.message}', error: error);

    handler.next(error);
  }

  Future<void> _addAuthHeaders(RequestOptions options) async {
    try {
      final session = await AuthService.to.fetchCognitoAuthSession();
      final accessToken = session.userPoolTokensResult.value.accessToken.raw;

      options.headers.addAll({
        'Authorization': 'Bearer $accessToken',
      });

      log.i('API Service - _addAuthHeaders() - headers: ${options.headers}');
    } catch (e) {
      log.w('Failed to add auth headers: $e');
      throw AuthenticationException.tokenExpired();
    }
  }

  Future<Response<T>> get<T>(
    String url, {
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) async {
    return _executeRequest<T>(
      () => _dio.get<T>(
        url,
        queryParameters: queryParameters,
        options: Options(extra: {'authorizationType': authType}),
      ),
    );
  }

  Future<Response<T>> post<T>(
    String url, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) async {
    return _executeRequest<T>(
      () => _dio.post<T>(
        url,
        data: data,
        queryParameters: queryParameters,
        options: Options(extra: {'authorizationType': authType}),
      ),
    );
  }

  Future<Response<T>> put<T>(
    String url, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) async {
    return _executeRequest<T>(
      () => _dio.put<T>(
        url,
        data: data,
        queryParameters: queryParameters,
        options: Options(extra: {'authorizationType': authType}),
      ),
    );
  }

  Future<Response<T>> delete<T>(
    String url, {
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) async {
    return _executeRequest<T>(
      () => _dio.delete<T>(
        url,
        queryParameters: queryParameters,
        options: Options(extra: {'authorizationType': authType}),
      ),
    );
  }

  Future<Response<Map<String, dynamic>>> getJson(
    String url, {
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) {
    return get<Map<String, dynamic>>(
      url,
      queryParameters: queryParameters,
      authType: authType,
    );
  }

  Future<Response<Map<String, dynamic>>> postJson(
    String url, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) {
    return post<Map<String, dynamic>>(
      url,
      data: data,
      queryParameters: queryParameters,
      authType: authType,
    );
  }

  Future<Response<Map<String, dynamic>>> putJson(
    String url, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) {
    return put<Map<String, dynamic>>(
      url,
      data: data,
      queryParameters: queryParameters,
      authType: authType,
    );
  }

  Future<Response<Map<String, dynamic>>> deleteJson(
    String url, {
    Map<String, dynamic>? queryParameters,
    ApiAuthorizationType authType = ApiAuthorizationType.auth,
  }) {
    return delete<Map<String, dynamic>>(
      url,
      queryParameters: queryParameters,
      authType: authType,
    );
  }

  Future<Response<T>> _executeRequest<T>(
    Future<Response<T>> Function() request,
  ) async {
    try {
      return await request();
    } on DioException catch (e) {
      throw ApiExceptionFactory.fromDioException(e);
    } catch (e) {
      log.e('Unexpected error in API request: $e', error: e);
      rethrow;
    }
  }
}
