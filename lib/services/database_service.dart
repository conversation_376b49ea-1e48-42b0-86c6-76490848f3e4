import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:habi_app/database/dao/log_dao.dart';
import 'package:habi_app/database/dao/log_dao_impl.dart';
import 'package:habi_app/database/table/log_table.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseService extends GetxService {

  static DatabaseService get to => Get.find();
  LogDao? logDao;

  Future<void> initDb() async {
    try {
      if (kIsWeb) {
        log.w('Database initialization skipped on web platform');
        return;
      }

      log.i('initDb...');
      final databasesPath = await getDatabasesPath();
      String path = join(databasesPath, 'habi.db');
      Database db = await openDatabase(path, version: 1, onCreate: _onCreate);
      logDao = LogDaoImpl(db);
      log.i('initDb completed...');
    } catch (e, stackTrace) {
      log.e('Failed to initialize database', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  void _onCreate(Database db, int newVersion) async {
    log.i('_onCreate started, newVersion=$newVersion');
    await db.execute(LogTable.createSQL);
    log.i('_onCreate completed');
  }

}