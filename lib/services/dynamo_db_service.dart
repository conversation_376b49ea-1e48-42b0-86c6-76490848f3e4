import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart' as GetX;
import 'package:aws_dynamodb_api/dynamodb-2012-08-10.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/dynamo_db/controller_fabric_property.dart';
import 'package:habi_app/models/dynamo_db/push_notification.dart';
import 'package:habi_app/models/dynamo_db/user_to_device_list.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/global_service.dart';

class DynamoDBService extends GetX.GetxService {
  static DynamoDBService get to => GetX.Get.find();

  final DemoModeService demoModeService;

  DynamoDBService({required this.demoModeService});

  Future<DynamoDB> _getDynamoDb() async {
    final data = await AuthService.to.fetchCognitoAuthSession();
    return DynamoDB(
      region: GlobalService.to.config.aws.region,
      credentials: AwsClientCredentials(
        accessKey: data.credentialsResult.value.accessKeyId,
        secretKey: data.credentialsResult.value.secretAccessKey,
        sessionToken: data.credentialsResult.value.sessionToken,
      ),
    );
  }

  Future<UserToDeviceList?> fetchUserToDeviceList() async {
    if (demoModeService.isDemo) {
      return demoModeService.getDemoUserToDeviceList();
    }

    final dynamoDb = await _getDynamoDb();
    final session = await AuthService.to.fetchCognitoAuthSession();
    final userId = session.identityIdResult.value;

    log.i('fetchUserToDeviceList() -> userId=$userId');

    var result = await dynamoDb.getItem(
      key: {'userid': AttributeValue(s: userId)},
      tableName: 'UserToDeviceList',
    );

    debugPrint('fetchUserToDeviceList() -> result.item=${result.item}');

    UserToDeviceList? userToDeviceList;
    if (result.item != null) {
      userToDeviceList = UserToDeviceList.fromDatabase(result.item!);
    }
    return userToDeviceList;
  }

  Future<ControllerFabricProperty?> fetchControllerFabricProperty(
      String deviceId) async {
    final dynamoDb = await _getDynamoDb();
    final session = await AuthService.to.fetchCognitoAuthSession();
    final userId = session.identityIdResult.value;

    log.i('fetchControllerFabricProperty() -> userId=$userId');

    var result = await dynamoDb.getItem(
      key: {'DeviceID': AttributeValue(s: deviceId)},
      tableName: 'ControllerFabricPropety',
    );

    log.i('fetchControllerFabricProperty() -> result.item=${result.item}');

    ControllerFabricProperty? controllerFabricProperty;
    if (result.item != null) {
      controllerFabricProperty =
          ControllerFabricProperty.fromDatabase(result.item!);
    }
    return controllerFabricProperty;
  }

  Future<List<PushNotification>> fetchNotifications() async {
    if (demoModeService.isDemo) {
      return [];
    }

    List<PushNotification> notificationList = [];
    final dynamoDb = await _getDynamoDb();
    final session = await AuthService.to.fetchCognitoAuthSession();
    final userId = session.identityIdResult.value;

    try {
      var result = await dynamoDb.query(
          tableName: 'NotificationTable',
          keyConditionExpression: 'UserID = :userId',
          expressionAttributeValues: {
            ':userId': AttributeValue(s: userId),
          });
      for (var item in result.items!) {
        final notification = PushNotification.fromDatabase(item);
        notificationList.add(notification);
      }
    } catch (e, s) {
      debugPrint("Error in fetchNotifications: $e");
    }

    return notificationList;
  }

  Future<List<PushNotification>> updateReadNotifications(
      List<PushNotification> notificationList) async {
    if (demoModeService.isDemo) {
      return [];
    }

    final dynamoDb = await _getDynamoDb();

    try {
      for (int i = 0; i < notificationList.length; i++) {
        final notification = notificationList[i];
        var key = {
          'UserID': AttributeValue(s: notification.userId),
          'Timestamp': AttributeValue(n: notification.timestamp.toString()),
        };

        var result = await dynamoDb.updateItem(
          tableName: "NotificationTable",
          key: key,
          updateExpression: 'SET IsRead = :isRead',
          expressionAttributeValues: {
            ':isRead': AttributeValue(boolValue: true),
          },
          returnValues: ReturnValue.updatedNew,
        );

        if (result.attributes != null) {
          notificationList[i].isRead = result.attributes!['IsRead']?.boolValue;
        }
      }
    } catch (e, s) {
      debugPrint("Error in updateReadNotification: $e");
    }

    return notificationList;
  }
}
