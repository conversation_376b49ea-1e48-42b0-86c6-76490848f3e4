import 'dart:convert';

import 'package:logger/logger.dart';

class CustomLogPrinter extends LogPrinter {
  final bool useColors;

  CustomLogPrinter({this.useColors = true});

  @override
  List<String> log(LogEvent event) {
    final message = _getMessage(event.message);
    final error = _getError(event);
    return ['$message $error'];
  }

  String _getError(LogEvent event) {
    final error = event.error != null ? '\n${event.error}' : '';
    final stackTrace = event.stackTrace != null ? '\n${event.stackTrace}' : '';
    return '$error$stackTrace';
  }

  String _getMessage(dynamic message) {
    final finalMessage = message is Function ? message() : message;
    if (finalMessage is Map || finalMessage is Iterable) {
      var encoder = const JsonEncoder.withIndent('  ');
      return encoder.convert(finalMessage);
    } else {
      return finalMessage.toString();
    }
  }
}
