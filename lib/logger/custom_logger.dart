import 'package:habi_app/logger/custom_log_output.dart';
import 'package:habi_app/logger/custom_log_printer.dart';
import 'package:habi_app/logger/database_log_output.dart';
import 'package:logger/logger.dart';

Logger get log => CustomLogger.instance;

class CustomLogger extends Logger {
  CustomLogger._()
      : super(
          level: Level.all,
          printer: CustomLogPrinter(),
          filter: ProductionFilter(),
          output: MultiOutput([
            CustomLogOutput(),
            DatabaseLogOutput()
          ]),
        );

  static final instance = CustomLogger._();
}
