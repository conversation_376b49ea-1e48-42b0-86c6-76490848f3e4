import 'package:habi_app/models/log_message.dart';
import 'package:habi_app/services/database_service.dart';
import 'package:logger/logger.dart';

class DatabaseLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    final logMessage = LogMessage(
      level: event.level.name,
      message: event.lines.join(),
    );
    DatabaseService.to.logDao?.insertLog(logMessage);
  }
}