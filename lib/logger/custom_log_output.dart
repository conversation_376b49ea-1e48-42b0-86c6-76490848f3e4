import 'dart:developer' as developer;
import 'package:logger/logger.dart';

class CustomLogOutput extends LogOutput {
  static final levelColors = {
    Level.trace: AnsiColor.fg(AnsiColor.grey(0.5)),
    Level.debug: const AnsiColor.fg(12),
    Level.info: const AnsiColor.fg(80),
    Level.warning: const AnsiColor.fg(208),
    Level.error: const AnsiColor.fg(196),
    Level.fatal: const AnsiColor.fg(199),
  };

  static final levelPrefixes = {
    Level.trace: '[t]',
    Level.debug: '[d]',
    Level.info: '[i]',
    Level.warning: '[w]',
    Level.error: '[e]',
    Level.fatal: '[FATAL]',
  };

  @override
  void output(OutputEvent event) {
    final StringBuffer buffer = StringBuffer();
    for (var logMessage in event.lines) {
      // add prefix
      final prefix = levelPrefixes[event.level];
      if (prefix != null) logMessage = '$prefix $logMessage';

      // add color
      logMessage = levelColors[event.level]!(logMessage);

      // add message to buffer
      buffer.writeln(logMessage);
    }

    // developer.log() was used because it supports displaying colors in VS code
    developer.log(buffer.toString());
  }
}
