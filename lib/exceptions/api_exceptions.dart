import 'package:dio/dio.dart';

abstract class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final dynamic originalError;

  const ApiException({
    required this.message,
    this.statusCode,
    this.errorCode,
    this.originalError,
  });

  @override
  String toString() => 'ApiException: $message';
}

class NetworkException extends ApiException {
  const NetworkException({
    required super.message,
    super.originalError,
  });

  factory NetworkException.noConnection() {
    return const NetworkException(
      message: 'No internet connection available',
    );
  }

  factory NetworkException.timeout() {
    return const NetworkException(
      message: 'Request timeout',
    );
  }

  factory NetworkException.connectionError(dynamic error) {
    return NetworkException(
      message: 'Connection error occurred',
      originalError: error,
    );
  }
}

class AuthenticationException extends ApiException {
  const AuthenticationException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.originalError,
  });

  factory AuthenticationException.tokenExpired() {
    return const AuthenticationException(
      message: 'Authentication token has expired',
      statusCode: 401,
      errorCode: 'TOKEN_EXPIRED',
    );
  }

  factory AuthenticationException.invalidToken() {
    return const AuthenticationException(
      message: 'Invalid authentication token',
      statusCode: 401,
      errorCode: 'INVALID_TOKEN',
    );
  }

  factory AuthenticationException.unauthorized() {
    return const AuthenticationException(
      message: 'Unauthorized access',
      statusCode: 401,
      errorCode: 'UNAUTHORIZED',
    );
  }
}

class ServerException extends ApiException {
  const ServerException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.originalError,
  });

  factory ServerException.internalError() {
    return const ServerException(
      message: 'Internal server error',
      statusCode: 500,
      errorCode: 'INTERNAL_ERROR',
    );
  }

  factory ServerException.serviceUnavailable() {
    return const ServerException(
      message: 'Service temporarily unavailable',
      statusCode: 503,
      errorCode: 'SERVICE_UNAVAILABLE',
    );
  }
}

class BusinessException extends ApiException {
  const BusinessException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.originalError,
  });
}

class ApiExceptionFactory {
  static ApiException fromDioException(DioException dioException) {
    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException.timeout();

      case DioExceptionType.connectionError:
        return NetworkException.connectionError(dioException);

      case DioExceptionType.badResponse:
        final statusCode = dioException.response?.statusCode;
        final responseData = dioException.response?.data;

        if (statusCode == 401) {
          return AuthenticationException.unauthorized();
        } else if (statusCode != null && statusCode >= 500) {
          return ServerException(
            message: 'Server error occurred',
            statusCode: statusCode,
            originalError: dioException,
          );
        } else {
          return BusinessException(
            message: _extractErrorMessage(responseData) ?? 'Request failed',
            statusCode: statusCode,
            originalError: dioException,
          );
        }

      case DioExceptionType.cancel:
        return const NetworkException(message: 'Request was cancelled');

      case DioExceptionType.unknown:
      default:
        return NetworkException.connectionError(dioException);
    }
  }

  static String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;

    if (responseData is Map<String, dynamic>) {
      return responseData['message'] ??
          responseData['error'] ??
          responseData['errorMessage'] ??
          responseData['msg'];
    }

    if (responseData is String) {
      return responseData;
    }

    return null;
  }
}
