import 'package:get/get.dart';
import 'package:habi_app/middleware/data_collection_middleware.dart';
import 'package:habi_app/pages/data_collection/data_collection_bindings.dart';
import 'package:habi_app/pages/data_collection/data_collection_page.dart';
import 'package:habi_app/pages/home_management/home_management_bindings.dart';
import 'package:habi_app/pages/home_management/home_management_page.dart';
import 'package:habi_app/pages/location_list/location_list_bindings.dart';
import 'package:habi_app/pages/location_list/location_list_page.dart';
import 'package:habi_app/pages/locations/locations_bindings.dart';
import 'package:habi_app/pages/locations/locations_page.dart';
import 'package:habi_app/pages/root/root_bindings.dart';
import 'package:habi_app/pages/root/root_page.dart';
import 'package:habi_app/routes/app_routes.dart';

class AppPages {
  AppPages._();

  static const initial = Routes.root;

  static final routes = [
    GetPage(
      name: Routes.root,
      page: () => const RootPage(),
      binding: RootBindings(),
    ),
    GetPage(
      name: Routes.dataCollection,
      page: () => const DataCollectionPage(),
      binding: DataCollectionBindings(),
    ),
    GetPage(
      name: Routes.homeManagement,
      page: () => const HomeManagementPage(),
      binding: HomeManagementBindings(),
      middlewares: [
        DataCollectionMiddleware(),
      ],
    ),
    GetPage(
      name: Routes.locationList,
      page: () => const LocationListPage(),
      binding: LocationListBindings(),
    ),
    GetPage(
      name: Routes.locations,
      page: () => const LocationsPage(),
      binding: LocationsBindings(),
    ),
  ];
}
