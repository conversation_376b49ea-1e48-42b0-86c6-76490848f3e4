
class AppTimeZones {

  AppTimeZones._();

  static const String defaultTimeZoneKey = 'London';
  static const String defaultTimeZoneValue = 'Europe/London';

  static Map<String, String> getTimeZoneConvertedMap() {
    final Map<String, String> timeZoneConvertedMap = {
      'Amsterdam': '(UTC+01:00) Amsterdam',
      'Anadyr': '(UTC+12:00) Anadyr',
      'Andorra': '(UTC+01:00) Andorra',
      'Athens': '(UTC+02:00) Athens',
      'Belfast': '(UTC+00:00) Belfast',
      'Belgrade': '(UTC+01:00) Belgrade',
      'Berlin': '(UTC+01:00) Berlin',
      'Bratislava': '(UTC+01:00) Bratislava',
      'Brussels': '(UTC+01:00) Brussels',
      'Bucharest': '(UTC+02:00) Bucharest',
      'Budapest': '(UTC+01:00) Budapest',
      'Buenos_Aires': '(UTC-3:00) Buenos_Aires',
      'Busingen': '(UTC+01:00) Busingen',
      'Chisinau': '(UTC+02:00) Chisinau',
      'Copenhagen': '(UTC+01:00) Copenhagen',
      'Dublin': '(UTC+00:00) Dublin',
      'Gibraltar': '(UTC+01:00) Gibraltar',
      'Guernsey': '(UTC+00:00) Guernsey',
      'Helsinki': '(UTC+02:00) Helsinki',
      'Irkutsk': '(UTC+08:00) Irkutsk',
      'Isle_of_Man': '(UTC+00:00) Isle_of_Man',
      'Istanbul': '(UTC+03:00) Istanbul',
      'Jersey': '(UTC+00:00) Jersey',
      'Kaliningrad': '(UTC+02:00) Kaliningrad',
      'Kiev': '(UTC+02:00) Kiev',
      'Krasnoyarsk': '(UTC+07:00) Krasnoyarsk',
      'Lima': '(UTC-05:00) Lima',
      'Lisbon': '(UTC+00:00) Lisbon',
      'Ljubljana': '(UTC+01:00) Ljubljana',
      'London': '(UTC+00:00) London',
      'Luxembourg': '(UTC+01:00) Luxembourg',
      'Madrid': '(UTC+01:00) Madrid',
      'Magadan': '(UTC+11:00) Magadan',
      'Malta': '(UTC+01:00) Malta',
      'Mariehamn': '(UTC+02:00) Mariehamn',
      'Minsk': '(UTC+03:00) Minsk',
      'Monaco': '(UTC+01:00) Monaco',
      'Moscow': '(UTC+03:00) Moscow',
      'Nicosia': '(UTC+02:00) Nicosia',
      'Novosibirsk': '(UTC+07:00) Novosibirsk',
      'Omsk': '(UTC+06:00) Omsk',
      'Oslo': '(UTC+01:00) Oslo',
      'Paris': '(UTC+01:00) Paris',
      'Podgorica': '(UTC+01:00) Podgorica',
      'Prague': '(UTC+01:00) Prague',
      'Reykjavik': '(UTC+00:00) Reykjavik',
      'Riga': '(UTC+02:00) Riga',
      'Rome': '(UTC+01:00) Rome',
      'Samara': '(UTC+04:00) Samara',
      'San_Marino': '(UTC+01:00) San_Marino',
      'Sarajevo': '(UTC+01:00) Sarajevo',
      'Shanghai': '(UTC+08:00) Shanghai',
      'Simferopol': '(UTC+03:00) Simferopol',
      'Skopje': '(UTC+01:00) Skopje',
      'Sofia': '(UTC+02:00) Sofia',
      'Stockholm': '(UTC+01:00) Stockholm',
      'Tallinn': '(UTC+02:00) Tallinn',
      'Tirane': '(UTC+01:00) Tirane',
      'Tiraspol': '(UTC+02:00) Tiraspol',
      'Uzhgorod': '(UTC+02:00) Uzhgorod',
      'Vaduz': '(UTC+01:00) Vaduz',
      'Vatican': '(UTC+01:00) Vatican',
      'Vienna': '(UTC+01:00) Vienna',
      'Vilnius': '(UTC+02:00) Vilnius',
      'Vladivostok': '(UTC+10:00) Vladivostok',
      'Volgograd': '(UTC+03:00) Volgograd',
      'Warsaw': '(UTC+01:00) Warsaw',
      'Yekaterinburg': '(UTC+05:00) Yekaterinburg',
      'Zagreb': '(UTC+01:00) Zagreb',
      'Zaporozhye': '(UTC+02:00) Zaporozhye',
      'Zurich': '(UTC+01:00) Zurich',
      'NZ': '(UTC+12:00) NZ',
    };
    return timeZoneConvertedMap;
  }

  static Map<String, String> getTimeZoneMap() {
    final Map<String, String> timeZoneMap = {
      'Amsterdam': 'Europe/Amsterdam',
      'Anadyr': 'Asia/Anadyr',
      'Andorra': 'Europe/Andorra',
      'Athens': 'Europe/Athens',
      'Belfast': 'Europe/Belfast',
      'Belgrade': 'Europe/Belgrade',
      'Berlin': 'Europe/Berlin',
      'Bratislava': 'Europe/Bratislava',
      'Brussels': 'Europe/Brussels',
      'Bucharest': 'Europe/Bucharest',
      'Budapest': 'Europe/Budapest',
      'Buenos_Aires': 'America/Argentina/Buenos_Aires',
      'Busingen': 'Europe/Busingen',
      'Chisinau': 'Europe/Chisinau',
      'Copenhagen': 'Europe/Copenhagen',
      'Dublin': 'Europe/Dublin',
      'Gibraltar': 'Europe/Gibraltar',
      'Guernsey': 'Europe/Guernsey',
      'Helsinki': 'Europe/Helsinki',
      'Irkutsk': 'Asia/Irkutsk',
      'Isle_of_Man': 'Europe/Isle_of_Man',
      'Istanbul': 'Europe/Istanbul',
      'Jersey': 'Europe/Jersey',
      'Kaliningrad': 'Europe/Kaliningrad',
      'Kiev': 'Europe/Kiev',
      'Krasnoyarsk': 'Asia/Krasnoyarsk',
      'Lima': 'America/Lima',
      'Lisbon': 'Europe/Lisbon',
      'Ljubljana': 'Europe/Ljubljana',
      'London': 'Europe/London',
      'Luxembourg': 'Europe/Luxembourg',
      'Madrid': 'Europe/Madrid',
      'Magadan': 'Asia/Magadan',
      'Malta': 'Europe/Malta',
      'Mariehamn': 'Europe/Mariehamn',
      'Minsk': 'Europe/Minsk',
      'Monaco': 'Europe/Monaco',
      'Moscow': 'Europe/Moscow',
      'Nicosia': 'Europe/Nicosia',
      'Novosibirsk': 'Asia/Novosibirsk',
      'Omsk': 'Asia/Omsk',
      'Oslo': 'Europe/Oslo',
      'Paris': 'Europe/Paris',
      'Podgorica': 'Europe/Podgorica',
      'Prague': 'Europe/Prague',
      'Reykjavik': 'Atlantic/Reykjavik',
      'Riga': 'Europe/Riga',
      'Rome': 'Europe/Rome',
      'Samara': 'Europe/Samara',
      'San_Marino': 'Europe/San_Marino',
      'Sarajevo': 'Europe/Sarajevo',
      'Shanghai': 'Asia/Shanghai',
      'Simferopol': 'Europe/Simferopol',
      'Skopje': 'Europe/Skopje',
      'Sofia': 'Europe/Sofia',
      'Stockholm': 'Europe/Stockholm',
      'Tallinn': 'Europe/Tallinn',
      'Tirane': 'Europe/Tirane',
      'Tiraspol': 'Europe/Tiraspol',
      'Uzhgorod': 'Europe/Uzhgorod',
      'Vaduz': 'Europe/Vaduz',
      'Vatican': 'Europe/Vatican',
      'Vienna': 'Europe/Vienna',
      'Vilnius': 'Europe/Vilnius',
      'Vladivostok': 'Asia/Vladivostok',
      'Volgograd': 'Europe/Volgograd',
      'Warsaw': 'Europe/Warsaw',
      'Yekaterinburg': 'Asia/Yekaterinburg',
      'Zagreb': 'Europe/Zagreb',
      'Zaporozhye': 'Europe/Zaporozhye',
      'Zurich': 'Europe/Zurich',
      'NZ': 'NZ',
    };
    return timeZoneMap;
  }

}