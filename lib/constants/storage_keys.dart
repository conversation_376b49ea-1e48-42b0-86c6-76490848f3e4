
class StorageKeys {
  StorageKeys._();
  static const String appData = 'appData';
  static const String appDataEmail = 'email';
  static const String appDataPassword = 'password';
  static const String appDataUserId = 'userId';
  static const String appDataFirstStartUp = 'firstStartUp';
  static const String appDataKeepMeLoggedIn = 'keepMeLoggedIn';

  static const String userData = 'userData';
  static const String userDataTheme = 'theme';
  static const String userDataCountry = 'country';
  static const String userDataLanguage = 'language';
  static const String userDataThingGroup = 'thingGroup';
  static const String userDataThingGroupList = 'thingGroupList';
  static const String userDataThingList = 'thingList';
  static const String userDataHomeList = 'homeList';
  static const String userDataFabricMap = 'fabricMap';
  static const String userDataWiFiMap = 'wifiMap';
  static const String userDataNodeId = 'nodeId';
  static const String userDataFactoryPairingWiFiPassword = 'factoryPairingWiFiPassword';
  static const String userDataControlMode = 'controlMode';

  static const String thingShadowData = 'thingShadowData';

  static const String keypadMode = 'keypadMode';

  static const String pinpointNotificationEnabled = 'push_notifications';

  static const String demoData = 'demoData';
}