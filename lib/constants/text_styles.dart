import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';

/// 定义应用中使用的文本样式
class TextStyles {
  static const String fontFamily = 'Poppins';

  TextStyles._();

  // 基础文本样式
  static const TextStyle regular = TextStyle(
    fontFamily: fontFamily,
    fontWeight: AppFontWeights.regular,
  );
  static const TextStyle medium = TextStyle(
    fontFamily: fontFamily,
    fontWeight: AppFontWeights.medium,
  );
  static const TextStyle semiBold = TextStyle(
    fontFamily: fontFamily,
    fontWeight: AppFontWeights.semiBold,
  );
  static const TextStyle bold = TextStyle(
    fontFamily: fontFamily,
    fontWeight: AppFontWeights.bold,
  );

  // 带有字体大小的文本样式
  static final TextStyle regular12 = regular.copyWith(
    fontSize: 12,
  );
  static final TextStyle regular14 = regular.copyWith(
    fontSize: 14,
  );
  static final TextStyle regular16 = regular.copyWith(
    fontSize: 16,
  );
  static final TextStyle regular18 = regular.copyWith(
    fontSize: 18,
  );
  static final TextStyle regular24 = regular.copyWith(
    fontSize: 24,
  );
  static final TextStyle medium14 = medium.copyWith(
    fontSize: 14,
  );
  static final TextStyle medium16 = medium.copyWith(
    fontSize: 16,
  );
  static final TextStyle medium18 = medium.copyWith(
    fontSize: 18,
  );
  static final TextStyle medium20 = medium.copyWith(
    fontSize: 20,
  );
  static final TextStyle semiBold16 = semiBold.copyWith(
    fontSize: 16,
  );
  static final TextStyle bold14 = bold.copyWith(
    fontSize: 14,
  );
  static final TextStyle bold16 = bold.copyWith(
    fontSize: 16,
  );
  static final TextStyle bold18 = bold.copyWith(
    fontSize: 18,
  );
  static final TextStyle bold20 = bold.copyWith(
    fontSize: 20,
  );

  // 带有颜色的文本样式
  static final TextStyle regular14Dark = regular14.copyWith(
    color: AppColors.ff1C1C1C,
  );
  static final TextStyle regular14Green = regular14.copyWith(
    color: AppColors.ff01A796,
  );
  static final TextStyle regular14Red = regular14.copyWith(
    color: AppColors.ffFF4E4E,
  );
  static final TextStyle regular16Dark = regular16.copyWith(
    color: AppColors.ff1C1C1C,
  );
  static final TextStyle regular16Green = regular16.copyWith(
    color: AppColors.ff01A796,
  );
  static final TextStyle regular16Red = regular16.copyWith(
    color: AppColors.ffFF4E4E,
  );
  static final TextStyle medium14Dark = medium14.copyWith(
    color: AppColors.ff1C1C1C,
  );
  static final TextStyle medium16Dark = medium16.copyWith(
    color: AppColors.ff1C1C1C,
  );
  static final TextStyle semiBold16Dark = semiBold16.copyWith(
    color: AppColors.ff1C1C1C,
  );
  static final TextStyle bold14White = bold14.copyWith(
    color: Colors.white,
  );
  static final TextStyle bold16Green = bold16.copyWith(
    color: AppColors.ff01A796,
  );
  static final TextStyle bold18Dark = bold18.copyWith(
    color: AppColors.ff1C1C1C,
  );
  static final TextStyle bold20Dark = bold20.copyWith(
    color: AppColors.ff1C1C1C,
  );

  // 动态获取主题颜色的文本样式
  static TextStyle get regular14FirstColor => regular14.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get regular16FirstColor => regular16.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get regular18FirstColor => regular18.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get regular24FirstColor => regular24.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get medium14FirstColor => medium14.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get medium16FirstColor => medium16.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get medium18FirstColor => medium18.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get medium20FirstColor => medium20.copyWith(
        color: AppColorsHelper.firstColor,
      );
  static TextStyle get medium14SecondColor => medium14.copyWith(
        color: AppColorsHelper.secondColor,
      );
}
