class AppImagePaths {
  AppImagePaths._();
  static const String habiLogoWhite = 'assets/images/habi_logo_white.svg';
  static const String welcome = 'assets/images/welcome.png';
  static const String boarding = 'assets/images/boarding.png';
  static const String welcomeBedroom = 'assets/images/welcome_bedroom.png';
  static const String countryGb = 'assets/images/country_gb.png';
  static const String backwardArrow = 'assets/images/backward_arrow.svg';
  static const String googleLogo = 'assets/images/google_logo.png';
  static const String appleLogo = 'assets/images/apple_logo.png';
  static const String edit = 'assets/images/edit.svg';
  static const String delete = 'assets/images/delete.svg';
  static const String add = 'assets/images/add.svg';
  static const String navDevices = 'assets/images/nav_devices.svg';
  static const String navHome = 'assets/images/nav_home.svg';
  static const String navAutomations = 'assets/images/nav_automations.svg';
  static const String navNotifications = 'assets/images/nav_notifications.svg';
  static const String navSettings = 'assets/images/nav_settings.svg';
  static const String addDevice = 'assets/images/add_device.svg';
  static const String receiver = 'assets/images/receiver.svg';
  static const String thermostat = 'assets/images/thermostat.svg';
  static const String iconThermostat = 'assets/images/icon_thermostat.svg';
  static const String camera = 'assets/images/camera.svg';
  static const String thermostatAuto = 'assets/images/thermostat_auto.svg';
  static const String thermostatCool = 'assets/images/thermostat_cool.svg';
  static const String thermostatFan = 'assets/images/thermostat_fan.svg';
  static const String thermostatHeat = 'assets/images/thermostat_heat.svg';
  static const String thermostatNoHeat = 'assets/images/thermostat_no_heat.svg';
  static const String thermostatToggleOn = 'assets/images/thermostat_toggle_on.svg';
  static const String thermostatToggleOff = 'assets/images/thermostat_toggle_off.svg';
  static const String deviceSchedule = 'assets/images/device_schedule.svg';
  static const String deviceScheduleEnable = 'assets/images/device_schedule_enable.svg';
  static const String deviceCardThree = 'assets/images/device_card_three.png';
  static const String deviceCardTwo = 'assets/images/device_card_two.png';
  static const String livingRoom = 'assets/images/living_room.png';
  static const String hotWaterSchedule = 'assets/images/hot_water_schedule.svg';
  static const String wifi = 'assets/images/wifi.svg';
  static const String lightOn = 'assets/images/light_on.svg';
  static const String lightOff = 'assets/images/light_off.svg';
  static const String closeSlot = 'assets/images/close_slot.svg';
  static const String batteryFull = 'assets/images/battery_full.svg';
  static const String batteryHalf = 'assets/images/battery_half.svg';
  static const String batteryLow = 'assets/images/battery_low.svg';
  static const String locationSettings = 'assets/images/location_settings.svg';
  static const String arrowRight = 'assets/images/arrow_right.svg';
  static const String arrowRightBlack = 'assets/images/arrow_right_black.svg';
  static const String identify = 'assets/images/identify.svg';
  static const String lock = 'assets/images/lock.svg';
  static const String unlock = 'assets/images/unlock.svg';
  static const String chevronForward = 'assets/images/chevron_forward.svg';
  static const String chevronBack = 'assets/images/chevron_back.svg';
  static const String iconInfo = 'assets/images/icon_info.svg';
  static const String iconSignal0 = "assets/images/icon_signal_0.svg";
  static const String iconSignal1 = "assets/images/icon_signal_1.svg";
  static const String iconSignal2 = "assets/images/icon_signal_2.svg";
  static const String iconSignal3 = "assets/images/icon_signal_3.svg";
  static const String iconSignal4 = "assets/images/icon_signal_4.svg";
  static const String matterQrCodeSticker = 'assets/images/matter_qr_code_sticker.svg';
  static const String boostMode = 'assets/images/boost_mode.svg';
  static const String scheduleMode = 'assets/images/schedule_mode.svg';
  static const String hotWaterTurnOff = 'assets/images/hot_water_turn_off.svg';
  static const String hotWaterTurnOn = 'assets/images/hot_water_turn_on.svg';
  static const String triangleUp = 'assets/images/triangle_up.svg';
  static const String triangleDown = 'assets/images/triangle_down.svg';
  static const String addBlack = 'assets/images/add_black.svg';
  static const String wifiLevelFull = 'assets/images/wifi_level_full.svg';
  static const String wifiLevelMedium = 'assets/images/wifi_level_medium.svg';
  static const String wifiLevelWeak = 'assets/images/wifi_level_weak.svg';
  static const String wifiLevelNo = 'assets/images/wifi_level_no.svg';
  static const String wifiWarning = 'assets/images/wifi_warning.svg';
  static const String wifiWarningBlack = 'assets/images/wifi_warning_black.svg';
  static const String switchOn = 'assets/images/switch_on.svg';
  static const String switchOff = 'assets/images/switch_off.svg';

  static String getCountryFlag(String countryCode) {
    return 'assets/images/flags/${countryCode.toLowerCase()}.png';
  }
}