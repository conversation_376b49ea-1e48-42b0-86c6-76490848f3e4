class ScheduleConfigurationTypes {
  ///0: Schedule is in Gateway
  static const int DISABLE = 0;

  ///1 : ALLDay(Single) Mon- Sun
  static const int FULLWEEK = 1;

  ///2 : 5(Mon-Fir)-2(Sat-Sun)
  static const int WORKWEEK = 2;

  ///3 :7 days(individual)
  static const int DAILY = 3;

  ///4 : 5(Mon-Fir) - 1(Sat) - 1(Sun)
  static const int HOLIDAY = 4;
}

class DayIndex {
  static const int MON = 1;
  static const int TUE = 2;
  static const int WED = 3;
  static const int THU = 4;
  static const int FRI = 5;
  static const int SAT = 6;
  static const int SUN = 7;
}