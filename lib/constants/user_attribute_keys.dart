import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';

/// Constants for user attribute keys used in AWS Cognito
class UserAttributeKeys {
  UserAttributeKeys._();

  // Standard Cognito attributes
  static const email = AuthUserAttributeKey.email;
  static const name = AuthUserAttributeKey.name;
  static const familyName = AuthUserAttributeKey.familyName;
  static const locale = AuthUserAttributeKey.locale;

  // Custom attributes
  static const country = CognitoUserAttributeKey.custom('country');
  static const language = CognitoUserAttributeKey.custom('language');
  static const dataCollection = CognitoUserAttributeKey.custom('is_data_collection');
  static const dataCollectionDateTime = CognitoUserAttributeKey.custom('data_collection_dt');
  static const privacyVersion = CognitoUserAttributeKey.custom('privacy_version');
  static const privacyAcceptDateTime = CognitoUserAttributeKey.custom('privacy_acceptdt');
  static const termsVersion = CognitoUserAttributeKey.custom('terms_version');
  static const termsAcceptDateTime = CognitoUserAttributeKey.custom('terms_acceptdt');
  static const hourFormat = CognitoUserAttributeKey.custom('hour_format');
  static const temperatureUnit = CognitoUserAttributeKey.custom('temperature_unit');
}
