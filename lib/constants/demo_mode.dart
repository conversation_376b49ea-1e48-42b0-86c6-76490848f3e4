const String demoUserId = 'eu-central-1:c8ab9b5d-29d3-41f1-bac4-11a6075be3f4';

const Map<String, List<String>> desiredToReportedMap = {
  'sMDO:sDeviceName': ['sMDO:DeviceName'],
  'sTherS:sHeatingSp': ['sTherS:HeatingSp', 'sTherS:HeatingSp_a'],
  'sTherS:sSystemMode': ['sTherS:SystemMode', 'sTherS:SystemMode_a'],
  'sTimeH:sScheduleEn': ['sTimeH:ScheduleEn'],
  'sTherUIS:sLockKey': ['sTherUIS:LockKey'],
  'sTherO:sDisHeatOD': ['sTherO:DisHeatOD'],
  'sTherS:sTempCalibr': ['sTherS:TempCalibr'],
  'sTimeH:SetScheduleType': ['sTimeH:ScheduleType'],
  'sTimeH:SetSchedule1': ['sTimeH:Schedule1'],
  'sTimeH:SetSchedule2': ['sTimeH:Schedule2'],
  'sTimeH:SetSchedule3': ['sTimeH:Schedule3'],
  'sTimeH:SetSchedule4': ['sTimeH:Schedule4'],
  'sTimeH:SetSchedule5': ['sTimeH:Schedule5'],
  'sTimeH:SetSchedule6': ['sTimeH:Schedule6'],
  'sTimeH:SetSchedule7': ['sTimeH:Schedule7'],
  'sGateway:SetDeviceName': ['sGateway:DeviceName'],
  'sGateway:SetTimeZone': ['sGateway:TimeZone'],
  'sMCtlr:sFabID': ['sMCtlr:FabID'],
  'sMCtlr:sFabRCAC': ['sMCtlr:FabRCAC'],
  'sMCtlr:sFabIPK': ['sMCtlr:FabIPK'],
  'sMCtlr:sFabCreTim': ['sMCtlr:FabCreTim'],
  'sMCtlr:sUserNOC': ['sMCtlr:UserNOC'],
  'sMCtlr:sNodeID': ['sMCtlr:NodeID'],
  'sBoiler:sEnableDHW': ['sBoiler:EnableDHW'],
  'sBoiler:sDHWRelay': ['sBoiler:DHWRelay'],
  'sBoiler:sHoldType': ['sBoiler:HoldType'],
  'sBoiler:sBoostDHW': ['sBoiler:BoostDHW'],
  'sTimeGH:SetScheduleType': ['sTimeGH:ScheduleType'],
  'sTimeGH:SetSchedule1': ['sTimeGH:Schedule1'],
  'sTimeGH:SetSchedule2': ['sTimeGH:Schedule2'],
  'sTimeGH:SetSchedule3': ['sTimeGH:Schedule3'],
  'sTimeGH:SetSchedule4': ['sTimeGH:Schedule4'],
  'sTimeGH:SetSchedule5': ['sTimeGH:Schedule5'],
  'sTimeGH:SetSchedule6': ['sTimeGH:Schedule6'],
  'sTimeGH:SetSchedule7': ['sTimeGH:Schedule7'],
};
