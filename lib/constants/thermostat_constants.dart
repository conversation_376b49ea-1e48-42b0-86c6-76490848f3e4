class ThermostatConstants {
  static final Map<double, int> tempToIndexForCelsiusMap = {
    5.0: 49,
    5.5: 49,
    6.0: 50,
    6.5: 51,
    7.0: 52,
    7.5: 53,
    8.0: 54,
    8.5: 55,
    9.0: 56,
    9.5: 56,

    10.0: 57,
    10.5: 58,
    11.0: 59,
    11.5: 60,
    12.0: 61,
    12.5: 62,
    13.0: 63,
    13.5: 64,
    14.0: 65,
    14.5: 65,

    15.0: 66,
    15.5: 67,
    16.0: 68,
    16.5: 69,
    17.0: 70,
    17.5: 71,
    18.0: 72,
    18.5: 73,
    19.0: 74,
    19.5: 74,

    20.0: 75,
    20.5: 76,
    21.0: 77,
    21.5: 78,
    22.0: 79,
    22.5: 79,
    23.0: 80,
    23.5: 81,
    24.0: 82,
    24.5: 83,

    25.0: 84,
    25.5: 84,
    26.0: 85,
    26.5: 86,
    27.0: 87,
    27.5: 88,
    28.0: 89,
    28.5: 90,
    29.0: 91,
    29.5: 92,

    30.0: 93,
    30.5: 94,
    31.0: 95,
    31.5: 96,
    32.0: 97,
    32.5: 97,
    33.0: 98,
    33.5: 99,
    34.0: 100,
    34.5: 101,
    35.0: 102,
  };

  static final Map<double, int> tempToIndexForScheduleCelsiusMap = {
    5.0: 0,
    5.5: 1,
    6.0: 2,
    6.5: 3,
    7.0: 4,
    7.5: 5,
    8.0: 6,
    8.5: 7,
    9.0: 8,
    9.5: 8,

    10.0: 9,
    10.5: 10,
    11.0: 11,
    11.5: 12,
    12.0: 13,
    12.5: 14,
    13.0: 14,
    13.5: 15,
    14.0: 16,
    14.5: 17,

    15.0: 17,
    15.5: 18,
    16.0: 19,
    16.5: 20,
    17.0: 20,
    17.5: 21,
    18.0: 22,
    18.5: 23,
    19.0: 23,
    19.5: 24,

    20.0: 24,
    20.5: 25,
    21.0: 26,
    21.5: 27,
    22.0: 28,
    22.5: 28,
    23.0: 29,
    23.5: 30,
    24.0: 30,
    24.5: 31,

    25.0: 31,
    25.5: 32,
    26.0: 33,
    26.5: 33,
    27.0: 34,
    27.5: 35,
    28.0: 36,
    28.5: 37,
    29.0: 38,
    29.5: 39,

    30.0: 39,
    30.5: 40,
    31.0: 41,
    31.5: 42,
    32.0: 43,
    32.5: 44,
    33.0: 45,
    33.5: 46,
    34.0: 47,
    34.5: 48,
    35.0: 49,
  };

  static final Map<double, int> tempToIndexForFahrenheitMap = {
    41.0: 49,
    42.0: 49,
    43.0: 50,
    44.0: 51,
    45.0: 52,
    46.0: 53,
    47.0: 54,
    48.0: 55,
    49.0: 56,
    50.0: 57,

    51.0: 58,
    52.0: 59,
    53.0: 60,
    54.0: 61,
    55.0: 62,
    56.0: 63,
    57.0: 64,
    58.0: 65,
    59.0: 66,
    60.0: 67,

    61.0: 68,
    62.0: 69,
    63.0: 70,
    64.0: 71,
    65.0: 72,
    66.0: 73,
    67.0: 74,
    68.0: 75,
    69.0: 76,
    70.0: 77,

    71.0: 78,
    72.0: 79,
    73.0: 80,
    74.0: 81,
    75.0: 82,
    76.0: 83,
    77.0: 84,
    78.0: 85,
    79.0: 86,
    80.0: 87,

    81.0: 88,
    82.0: 89,
    83.0: 90,
    84.0: 91,
    85.0: 92,
    86.0: 93,
    87.0: 94,
    88.0: 95,
    89.0: 96,
    90.0: 97,

    91.0: 98,
    92.0: 99,
    93.0: 100,
    94.0: 101,
    95.0: 102,
  };

  static final Map<double, int> tempToIndexForScheduleFahrenheitMap = {
    41.0: 0,
    42.0: 1,
    43.0: 2,
    44.0: 3,
    45.0: 4,
    46.0: 5,
    47.0: 6,
    48.0: 7,
    49.0: 8,
    50.0: 9,

    51.0: 9,
    52.0: 10,
    53.0: 11,
    54.0: 12,
    55.0: 13,
    56.0: 14,
    57.0: 15,
    58.0: 16,
    59.0: 17,
    60.0: 17,

    61.0: 18,
    62.0: 19,
    63.0: 20,
    64.0: 21,
    65.0: 22,
    66.0: 23,
    67.0: 24,
    68.0: 24,
    69.0: 25,
    70.0: 26,

    71.0: 27,
    72.0: 28,
    73.0: 29,
    74.0: 30,
    75.0: 31,
    76.0: 31,
    77.0: 32,
    78.0: 33,
    79.0: 34,
    80.0: 35,

    81.0: 36,
    82.0: 36,
    83.0: 37,
    84.0: 38,
    85.0: 39,
    86.0: 40,
    87.0: 41,
    88.0: 42,
    89.0: 43,
    90.0: 44,

    91.0: 45,
    92.0: 46,
    93.0: 47,
    94.0: 48,
    95.0: 49,
  };
}