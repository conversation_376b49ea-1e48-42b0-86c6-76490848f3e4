class AppStatusCode {
  static const int parsingQrcodeError = -2;
  static const int unableToCreateMatterClientError = -50;
  static const int unableToGetThingShadow = -51;
  static const int datasetCannotBeEmptyError = -52;
  static const int parsingDatasetFailedError = -53;
  static const int fabricIdCannotBeEmptyError = -54;
  static const int wifiMismatchError = -55;
  static const int failedToGenerateThingName = -56;
  static const int nodeIdVerificationDidNotPass = -57;
  static const int baIdCannotBeEmptyError = -58;
  static const int unableToParseThingShadow = -59;
  static const int deviceShadowIsNotPresent = -60;
  static const int timeoutError = -61;
  static const int extendedAddressCannotBeEmptyError = -62;
  static const int writeCatValueError = -63;
  static const int publishNodeIdToCloudError = -64;
  static const int readVidError = -65;
  static const int readPidError = -66;
  static const int receiverIsOffline = -67;
  static const int notConnectedToWiFi = -68;
}