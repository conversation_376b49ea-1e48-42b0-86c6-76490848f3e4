enum DataCollectionStatus {
  on("ON"),
  off("OFF");

  const DataCollectionStatus(this.value);

  final String value;

  static DataCollectionStatus fromValue(String? value) {
    return DataCollectionStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => DataCollectionStatus.off,
    );
  }
}

enum HourFormatStatus {
  format24h("24H"),
  format12h("12H");

  const HourFormatStatus(this.value);

  final String value;

  static HourFormatStatus fromValue(String? value) {
    return HourFormatStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => HourFormatStatus.format24h,
    );
  }
}

enum TemperatureUnitStatus {
  celsius("C"),
  fahrenheit("F");

  const TemperatureUnitStatus(this.value);

  final String value;

  static TemperatureUnitStatus fromValue(String? value) {
    return TemperatureUnitStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => TemperatureUnitStatus.celsius,
    );
  }
}
