import 'dart:convert';
import 'package:amazon_cognito_identity_dart_2/sig_v4.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

class HBMqttClient {

  MqttClient? client;
  String? thingName;
  List<String>? thingNames;
  int autoReconnectCount = 0;

  Future<void> init(String thingName) async {
    this.thingName = thingName;

    final data = await AuthService.to.fetchCognitoAuthSession();
    final accessKey = data.credentialsResult.value.accessKeyId;
    final secretKey = data.credentialsResult.value.secretAccessKey;
    final sessionToken = data.credentialsResult.value.sessionToken!;
    final identityId = data.identityIdResult.value;
    final GlobalService service = GlobalService.to;

    log.d('MQTT config: ${service.config}');

    final signedUrl = getWebSocketURL(
      accessKey: accessKey,
      secretKey: secretKey,
      sessionToken: sessionToken,
      region: service.config.aws.region,
      scheme: service.config.aws.mqttScheme,
      endpoint: service.config.aws.endpointUrl,
      urlPath: service.config.aws.mqttUrlPath,
    );

    log.d('MQTT signedUrl: $signedUrl');

    client = create(signedUrl, identityId, thingName);
  }

  MqttClient create(String serverAddress, String uniqueID, String thingName) {
    log.d('MQTT uniqueID:$uniqueID, thingName:$thingName');
    final MqttServerClient client = MqttServerClient(serverAddress, uniqueID);
    client.useWebSocket = true;
    client.connectTimeoutPeriod = 1000;
    // Set the protocol to V3.1.1 for AWS IoT Core, if you fail to do this you
    // will not receive a connect ack with the response code
    client.setProtocolV311();
    client.logging(on: false);
    client.port = GlobalService.to.config.aws.mqttPort;
    client.autoReconnect = true;
    client.disconnectOnNoResponsePeriod = 1;
    client.resubscribeOnAutoReconnect = true;
    client.keepAlivePeriod = 1200;
    client.onDisconnected = _onDisconnected;
    client.onConnected = _onConnected;
    client.onSubscribed = _onSubscribed;
    client.onSubscribeFail = _onSubscribeFail;
    client.onUnsubscribed = _onUnsubscribed;
    client.onAutoReconnect = _onAutoReconnect;
    client.onAutoReconnected = _onAutoReconnected;

    final MqttConnectMessage connMess = MqttConnectMessage()
        .withClientIdentifier('$thingName-${DateTime.now().toString()}')
        .startClean();

    client.connectionMessage = connMess;

    return client;
  }

  Future<void> connect() async {
    await client?.connect();
  }

  Future<void> reconnect() async {
    log.d('MQTT reconnect: ${client?.connectionStatus?.state}');
  }

  void disconnect() {
    log.d('MQTT disconnect');
    client?.autoReconnect = false;
    client?.resubscribeOnAutoReconnect = false;
    client?.disconnect();
    client = null;
    autoReconnectCount = 0;
    thingNames?.clear();
    thingNames = null;
  }

  void subscribeAllThings(List<String> thingNames) {
    log.d('MQTT subscribing all topic: $thingNames');
    this.thingNames = thingNames;
    for (final thingName in thingNames) {
      subscribe(thingName);
    }
  }

  void unsubscribeAllThings() {
    log.d('MQTT unsubscribing all topic: ${this.thingNames}');
    List<String> thingNames = this.thingNames!;
    for (final thingName in thingNames) {
      unsubscribe(thingName);
    }
  }

  void subscribe(String thingName) {
    log.d('MQTT subscribe thingName: $thingName');
    final state = client?.connectionStatus?.state ?? MqttConnectionState.disconnected;
    if (state == MqttConnectionState.connected) {
      String topic = getSubscribeTopic(thingName);
      log.d('MQTT subscribe topic: $topic');
      try {
        client?.subscribe(topic, MqttQos.atLeastOnce);
        client?.updates?.listen((List<MqttReceivedMessage<MqttMessage>> c) async {
          if (c.isEmpty) {
            log.e('MQTT no message.');
            return;
          }
          final recTopic = c[0].topic;
          final recMessage = c[0].payload as MqttPublishMessage;
          final thingName = getThingNameByTopic(recTopic);
          final thingShadow = jsonDecode(utf8.decode(recMessage.payload.message));
          log.i('MQTT ThingName: $thingName');
          log.i('MQTT ThingShadow: $thingShadow');
          GlobalService.to.getMqttStreamController()
              .add(AppShadow(thingName: thingName, thingShadow: thingShadow));
        });
      } catch (e) {
        log.e('MQTT subscribe error: $thingName, $e', stackTrace: StackTrace.current);
      }
    }
  }

  void unsubscribe(String thingName) {
    log.d('MQTT unsubscribe thingName: $thingName');
    final state = client?.connectionStatus?.state ?? MqttConnectionState.disconnected;
    if (state == MqttConnectionState.connected) {
      String topic = getSubscribeTopic(thingName);
      log.d('MQTT unsubscribe topic: $topic');
      try {
        client?.unsubscribe(topic);
      } catch (e) {
        log.e('MQTT unsubscribe error: $thingName, $e', stackTrace: StackTrace.current);
      }
    }
  }

  void _onAutoReconnect() {
    log.d('MQTT Auto Reconnect');
  }

  void _onAutoReconnected() {
    log.d('MQTT Auto Reconnected');
  }

  void _onConnected() {
    log.d('MQTT Connected');
  }

  void _onDisconnected() {
    log.d('MQTT Disconnected');
  }

  void _onSubscribed(String topic) {
    log.d('MQTT Subscribed');
  }

  void _onSubscribeFail(String topic) {
    log.d('MQTT Subscribe Fail');
  }

  void _onUnsubscribed(String? topic) {
    log.d('MQTT Unsubscribed');
  }

  // SigV4 Sign and create the AWS IOT MQTT Websocket URL
  String getWebSocketURL({
    required String accessKey,
    required String secretKey,
    required String sessionToken,
    required String region,
    required String scheme,
    required String endpoint,
    required String urlPath,
  }) {
    const serviceName = 'iotdevicegateway';
    const awsS4Request = 'aws4_request';
    const aws4HmacSha256 = 'AWS4-HMAC-SHA256';
    var now = SigV4.generateDatetime();

    var creds = [
      accessKey,
      now.substring(0, 8),
      region,
      serviceName,
      awsS4Request,
    ];

    var queryParams = {
      'X-Amz-Algorithm': aws4HmacSha256,
      'X-Amz-Credential': creds.join('/'),
      'X-Amz-Date': now,
      'X-Amz-SignedHeaders': 'host',
    };

    var canonicalQueryString = SigV4.buildCanonicalQueryString(queryParams);

    var request = SigV4.buildCanonicalRequest(
      'GET',
      urlPath,
      queryParams,
      Map.from({'host': endpoint}),
      '',
    );

    var hashedCanonicalRequest = SigV4.hashCanonicalRequest(request);
    var stringToSign = SigV4.buildStringToSign(
      now,
      SigV4.buildCredentialScope(now, region, serviceName),
      hashedCanonicalRequest,
    );

    var signingKey = SigV4.calculateSigningKey(
      secretKey,
      now,
      region,
      serviceName,
    );

    var signature = SigV4.calculateSignature(signingKey, stringToSign);

    var finalParams =
        '$canonicalQueryString&X-Amz-Signature=$signature&X-Amz-Security-Token=${
        Uri.encodeComponent(sessionToken)}';

    return '$scheme$endpoint$urlPath?$finalParams';
  }

  String getSubscribeTopic(String thingName, [String filter = "update/accepted",]) {
    return "\$aws/things/$thingName/shadow/$filter";
  }

  String getThingNameByTopic(String topic) {
    return topic.split("/")[2];
  }

}
