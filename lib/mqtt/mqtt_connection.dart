import 'dart:async';
import 'dart:convert';

import 'package:amazon_cognito_identity_dart_2/sig_v4.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/thing_shadow_service.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:uuid/uuid.dart';

/// 管理与 AWS IoT Core 的 MQTT 连接，处理设备 Shadow 的订阅和更新
class MqttConnection {
  final String gatewayId;
  final Set<String> devices;
  final AuthService authService;
  final GlobalService globalService;
  final ThingShadowService thingShadowService;
  final DeviceShadowService deviceShadowService;

  MqttConnection(
      this.gatewayId,
      this.devices, {
        required this.authService,
        required this.globalService,
        required this.thingShadowService,
        required this.deviceShadowService,
      });

  static const int _mqttReconnectDelaySeconds = 5;
  static const int _httpPollIntervalSeconds = 6;
  static const int _maxHttpRetryAttempts = 3;
  static const Uuid _uuid = Uuid();

  String? _connectionId;
  MqttServerClient? _client;
  StreamSubscription? _messageSubscription;
  String? _pollDeviceShadowTimerId;
  String? _fetchDeviceShadowTimerId;
  String? _reconnectTimerId;
  Timer? _pollDeviceShadowTimer;
  Timer? _fetchDeviceShadowTimer;
  Timer? _reconnectTimer;
  bool _isCleanedUp = false;
  final _fetchedShadowDevices = <String>{};

  bool get isConnected =>
      _client?.connectionStatus?.state == MqttConnectionState.connected;

  int get deviceCount => devices.length;

  String get _logTag => '[MqttConnection] $_connectionId';

  /// 创建 MQTT 连接
  Future<void> create() async {
    try {
      _connectionId = _generateConnectionId();
      _client = await _connectClient();
      _subscribeAllDevices();
      _messageSubscription = _setupMessageHandler();
      _onConnectSuccess();
    } catch (e, stackTrace) {
      log.e('$_logTag 创建连接失败', error: e, stackTrace: stackTrace);
      _onConnectFailure();
    }
  }

  /// 生成连接 ID
  String _generateConnectionId() {
    return '$gatewayId-${_uuid.v4()}';
  }

  /// 创建 MQTT 客户端并连接
  Future<MqttServerClient> _connectClient() async {
    final data = await authService.fetchCognitoAuthSession();
    final config = globalService.config;

    final signedUrl = _getWebSocketURL(
      accessKey: data.credentialsResult.value.accessKeyId,
      secretKey: data.credentialsResult.value.secretAccessKey,
      sessionToken: data.credentialsResult.value.sessionToken!,
      region: config.aws.region,
      scheme: config.aws.mqttScheme,
      endpoint: config.aws.endpointUrl,
      urlPath: config.aws.mqttUrlPath,
    );

    final client = MqttServerClient(signedUrl, _connectionId!);

    try {
      client.useWebSocket = true;
      client.port = config.aws.mqttPort;
      client.keepAlivePeriod = 1200;
      client.connectTimeoutPeriod = 1000;
      client.disconnectOnNoResponsePeriod = 1;
      client.autoReconnect = false; // 使用自定义的重连机制
      client.resubscribeOnAutoReconnect = false;
      client.logging(on: false);
      client.setProtocolV311();

      client.onDisconnected = () => _handleDisconnection();
      client.onConnected = () => _handleConnection();

      client.connectionMessage = MqttConnectMessage()
          .withClientIdentifier(_connectionId!)
          .startClean();

      await client.connect();

      if (client.connectionStatus?.state != MqttConnectionState.connected) {
        throw Exception('连接状态无效');
      }

      if (_isCleanedUp) {
        throw Exception('连接已被清理');
      }
    } catch (e) {
      client.disconnect();
      rethrow;
    }

    return client;
  }

  /// 订阅所有设备
  void _subscribeAllDevices() {
    log.i('$_logTag 订阅所有设备，设备数量: ${devices.length}');

    for (final deviceId in devices) {
      _subscribeDevice(deviceId);
    }
  }

  /// 设置 MQTT 消息处理器
  StreamSubscription _setupMessageHandler() {
    if (_client!.updates == null) {
      throw Exception('客户端消息流未初始化');
    }

    return _client!.updates!.listen(
          (List<MqttReceivedMessage<MqttMessage>> messages) {
        for (final msg in messages) {
          _handleMqttMessage(msg);
        }
      },
    );
  }

  /// 处理接收到的 MQTT 消息
  void _handleMqttMessage(MqttReceivedMessage<MqttMessage> msg) {
    try {
      final topic = msg.topic;
      if (!topic.endsWith('/shadow/update/accepted')) {
        log.w('$_logTag 收到非 Shadow 更新消息 - Topic: $topic');
        return;
      }

      final deviceId = _extractDeviceIdFromTopic(topic);
      if (!devices.contains(deviceId)) {
        log.w('$_logTag 收到未知设备的消息 - Device ID: $deviceId');
        return;
      }

      final recMess = msg.payload as MqttPublishMessage;
      final payload =
      MqttPublishPayload.bytesToStringAsString(recMess.payload.message);
      final data = json.decode(payload);
      log.w('$_logTag 收到设备的消息 - Device ID: $deviceId - Data: $data');

      deviceShadowService.handleMqttShadowUpdated(deviceId, data);

      globalService
          .getRealTimeMqttStreamController()
          .add(AppShadow(thingName: deviceId, thingShadow: data));
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 处理 MQTT 消息失败',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 从 Topic 中提取设备 ID
  String _extractDeviceIdFromTopic(String topic) {
    return topic.split('/')[2];
  }

  /// 生成签名的 WebSocket URL
  String _getWebSocketURL({
    required String accessKey,
    required String secretKey,
    required String sessionToken,
    required String region,
    required String scheme,
    required String endpoint,
    required String urlPath,
  }) {
    const serviceName = 'iotdevicegateway';
    const awsS4Request = 'aws4_request';
    const aws4HmacSha256 = 'AWS4-HMAC-SHA256';
    final now = SigV4.generateDatetime();

    final creds = [
      accessKey,
      now.substring(0, 8),
      region,
      serviceName,
      awsS4Request,
    ];

    final queryParams = {
      'X-Amz-Algorithm': aws4HmacSha256,
      'X-Amz-Credential': creds.join('/'),
      'X-Amz-Date': now,
      'X-Amz-SignedHeaders': 'host',
    };

    final canonicalQueryString = SigV4.buildCanonicalQueryString(queryParams);

    final request = SigV4.buildCanonicalRequest(
      'GET',
      urlPath,
      queryParams,
      Map.from({'host': endpoint}),
      '',
    );

    final hashedCanonicalRequest = SigV4.hashCanonicalRequest(request);
    final stringToSign = SigV4.buildStringToSign(
      now,
      SigV4.buildCredentialScope(now, region, serviceName),
      hashedCanonicalRequest,
    );

    final signingKey = SigV4.calculateSigningKey(
      secretKey,
      now,
      region,
      serviceName,
    );

    final signature = SigV4.calculateSignature(signingKey, stringToSign);

    final finalParams =
        '$canonicalQueryString&X-Amz-Signature=$signature&X-Amz-Security-Token=${Uri.encodeComponent(sessionToken)}';

    return '$scheme$endpoint$urlPath?$finalParams';
  }

  /// 处理 MQTT 连接断开事件
  void _handleDisconnection() {
    log.w('$_logTag 连接已断开');
    _onConnectFailure();
  }

  /// 处理 MQTT 连接建立事件
  Future<void> _handleConnection() async {
    log.i('$_logTag 连接已建立');
  }

  /// 执行连接成功的后续操作
  void _onConnectSuccess() {
    log.i('$_logTag 启动连接成功流程');
    _cancelReconnectTimer();
    _cancelPollDeviceShadowTimer();
    _fetchedShadowDevices.clear();
    _createFetchDeviceShadowTimer(duration: Duration.zero);
  }

  /// 执行连接失败的后续操作
  void _onConnectFailure() {
    log.i('$_logTag 启动连接失败流程');
    _cancelMessageSubscription();
    _disconnectClient();
    _cancelFetchDeviceShadowTimer();
    _createReconnectTimer();
    _createPollDeviceShadowTimer(isInitial: true);
  }

  /// 创建获取设备 Shadow 定时器
  void _createFetchDeviceShadowTimer({Duration? duration}) {
    if (_isCleanedUp) {
      log.i('$_logTag 连接已被清理，停止创建获取设备 Shadow 定时器');
      return;
    }
    if (_fetchDeviceShadowTimerId != null) {
      log.i('$_logTag 获取设备 Shadow 定时器已存在，停止创建');
      return;
    }
    _fetchDeviceShadowTimer?.cancel();

    final timerId = _uuid.v4();
    _fetchDeviceShadowTimerId = timerId;
    _fetchDeviceShadowTimer = Timer(
      duration ?? const Duration(seconds: 1),
          () async {
        if (_isCleanedUp) {
          log.d('$_logTag 连接已被清理，停止获取设备 Shadow');
          return;
        }
        if (_fetchDeviceShadowTimerId != timerId) {
          log.d('$_logTag 获取设备 Shadow 定时器已被清理，停止获取设备 Shadow');
          return;
        }

        final unfetchedDevices = devices
            .where((deviceId) => !_fetchedShadowDevices.contains(deviceId))
            .toList();

        if (unfetchedDevices.isEmpty) {
          log.i('$_logTag 所有设备的 Shadow 都已获取，停止获取设备 Shadow');
          _cancelFetchDeviceShadowTimer();
          return;
        }

        bool isSuccess = false;
        try {
          log.i('$_logTag 未获取设备 Shadow 的设备: $unfetchedDevices');

          await Future.wait(
            unfetchedDevices.map((deviceId) async {
              await _fetchDeviceShadow(deviceId, maxRetries: 0);
            }),
          );

          isSuccess = true;
        } catch (e) {
          log.e('$_logTag 获取设备 Shadow 失败', error: e);
        }

        if (_isCleanedUp) {
          log.d('$_logTag 连接已被清理，停止获取设备 Shadow');
          return;
        }
        if (_fetchDeviceShadowTimerId != timerId) {
          log.d('$_logTag 获取设备 Shadow 定时器已被清理，停止获取设备 Shadow');
          return;
        }

        _cancelFetchDeviceShadowTimer();
        _createFetchDeviceShadowTimer(
          duration: isSuccess ? Duration.zero : null,
        );
      },
    );
  }

  /// 取消获取设备 Shadow 定时器
  void _cancelFetchDeviceShadowTimer() {
    _fetchDeviceShadowTimer?.cancel();
    _fetchDeviceShadowTimer = null;
    _fetchDeviceShadowTimerId = null;
  }

  /// 创建重连定时器
  void _createReconnectTimer() {
    if (_isCleanedUp) {
      log.i('$_logTag 连接已被清理，停止创建重连定时器');
      return;
    }
    if (_reconnectTimerId != null) {
      log.i('$_logTag 重连定时器已存在，停止创建');
      return;
    }
    _reconnectTimer?.cancel();

    final timerId = _uuid.v4();
    _reconnectTimerId = timerId;
    _reconnectTimer = Timer(
      const Duration(seconds: _mqttReconnectDelaySeconds),
          () async {
        if (_isCleanedUp) {
          log.i('$_logTag 连接已被清理，停止重连');
          return;
        }
        if (_reconnectTimerId != timerId) {
          log.i('$_logTag 重连定时器已被清理，停止重连');
          return;
        }

        try {
          log.i('$_logTag 尝试重连');
          await create();
        } catch (e, stackTrace) {
          log.e('$_logTag 重连失败', error: e, stackTrace: stackTrace);
        }

        if (_isCleanedUp) {
          log.i('$_logTag 连接已被清理，停止重连');
          return;
        }
        if (_reconnectTimerId != timerId) {
          log.i('$_logTag 重连定时器已被清理，停止重连');
          return;
        }

        _cancelReconnectTimer();
        _createReconnectTimer();
      },
    );
  }

  /// 取消重连定时器
  void _cancelReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    _reconnectTimerId = null;
  }

  /// 创建轮询设备 Shadow 定时器
  void _createPollDeviceShadowTimer({bool isInitial = false}) {
    if (_isCleanedUp) {
      log.i('$_logTag 连接已被清理，停止创建轮询设备 Shadow 定时器');
      return;
    }
    if (_pollDeviceShadowTimerId != null) {
      log.i('$_logTag 轮询设备 Shadow 定时器已存在，停止创建');
      return;
    }
    _pollDeviceShadowTimer?.cancel();

    final timerId = _uuid.v4();
    _pollDeviceShadowTimerId = timerId;
    _pollDeviceShadowTimer = Timer(
      isInitial
          ? Duration.zero
          : const Duration(seconds: _httpPollIntervalSeconds),
          () async {
        if (_isCleanedUp) {
          log.d('$_logTag 连接已被清理，停止轮询设备 Shadow');
          return;
        }
        if (_pollDeviceShadowTimerId != timerId) {
          log.d('$_logTag 轮询设备 Shadow 定时器已被清理，停止获取设备 Shadow');
          return;
        }

        final deviceIds = isInitial
            ? devices.where((id) => !_fetchedShadowDevices.contains(id)).toSet()
            : devices;

        log.d(
          '$_logTag 轮询设备 Shadow - Initial: $isInitial, Device Count: ${deviceIds.length}',
        );

        if (deviceIds.isNotEmpty) {
          try {
            await Future.wait(
              deviceIds.map((deviceId) => _fetchDeviceShadow(deviceId)),
            );
          } catch (e) {
            log.e('$_logTag 轮询设备 Shadow 失败', error: e);
          }

          if (_isCleanedUp) {
            log.d('$_logTag 连接已被清理，停止轮询设备 Shadow');
            return;
          }
          if (_pollDeviceShadowTimerId != timerId) {
            log.d('$_logTag 轮询设备 Shadow 定时器已被清理，停止获取设备 Shadow');
            return;
          }
        }

        _cancelPollDeviceShadowTimer();
        _createPollDeviceShadowTimer();
      },
    );
  }

  /// 取消轮询设备 Shadow 定时器
  void _cancelPollDeviceShadowTimer() {
    _pollDeviceShadowTimer?.cancel();
    _pollDeviceShadowTimer = null;
    _pollDeviceShadowTimerId = null;
  }

  /// 添加设备并订阅
  void addDevice(String deviceId) async {
    log.i('$_logTag 添加设备 - Device ID: $deviceId');

    if (devices.add(deviceId)) {
      if (!isConnected) {
        log.i('$_logTag 设备已添加，无需订阅');
        return;
      }

      try {
        _subscribeDevice(deviceId);
        _createFetchDeviceShadowTimer(duration: Duration.zero);
        log.i('$_logTag 设备已添加并订阅成功');
      } catch (e) {
        log.e('$_logTag 设备已添加但订阅失败，触发连接失败', error: e);
        _onConnectFailure();
      }
    }
  }

  /// 重新获取设备 Shadow
  void reFetchDeviceShadow(String deviceId) async {
    log.i('$_logTag 重新获取设备 Shadow - Device ID: $deviceId');

    if (!isConnected) {
      log.i('$_logTag 连接未就绪，无法重新获取设备 Shadow - Device ID: $deviceId');
      return;
    }

    if (_fetchedShadowDevices.remove(deviceId)) {
      _createFetchDeviceShadowTimer(duration: Duration.zero);
    }
  }

  /// 移除设备并取消订阅
  void removeDevice(String deviceId) async {
    log.i('$_logTag 移除设备 - Device ID: $deviceId');

    if (devices.remove(deviceId)) {
      _fetchedShadowDevices.remove(deviceId);

      if (!isConnected) {
        log.i('$_logTag 设备已移除，无需取消订阅');
        return;
      }

      try {
        _unsubscribeDevice(deviceId);
        log.i('$_logTag 设备已移除并取消订阅成功');
      } catch (e) {
        log.e('$_logTag 设备已移除但取消订阅失败，触发连接失败', error: e);
        _onConnectFailure();
      }
    }
  }

  /// 获取 Shadow 更新 Topic
  String _getDeviceShadowUpdateTopic(String deviceId) {
    return '\$aws/things/$deviceId/shadow/update';
  }

  /// 获取 Shadow 更新确认 Topic
  String _getDeviceShadowUpdateAcceptedTopic(String deviceId) {
    return '\$aws/things/$deviceId/shadow/update/accepted';
  }

  /// 订阅设备
  void _subscribeDevice(String deviceId) {
    log.i('$_logTag 订阅设备 - Device ID: $deviceId');

    try {
      if (!isConnected) throw Exception('连接未就绪');

      final topic = _getDeviceShadowUpdateAcceptedTopic(deviceId);
      _client!.subscribe(topic, MqttQos.atLeastOnce);
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 订阅设备 - Device ID: $deviceId 失败',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 取消订阅设备
  void _unsubscribeDevice(String deviceId) {
    log.i('$_logTag 取消订阅设备 - Device ID: $deviceId');

    try {
      if (!isConnected) throw Exception('连接未就绪');

      final topic = _getDeviceShadowUpdateAcceptedTopic(deviceId);
      _client!.unsubscribe(topic);
    } catch (e, stackTrace) {
      log.e(
        '$_logTag 取消订阅设备 - Device ID: $deviceId 失败',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 断开 MQTT 连接
  void _disconnectClient() {
    if (_client == null) {
      log.i('$_logTag 客户端未初始化，无需断开连接');
      return;
    }

    try {
      final client = _client!;
      _client = null;
      client.disconnect();
      log.i('$_logTag 断开连接成功');
    } catch (e, stackTrace) {
      log.e('$_logTag 断开连接失败', error: e, stackTrace: stackTrace);
    }
  }

  /// 取消 MQTT 消息订阅
  void _cancelMessageSubscription() {
    _messageSubscription?.cancel();
    _messageSubscription = null;
  }

  /// 获取设备 Shadow
  Future<void> _fetchDeviceShadow(
      String deviceId, {
        int maxRetries = _maxHttpRetryAttempts,
      }) async {
    await deviceShadowService.fetchDeviceShadow(
      deviceId,
      maxRetries: maxRetries,
    );
    _fetchedShadowDevices.add(deviceId); // 标记设备已获取设备 Shadow
  }

  /// 更新设备属性
  Future<void> updateProperties(
      String deviceId,
      Map<String, dynamic> payload,
      ) async {
    if (!isConnected) {
      log.w('$_logTag 连接未就绪，无法更新设备属性');
      throw Exception('连接未就绪');
    }

    final topic = _getDeviceShadowUpdateTopic(deviceId);
    final builder = MqttClientPayloadBuilder();
    builder.addUTF8String(json.encode(payload));
    _client!.publishMessage(topic, MqttQos.atLeastOnce, builder.payload!);
  }

  /// 清理连接资源
  void cleanup() async {
    log.d('$_logTag 清理连接');

    if (_isCleanedUp) {
      log.i('$_logTag 连接已被清理，停止清理');
      return;
    }

    _isCleanedUp = true;
    _cancelReconnectTimer();
    _cancelPollDeviceShadowTimer();
    _cancelFetchDeviceShadowTimer();
    _cancelMessageSubscription();
    _disconnectClient();
    devices.clear();
    _fetchedShadowDevices.clear();
  }
}
