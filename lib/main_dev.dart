import 'dart:async';
import 'package:amplify_push_notifications_pinpoint/amplify_push_notifications_pinpoint.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:habi_app/constants/storage_keys.dart';
import 'package:habi_app/helpers/app_theme_helper.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/i18n/app_translations.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_config.dart';
import 'package:habi_app/models/app_route.dart';
import 'package:habi_app/routes/app_pages.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_notifications_service.dart';
import 'package:habi_app/utility/sentry_utils.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:get_storage/get_storage.dart';
import 'initial_bindings.dart';

void main() async {
  try {
    runZonedGuarded(
      () async {
        WidgetsFlutterBinding.ensureInitialized();

        FlutterError.onError = (FlutterErrorDetails details) {
          debugPrint('FlutterError.onError(), error:${details.exception}, '
              'stackTrace:${details.stack}');
        };

        // get configuration file
        final appConfig = await AppConfig.forEnvironment('dev');

        // initialize Amplify
        appConfig.amplifyConfigured = await configureAmplify(appConfig);

        // initialize GetStorage
        await GetStorage.init(StorageKeys.appData);

        final appThemeMode = await getThemeMode();

        // get translations
        final translations = AppTranslations();
        await translations.load();

        tz.initializeTimeZones();

        await initializeLocalNotifications();

        if (kDebugMode) {
          runApp(MyApp(translations, appConfig, appThemeMode));
        } else {
          try {
            await SentryFlutter.init((options) {
                options.dsn = 'https://<EMAIL>/4508843463278592';
                // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
                // We recommend adjusting this value in production.
                options.tracesSampleRate = 1.0;
                // The sampling rate for profiling is relative to tracesSampleRate
                // Setting to 1.0 will profile 100% of sampled transactions:
                options.profilesSampleRate = 1.0;
                options.debug = false;
                options.diagnosticLevel = SentryLevel.debug;
              },
              appRunner: () => runApp(
                SentryWidget(
                  child: MyApp(translations, appConfig, appThemeMode),
                ),
              ),
            );
          } catch (error, stackTrace) {
            debugPrint('SentryFlutter.init(), error:$error, stackTrace: $stackTrace');
            runApp(MyApp(translations, appConfig, appThemeMode));
          }
        }

      },
      (error, stackTrace) {
        debugPrint('runZonedGuarded(), error:$error, stackTrace: $stackTrace');
        SentryUtils.captureException(error, stackTrace: stackTrace);
      },
    );
  } catch (error, stackTrace) {
    debugPrint('Main(), error:$error, stackTrace: $stackTrace');
  }
}

Future<String> getThemeMode() async {
  var appDataBox = GetStorage(StorageKeys.appData);
  var email = appDataBox.read(StorageKeys.appDataEmail);
  var defaultTheme = 'light';
  if (email != null) {
    String name = '${StorageKeys.userData}_$email';
    await GetStorage.init(name);
    var userDataBox = GetStorage(name);
    var theme = userDataBox.read(StorageKeys.userDataTheme) ?? defaultTheme;
    return theme;
  }
  return defaultTheme;
}

Future<bool> configureAmplify(AppConfig appConfig) async {
  try {
    await Amplify.addPlugins([
      AmplifyAuthCognito(),
      if (!kIsWeb) AmplifyPushNotificationsPinpoint(),
    ]);
    await Amplify.configure(appConfig.amplify);
    return true;
  } on Exception catch (e) {
    debugPrint('An error occurred configuring Amplify: $e');
    return false;
  }
}

Future<void> initializeLocalNotifications() async {
  try {
    await LocalNotificationsService().initialize();
    debugPrint('Local notifications initialized');
  } catch (e) {
    debugPrint('Failed to initialize local notifications: $e');
  }
}

class MyApp extends StatelessWidget {

  final Translations translations;
  final AppConfig appConfig;
  final String appThemeMode;

  const MyApp(this.translations, this.appConfig, this.appThemeMode,
      {super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return GestureDetector(
      onTap: () => KeyboardHelper.dismissKeyboard(context),
      child: ScreenUtilInit(
        designSize: const Size(414, 896),
        splitScreenMode: true,
        child: GetMaterialApp(
          routingCallback: (routing) {
            debugPrint('routing: ${routing?.current}');
            var globalService = GlobalService.to;
            globalService
                .getRouteStreamController()
                .add(AppRoute(name: routing?.current));
          },
          title: 'habi Smart Home',
          theme: appThemeMode == 'dark' ? appDarkTheme() : appLightTheme(),
          initialBinding: InitialBindings(appConfig),
          initialRoute: AppPages.initial,
          getPages: AppPages.routes,
          translations: translations,
          locale: Get.deviceLocale,
          fallbackLocale: const Locale.fromSubtags(languageCode: 'en'),
          logWriterCallback: _logWriterCallback,
        ),
      ),
    );
  }

  void _logWriterCallback(String text, {bool isError = false}) {
    final logMessage = '[GetX] $text';
    if (isError) {
      debugPrint(logMessage);
    } else {
      debugPrint(logMessage);
    }
  }

}


