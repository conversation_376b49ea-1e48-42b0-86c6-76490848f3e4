import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/widgets/habi_dialog.dart';

/// Dialog utility class, provides various static methods to quickly create dialogs
class DialogUtils {
  DialogUtils._();

  /// Hide dialog
  static void hideDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// Show common dialog
  static Future<T?> showDialog<T>({
    String? title,
    String? content,
    List<String>? paragraphs,
    Widget? customContent,
    String? primaryButtonText,
    String? secondaryButtonText,
    Color primaryButtonColor = AppColors.ff01A796,
    Color secondaryButtonColor = AppColors.ff868788,
    VoidCallback? onPrimaryButtonPressed,
    VoidCallback? onSecondaryButtonPressed,
    bool barrierDismissible = true,
    TextAlign contentTextAlign = TextAlign.center,
  }) {
    return Get.dialog<T>(
      useSafeArea: false,
      HabiDialog(
        title: title,
        content: content,
        paragraphs: paragraphs,
        customContent: customContent,
        primaryButtonText: primaryButtonText ?? 'ok'.tr,
        secondaryButtonText: secondaryButtonText,
        primaryButtonColor: primaryButtonColor,
        secondaryButtonColor: secondaryButtonColor,
        contentTextAlign: contentTextAlign,
        onPrimaryButtonPressed: () {
          Get.back();
          onPrimaryButtonPressed?.call();
        },
        onSecondaryButtonPressed: secondaryButtonText != null
            ? () {
                Get.back();
                onSecondaryButtonPressed?.call();
              }
            : null,
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  /// Show confirmation dialog (with primary and secondary buttons)
  static Future<bool> showConfirmDialog({
    String? title,
    required String content,
    String? primaryButtonText,
    String? secondaryButtonText,
    VoidCallback? onPrimaryButtonPressed,
    VoidCallback? onSecondaryButtonPressed,
    TextAlign contentTextAlign = TextAlign.center,
    bool barrierDismissible = false,
  }) async {
    final result = await Get.dialog<bool>(
      useSafeArea: false,
      HabiDialog(
        title: title,
        content: content,
        primaryButtonText: primaryButtonText ?? 'confirm'.tr,
        secondaryButtonText: secondaryButtonText ?? 'cancel'.tr,
        primaryButtonColor: AppColors.ff01A796,
        secondaryButtonColor: AppColors.ffFF4E4E,
        contentTextAlign: contentTextAlign,
        onPrimaryButtonPressed: () {
          Get.back(result: true);
          onPrimaryButtonPressed?.call();
        },
        onSecondaryButtonPressed: () {
          Get.back(result: false);
          onSecondaryButtonPressed?.call();
        },
      ),
      barrierDismissible: barrierDismissible,
    );
    return result ?? false;
  }

  /// Show error dialog
  static Future<void> showErrorDialog({
    String? title,
    String? content,
    List<String>? paragraphs,
    String? primaryButtonText,
    VoidCallback? onPrimaryButtonPressed,
    TextAlign contentTextAlign = TextAlign.center,
  }) {
    return Get.dialog(
      useSafeArea: false,
      HabiDialog(
        title: title,
        content: content,
        paragraphs: paragraphs,
        primaryButtonText: primaryButtonText ?? 'ok'.tr,
        primaryButtonColor: AppColors.ffFF4E4E,
        contentTextAlign: contentTextAlign,
        onPrimaryButtonPressed: () {
          Get.back();
          onPrimaryButtonPressed?.call();
        },
      ),
      barrierDismissible: false,
    );
  }

  /// Show loading dialog
  static Future<void> showLoadingDialog({
    String? message,
  }) {
    return Get.dialog(
      useSafeArea: false,
      PopScope(
        canPop: false,
        child: UnconstrainedBox(
          child: SizedBox(
            width: 145,
            height: 145,
            child: HabiDialog(
              borderRadius: 8,
              insetPadding: EdgeInsets.zero,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      color: AppColors.ff01A796,
                    ),
                    if (message != null) ...[
                      const SizedBox(height: 12),
                      Text(
                        message,
                        maxLines: 2,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyles.regular14FirstColor,
                      ),
                    ]
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// Show dialog with icon
  ///
  /// Just an example of how to use the customContent property
  static Future<T?> showIconDialog<T>({
    required Widget icon,
    String? title,
    required String content,
    String primaryButtonText = 'OK',
    String? secondaryButtonText,
    Color primaryButtonColor = AppColors.ff01A796,
    Color secondaryButtonColor = AppColors.ff868788,
    VoidCallback? onPrimaryButtonPressed,
    VoidCallback? onSecondaryButtonPressed,
    bool barrierDismissible = true,
    TextAlign contentTextAlign = TextAlign.center,
  }) {
    return Get.dialog<T>(
      useSafeArea: false,
      HabiDialog(
        title: title,
        customContent: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon,
            const SizedBox(height: 16.0),
            Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: AppColors.ff1C1C1C,
              ),
              textAlign: contentTextAlign,
            ),
          ],
        ),
        primaryButtonText: primaryButtonText,
        secondaryButtonText: secondaryButtonText,
        primaryButtonColor: primaryButtonColor,
        secondaryButtonColor: secondaryButtonColor,
        onPrimaryButtonPressed: () {
          Get.back(result: true);
          onPrimaryButtonPressed?.call();
        },
        onSecondaryButtonPressed: secondaryButtonText != null
            ? () {
                Get.back(result: false);
                onSecondaryButtonPressed?.call();
              }
            : null,
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  /// Show dialog with input field
  ///
  /// Just an example of how to use the customContent property
  static Future<String?> showInputDialog({
    String? title,
    String? initialValue,
    String hintText = '',
    String primaryButtonText = 'OK',
    String secondaryButtonText = 'Cancel',
    Color primaryButtonColor = AppColors.ff01A796,
    Color secondaryButtonColor = AppColors.ff868788,
    bool barrierDismissible = true,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    TextAlign contentTextAlign = TextAlign.center,
  }) async {
    final controller = TextEditingController(text: initialValue);
    final formKey = GlobalKey<FormState>();

    try {
      return await Get.dialog<String>(
        useSafeArea: false,
        HabiDialog(
          title: title,
          customContent: Form(
            key: formKey,
            child: TextFormField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText,
                border: const OutlineInputBorder(),
              ),
              keyboardType: keyboardType,
              validator: validator,
              autofocus: true,
            ),
          ),
          primaryButtonText: primaryButtonText,
          secondaryButtonText: secondaryButtonText,
          primaryButtonColor: primaryButtonColor,
          secondaryButtonColor: secondaryButtonColor,
          onPrimaryButtonPressed: () {
            if (formKey.currentState?.validate() ?? false) {
              final text = controller.text;
              Get.back(result: text);
            }
          },
          onSecondaryButtonPressed: () {
            Get.back(result: null);
          },
        ),
        barrierDismissible: barrierDismissible,
      );
    } finally {
      controller.dispose();
    }
  }
}
