class DeepCopy {
  static List<T> deepCopyList<T>(List<T> list) {
    List<T> copy = [];

    for (final item in list) {
      if (item is Map) {
        copy.add(deepCopyMap(item) as T);
      } else if (item is List) {
        copy.add(deepCopyList(item) as T);
      } else if (item is Set) {
        copy.add(deepCopySet(item) as T);
      } else {
        copy.add(item);
      }
    }
    return copy;
  }

  static Map<K, V> deepCopyMap<K, V>(Map<K, V> map) {
    Map<K, V> copy = {};

    for (final entry in map.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Map) {
        copy[key] = deepCopyMap(value) as V;
      } else if (value is List) {
        copy[key] = deepCopyList(value) as V;
      } else if (value is Set) {
        copy[key] = deepCopySet(value) as V;
      } else {
        copy[key] = value;
      }
    }
    return copy;
  }

  static Set<T> deepCopySet<T>(Set<T> set) {
    Set<T> copy = {};

    for (final item in set) {
      if (item is Map) {
        copy.add(deepCopyMap(item) as T);
      } else if (item is List) {
        copy.add(deepCopyList(item) as T);
      } else if (item is Set) {
        copy.add(deepCopySet(item) as T);
      } else {
        copy.add(item);
      }
    }
    return copy;
  }
}

extension MapExtension<K, V> on Map<K, V> {
  Map<K, V> deepCopy() {
    return DeepCopy.deepCopyMap<K, V>(this);
  }
}

extension ListExtension<T> on List<T> {
  List<T> deepCopy() {
    return DeepCopy.deepCopyList<T>(this);
  }
}

extension SetExtension<T> on Set<T> {
  Set<T> deepCopy() {
    return DeepCopy.deepCopySet<T>(this);
  }
}
