class ThingNameUtils {
  /// Extract device model from Thing Name
  ///
  /// [thingName] Thing Name string
  /// Examples:
  /// - SAIT85R-001E5E0B7E48 -> SAIT85R
  /// - SAIT85R-001E5E0B7E48-MCTLR-0000001E5E0B7E48 -> MCTLR
  /// - SAIT85R-001E5E0B7E48-FFF1_8000-001E5E090C0B8064 -> FFF1_8000
  static String extractDeviceModel(String thingName) {
    if (thingName.isEmpty) return '';

    final parts = thingName.split('-');

    // If there are only two parts, return the first part (e.g., SAIT85R-001E5E0B7E48)
    if (parts.length == 2) {
      return parts[0];
    }

    // If there are four parts, return the third part
    // (e.g., SAIT85R-001E5E0B7E48-MCTLR-0000001E5E0B7E48)
    // or (e.g., SAIT85R-001E5E0B7E48-FFF1_8000-001E5E090C0B8064)
    if (parts.length >= 3) {
      return parts[2];
    }

    return '';
  }
}
