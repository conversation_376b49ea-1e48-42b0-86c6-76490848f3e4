import 'package:habi_app/logger/custom_logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class SentryUtils {
  static void captureException(
    dynamic throwable, {
    dynamic stackTrace,
    Hint? hint,
    ScopeCallback? withScope,
  }) {
    try {
      Sentry.captureException(throwable,
          stackTrace: stackTrace, hint: hint, withScope: withScope);
    } catch (e, s) {
      log.i('captureException()', error: e, stackTrace: s);
    }
  }

  static void captureMessage(
      String? message, {
        SentryLevel? level = SentryLevel.info,
        String? template,
        List<dynamic>? params,
        Hint? hint,
        ScopeCallback? withScope,
      }) {
    try {
      Sentry.captureMessage(message, level: level, template: template,
          params: params, hint: hint, withScope: withScope);
    } catch (e, s) {
      log.i('captureMessage()', error: e, stackTrace: s);
    }
  }

  static ISentrySpan? startTransaction(String name, String operation,
      {String? description,
      DateTime? startTimestamp,
      bool? bindToScope,
      bool? waitForChildren,
      Duration? autoFinishAfter,
      bool? trimEnd,
      OnTransactionFinish? onFinish,
      Map<String, dynamic>? customSamplingContext}) {
    ISentrySpan? span;
    try {
      span = Sentry.startTransaction(
        name,
        operation,
        description: description,
        startTimestamp: startTimestamp,
        bindToScope: bindToScope,
        waitForChildren: waitForChildren,
        autoFinishAfter: autoFinishAfter,
        trimEnd: trimEnd,
        onFinish: onFinish,
        customSamplingContext: customSamplingContext,
      );
    } catch (e, s) {
      log.i('startTransaction()', error: e, stackTrace: s);
    }
    return span;
  }

  static ISentrySpan? startChild(
    ISentrySpan? transaction,
    String operation, {
    String? description,
    DateTime? startTimestamp,
  }) {
    if (transaction == null) {
      return null;
    }
    ISentrySpan? span;
    try {
      span = transaction.startChild(
        operation,
        description: description,
        startTimestamp: startTimestamp,
      );
    } catch (e, s) {
      log.i('startChild()', error: e, stackTrace: s);
    }
    return span;
  }
}
