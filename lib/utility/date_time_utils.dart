import 'package:intl/intl.dart';

class DateTimeUtils {
  static String DATE_ONLY_FORMAT = "yyyy-MM-dd";
  static String DATE_MONTH_FORMAT = "yyyy-MM";
  static String DATE_TIME_FORMAT = "dd/MM/yyyy HH:mm:ss";
  static String DATE_TIME_FORMAT_YYYYMMDD_HHMMSS = "yyyy-MM-dd HH:mm:ss";
  static String DATE_TIME_FORMAT_HHmm = "HH:mm";

  static int getCurrentUtcTimeStamp() {
    final ms = DateTime.now().toUtc().millisecondsSinceEpoch;
    return (ms / 1000).round();
  }

  static String dateConverter(
    String date,
    String inputFormat,
    String outputFormat,
  ) {
    final format = DateFormat(inputFormat);
    final DateTime objDateTime = format.parse(date);
    final DateFormat formatter = DateFormat(outputFormat);
    final String formatted = formatter.format(objDateTime);
    return formatted;
  }

  int daysInMonth(DateTime date) {
    final DateTime currentMonthLastDate =
        DateTime(date.year, date.month + 1, 0);

    return currentMonthLastDate.day;
  }

  /// Converts time in HHmm format to total minutes
  /// Example: "0830" converts to 8*60+30=510 minutes
  ///
  /// [time] Time string in HHmm format
  /// Returns total minutes from 00:00
  /// Throws FormatException if input format is invalid
  static int convertHHmmToMinutes(String time) {
    if (time.length != 4 || !RegExp(r'^\d{4}$').hasMatch(time)) {
      throw FormatException('Invalid time format: $time');
    }

    final hours = int.parse(time.substring(0, 2));
    final minutes = int.parse(time.substring(2));

    if (hours < 0 || hours >= 24) {
      throw FormatException('Invalid hours value: $hours');
    }
    if (minutes < 0 || minutes >= 60) {
      throw FormatException('Invalid minutes value: $minutes');
    }

    return hours * 60 + minutes;
  }
}
