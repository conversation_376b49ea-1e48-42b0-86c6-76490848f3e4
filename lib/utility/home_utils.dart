import 'dart:convert';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/models/app_room.dart';
import 'package:habi_app/services/local_storage_service.dart';

class HomeUtils {

  static Future<List<AppHome>?> getCachedHomeList() async {
    String? json = await LocalStorageService.to.getHomeList();
    log.i('getCachedHomeList() -> json=$json');

    if (json == null || json.isEmpty) {
      return null;
    }

    List<dynamic> homeList = jsonDecode(json);
    if (homeList.isNotEmpty) {
      List<AppHome> appHomes = homeList.map((item) =>
          AppHome.fromJson(item)).toList();
      return appHomes;
    }

    return null;
  }

  static Future<AppHome?> getCachedHomeByThingName(String thingName) async {
    List<AppHome>? appHomes = await getCachedHomeList();
    if (appHomes == null || appHomes.isEmpty) {
      return null;
    }

    for (AppHome appHome in appHomes) {
      List<String>? deviceList = appHome.deviceList;
      if (deviceList == null || deviceList.isEmpty) {
        continue;
      }

      if (deviceList.first == thingName) {
        return appHome;
      }

      if (deviceList.contains(thingName)) {
        return appHome;
      }
    }

    return null;
  }

  static Future<bool> removeCachedHome(AppHome appHome) async {
    return removeCachedHomeById(appHome.homeId!);
  }

  static Future<bool> removeCachedHomeById(String homeId) async {
    List<AppHome>? appHomes = await getCachedHomeList();
    if (appHomes == null || appHomes.isEmpty) {
      return false;
    }

    appHomes.removeWhere((item) => item.homeId == homeId);

    String json = jsonEncode(appHomes);
    await LocalStorageService.to.setHomeList(json);
    log.i('removeCachedHome() -> json=$json');

    return true;
  }

  static Future<bool> removeCachedRoomDevice(String homeId, String roomId,
      String deviceId) async {
    List<AppHome>? appHomes = await getCachedHomeList();
    if (appHomes == null || appHomes.isEmpty) {
      return false;
    }

    AppHome appHome = appHomes.firstWhere(
            (item) => item.homeId == homeId, orElse: () => AppHome());
    if (appHome.homeId == null || appHome.homeId!.isEmpty) {
      return false;
    }

    AppRoom appRoom = appHome.rooms!.firstWhere(
            (item) => item.roomId == roomId, orElse: () => AppRoom());
    if (appRoom.roomId == null || appRoom.roomId!.isEmpty) {
      return false;
    }

    appRoom.deviceList?.removeWhere((item) => item == deviceId);

    String json = jsonEncode(appHomes);
    await LocalStorageService.to.setHomeList(json);
    log.i('removeCachedRoomDevice() -> json=$json');

    return true;
  }

}