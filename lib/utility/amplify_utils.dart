import 'dart:convert';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_push_notifications_pinpoint/amplify_push_notifications_pinpoint.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:amplify_push_notifications/amplify_push_notifications.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:google_api_availability/google_api_availability.dart';
import 'package:habi_app/utility/platform_utils.dart';

class AmplifyUtils {

  static Future<bool> reinitConfigure() async {
    log.i('reinitConfigure() -> start');
    try {
      GlobalService service = GlobalService.to;
      String amplifyJson = service.config.amplify;
      log.e('reinitConfigure() -> amplifyJson: $amplifyJson');
      final json = jsonDecode(amplifyJson) as Map;
      AmplifyConfig amplifyConfig = AmplifyConfig.fromJson(json.cast());
      final AmplifyAuthProviderRepository authProviderRepo = AmplifyAuthProviderRepository();

      if (PlatformUtils.isAndroid) {
        GooglePlayServicesAvailability availability = await GoogleApiAvailability.instance.checkGooglePlayServicesAvailability();
        log.i('reinitConfigure() -> availability: $availability');
        if (availability == GooglePlayServicesAvailability.success) {
          AmplifyPushNotificationsPinpoint? amplifyPushNotificationsPinpoint = Amplify.Notifications.Push.plugins.first as AmplifyPushNotificationsPinpoint;
          AmplifyPushNotifications amplifyPushNotifications = amplifyPushNotificationsPinpoint as AmplifyPushNotifications;
          await amplifyPushNotifications.reset();
          await amplifyPushNotifications.configure(
              config: amplifyConfig,
              authProviderRepo: authProviderRepo
          );

          log.i('reinitConfigure() -> push notification success!');
        }
      } else if (PlatformUtils.isIOS){
        AmplifyPushNotificationsPinpoint? amplifyPushNotificationsPinpoint = Amplify.Notifications.Push.plugins.first as AmplifyPushNotificationsPinpoint;
        AmplifyPushNotifications amplifyPushNotifications = amplifyPushNotificationsPinpoint as AmplifyPushNotifications;
        await amplifyPushNotifications.reset();
        await amplifyPushNotifications.configure(
            config: amplifyConfig,
            authProviderRepo: authProviderRepo
        );

        log.i('reinitConfigure() -> push notification success!');
      }

      return true;
    } catch (e, r) {
      log.e('reinitConfigure() -> e=$e, r=$r');
      return false;
    }
  }


}