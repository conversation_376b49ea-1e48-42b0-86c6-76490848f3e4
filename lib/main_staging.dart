import 'dart:async';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:habi_app/constants/storage_keys.dart';
import 'package:habi_app/helpers/app_theme_helper.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/i18n/app_translations.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_config.dart';
import 'package:habi_app/routes/app_pages.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:get_storage/get_storage.dart';
import 'initial_bindings.dart';
import 'models/app_route.dart';

void main() async {
  try {
    runZonedGuarded(() async {
      WidgetsFlutterBinding.ensureInitialized();

      FlutterError.onError = (FlutterErrorDetails details) {
        log.e(
          'FlutterError.onError()',
          error: details.exception,
          stackTrace: details.stack,
        );
      };

      // get configuration file
      final appConfig = await AppConfig.forEnvironment('staging');

      // initialize Amplify
      await configureAmplify(appConfig);

      // initialize GetStorage
      await GetStorage.init(StorageKeys.appData);

      final appThemeMode = await getThemeMode();

      // get translations
      final translations = AppTranslations();
      await translations.load();

      tz.initializeTimeZones();

      runApp(MyApp(translations, appConfig, appThemeMode));
    }, (error, stackTrace) {
      log.e(
        'runZonedGuarded()',
        error: error,
        stackTrace: stackTrace,
      );
    },
    );
  } catch(error, stackTrace) {
    log.e(
      'Main()',
      error: error,
      stackTrace: stackTrace,
    );
  }
}

Future<String> getThemeMode() async {
  var appDataBox = GetStorage(StorageKeys.appData);
  var email = appDataBox.read(StorageKeys.appDataEmail);
  var defaultTheme = 'light';
  if (email != null) {
    String name = '${StorageKeys.userData}_$email';
    await GetStorage.init(name);
    var userDataBox = GetStorage(name);
    var theme = userDataBox.read(StorageKeys.userDataTheme) ?? defaultTheme;
    return theme;
  }
  return defaultTheme;
}

Future<void> configureAmplify(AppConfig appConfig) async {
  try {
    await Amplify.addPlugins([
      AmplifyAuthCognito(),
    ]);
    await Amplify.configure(appConfig.amplify);
  } on Exception catch (e) {
    log.e('An error occurred configuring Amplify: $e');
  }
}

class MyApp extends StatelessWidget {

  final Translations translations;
  final AppConfig appConfig;
  final String appThemeMode;

  const MyApp(this.translations, this.appConfig, this.appThemeMode, {super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return GestureDetector(
      onTap: () => KeyboardHelper.dismissKeyboard(context),
      child: ScreenUtilInit(
        designSize: const Size(414, 896),
        splitScreenMode: true,
        child: GetMaterialApp(
          routingCallback: (routing) {
            log.d('routing: ${routing?.current}');
            var globalService = GlobalService.to;
            globalService.getRouteStreamController()
                .add(AppRoute(name: routing?.current));
          },
          title: 'habi Smart Home',
          theme: appThemeMode == 'dark' ? appDarkTheme() : appLightTheme(),
          initialBinding: InitialBindings(appConfig),
          initialRoute: AppPages.initial,
          getPages: AppPages.routes,
          translations: translations,
          locale: Get.deviceLocale,
          fallbackLocale: const Locale.fromSubtags(languageCode: 'en'),
          logWriterCallback: _logWriterCallback,
        ),
      ),
    );
  }

  void _logWriterCallback(String text, {bool isError = false}) {
    final logMessage = '[GetX] $text';
    if (isError) {
      log.e(logMessage);
    } else {
      log.i(logMessage);
    }
  }

}


