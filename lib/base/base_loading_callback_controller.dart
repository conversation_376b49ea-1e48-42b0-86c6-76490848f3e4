import 'package:get/get.dart';

class BaseLoadingCallbackController extends GetxController{
  void Function(bool value)? _updateLoadingCallback;
  void Function(String value)? _updateLoadingMessageCallback;
  void Function(bool value)? _updateTransparentLoadingCallback;
  bool Function()? _isLoadingCallback;
  bool Function()? _isTransparentLoadingCallback;

  @override
  void onClose() {
    _updateLoadingCallback = null;
    _updateTransparentLoadingCallback = null;
    _isLoadingCallback = null;
    _isTransparentLoadingCallback = null;
    super.onClose();
  }

  void setUpdateLoadingCallback(void Function(bool value) callback) {
    _updateLoadingCallback = callback;
  }

  void setUpdateLoadingMessageCallback(void Function(String value) callback) {
    _updateLoadingMessageCallback = callback;
  }

  void setUpdateTransparentLoadingCallback(void Function(bool value) callback) {
    _updateTransparentLoadingCallback = callback;
  }

  void setIsLoadingCallback(bool Function() callback) {
    _isLoadingCallback = callback;
  }

  void setIsTransparentLoadingCallback(bool Function() callback) {
    _isTransparentLoadingCallback = callback;
  }

  bool get isLoading => _isLoadingCallback?.call() ?? false;

  bool get isTransparentLoading => _isTransparentLoadingCallback?.call() ?? false;

  void updateLoading(bool value) {
    if (_updateLoadingCallback != null){
      _updateLoadingCallback!(value);
    }
  }

  void updateLoadingMessage(String value) {
    if (_updateLoadingMessageCallback != null){
      _updateLoadingMessageCallback!(value);
    }
  }

  void updateTransparentLoading(bool value) {
    if (_updateTransparentLoadingCallback != null){
      _updateTransparentLoadingCallback!(value);
    }
  }
}