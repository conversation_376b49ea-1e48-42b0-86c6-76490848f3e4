import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/widgets/loading_widget.dart';

abstract class BasePage<T extends BaseController> extends GetView<T> {

  const BasePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        buildBody(context),
        buildLoading(),
        buildTransparentLoading(),
      ],
    );
  }

  Widget buildBody(BuildContext context);

  Widget buildLoading() {
    return Obx(() {
          log.i("BasePage{} -> Loading: ${controller.loading.value.isLoading}");
          if (controller.loading.value.isLoading) {
            return LoadingContent(message: controller.loading.value.message,);
          }
          return const SizedBox();
        }
    );
  }

  Widget buildTransparentLoading() {
    return Obx(() {
          log.i("BasePage{} -> TransparentLoading: ${controller.transparentLoading.value}");
          if (controller.transparentLoading.value) {
            return Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.transparent
            );
          }
          return const SizedBox();
        }
    );
  }


}
