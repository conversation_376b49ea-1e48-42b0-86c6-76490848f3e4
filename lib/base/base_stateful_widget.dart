import 'package:flutter/material.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_loading.dart';
import 'package:habi_app/widgets/loading_widget.dart';

abstract class BaseStatefulWidget extends StatefulWidget {
  const BaseStatefulWidget({super.key});
}

abstract class BaseStatefulState<T extends BaseStatefulWidget> extends State<T> {

  late ValueNotifier<AppLoading> loading;
  late ValueNotifier<bool> transparentLoading;

  @override
  void initState() {
    super.initState();
    loading = ValueNotifier<AppLoading>(AppLoading(isLoading: false));
    transparentLoading = ValueNotifier<bool>(false);
  }

  @override
  void dispose() {
    loading.dispose();
    transparentLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        buildBody(context),
        buildLoading(),
        buildTransparentLoading(),
      ],
    );
  }

  Widget buildBody(BuildContext context);

  void updateLoading(bool value) {
    loading.value = AppLoading(isLoading: value, message: 'loading...');
  }

  void updateLoadingMessage(String value) {
    loading.value = AppLoading(isLoading: true, message: value);
  }

  void updateTransparentLoading(bool value) {
    transparentLoading.value = value;
  }

  bool get isLoading => loading.value.isLoading;

  bool get isTransparentLoading => transparentLoading.value;

  Widget buildLoading() {
    return ValueListenableBuilder(
        valueListenable: loading,
        builder: (context, value, child) {
          log.i("BaseStatefulState{} -> Loading: $value");
          if (loading.value.isLoading) {
            return LoadingContent(message: loading.value.message);
          }
          return const SizedBox();
        }
    );
  }

  Widget buildTransparentLoading() {
    return ValueListenableBuilder(
      valueListenable: transparentLoading,
      builder: (context, value, child) {
        log.i("BaseStatefulState{} -> TransparentLoading: $value");
        if (value) {
          return Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent
          );
        }
        return const SizedBox();
      }
    );
  }


}



