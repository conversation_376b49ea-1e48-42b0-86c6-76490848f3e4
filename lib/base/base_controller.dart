import 'package:get/get.dart';
import 'package:habi_app/models/app_loading.dart';

class BaseController extends GetxController{
  var loading = AppLoading(isLoading: false).obs;
  var transparentLoading = false.obs;

  bool isLoading() {
    return loading.value.isLoading;
  }

  bool isTransparentLoading() {
    return transparentLoading.value;
  }

  void updateLoading(bool value) {
    loading.value = AppLoading(isLoading: value, message: 'loading...');
  }

  void updateLoadingMessage(String value) {
    loading.value = AppLoading(isLoading: true, message: value);
  }

  void updateTransparentLoading(bool value) {
    transparentLoading.value = value;
  }
}