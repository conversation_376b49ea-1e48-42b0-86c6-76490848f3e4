import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';

class CardUIType {
  static const int loading = 0;
  static const int content = 1;
  int value = loading;

  CardUIType(this.value);

  factory CardUIType.copyLoading() {
    return CardUIType(CardUIType.loading);
  }

  factory CardUIType.copyContent() {
    return CardUIType(CardUIType.content);
  }
}

abstract class BaseCardWidget extends StatefulWidget {

  final String thingName;

  BaseCardWidget({
    super.key,
    required this.thingName,
  });

}

abstract class BaseCardState<T extends BaseCardWidget> extends State<T> {

  Widget buildLoading() {
    return const Center(
      child: CircularProgressIndicator(
        color: AppColors.ff01A796,
      ),
    );
  }



}