import 'dart:math';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/boarding/boarding_bindings.dart';
import 'package:habi_app/pages/boarding/boarding_page.dart';
import 'package:habi_app/pages/welcome/welcome_controller.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/widgets/habi_logo.dart';

class WelcomePage extends GetView<WelcomeController> {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;
    return Scaffold(
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: SizedBox(),
      ),
      body: Padding(
        padding: EdgeInsets.only(top: topPadding, left: 36, right: 36),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final double totalHeight = min(constraints.maxHeight, 900);

            return Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                SizedBox(
                  height: totalHeight * 0.039,
                ),
                HabiLogo(
                  height: totalHeight * 0.058,
                ),
                SizedBox(
                  height: totalHeight * 0.048,
                ),
                SizedBox(
                  height: totalHeight * 0.416,
                  child: Image.asset(
                    AppImagePaths.welcomeBedroom,
                  ),
                ),
                SizedBox(
                  height: totalHeight * 0.008,
                ),
                SizedBox(
                  height: totalHeight * 0.069,
                  child: AutoSizeText(
                    'welcomeTitle'.tr,
                    textAlign: TextAlign.center,
                    minFontSize: 0,
                    style: const TextStyle(
                      fontSize: 40,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ff01A796,
                    ),
                    maxLines: 2,
                  ),
                ),
                SizedBox(
                  height: totalHeight * 0.021,
                ),
                SizedBox(
                  height: totalHeight * 0.063,
                  child: AutoSizeText(
                    'welcomeBody'.tr,
                    textAlign: TextAlign.center,
                    minFontSize: 0,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!
                          .firstColor,
                    ),
                    maxLines: 5,
                  ),
                ),
                SizedBox(
                  height: totalHeight * 0.074,
                ),
                SizedBox(
                  width: double.infinity,
                  height: totalHeight * 0.056,
                  child: FilledButton(
                    onPressed: _onNext,
                    child: Text(
                      'next'.tr.toUpperCase(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.bold,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!
                            .secondColor,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _onNext() {
    Future(() async {
      await LocalStorageService.to.setFirstStartUp(false);
      Get.to(() => const BoardingPage(), binding: BoardingBindings());
    });
  }
}
