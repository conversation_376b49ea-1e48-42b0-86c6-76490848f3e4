import 'dart:async';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class ChangePasswordController extends GetxController {
  final oldPasswordController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  final isLoading = false.obs;
  bool isPasswordIncorrect = false;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    oldPasswordController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
  }

  Future<void> updatePassword() async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      UpdatePasswordResult result = await AuthService.to
          .updatePassword(
            oldPassword: oldPasswordController.text,
            newPassword: passwordController.text,
          )
          .timeout(const Duration(seconds: 30));

      log.i('updatePassword() -> result: $result, isClosed: $isClosed');
      if (!isClosed) {
        DialogUtils.showDialog(
          content: 'passwordChangedSuccessfully'.tr,
          onPrimaryButtonPressed: () {
            Get.back();
          },
        );
      }
    } catch (e) {
      log.e('updatePassword() -> e: $e, isClosed: $isClosed');
      if (!isClosed) {
        if (e is NetworkException) {
          DialogUtils.showErrorDialog(content: 'networkError'.tr);
        } else if (e is TimeoutException) {
          DialogUtils.showErrorDialog(content: 'timeoutError'.tr);
        } else if (e is NotAuthorizedServiceException) {
          isPasswordIncorrect = true;
          formKey.currentState?.validate();
        } else {
          DialogUtils.showErrorDialog(
            paragraphs: ['requestFailed'.tr, e.toString()],
          );
        }
      }
    } finally {
      isLoading.value = false;
    }
  }
}
