import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/reg_exp_helper.dart';
import 'package:habi_app/pages/change_password/change_password_controller.dart';
import 'package:habi_app/pages/forgot_password/forgot_password_bindings.dart';
import 'package:habi_app/pages/forgot_password/forgot_password_page.dart';
import 'package:habi_app/widgets/expanded_scroll_view.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';

class ChangePasswordPage extends GetView<ChangePasswordController> {


  ChangePasswordPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'changePassword'.tr,
      ),
      body: ExpandedScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        topContent: Column(
          children: [
            SizedBox(height: 33.h),
            _buildForm(context),
            SizedBox(height: 33.h),
            InkWell(
              onTap: _onForgotPasswordTap,
              child: SizedBox(
                width: double.infinity,
                child: Center(
                  child: Text(
                    'forgotPassword'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ff01A796,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 33.h),
          ],
        ),
        bottomContent: Column(
          children: [
            SizedBox(
                width: double.infinity,
                height: 48,
                child: Obx(() {
                  return FilledButton(
                    onPressed: _onContinue,
                    child: controller.isLoading.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : Text(
                            'continue'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.medium,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!
                                  .secondColor,
                            ),
                          ),
                  );
                })),
            SizedBox(height: 113.h),
          ],
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
        key: controller.formKey,
        child: Column(
          children: [

            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'currentPassword'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.oldPasswordController,
                obscureText: true,
                validator: _validateOldPassword,
              ),
            ),

            SizedBox(height: 33.h,),

            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'newPassword'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.passwordController,
                obscureText: true,
                validator: _validatePassword,
              ),
            ),

            SizedBox(height: 33.h,),

            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'repeatNewPassword'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.confirmPasswordController,
                obscureText: true,
                validator: _validateConfirmPassword,
              ),
            ),

          ],
        )
    );
  }

  String? _validateOldPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 8) {
      return 'passwordTooShort'.tr;
    }

    if (value.length > 14) {
      return 'passwordTooLong'.tr;
    }

    if (controller.isPasswordIncorrect) {
      return 'incorrectPassword'.tr;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 8) {
      return 'passwordTooShort'.tr;
    }

    if (value.length > 14) {
      return 'passwordTooLong'.tr;
    }

    if (!RegExp(RegExpHelper.userPassword).hasMatch(value)) {
      return 'passwordTooSimple'.tr;
    }

    if(!RegExpHelper.specialSymbol.any(value.contains)){
      return 'passwordMustContain'.tr;
    }

    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 8) {
      return 'passwordTooShort'.tr;
    }

    if (value.length > 14) {
      return 'passwordTooLong'.tr;
    }

    if (!RegExp(RegExpHelper.userPassword).hasMatch(value)) {
      return 'passwordTooSimple'.tr;
    }

    if(!RegExpHelper.specialSymbol.any(value.contains)){
      return 'passwordMustContain'.tr;
    }

    if(controller.passwordController.text != controller.confirmPasswordController.text){
      return 'passwordNotMatching'.tr;
    }

    return null;
  }

  void _onContinue() async {
    controller.isPasswordIncorrect = false;
    bool validate = controller.formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.updatePassword();
  }

  void _onForgotPasswordTap() {
    Get.to(
        () => ForgotPasswordPage(),
        binding: ForgotPasswordBindings()
    );
  }

}
