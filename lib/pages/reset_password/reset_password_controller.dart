import 'dart:async';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/login/login_bindings.dart';
import 'package:habi_app/pages/login/login_page.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class ResetPasswordController extends GetxController {
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final isLoading = false.obs;
  Completer<void>? _resetPasswordCompleter;

  @override
  void onClose() {
    if (_resetPasswordCompleter?.isCompleted == false) {
      _resetPasswordCompleter?.completeError(Exception('canceled'));
    }
    super.onClose();
    passwordController.dispose();
    confirmPasswordController.dispose();
  }

  Future<void> confirmResetPassword(
    String email,
    String confirmationCode,
  ) async {
    if (isLoading.value) {
      return;
    }

    try {
      isLoading.value = true;
      final result = await _confirmResetPassword(email, confirmationCode);

      log.i(
        "confirmResetPassword() -> signUpStep=${result.nextStep.updateStep}",
      );

      switch (result.nextStep.updateStep) {
        case AuthResetPasswordStep.done:
          log.i("confirmResetPassword() -> done");
          navigateToLoginPage();
          break;
        default:
          log.i("confirmResetPassword() -> default");
          break;
      }
    } catch (e, r) {
      log.e("confirmResetPassword() -> Exception{} -> e=$e, r=$r");
    } finally {
      isLoading.value = false;
    }
  }

  void navigateToLoginPage() {
    bool hasLoginPage = false;

    Get.until((route) {
      final isLoginPage = route.settings.name == LoginPage.routeName;
      if (isLoginPage) {
        hasLoginPage = true;
      }
      return isLoginPage;
    });

    log.i('navigateToLoginPage() -> hasLoginPage=$hasLoginPage');

    if (!hasLoginPage) {
      Get.offAll(
        () => LoginPage(),
        routeName: LoginPage.routeName,
        binding: LoginBindings(),
      );
    }
  }

  Future<ResetPasswordResult> _confirmResetPassword(
    String email,
    String confirmationCode,
  ) {
    final completer = Completer<ResetPasswordResult>();
    _resetPasswordCompleter = completer;

    Future.delayed(Duration.zero, () async {
      try {
        final result = await AuthService.to
            .confirmResetPassword(
              username: email,
              newPassword: passwordController.text,
              confirmationCode: confirmationCode,
            )
            .timeout(const Duration(seconds: 30));

        if (!completer.isCompleted) {
          completer.complete(result);
        }
      } on NetworkException catch (e, r) {
        log.e("_confirmResetPassword() -> NetworkException{} -> e=$e, r=$r");
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(content: 'networkError'.tr);
          completer.completeError(e);
        }
      } on TimeoutException catch (e, r) {
        log.e("_confirmResetPassword() -> TimeoutException{} -> e=$e, r=$r");
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(content: 'timeoutError'.tr);
          completer.completeError(e);
        }
      } catch (e, r) {
        if (e is CodeMismatchException || e is ExpiredCodeException) {
          log.e(
            "_confirmResetPassword() -> CodeMismatchException{} -> e=$e, r=$r",
          );
          if (!completer.isCompleted) {
            DialogUtils.showErrorDialog(
              content: 'invalidConfirmationCodeError'.tr,
              onPrimaryButtonPressed: () {
                Get.back();
              },
            );
            completer.completeError(e);
          }
        } else {
          log.e("_confirmResetPassword() -> Exception{} -> e=$e, r=$r");
          if (!completer.isCompleted) {
            DialogUtils.showErrorDialog(
              paragraphs: ['requestFailed'.tr, e.toString()],
            );
            completer.completeError(e);
          }
        }
      }
    });

    return completer.future;
  }
}
