import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_stateful_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/extensions/it850_extension.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/pages/device_type/thermostat/save_or_edit_schedule/save_or_edit_it850_schedule_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/save_or_edit_schedule/save_or_edit_it850_schedule_page.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'package:habi_app/widgets/habi_button.dart';
import 'package:habi_app/widgets/schedule_widgets/schedule_data.dart';
import 'package:habi_app/widgets/schedule_widgets/schedule_tab_bar.dart';

class It850SchedulePage extends BaseStatefulWidget {
  final String thingName;

  It850SchedulePage({
    required this.thingName,
    super.key,
  });

  @override
  It850SchedulePageState createState() => It850SchedulePageState();
}

class It850SchedulePageState extends BaseStatefulState<It850SchedulePage>
    with SingleTickerProviderStateMixin {
  final deviceShadowService = DeviceShadowService.to;

  IT850? it850;

  var mon_ = <Schedule>[].obs;
  var tue_ = <Schedule>[].obs;
  var wed_ = <Schedule>[].obs;
  var thu_ = <Schedule>[].obs;
  var fri_ = <Schedule>[].obs;
  var sat_ = <Schedule>[].obs;
  var sun_ = <Schedule>[].obs;
  var monSun_ = <Schedule>[].obs;
  var monFri_ = <Schedule>[].obs;
  var satSun_ = <Schedule>[].obs;

  var initializing = true.obs;
  var isSending = false.obs;
  var isScheduleUpdate = false.obs;
  var isDeleteAllVisible = false.obs;
  var isModifyEnable = false.obs;

  late TabController _tabController;

  String emptyMessage = "noSlot".tr;
  int initScheduleType = 2;
  int totalIntervals = 0;

  // Avoid external padding to prevent shadow clipping on HabiShadowCard
  final _pageHorizontalPadding = const EdgeInsets.symmetric(horizontal: 30);

  @override
  void initState() {
    super.initState();
    AppShadow appShadow = deviceShadowService.getDeviceShadow(widget.thingName);
    Map<String, dynamic> thingShadow = appShadow.thingShadow;
    // log.i('initState() -> thingShadow=$thingShadow');

    it850 = IT850.fromJson(thingShadow, widget.thingName);
    it850?.parseSchedules();

    isModifyEnable.value = it850?.shadow?.state?.reported?.connected == "true";
    initScheduleType = it850?.shadow?.state?.reported?.model?.properties?.schedules?.sTimeH?.scheduleType ?? 2;
    isDeleteAllVisible.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.isDeleteAllIntervalVisible() ?? false;

    _getSchedules();

    _tabController = TabController(length: 3, vsync: this, initialIndex: 1);
    _tabController.addListener(_onTabChanged);
    _tabController.index = initScheduleType == 0 ? 0 : initScheduleType - 1;

    initializing.value = false;
  }

  @override
  void dispose() {
    if (isScheduleUpdate.value) {
      it850?.shadow?.state?.reported?.model?.properties?.schedules?.cancel();
      it850?.shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
    }
    it850?.shadow?.state?.reported?.model?.properties?.schedules?.changeBackScheduleType();
    it850?.shadow?.state?.reported?.model?.properties?.schedules?.clearOtherTypeSchedules();
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    it850?.shadow?.state?.reported?.model?.properties?.schedules?.setScheduleType(_tabController.index + 1);
    // it850?.shadow?.state?.reported?.model?.properties?.schedules?.setCurrentSelectedDayIndex(0);
    it850?.shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
    setState(() {});
    log.i('当前选中的Tab索引：${_tabController.index}');
  }

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'schedule'.tr,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          SizedBox(height: 20.h),
          _buildTabBar(),
          SizedBox(height: 20.h),
          _buildTabBarView(),
          SizedBox(height: 20.h),
          _buildButtons(),
          SizedBox(height: 30.h),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Padding(
      padding: _pageHorizontalPadding,
      child: TabBar(
        controller: _tabController,
        tabs: _getTabs(),
        labelColor: AppColorsHelper.firstColor,
        unselectedLabelColor: AppColorsHelper.firstColor.withOpacity(0.4),
        indicatorColor: AppColors.ff01A796,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: AppColors.ff707070.withOpacity(0.5),
        dividerHeight: 2,
      ),
    );
  }

  List<Widget> _getTabs() {
    final List<Tab> tabs = [];
    final networkScanText = 'all'.tr;
    final pairingCodeText = 'workWeek'.tr;
    final qrCodeText = 'individual'.tr;

    tabs.add(Tab(text: networkScanText));
    tabs.add(Tab(text: pairingCodeText));
    tabs.add(Tab(text: qrCodeText));

    return tabs;
  }

  Widget _buildTabBarView() {
    return Obx(() {
      if (initializing.value) {
        return Center(
          child: CircularProgressIndicator(
            color: AppColorsHelper.firstColor,
          ),
        );
      }
      return Expanded(
        child: TabBarView(
          controller: _tabController,
          children: _getTabPages(),
        ),
      );
    });
  }

  List<Widget> _getTabPages() {
    return [
      ScheduleTabBar(
        tabBarPadding: _pageHorizontalPadding,
        scheduleHeader: _buildScheduleHeader(),
        scheduleType: 1,
        initialIndex: 0,
        allDay: _buildFullWeekContainers(context),
        weekDay: _buildWeekDayContainers(context),
        daily: _buildDailyContainers(context),
        key: UniqueKey(),
      ),
      ScheduleTabBar(
        tabBarPadding: _pageHorizontalPadding,
        scheduleHeader: _buildScheduleHeader(),
        scheduleType: 2,
        initialIndex: 0,
        allDay: _buildFullWeekContainers(context),
        weekDay: _buildWeekDayContainers(context),
        daily: _buildDailyContainers(context),
        key: UniqueKey(),
      ),
      ScheduleTabBar(
        tabBarPadding: _pageHorizontalPadding,
        scheduleHeader: _buildScheduleHeader(),
        scheduleType: 3,
        initialIndex: 0,
        allDay: _buildFullWeekContainers(context),
        weekDay: _buildWeekDayContainers(context),
        daily: _buildDailyContainers(context),
        key: UniqueKey(),
      ),
    ];
  }

  List<Widget> _buildWeekDayContainers(BuildContext context) {
    final List<Widget> list = [];

    list.add(
      Obx(() => _buildScheduleData(
        context,
        monFri_,
        getDefaultSchedule(1),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        satSun_,
        getDefaultSchedule(6),
      )),
    );
    return list;
  }

  List<Widget> _buildFullWeekContainers(BuildContext context) {
    final List<Widget> list = [];
    list.add(
      Obx(() => _buildScheduleData(
        context,
        monSun_,
        getDefaultSchedule(1),
      )),
    );
    return list;
  }

  List<Widget> _buildDailyContainers(BuildContext context) {
    final List<Widget> list = [];
    list.add(
      Obx(() => _buildScheduleData(
        context,
        sun_,
        getDefaultSchedule(7),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        mon_,
        getDefaultSchedule(1),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        tue_,
        getDefaultSchedule(2),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        wed_,
        getDefaultSchedule(3),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        thu_,
        getDefaultSchedule(4),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        fri_,
        getDefaultSchedule(5),
      )),
    );
    list.add(
      Obx(() => _buildScheduleData(
        context,
        sat_,
        getDefaultSchedule(6),
      )),
    );
    return list;
  }

  Widget _buildScheduleData(BuildContext context, List<Schedule> objList, Schedule defaultSchedule) {
    _setTotalInterval(objList.length);
    return ScheduleData(
      horizontalPadding: _pageHorizontalPadding,
      limit: it850?.shadow?.state?.reported?.model?.properties?.schedules?.limit() ?? 6,
      emptyMessage: emptyMessage,
      isModifyEnable: isModifyEnable.value,
      objList: objList,
      onTabShowDialog: _onTabShowDialog,
      defaultSchedule: defaultSchedule,
    );
  }

  void _setTotalInterval(int interval) {
    totalIntervals = totalIntervals + interval;
  }

  Widget _buildScheduleHeader() {
    return Row(
      children: [
        Expanded(
          child: Opacity(
            opacity: 0.5,
            child: Text(
              'startTime'.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),
          ),
        ),
        ConstrainedBox(
          constraints: const BoxConstraints(
            minWidth: 120,
          ),
          child: Opacity(
            opacity: 0.5,
            child: Text(
              'it850ScheduleSetting'.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Schedule getDefaultSchedule(int dayIndex) {
    final Schedule objSchedule = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getDefaultSchedule(dayIndex)
        ?? Schedule(scheduleMode: ScheduleMode.heatOnly);
    return objSchedule;
  }

  void _getSchedules() {
    mon_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getMon() ?? [];
    tue_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getTue() ?? [];
    wed_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getWed() ?? [];
    thu_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getThu() ?? [];
    fri_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getFri() ?? [];
    sat_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getSat() ?? [];
    sun_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getSun() ?? [];
    monFri_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getMonToFri() ?? [];
    satSun_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getSatToSun() ?? [];
    monSun_.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getMonToSun() ?? [];
  }

  Widget _buildButtons() {
    return Padding(
      padding: _pageHorizontalPadding,
      child: Obx(() {
        if (isScheduleUpdate.value) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCancelButton(),
              const Spacer(),
              _buildSaveSchedule(),
            ],
          );
        } else {
          if (isDeleteAllVisible.value) {
            return _buildDeleteAllSlot();
          }
        }
        return Container();
      }),
    );
  }

  Widget _buildDeleteAllSlot() {
    return SizedBox(
      width: double.infinity,
      child: HabiButton(
        text: 'deleteAllSlots'.tr,
        textColor:
            isModifyEnable.value ? Colors.white : Colors.white.withOpacity(0.5),
        backgroundColor: isModifyEnable.value
            ? AppColors.ff01A796
            : AppColorsHelper.firstColor.withOpacity(0.5),
        onPressed: () {
          if (!isModifyEnable.value) {
            return;
          }
          it850?.shadow?.state?.reported?.model?.properties?.schedules?.clearSchedulesWithScheduleUpdateFlag();
          isScheduleUpdate.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getIsScheduleUpdate() ?? true;
          _getSchedules();
        },
      ),
    );
  }

  Widget _buildCancelButton() {
    return HabiButton(
      onPressed: () {
        if (it850?.shadow?.state?.reported?.model?.properties?.schedules?.getScheduleType() != initScheduleType) {
          it850?.shadow?.state?.reported?.model?.properties?.schedules?.clearSchedules();
        }
        it850?.shadow?.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
        it850?.shadow?.state?.reported?.model?.properties?.schedules?.cancel();
        _getSchedules();
        _tabController.index = initScheduleType == 0 ? 0 : initScheduleType - 1;
        isScheduleUpdate.value = false;
        isDeleteAllVisible.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.isDeleteAllIntervalVisible() ?? true;
      },
      text: 'cancel'.tr,
      textColor: Colors.white,
      backgroundColor: AppColors.ff01A796,
    );
  }

  Widget _buildSaveSchedule() {
    return Stack(
      alignment: Alignment.center,
      children: [
        HabiButton(
          onPressed: () async{
            if (isSending.value || !isModifyEnable.value) {
              return;
            }
            isSending.value = true;
            await it850?.setSchedule();
            initScheduleType =  it850?.shadow?.state?.reported?.model?.properties?.schedules?.getScheduleType() ?? initScheduleType;
            isScheduleUpdate.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getIsScheduleUpdate() ?? false;
            isDeleteAllVisible.value = it850?.shadow?.state?.reported?.model?.properties?.schedules?.isDeleteAllIntervalVisible() ?? true;
            isSending.value = false;
          },
          text: 'save'.tr,
          textColor: isModifyEnable.value
              ? Colors.white
              : Colors.white.withOpacity(0.5),
          backgroundColor: isModifyEnable.value
              ? AppColors.ff01A796
              : AppColorsHelper.firstColor.withOpacity(0.5),
        ),
        if (isSending.value)
          CircularProgressIndicator(
            color: Theme.of(context)
                .extension<AppThemeExtension>()!
                .firstColor,
          ),
      ],
    );
  }

  void _onTabShowDialog(Schedule schedule, List<Schedule> objList, bool isEdit) async {
    int action = isEdit ? SaveOrEditIt850SchedulePage.editAction
        : SaveOrEditIt850SchedulePage.saveAction;
    Schedule copySchedule = schedule.copyWith();

    final data = await Get.to(() => SaveOrEditIt850SchedulePage(),
        binding: SaveOrEditIt850ScheduleBindings(
          schedule: copySchedule,
          action: action,
        )
    );

    if (data == null) {
      log.i('empty data.');
      return;
    }

    final (type, newSchedule) = data;
    if (type == 'save') {

      if(action == SaveOrEditIt850SchedulePage.saveAction) {
        bool isUniqueTime = false;
        for(Schedule objSchedule in objList) {
          if (objSchedule.time == newSchedule.time) {
            isUniqueTime = true;
            break;
          }
        }

        if (isUniqueTime) {
          showErrorSnackBar("uniqueStartTime".tr);
          return;
        }
      }


      it850?.shadow?.state?.reported?.model?.properties?.schedules?.addUpdateSchedules(newSchedule);
      it850?.shadow?.state?.reported?.model?.properties?.schedules?.buildScheduleStrings();
      _getSchedules();

      try {
        updateLoadingMessage('saving...');
        await it850?.setSchedule().timeout(const Duration(seconds: 60));
        initScheduleType = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getScheduleType() ?? initScheduleType;
        await Future.delayed(const Duration(seconds: 1));
      } catch (e, s) {
        log.i('error:$e, stack:$s');
      } finally {
        updateLoading(false);
      }

    } else if (type == 'delete') {
      if(action == SaveOrEditIt850SchedulePage.saveAction) {
        return;
      }

      it850?.shadow?.state?.reported?.model?.properties?.schedules?.removeSchedule(newSchedule);
      _getSchedules();

      try {
        updateLoadingMessage('deleting...');
        await it850?.setSchedule().timeout(const Duration(seconds: 60));
        initScheduleType = it850?.shadow?.state?.reported?.model?.properties?.schedules?.getScheduleType() ?? initScheduleType;
        await Future.delayed(const Duration(seconds: 1));
      } catch (e, s) {
        log.i('error:$e, stack:$s');
      } finally {
        updateLoading(false);
      }
    }
  }

}
