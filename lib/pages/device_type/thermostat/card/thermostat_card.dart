import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_card_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/system_modes.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/device_type/thermostat/control/thermostat_control_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/control/thermostat_control_page.dart';
import 'package:habi_app/widgets/habi_switch.dart';
import 'package:habi_app/widgets/habi_shadow_card.dart';
import 'thermostat_card_conntroller.dart';

class ThermostatCard extends BaseCardWidget {
  final String homeId;
  final String roomId;

  ThermostatCard({
    super.key,
    required super.thingName,
    required this.homeId,
    required this.roomId,
  });

  @override
  State<ThermostatCard> createState() {
    return ThermostatCardState();
  }
}

class ThermostatCardState extends BaseCardState<ThermostatCard> {
  late ThermostatCardController controller;

  @override
  void initState() {
    super.initState();
    log.i('ThermostatCard{} -> initState');
    controller = ThermostatCardController(thingName: widget.thingName);
    controller.init();
  }

  @override
  void dispose() {
    log.i('ThermostatCard{} -> dispose');
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    log.i('ThermostatCard{} -> build');
    return ValueListenableBuilder(
      valueListenable: controller.uiType,
      builder: (context, value, child) {
        if (value.value == CardUIType.loading) {
          return buildLoading();
        }
        return _buildContent(context);
      },
    );
  }

  Widget _buildContent(BuildContext context) {
    return HabiShadowCard(
      border: Border.all(
        color: _getPrimaryColor(),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 2,
              child: InkWell(
                onTap: () {
                  Get.to(
                    () => ThermostatControlPage(),
                    binding: ThermostatControlBindings(
                      homeId: widget.homeId,
                      roomId: widget.roomId,
                      thingName: widget.thingName,
                    ),
                  );
                },
                child: Column(
                  children: [
                    Expanded(
                      child: _buildDeviceName(),
                    ),
                    const SizedBox(height: 10),
                    Expanded(
                      child: _buildDeviceStatus(),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 15),
            Expanded(
              flex: 1,
              child: _buildDeviceControl(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceName() {
    return Align(
      alignment: Alignment.bottomLeft,
      child: Text(
        controller.deviceName,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: TextStyles.medium16FirstColor,
      ),
    );
  }

  Widget _buildDeviceStatus() {
    if (!controller.isConnected) {
      return Row(
        children: [
          Flexible(
            child: Text(
              'deviceOffline'.tr,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.regular16.copyWith(
                color: AppColorsHelper.firstColor.withOpacity(0.5),
                height: 1.0,
              ),
            ),
          ),
        ],
      );
    }

    if (controller.systemMode == SystemModes.off) {
      return Row(
        children: [
          Flexible(
            child: Text(
              'off'.tr,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.regular16.copyWith(
                color: AppColorsHelper.firstColor.withOpacity(0.2),
                height: 1.0,
              ),
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        Flexible(
          child: Text(
            'currently'.tr,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyles.regular16.copyWith(
              color: AppColorsHelper.firstColor.withOpacity(0.5),
              height: 1.0,
            ),
          ),
        ),
        const SizedBox(width: 10),
        _buildFormatTemp(temp: controller.localTemp),
      ],
    );
  }

  Widget _buildDeviceControl() {
    return controller.systemMode == SystemModes.off
        ? _buildOffModeControl()
        : _buildOnModeControl();
  }

  Widget _buildOnModeControl() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSetPointButton(
              icon: Icons.remove,
              isLoading: controller.decreaseSetPointFlag,
              onTap: () {
                controller.changeSetPoint('decrease');
              },
            ),
            _buildFormatTemp(
              temp: controller.desiredTemp,
              color: _getPrimaryColor(),
            ),
            _buildSetPointButton(
              icon: Icons.add,
              isLoading: controller.increaseSetPointFlag,
              onTap: () {
                controller.changeSetPoint('increase');
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOffModeControl() {
    return ValueListenableBuilder(
      valueListenable: controller.isChangingMode,
      builder: (context, value, child) {
        return HabiSwitch(
          isLoading: value,
          onTap: controller.isConnected
              ? () {
                  controller.changeSystemMode(SystemModes.heat);
                }
              : null,
        );
      },
    );
  }

  Widget _buildFormatTemp({
    required double temp,
    Color? color,
  }) {
    final tempInt = temp.toInt();
    final tempStr = temp == tempInt ? tempInt.toString() : temp.toString();
    final parts = tempStr.split('.');
    final intPart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';

    return Text(
      decimalPart.isEmpty ? '$intPart°' : '$intPart.$decimalPart°',
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyles.regular16.copyWith(
        color: color ?? AppColorsHelper.firstColor,
        height: 1.0,
      ),
    );
  }

  Widget _buildSetPointButton({
    required IconData icon,
    required bool isLoading,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: controller.isConnected ? onTap : null,
      child: Container(
        width: 40,
        height: 25,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: _getPrimaryColor(),
          borderRadius: BorderRadius.circular(20),
        ),
        child: isLoading
            ? const SizedBox(
                width: 14,
                height: 14,
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              )
            : Icon(
                icon,
                color: Colors.white,
                size: 14,
              ),
      ),
    );
  }

  Color _getPrimaryColor() {
    if (!controller.isConnected || controller.systemMode == SystemModes.off) {
      return AppColorsHelper.firstColor.withOpacity(0.5);
    }
    return AppColors.ff01A796;
  }
}
