import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_card_widget.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/control_modes.dart';
import 'package:habi_app/constants/system_modes.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/value_completer.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/extensions/it850_extension.dart';
import 'package:habi_app/services/user_attributes_service.dart';

class ThermostatCardController {
  final globalService = GlobalService.to;
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;
  final userAttributesService = UserAttributesService.to;
  final String thingName;

  ThermostatCardController({
    required this.thingName,
  });

  late ValueNotifier<CardUIType> uiType;
  late ValueNotifier<bool> isChangingMode;
  late IT850 it850;
  late Map<String, dynamic> _manualPropertyMap;

  bool isConnected = false;
  String deviceName = 'myThermostat'.tr;
  int systemMode = SystemModes.off;
  double desiredTemp = 0.0;
  double localTemp = 0.0;
  bool increaseSetPointFlag = false;
  bool decreaseSetPointFlag = false;

  Timer? _changeSetPointTimer;
  StreamSubscription? _mqttSubscription;
  StreamSubscription? _eventSubscription;
  ValueCompleter<void, int>? _setModeCompleter;

  void init() {
    uiType = ValueNotifier<CardUIType>(CardUIType.copyLoading());
    isChangingMode = ValueNotifier<bool>(false);
    _manualPropertyMap = {};
    setMqttListener();
    setEventListener();
    Future(() async {
      await fetchShadow();
    });
  }

  void dispose() {
    uiType.dispose();
    isChangingMode.dispose();
    _manualPropertyMap.clear();
    _mqttSubscription?.cancel();
    _eventSubscription?.cancel();
    _changeSetPointTimer?.cancel();
    _changeSetPointTimer = null;

    if (_setModeCompleter?.isCompleted == false) {
      _setModeCompleter?.completeError(Exception('canceled'));
    }
  }

  void setMqttListener() {
    _mqttSubscription = globalService
        .getMqttStream()
        .listen((shadow) {
      if (shadow.thingName == thingName) {
        try {
          it850 = it850.mapJson(shadow.thingShadow);

          bool thatIsConnected = it850.isConnected();
          if (thatIsConnected != isConnected) {
            log.i('setMqttListener() -> 刷新connected');
            isConnected = thatIsConnected;
            uiType.value = CardUIType.copyContent();
          }

          double thatLocalTemp = it850.getLocalTempValue();
          if (thatLocalTemp != localTemp) {
            log.i('setMqttListener() -> 刷新localTemp');
            localTemp = thatLocalTemp;
            uiType.value = CardUIType.copyContent();
          }

          int thatSystemMode = it850.getSystemMode();
          if (thatSystemMode != systemMode) {
            log.i('setMqttListener() -> 刷新systemMode');
            systemMode = thatSystemMode;
            uiType.value = CardUIType.copyContent();
            if (_setModeCompleter?.isCompleted == false &&
                _setModeCompleter?.extraValue == thatSystemMode) {
              _setModeCompleter!.complete();
            }
          }

          if (systemMode == SystemModes.cool) {
            String coolingSpKey = 'ep1:sTherS:CoolingSp';
            if (_manualPropertyMap.containsKey(coolingSpKey)) {
              log.i('setMqttListener() -> 包含coolingSpKey');
              int setCoolingSp = _manualPropertyMap[coolingSpKey] as int;
              int coolingSp = it850.getCoolingSp();
              if (coolingSp == setCoolingSp) {
                log.i('setMqttListener() -> 取消setPoint定时器');
                _manualPropertyMap.remove(coolingSpKey);
                _changeSetPointTimer?.cancel();
                _changeSetPointTimer = null;
              }
            } else {
              double value = it850.getCoolingSpValue();
              if (value != desiredTemp) {
                log.i('setMqttListener() -> 刷新coolingSp');
                desiredTemp = value;
                uiType.value = CardUIType.copyContent();
              }
            }
          } else {
            String heatingSpKey = 'ep1:sTherS:HeatingSp';
            if (_manualPropertyMap.containsKey(heatingSpKey)) {
              log.i('setMqttListener() -> 包含heatingSpKey');
              int setHeatingSp = _manualPropertyMap[heatingSpKey] as int;
              int heatingSp = it850.getHeatingSp();
              if (heatingSp == setHeatingSp) {
                log.i('setMqttListener() -> 取消setPoint定时器');
                _manualPropertyMap.remove(heatingSpKey);
                _changeSetPointTimer?.cancel();
                _changeSetPointTimer = null;
              }
            } else {
              double value = it850.getHeatingSpValue();
              if (value != desiredTemp) {
                log.i('setMqttListener() -> 刷新heatingSp');
                desiredTemp = value;
                uiType.value = CardUIType.copyContent();
              }
            }
          }

          String thatDeviceName = it850.getDeviceName();
          if (thatDeviceName != deviceName) {
            log.i('setMqttListener() -> 刷新deviceName');
            deviceName = thatDeviceName;
            uiType.value = CardUIType.copyContent();
          }
        } catch (e) {
          log.e('setMqttListener() -> failed: $e');
        }
      }
    });
  }

  void setEventListener() {
    _eventSubscription = globalService.getEventStream().listen((event) {
      if (event.name == AppEvents.changeTemperatureUnit) {
        log.i('setEventListener() -> 监听到事件: $event');
        localTemp = it850.getLocalTempValue();
        if (systemMode == SystemModes.cool) {
          desiredTemp = it850.getCoolingSpValue();
        } else {
          desiredTemp = it850.getHeatingSpValue();
        }
        uiType.value = CardUIType.copyContent();
      }
    });
  }

  Future<void> fetchShadow() async {
    try {
      AppShadow shadow = deviceShadowService.getDeviceShadow(thingName);
      if (shadow.thingShadow.isEmpty) {
        Map<String, dynamic> thingShadow = await deviceShadowService.fetchDeviceShadow(thingName);
        log.i('fetchShadow() -> getShadow success: $thingName');
        shadow = AppShadow(thingShadow: thingShadow, thingName: thingName);
      }

      it850 = IT850.fromJson(shadow.thingShadow, thingName);
      it850.parseSchedules();
      isConnected = it850.isConnected();
      deviceName = it850.getDeviceName();
      systemMode = it850.getSystemMode();
      localTemp = it850.getLocalTempValue();

      if (systemMode == SystemModes.cool) {
        desiredTemp = it850.getCoolingSpValue();
      } else {
        desiredTemp = it850.getHeatingSpValue();
      }

      uiType.value = CardUIType.copyContent();
    } catch (e) {
      log.e('fetchShadow() -> getShadow error: $thingName, $e');
    }
  }

  Future<void> changeSetPoint(String type) async {
    if (decreaseSetPointFlag || increaseSetPointFlag) {
      log.i('changeSetPoint() -> 正在改变setPoint中...');
      return;
    }

    int controlMode = await localStorageService.getControlMode() ?? ControlModes.remote;
    if (controlMode == ControlModes.remote) {
      changeSetPointWithRemote(type);
    } else {
      changeSetPointWithLocal(type);
    }
  }

  Future<void> changeSetPointWithRemote(String type) async {
    int thatSystemMode = it850.getSystemMode();
    log.i("changeSetPointWithRemote() -> systemMode=$thatSystemMode");

    Map<String, dynamic> property = {};
    if (thatSystemMode == SystemModes.heat) {
      if (type == 'increase') {
        double maxHeatSp = it850.getSliderMaxHeatSpValue();
        if (desiredTemp >= maxHeatSp) {
          log.e('已经到达最大值=$maxHeatSp');
          return;
        }
        desiredTemp += it850.getCardVectorValue();
      } else {
        double minHeatSp = it850.getSliderMinHeatSpValue();
        if (desiredTemp <= minHeatSp) {
          log.e('已经到达最小值=$minHeatSp');
          return;
        }
        desiredTemp -= it850.getCardVectorValue();
      }

      log.i('changeSetPointWithRemote() -> desiredTemp=$desiredTemp');

      it850.setHeatingSpValue(desiredTemp);
      uiType.value = CardUIType.copyContent();

      int thatHeatingSp = it850.getHeatingSp();
      property['ep1:sTherS:sHeatingSp'] = thatHeatingSp;
      _manualPropertyMap['ep1:sTherS:HeatingSp'] = thatHeatingSp;

      log.i('changeSetPointWithRemote() -> heatingSp=$thatHeatingSp');
    } else if (thatSystemMode == SystemModes.cool) {
      if (type == 'increase') {
        double maxCoolSp = it850.getSliderMaxCoolSpValue();
        if (desiredTemp >= maxCoolSp) {
          log.e('已经到达最大值=$maxCoolSp');
          return;
        }
        desiredTemp += it850.getCardVectorValue();
      } else {
        double minCoolSp = it850.getSliderMinCoolSpValue();
        if (desiredTemp <= minCoolSp) {
          log.e('已经到达最小值=$minCoolSp');
          return;
        }
        desiredTemp -= it850.getCardVectorValue();
      }

      log.i('changeSetPointWithRemote() -> desiredTemp=$desiredTemp');

      it850.setCoolingSpValue(desiredTemp);
      uiType.value = CardUIType.copyContent();

      int thatCoolingSp = it850.getCoolingSp();
      property['ep1:sTherS:sCoolingSp'] = thatCoolingSp;
      _manualPropertyMap['ep1:sTherS:CoolingSp'] = thatCoolingSp;

      log.i('changeSetPointWithRemote() -> coolingSp=$thatCoolingSp');
    } else {
      log.e("changeSetPointWithRemote() -> unknown mode");
    }

    if (property.isEmpty) {
      log.e("changeSetPointWithRemote() -> property is empty.");
      increaseSetPointFlag = false;
      decreaseSetPointFlag = false;
      uiType.value = CardUIType.copyContent();
      return;
    }

    _changeSetPointTimer?.cancel();
    _changeSetPointTimer = Timer(const Duration(milliseconds: 1200), () async {
      log.i('changeSetPointWithRemote() -> 执行change set-point');

      if (type == 'increase') {
        increaseSetPointFlag = true;
        uiType.value = CardUIType.copyContent();
      } else {
        decreaseSetPointFlag = true;
        uiType.value = CardUIType.copyContent();
      }

      try {
        await deviceShadowService.updateDeviceProperties(
            thingName: thingName,
            property: property,
            subId: IT850.subId
        ).timeout(const Duration(seconds: 30));
        log.i('changeSetPointWithRemote() -> successfully publish data to cloud!');
      } catch (e, r) {
        log.e('changeSetPointWithRemote() -> failed to publish data to cloud: $e, $r');
      } finally {
        increaseSetPointFlag = false;
        decreaseSetPointFlag = false;
        uiType.value = CardUIType.copyContent();
      }
    });

  }

  Future<void> changeSetPointWithLocal(String type) async {

  }

  void changeSystemMode(int mode) async {
    if (isChangingMode.value) {
      return;
    }

    isChangingMode.value = true;

    try {
      final completer = ValueCompleter<void, int>(mode);
      _setModeCompleter = completer;

      Future(() async {
        try {
          await deviceShadowService.updateDeviceProperties(
            thingName: thingName,
            property: {
              'ep1:sTherS:sSystemMode': mode,
            },
            subId: IT850.subId,
          );

          await Future.delayed(const Duration(seconds: 30));

          throw Exception('timeout');
        } catch (e) {
          if (!completer.isCompleted) {
            completer.completeError(e);
          }
        }
      });

      await completer.future;
    } catch (e) {
      log.e('changeSystemMode() - failed', error: e);
    }

    isChangingMode.value = false;
  }
}