import 'package:get/instance_manager.dart';
import 'device_pairing_mode_controller.dart';

class DevicePairingModeBindings extends Bindings {

  final Map<Object?, Object?> openPairingWindowData;
  final int vendorId;
  final int productId;
  final int discriminator;
  final int setupPinCode;

  DevicePairingModeBindings({
    required this.openPairingWindowData,
    required this.vendorId,
    required this.productId,
    required this.discriminator,
    required this.setupPinCode,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => DevicePairingModeController(
        openPairingWindowData: openPairingWindowData,
        vendorId: vendorId,
        productId: productId,
        discriminator: discriminator,
        setupPinCode: setupPinCode,
    ));
  }
}
