import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/matter_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/utility/platform_utils.dart';

class DevicePairingModeController extends GetxController {

  final Map<Object?, Object?> openPairingWindowData;
  final int vendorId;
  final int productId;
  final int discriminator;
  final int setupPinCode;
  var qrcode = ''.obs;
  var manualPairingCode = ''.obs;

  DevicePairingModeController({
    required this.openPairingWindowData,
    required this.vendorId,
    required this.productId,
    required this.discriminator,
    required this.setupPinCode,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    Future(() async {
      log.i('onReady() -> openPairingWindowData=$openPairingWindowData');
      if (openPairingWindowData.isNotEmpty) {
        try {
          var map = openPairingWindowData;
          log.i('onReady() -> map=$map');

          if (PlatformUtils.isAndroid) {
            qrcode.value = map['qrCode'] as String;
          }

          var mpCode = map['manualPairingCode'] as String;
          manualPairingCode.value = MatterHelper.formatManualPairingCode(mpCode);
        } catch (e) {
          log.e('onReady() -> generateQRCodeAndManualPairingCode failed: $e');
        }
      } else {
        try {
          var map = await CtFlutterMatterPlugin.getInstance()
              .generateQRCodeAndManualPairingCode({
            'vendorId': vendorId,
            'productId': productId,
            'discriminator': discriminator,
            'setupPinCode': setupPinCode,
            'commissioningFlow': 0,
            'discoveryCapabilities': 'allMask',
            'isShortDiscriminator': false,
          });
          log.i('onReady() -> map=$map');

          if (PlatformUtils.isAndroid) {
            qrcode.value = map['qrCode'] as String;
          }

          var mpCode = map['manualPairingCode'] as String;
          manualPairingCode.value = MatterHelper.formatManualPairingCode(mpCode);
        } catch (e) {
          log.e('onReady() -> generateQRCodeAndManualPairingCode failed: $e');
        }
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
  }



}
