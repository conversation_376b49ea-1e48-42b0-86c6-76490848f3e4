import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/pages/device_type/thermostat/device_pairing_mode/device_pairing_mode_controller.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

class DevicePairingModePage extends GetView<DevicePairingModeController> {

  DevicePairingModePage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'devicePairingMode'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: I<PERSON><PERSON><PERSON>on(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [

                Obx(() {
                  if (controller.qrcode.value.isEmpty) {
                    return const SizedBox();
                  }
                  if (context.isTablet) {
                    return Container(
                        color: Colors.white,
                        padding: const EdgeInsets.all(100),
                        child: createQRImage(context, controller.qrcode.value)
                    );
                  }
                  return Container(
                      color: Colors.white,
                      padding: const EdgeInsets.all(20),
                      child: createQRImage(context, controller.qrcode.value)
                  );
                }),

                const SizedBox(height: 20,),

                Text(
                  'openDevicePairingModeText'.tr,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),

                const SizedBox(height: 50,),

                Obx(() {
                  return Container(
                    width: double.infinity,
                    height: 50,
                    decoration: const BoxDecoration(
                        color: AppColors.ff01A796,
                        borderRadius: BorderRadius.all(Radius.circular(50))
                    ),
                    child: TextButton.icon(
                      onPressed: onPairingCodeClick,
                      icon: const Icon(Icons.copy, color: Colors.white),
                      label: Text(
                          controller.manualPairingCode.value,
                          style: const TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.bold,
                              color: Colors.white
                          )
                      ),
                    ),
                  );
                })


              ],
            ),
          ),
        )
    );
  }

  Widget createQRImage(BuildContext context, String data) {
    final qrCode = QrCode.fromData(
      data: data,
      errorCorrectLevel: QrErrorCorrectLevel.H,
    );

    final qrImage = QrImage(qrCode);
    
    return PrettyQrView(
      qrImage: qrImage,
      // decoration: PrettyQrDecoration(
      //   background: Theme.of(context)
      //     .extension<AppThemeExtension>()!.secondColor
      // ),
    );
  }

  void onPairingCodeClick() async {
    try {
      await Clipboard.setData(ClipboardData(text: controller.manualPairingCode.value));
      showSuccessSnackBar('copy success!');
    } catch (e) {
      showErrorSnackBar('copy failed!');
    }
  }

}
