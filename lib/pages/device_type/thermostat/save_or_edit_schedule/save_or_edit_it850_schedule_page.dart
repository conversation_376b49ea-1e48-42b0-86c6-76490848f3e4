import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/app_number_helper.dart';
import 'package:habi_app/widgets/linear_ticks_widget.dart';
import 'save_or_edit_it850_schedule_controller.dart';

class SaveOrEditIt850SchedulePage extends GetView<SaveOrEditIt850ScheduleController> {

  static const int saveAction = 0;
  static const int editAction = 1;

  SaveOrEditIt850SchedulePage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          controller.action == saveAction ? 'addSlot'.tr :'editSlot'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 20.h,),
        
              Row(
                children: [
                  Expanded(
                    child: Opacity(
                      opacity: 0.5,
                      child: Text(
                        'startTime'.tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      ),
                    ),
                  ),

                  // Expanded(
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.end,
                  //     children: [
                  //       Text(
                  //         'repeat'.tr,
                  //         style: TextStyle(
                  //           fontSize: 14,
                  //           fontWeight: AppFontWeights.regular,
                  //           color: Theme.of(context)
                  //               .extension<AppThemeExtension>()!.firstColor,
                  //         ),
                  //       ),
                  //
                  //       const SizedBox(width: 10,),
                  //
                  //       Obx(() => SizedBox(
                  //         width: 40,
                  //         height: 24,
                  //         child: Switch(
                  //             value: controller.repeat.value,
                  //             onChanged: (value) {
                  //               controller.repeat.value = value;
                  //             }
                  //         ),
                  //       ))
                  //     ],
                  //   ),
                  // ),
                ],
              ),

              SizedBox(height: 10.h,),

              _buildStartTimeRangeWidget(context: context),

              SizedBox(height: 67.h,),
        
              Align(
                alignment: Alignment.centerLeft,
                child: Opacity(
                  opacity: 0.5,
                  child: Text(
                    'desiredTemperature'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 5.h,),

              Obx(() {
                return Text(
                  '${controller.setPoint.value}',
                  style: TextStyle(
                    fontSize: 56,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                );
              }),

              SizedBox(height: 25.h,),

              Obx(() {
                double min = controller.schedule.getMinHeat();
                double max = controller.schedule.getMaxHeat();
                bool isShowSlider = controller.setPoint.value > 0;
                if (!isShowSlider) {
                  return const SizedBox();
                }
                return Column(
                  children: [
                    CustomPaint(
                      size: Size(Get.width, 10.h),
                      painter: LinearTicksWidget(
                        highlightedIndex: controller.highlightedIndex.value,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!
                            .firstColor,
                      ),
                    ),
                    SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                            trackHeight: 3,
                            activeTrackColor: AppColors.ff01A796,
                            inactiveTrackColor: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .fourthColor
                                .withOpacity(0.3),
                            thumbColor: AppColors.ff01A796,
                            thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 14
                            ),
                            overlayShape: SliderComponentShape.noOverlay,
                            trackShape: const RectangularSliderTrackShape()
                        ),
                        child: Slider(
                            value: controller.setPoint.value,
                            min: min,
                            max: max,
                            onChanged: (value) {
                              int mode = controller.schedule.temperatureDisplayMode ?? 0;
                              if (mode == 0) {
                                var roundValue = AppNumberHelper.roundValueTo(value);
                                controller.setPoint.value = roundValue;
                              } else {
                                var roundValue = AppNumberHelper.roundValueTo(value, to: 1);
                                controller.setPoint.value = roundValue;
                              }
                              controller.highlightedIndex.value = controller.getHighLightIndex();
                            }
                        ),
                    ),
                  ],
                );
              }),

              SizedBox(height: 67.h,),

              _buildBottomWidget(context),

              SizedBox(height: 126.h ,),
        
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomWidget(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [

            SizedBox(
              height: 48,
              child: OutlinedButton(
                onPressed: _onCancelTap,
                child: Text(
                  'cancel'.tr.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.ff01A796,
                  ),
                ),
              ),
            ),

            SizedBox(
              height: 48,
              child: OutlinedButton(
                onPressed: _deleteTap,
                style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                    overlayColor: Colors.red
                ),
                child: Text(
                  'delete'.tr.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.medium,
                    color: Colors.red,
                  ),
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 26.h,),

        SizedBox(
          width: double.infinity,
          height: 48,
          child: FilledButton(
            onPressed: _onSaveTap,
            child: Text(
              'save'.tr.toUpperCase(),
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.bold,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.secondColor,
              ),
            ),
          ),
        ),

      ],
    );
  }

  void _onCancelTap() {
    Get.back(result: ('cancel', null));
  }

  void _deleteTap() {
    controller.deleteSchedule();
  }

  void _onSaveTap() {
    controller.saveSchedule();
  }

  Widget _buildStartTimeRangeWidget({
    required BuildContext context,
  }) {
    return Row(
      children: [
        Expanded(
            flex: 3,
            child: controller.createTimeWheelWidget()
        ),

        const SizedBox(width: 22,),

        Expanded(
            flex: 1,
            child: controller.schedule.is24HourFormat() ? Container() : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Obx(() => _buildTimeRangeButton('am'.tr, controller.isAM.value == true, () {
                  controller.isAM.value = true;
                })),
                SizedBox(height: 29.h,),
                Obx(() => _buildTimeRangeButton('pm'.tr, controller.isAM.value == false, () {
                  controller.isAM.value = false;
                }))
              ],
            )
        )
      ],
    );
  }

  Widget _buildTimeRangeButton(String text, bool checked, GestureTapCallback tap) {

    BoxDecoration decoration;
    Color textColor;
    if (checked) {
      decoration = const BoxDecoration(
        color: AppColors.ff01A796,
        borderRadius: BorderRadius.all(Radius.circular(30)),
      );
      textColor = Colors.white;
    } else {
      decoration = BoxDecoration(
        border: Border.all(
           color: AppColors.ff01A796
        ),
        borderRadius: const BorderRadius.all(Radius.circular(30)),
      );
      textColor = AppColors.ff01A796;
    }

    return InkWell(
      onTap: tap,
      child: Container(
        width: 66.w,
        height: 37.h,
        decoration: decoration,
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 16,
            fontWeight: AppFontWeights.regular,
            color: textColor,
          ),
        ),
      ),
    );
  }




}



