import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/thermostat_constants.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/widgets/time_wheel.dart';
import 'save_or_edit_it850_schedule_page.dart';

class SaveOrEditIt850ScheduleController extends GetxController {
  final Schedule schedule;
  final int action;
  late FixedExtentScrollController hourController;
  late FixedExtentScrollController minuteController;
  var repeat = false.obs;
  var isAM = true.obs;
  var setPoint = 0.0.obs;
  var highlightedIndex = 0.obs;
  String hourValue = '08';
  String minuteValue = '00';
  TimeWheel? timeWheel;

  SaveOrEditIt850ScheduleController({
    required this.schedule,
    required this.action,
  });

  @override
  void onInit() {
    super.onInit();
    hourController = FixedExtentScrollController(initialItem: 0);
    minuteController = FixedExtentScrollController(initialItem: 0);
  }

  @override
  void onReady() {
    super.onReady();

    if (action == SaveOrEditIt850SchedulePage.saveAction) {

      int temperatureDisplayMode = schedule.temperatureDisplayMode ?? 0;
      log.e('initData() -> temperatureDisplayMode: $temperatureDisplayMode');
      if (temperatureDisplayMode == 0) {
        setPoint.value = 21.0;
      } else {
        setPoint.value = 71.0;
      }

      highlightedIndex.value = getHighLightIndex();
      setCurrentTimeToSchedule();
    } else {

      try {
        setPoint.value = double.parse(schedule.getHeating());
        highlightedIndex.value = getHighLightIndex();
      } catch (e) {
        log.e('initData() -> error: $e');
      }

      parseScheduleTime();
    }

  }

  @override
  void onClose() {
    hourController.dispose();
    minuteController.dispose();
    super.onClose();
  }

  Widget createTimeWheelWidget() {
    timeWheel ??= TimeWheel(
      onHourChanged: (int index, String value) {
        log.i('hour-index: $index');
        log.i('hour-value: $value');
        hourValue = value;
      },
      onMinuteChanged: (int index, String value) {
        log.i('minute-index: $index');
        log.i('minute-value: $value');
        minuteValue = value;
      },
      hourController: hourController,
      minuteController: minuteController,
      is24Hour: schedule.is24HourFormat(),
    );
    return timeWheel!;
  }


  void deleteSchedule() {
    Get.back(result: ('delete', schedule));
  }

  void saveSchedule() {
    log.i('saveSchedule() -> setPoint: ${setPoint.value}, hourStr: $hourValue, minuteStr: $minuteValue');

    schedule.setHeating(setPoint.value);

    if (isAM.value) {
      schedule.setTime('$hourValue:$minuteValue AM');
    } else {
      schedule.setTime('$hourValue:$minuteValue PM');
    }

    log.i('saveSchedule() -> getTime: ${schedule.getTime()}');
    log.i('saveSchedule() -> getHeating: ${schedule.getHeating()}');

    Get.back(result: ('save', schedule));
  }


  void setCurrentTimeToSchedule() {
    try {
      DateTime now = DateTime.now();
      String hourStr = now.hour.toString().padLeft(2, '0');
      String minuteStr = now.minute.toString().padLeft(2, '0');

      log.i('setCurrentTimeToSchedule() -> now: $now');
      log.i('setCurrentTimeToSchedule() -> hourStr: $hourStr');
      log.i('setCurrentTimeToSchedule() -> minuteStr: $minuteStr');

      if (schedule.is24HourFormat()) {
        schedule.time = '$hourStr$minuteStr';
        hourValue = hourStr;
        minuteValue = minuteStr;
      } else {
        String period = now.hour < 12 ? 'am' : 'pm';
        int hour12 = now.hour % 12 == 0 ? 12 : now.hour % 12;

        log.i('setCurrentTimeToSchedule() -> period: $period');
        log.i('setCurrentTimeToSchedule() -> hour12: $hour12');

        String newHourStr = hour12.toString().padLeft(2, '0');
        log.i('setCurrentTimeToSchedule() -> newHourStr: $newHourStr');

        schedule.time = '$newHourStr$minuteStr $period';
        isAM.value = period.toLowerCase().contains('am');

        hourValue = newHourStr;
        minuteValue = minuteStr;
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);

      log.i('setCurrentTimeToSchedule() -> hourValue: $hourValue');
      log.i('setCurrentTimeToSchedule() -> minuteValue: $minuteValue');
      log.i('setCurrentTimeToSchedule() -> schedule.time: ${schedule.time}');
    } catch (e) {
      log.e('setCurrentTimeToSchedule() -> error: $e');
      schedule.time = '0800';
      hourValue = '08';
      minuteValue = '00';

      if (!schedule.is24HourFormat()) {
        isAM.value = true;
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);
    }
  }

  void parseScheduleTime() {
    try {
      schedule.time ??= '0800';
      String scheduleTime = schedule.getTime().trim();
      log.i('parseScheduleTime() -> scheduleTime: $scheduleTime');
      log.i('parseScheduleTime() -> is24HourFormat: ${schedule.is24HourFormat()}');

      if (schedule.is24HourFormat()) {
        List timeSplit = scheduleTime.split(':');
        log.i('parseScheduleTime() -> timeSplit: $timeSplit');
        String hourStr = timeSplit[0];
        String minuteStr = timeSplit[1];
        log.i('parseScheduleTime() -> hourStr: $hourStr');
        log.i('parseScheduleTime() -> minuteStr: $minuteStr');
        hourValue = hourStr;
        minuteValue = minuteStr;
      } else {
        List parts = scheduleTime.split(' ');
        log.i('parseScheduleTime() -> parts: $parts');
        String timePart = parts[0];
        log.i('parseScheduleTime() -> timePart: $timePart');
        String amPm = parts[1].trim();
        log.i('parseScheduleTime() -> amPm: $amPm');
        List timeSplit = timePart.split(':');
        log.i('parseScheduleTime() -> timeSplit: $timeSplit');
        String hourStr = timeSplit[0];
        String minuteStr = timeSplit[1];
        log.i('parseScheduleTime() -> hourStr: $hourStr');
        log.i('parseScheduleTime() -> minuteStr: $minuteStr');
        hourValue = hourStr;
        minuteValue = minuteStr;
        isAM.value = amPm.toLowerCase().contains('am');
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);
    } catch (e) {
      log.e('parseScheduleTime() -> error: $e');
      schedule.time = '0800';
      hourValue = '08';
      minuteValue = '00';

      if (!schedule.is24HourFormat()) {
        isAM.value = true;
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);
    }
  }

  int getHighLightIndex() {
    int temperatureDisplayMode = schedule.temperatureDisplayMode ?? 0;
    if (temperatureDisplayMode == 0) {
      return ThermostatConstants.tempToIndexForScheduleCelsiusMap[setPoint.value] ?? 0;
    } else {
      return ThermostatConstants.tempToIndexForScheduleFahrenheitMap[setPoint.value] ?? 0;
    }
  }

}
