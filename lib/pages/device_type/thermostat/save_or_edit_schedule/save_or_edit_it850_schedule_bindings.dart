import 'package:get/get.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'save_or_edit_it850_schedule_controller.dart';

class SaveOrEditIt850ScheduleBindings extends Bindings {
  final Schedule schedule;
  final int action;

  SaveOrEditIt850ScheduleBindings({
    required this.schedule,
    required this.action,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => SaveOrEditIt850ScheduleController(
        schedule: schedule,
        action: action,
    ));
  }
}
