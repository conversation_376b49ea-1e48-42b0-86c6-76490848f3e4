import 'package:get/instance_manager.dart';
import 'thermostat_settings_controller.dart';

class ThermostatSettingsBindings extends Bindings {

  final String homeId;
  final String roomId;
  final String thingName;

  ThermostatSettingsBindings({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => ThermostatSettingsController(
      homeId: homeId,
      roomId: roomId,
      thingName: thingName,
    ));
  }
}
