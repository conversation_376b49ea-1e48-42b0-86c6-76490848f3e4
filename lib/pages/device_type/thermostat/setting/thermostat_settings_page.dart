import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/device_type/thermostat/setting/thermostat_settings_controller.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'package:habi_app/widgets/habi_switch.dart';

class ThermostatSettingsPage extends BasePage<ThermostatSettingsController> {
  const ThermostatSettingsPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'deviceSettings'.tr,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(
            top: 0,
            bottom: 20,
            left: 20,
            right: 20,
          ),
          child: Obx(() {
            return Column(
              children: [
                _buildDeviceName(),
                Opacity(
                  opacity: controller.isOnline ? 1.0 : 0.5,
                  child: Column(
                    children: [
                      _buildDeviceSettings(),
                      if(controller.isTRV()) _buildHeatOnDemand(),
                      _buildChildLock(),
                      _buildIdentifyMe(),
                      _buildDeviceStatus(),
                    ],
                  ),
                ),
                _buildDeleteButton(),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildDeviceName() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: SvgPicture.asset(
              AppImagePaths.iconThermostat,
              width: 53,
              height: 53,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Obx(() {
                if (controller.editName.value) {
                  return TextFormField(
                    onFieldSubmitted: (value) async {
                      final name = value.trim();
                      log.i('onFieldSubmitted() -> name: $name');
                      await controller.changeName(name);
                      controller.editName.value = false;
                    },
                    style: TextStyles.regular14.copyWith(
                      color: AppColorsHelper.firstColor,
                    ),
                    controller: controller.editNameController,
                  );
                }

                return Text(
                  controller.deviceName.value,
                  style: TextStyles.regular24.copyWith(
                    color: AppColorsHelper.firstColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }),
            ),
          ),
          if (controller.isOnline)
            InkWell(
              onTap: controller.onEditDeviceNameTap,
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: SvgPicture.asset(
                  AppImagePaths.edit,
                  width: 24,
                  height: 24,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDeviceSettings() {
    return Column(
      children: [
        _buildSettingItem(
          'schedule'.tr,
          onTap: () => controller.onScheduleTap(),
        ),
        Opacity(
          opacity: controller.enableParingMode.value ? 1.0 : 0.5,
          child: _buildSettingItem(
            'matterPairingMode'.tr,
            onTap: () => controller.onMatterPairingModeTap(),
          ),
        ),
        _buildSettingItem(
          'changeRoom'.tr,
          onTap: () => controller.onChangeRoomTap(),
        ),
        _buildSettingItem(
          'installerSetting'.tr,
          onTap: () => controller.onInstallerSettingTap(),
        ),
      ],
    );
  }

  Widget _buildSettingItem(String title, {VoidCallback? onTap}) {
    return InkWell(
      onTap: controller.isOnline ? onTap : null,
      child: Padding(
        padding: const EdgeInsets.only(left: 10),
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title.tr,
                  style: TextStyles.medium18.copyWith(
                    color: AppColorsHelper.firstColor,
                  ),
                ),
              ),
              SizedBox(
                width: 40,
                height: 50,
                child: Center(
                  child: SvgPicture.asset(
                    AppImagePaths.chevronForward,
                    colorFilter: ColorFilter.mode(
                      AppColorsHelper.firstColor,
                      BlendMode.srcIn,
                    ),
                    width: 18,
                    height: 18,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeatOnDemand() {
    return Padding(
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: Obx(() {
        final enableHeatOnDemand = controller.heatOnDemand.value == 0;
        return SizedBox(
          width: double.infinity,
          height: 50,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  'heatOnDemand'.tr,
                  style: TextStyles.medium18.copyWith(
                    color: AppColorsHelper.firstColor,
                  ),
                ),
              ),
              SizedBox(
                width: 40,
                height: 50,
                child: Center(
                  child: HabiSwitch(
                    value: enableHeatOnDemand,
                    isLoading: controller.isHeatOnDemand.value,
                    onTap: controller.isOnline
                        ? () => controller.onHeatOnDemandTap()
                        : null,
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildChildLock() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Obx(() {
        final isLocked = controller.isLocked;
        final iconColor = controller.isOnline
            ? AppColors.ff01A796
            : AppColorsHelper.firstColor;

        return Row(
          children: [
            SizedBox.square(
              dimension: 44,
              child: Center(
                child: SvgPicture.asset(
                  isLocked ? AppImagePaths.lock : AppImagePaths.unlock,
                  colorFilter: ColorFilter.mode(
                    iconColor,
                    BlendMode.srcIn,
                  ),
                  width: 24,
                  height: 24,
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Text(
                  'childLock'.tr,
                  style: TextStyles.regular16.copyWith(
                    color: AppColorsHelper.firstColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
            SizedBox(
              width: 60,
              height: 44,
              child: Center(
                child: HabiSwitch(
                  value: isLocked,
                  isLoading: controller.isUpdatingLockKey.value,
                  onTap: controller.isOnline
                      ? () => controller.onChildLockTap()
                      : null,
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildIdentifyMe() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double calculatedWidth = constraints.maxWidth * 0.8;
        if (calculatedWidth > 360) {
          calculatedWidth = 360;
        }
        return Container(
          width: calculatedWidth,
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Obx(() {
            Color buttonColor = AppColors.ff01A796;
            Color backgroundColor = Colors.transparent;

            if (!controller.isOnline) {
              buttonColor = AppColorsHelper.firstColor;
            } else if (controller.isIdentifying.value) {
              buttonColor = Colors.white;
              backgroundColor = AppColors.ff01A796;
            }

            return OutlinedButton(
              onPressed:
                  controller.isOnline ? controller.onIdentifyMeTap : null,
              style: OutlinedButton.styleFrom(
                backgroundColor: backgroundColor,
                foregroundColor: buttonColor,
                side: BorderSide(color: buttonColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24.0),
                ),
                padding: const EdgeInsets.all(12.0),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox.square(
                    dimension: 24,
                    child: SvgPicture.asset(
                      AppImagePaths.identify,
                      colorFilter: ColorFilter.mode(
                        buttonColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Flexible(
                    child: Text(
                      'identifyMe'.tr,
                      style: TextStyles.medium14.copyWith(
                        color: buttonColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 10),
                  const SizedBox.square(
                    dimension: 24,
                  ),
                ],
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildDeviceStatus() {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusItem(
            'batteryStatus'.tr,
            content: _buildBatteryStatus(),
          ),
          const SizedBox(height: 20),
          _buildStatusItem(
            'connectivityStatus'.tr,
            content: _buildConnectivityStatus(),
          ),
          const SizedBox(height: 20),
          _buildStatusItem(
            'model'.tr,
            content: _buildModel(),
          ),
          const SizedBox(height: 20),
          _buildStatusItem(
            'nodeId'.tr,
            content: _buildNodeId(),
          ),
          const SizedBox(height: 20),
          _buildStatusItem(
            'thingName'.tr,
            content: _buildThingName(),
          ),
          const SizedBox(height: 20),
          _buildStatusItem(
            'firmwareVersion'.tr,
            content: _buildFirmwareVersion(),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String title, {required Widget content}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Opacity(
          opacity: 0.5,
          child: Text(
            title.tr,
            style: TextStyles.regular16.copyWith(
              color: AppColorsHelper.firstColor,
            ),
          ),
        ),
        const SizedBox(height: 5),
        content,
      ],
    );
  }

  Widget _buildBatteryStatus() {
    return Obx(() {
      return SvgPicture.asset(
        controller.batteryIcon,
        colorFilter: !controller.isOnline
            ? ColorFilter.mode(
                AppColorsHelper.firstColor,
                BlendMode.srcIn,
              )
            : null,
        width: 18,
        height: 18,
      );
    });
  }

  Widget _buildConnectivityStatus() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Obx(() {
            return Text(
              controller.connectionStatus,
              style: TextStyles.regular16.copyWith(
                color: AppColorsHelper.firstColor,
                height: 1,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }),
        ),
        const SizedBox(width: 10),
        InkWell(
          onTap: controller.isOnline ? controller.onSignalIconTap : null,
          child: SizedBox.square(
            dimension: 44,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Obx(() {
                  return SvgPicture.asset(
                    controller.signalIcon,
                    width: 24,
                    height: 24,
                    theme: SvgTheme(
                      currentColor: controller.isOnline
                          ? AppColors.ff01A796
                          : AppColorsHelper.firstColor,
                    ),
                  );
                }),
                const SizedBox(height: 2),
                Obx(() {
                  final readRssiCount = controller.readRssiCount.value;
                  final readRssiProgress = <Widget>[];
                  for (int i = 0; i < controller.maxReadRssiCount; i++) {
                    readRssiProgress.add(
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 1),
                        width: 5,
                        height: i < readRssiCount ? 5 : 0,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.ff01A796,
                        ),
                      ),
                    );
                  }
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: readRssiProgress,
                  );
                }),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 44,
          child: Align(
            alignment: Alignment.topCenter,
            child: InkWell(
                onTap: controller.onSignalInfoIconTap,
                child: Padding(
                  padding: const EdgeInsets.all(5),
                  child: SvgPicture.asset(
                    colorFilter: ColorFilter.mode(
                      AppColorsHelper.firstColor,
                      BlendMode.srcIn,
                    ),
                    AppImagePaths.iconInfo,
                    width: 16,
                    height: 16,
                  ),
                )),
          ),
        ),
      ],
    );
  }

  Widget _buildModel() {
    return Text(
      controller.model,
      style: TextStyles.regular16.copyWith(
        color: AppColorsHelper.firstColor,
      ),
    );
  }

  Widget _buildNodeId() {
    return Obx(() {
      return Text(
        controller.nodeId.value,
        style: TextStyles.regular16.copyWith(
          color: AppColorsHelper.firstColor,
        ),
      );
    });
  }

  Widget _buildThingName() {
    return Text(
      controller.thingName,
      style: TextStyles.regular16.copyWith(
        color: AppColorsHelper.firstColor,
      ),
    );
  }

  Widget _buildFirmwareVersion() {
    return Obx(() {
      return Text(
        controller.firmwareVersion.value,
        style: TextStyles.regular16.copyWith(
          color: AppColorsHelper.firstColor,
        ),
      );
    });
  }

  Widget _buildDeleteButton() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double calculatedWidth = constraints.maxWidth * 0.8;
        if (calculatedWidth > 360) {
          calculatedWidth = 360;
        }
        return Container(
          width: calculatedWidth,
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: OutlinedButton(
            onPressed: () => controller.onDeleteDeviceTap(),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.ffFF4E4E,
              side: const BorderSide(color: AppColors.ffFF4E4E),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.0),
              ),
              padding: const EdgeInsets.all(12.0),
            ),
            child: Text(
              'deleteDevice'.tr,
              style: TextStyles.medium14.copyWith(
                color: AppColors.ffFF4E4E,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }
}
