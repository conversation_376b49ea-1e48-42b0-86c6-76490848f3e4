import 'package:get/instance_manager.dart';
import 'add_thermostat_settings_controller.dart';

class AddThermostatSettingsBindings extends Bindings {
  final String gwThingName;
  final String thingName;

  AddThermostatSettingsBindings({
    required this.gwThingName,
    required this.thingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => AddThermostatSettingsController(
        gwThingName: gwThingName,
        thingName: thingName,
    ));
  }
}
