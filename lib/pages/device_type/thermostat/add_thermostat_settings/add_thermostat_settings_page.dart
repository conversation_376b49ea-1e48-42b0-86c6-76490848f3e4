import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'add_thermostat_settings_controller.dart';
import 'package:habi_app/pages/add_room/add_room_bindings.dart';
import 'package:habi_app/pages/add_room/add_room_page.dart';

class AddThermostatSettingsPage extends BasePage<AddThermostatSettingsController> {

  AddThermostatSettingsPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Obx(() {
            return Text(
              controller.title.value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            );
          }),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 56.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [

                      SizedBox(
                        width: 56.w,
                        height: 56.h,
                        child: Center(
                          child: SvgPicture.asset(
                            AppImagePaths.thermostat,
                            width: 56,
                            height: 56,
                          ),
                        ),
                      ),

                      Expanded(
                        child: Center(
                          child: Obx(() {
                            if (controller.editName.value) {
                              return ConstrainedBox(
                                constraints: const BoxConstraints(
                                    maxHeight: 148,
                                    minHeight: 48
                                ),
                                child: TextFormField(
                                  onFieldSubmitted: (value) {
                                    log.i('onFieldSubmitted() -> value: $value');
                                    controller.deviceName.value = value;
                                    controller.editName.value = false;
                                  },
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: AppFontWeights.regular,
                                    color: Theme.of(context)
                                        .extension<AppThemeExtension>()!.firstColor,
                                  ),
                                  controller: controller.editNameController,
                                ),
                              );
                            }

                            return Text(controller.deviceName.value,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: AppFontWeights.regular,
                                  overflow: TextOverflow.ellipsis,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!.firstColor,
                                )
                            );
                          }),
                        ),
                      ),

                      SizedBox(
                        width: 56.w,
                        height: 56.h,
                        child: InkWell(
                          onTap: () {
                            controller.editName.value = !controller.editName.value;
                          },
                          child: Center(
                            child: SvgPicture.asset(
                              AppImagePaths.edit,
                              width: 22,
                              height: 22,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                Obx(() {
                  if (controller.roomEntries.isEmpty) {
                    return const SizedBox();
                  }
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 14.h),
                      Opacity(
                        opacity: 0.5,
                        child: Text(
                          'room'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),

                      SizedBox(height: 14.h),

                      DropdownMenu(
                        width: Get.width - 72,
                        menuHeight: 350.h,
                        dropdownMenuEntries: controller.roomEntries,
                        initialSelection: controller.selectedRoomEntry.value,
                        onSelected: _onRoomSelected,
                      ),
                    ],
                  );
                }),

                Column(
                  children: [
                    SizedBox(height: 14.h),
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: OutlinedButton.icon(
                        onPressed: _addToNewRoom,
                        icon: const Icon(
                          Icons.add,
                          color: AppColors.ff01A796,
                        ),
                        label: Text(
                          'addNewRoom'.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: AppFontWeights.regular,
                            color: AppColors.ff01A796,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 149.h),
          
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _onSave,
                    child: Text(
                      'save'.tr.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
          
                SizedBox(height: 21.h),

              ],
            ),
          ),
        )
    );
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    return null;
  }

  void _onRoomSelected(String? value) {
    if (value == null || value.isEmpty) {
      return;
    }
    controller.selectedRoomEntry.value = value;
  }


  void _addToNewRoom() async {
    String homeId = controller.currentHome?.homeId ?? '';
    if (homeId.isEmpty) {
      showSnackBar('No home found');
      return;
    }
    var result = await Get.to(
      () => AddRoomPage(),
      binding: AddRoomBindings(
        homeId: homeId,
        rooms: controller.currentRooms,
      ),
    );
    if (result != null) {
      await controller.getHomeRooms();
    }
  }

  void _onSave() {
    if (controller.selectedRoomEntry.isEmpty) {
      showSnackBar('Please choose which Room you want Device to join.');
      return;
    }
    controller.handelAddDevice();
  }


}
