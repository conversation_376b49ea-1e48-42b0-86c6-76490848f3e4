import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/models/app_room.dart';
import 'package:habi_app/models/dev/hb_home_response.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/models/dev/hb_room_response.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class AddThermostatSettingsController extends BaseController {
  final deviceShadowService = DeviceShadowService.to;

  final String gwThingName;
  final String thingName;
  final TextEditingController editNameController = TextEditingController(text: 'myThermostat'.tr);

  AppHome? currentHome;

  var title = 'addThermostat'.tr.obs;
  var selectedUnit = 0.obs;
  var selectedFormat = 0.obs;
  var editName = false.obs;
  var deviceName = 'myThermostat'.tr.obs;
  var roomEntries = <DropdownMenuEntry<String>>[].obs;
  var selectedRoomEntry = ''.obs;
  List<HBRoom> currentRooms = [];
  Set<String> currentRoomIds = {}; // 可能由于某些错误导致多个 Room 同时拥有同一台设备

  AddThermostatSettingsController({
    required this.gwThingName,
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    var parseResult = Sait85rHelper.parseThingName(thingName);
    if (parseResult.isNotEmpty) {
      var pid = parseResult['pid'] as String;
      if (pid == '8000' || pid == '0001') {
        title.value = 'addThermostat'.tr;
        deviceName.value = 'myThermostat'.tr;
        editNameController.text = 'myThermostat'.tr;
      } else if (pid == '8001' || pid == '0002') {
        title.value = 'addTrv'.tr;
        deviceName.value = 'myTrv'.tr;
        editNameController.text = 'myTrv'.tr;
      }
    }

    Future(loadAllData);
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> loadAllData() async {
    updateLoading(true);
    try {
      await _getCurrentHome();

      if (currentHome != null) {
        await _getCurrentHomeRooms();
      } else {
        log.e('loadAllData() -> no home found');
      }

      updateLoading(false);
    } catch (e) {
      log.e('loadAllData() -> failed: $e');
      updateLoading(false);
      showLoadDataErrorDialog(() {
        loadAllData();
      });
    }
  }

  Future<void> getHomeRooms() async {
    updateLoading(true);
    try {
      await _getCurrentHomeRooms();
      updateLoading(false);
    } catch (e) {
      updateLoading(false);
      showLoadDataErrorDialog(() {
        getHomeRooms();
      });
    }
  }

  Future<void> _getCurrentHome() async {
    try {
      HBHomeResponse response = await DevService.to
          .getUserHomes()
          .timeout(const Duration(seconds: 60));
      bool isSuccess = response.success ?? false;

      if (isSuccess) {
        log.i('getCurrentHome() -> load homes succeeded');
        var homes = response.homes ?? [];
        var appHomes = <AppHome>[];

        for (var home in homes) {
          var appHome = AppHome.fromHBHome(home);
          appHomes.add(appHome);
        }

        if (appHomes.isNotEmpty) {
          for (var appHome in appHomes) {
            var devices = appHome.deviceList ?? [];
            for (var deviceId in devices) {
              if (deviceId == gwThingName) {
                currentHome = appHome;
                log.i('getCurrentHome() -> found home: ${appHome.homeId}');
                break;
              }

              if (deviceId.contains(gwThingName)) {
                currentHome = appHome;
                log.i('getCurrentHome() -> found home: ${appHome.homeId}');
                break;
              }
            }
          }
        }
      } else {
        throw Exception('Failed to get user homes: ${response.errorCode}');
      }
    } catch (e) {
      log.e('getCurrentHome() -> failed: $e');
      rethrow;
    }
  }

  Future<void> _getCurrentHomeRooms() async {
    try {
      HBRoomResponse response = await DevService.to
          .getHomeRooms(currentHome!.homeId!)
          .timeout(const Duration(seconds: 60));
      bool isSuccess = response.success ?? false;

      if (isSuccess) {
        log.i('getCurrentHomeRooms() -> load rooms succeeded');
        currentRooms = response.rooms ?? [];
        currentRoomIds = {};

        if (currentRooms.isEmpty) {
          return;
        }

        List<DropdownMenuEntry<String>> roomOptions = [];

        for (var room in currentRooms) {
          final name = room.name;
          final id = room.roomId;
          final deviceList = room.deviceList ?? [];

          if (name != null && id != null && id.isNotEmpty) {
            if (deviceList.contains(thingName)) {
              currentRoomIds.add(id);
            }
            roomOptions.add(
              DropdownMenuEntry(
                label: name,
                value: id,
              ),
            );
          }
        }

        log.i('getCurrentHomeRooms() -> currentRoomIds: $currentRoomIds');

        roomEntries.value = roomOptions;
        if (roomOptions.isNotEmpty) {
          if (currentRoomIds.isNotEmpty) {
            selectedRoomEntry.value = currentRoomIds.first;
          } else {
            selectedRoomEntry.value = roomOptions.first.value;
          }
        }
      } else {
        throw Exception('Failed to get home rooms: ${response.errorCode}');
      }
    } catch (e) {
      log.e('getCurrentHomeRooms() -> failed: $e');
      rethrow;
    }
  }

  Future<void> handelAddDevice() async {
    updateLoadingMessage('saving...');

    String newRoomId = selectedRoomEntry.value;

    if (!currentRoomIds.contains(newRoomId)) {
      try {
        log.i('handelAddDevice() -> addDeviceToRoom: $newRoomId');
        await DevService.to
            .addDeviceToRoom(newRoomId, thingName)
            .timeout(const Duration(seconds: 60));
      } catch (e) {
        log.e('handelAddDevice() -> addDeviceToRoom failed', error: e);
        updateLoading(false);
        DialogUtils.showErrorDialog(
          title: 'saveFailed'.tr,
          content: 'unableToAddDeviceToRoom'.tr,
        );
        return;
      }
    }

    final oldRoomIds = currentRoomIds.where((id) => id != newRoomId);

    if (oldRoomIds.isNotEmpty) {
      try {
        log.i('handelAddDevice() -> removeDeviceFromRoom: $oldRoomIds');
        await Future.wait(oldRoomIds.map((id) {
          return DevService.to.removeDeviceFromRoom(id, thingName);
        })).timeout(const Duration(seconds: 60));
      } catch (e) {
        log.e('handelAddDevice() -> removeDeviceFromRoom failed', error: e);
        updateLoading(false);
        DialogUtils.showErrorDialog(
          title: 'saveFailed'.tr,
          content: 'unableToRemoveDeviceFromRoom'.tr,
        );
        return;
      }
    }

    publishProperty();

    Get.until((route) {
      return route.settings.name == Routes.homeManagement;
    });

    GlobalService.to
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.addDevice));

    updateLoading(false);
  }


  Future<void> publishProperty() async {
    try {
      await deviceShadowService.updateDeviceProperties(
          thingName: thingName,
          property: {
            'ep1:sMDO:sDeviceName': deviceName.value,
          },
          subId: IT850.subId
      ).timeout(const Duration(seconds: 60));
      log.i('handelAddReceiver() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('handelAddReceiver() -> failed to publish data to cloud: $e, $r');
    }
  }

  void showErrorDialog(String content) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                'saveFailed'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                content,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

  void showLoadDataErrorDialog(void Function() onRetry) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                'failedToLoadData'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                'unableToFetchDataFromServer'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'cancel'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  onRetry();
                },
                child: Text(
                  'tryAgain'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

}
