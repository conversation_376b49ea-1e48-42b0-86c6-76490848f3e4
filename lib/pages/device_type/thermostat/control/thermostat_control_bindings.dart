import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/device_type/thermostat/control/thermostat_control_controller.dart';

class ThermostatControlBindings extends Bindings {

  final String homeId;
  final String roomId;
  final String thingName;

  ThermostatControlBindings({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => ThermostatControlController(
      homeId: homeId,
      roomId: roomId,
      thingName: thingName,
    ));
  }
}
