import 'dart:async';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/control_modes.dart';
import 'package:habi_app/constants/system_modes.dart';
import 'package:habi_app/constants/thermostat_constants.dart';
import 'package:habi_app/helpers/app_number_helper.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/extensions/it850_extension.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'thermostat_control_page.dart';

class ThermostatControlController extends BaseController {
  final globalService = GlobalService.to;
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;

  final String homeId;
  final String roomId;
  final String thingName;

  late String gwThingName;
  late String mcThingName;
  late IT850 it850;
  late Map<String, dynamic> _manualPropertyMap;

  var nodeId = ''.obs;
  var deviceName = 'myThermostat'.tr.obs;
  var systemMode = SystemModes.off.obs;
  var scheduleMode = 0.obs;
  var isConnected = true.obs;
  var currentTemp = 0.0.obs;
  var desiredTemp = 0.0;
  var desiredTempForSlider = 5.0;
  var colorIndex = 0;
  var runningState = 0.obs;
  var highlightIndex = 0;

  bool it850Initialized = false;
  double lastDesiredTemp = 0.0;

  StreamSubscription? localSubscription;
  StreamSubscription? remoteSubscription;
  
  Timer? _changeActionModeTimer;
  Timer? _changeSetPointTimer;
  Timer? _sliderColorsAnimationTimer;

  ThermostatControlController({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  }) {
    var result = Sait85rHelper.parseThingName(thingName);
    gwThingName = result['gwThingName'] ?? '';
    mcThingName = Sait85rHelper.getMBRCThingName(gwThingName);
    log.i('gwThingName=$gwThingName, mcThingName=$mcThingName');
  }

  @override
  void onInit() {
    super.onInit();
    _manualPropertyMap = {};
  }

  @override
  void onReady() {
    super.onReady();
    setMqttListener();
    Future(() async {
      int controlMode = await localStorageService.getControlMode() ?? ControlModes.remote;
      if (controlMode == ControlModes.remote) {
        await loadDataWithRemote();
      } else {
        await loadDataWithLocal();
      }
    });
  }

  @override
  void onClose() {
    colorIndex = 0;
    _manualPropertyMap.clear();
    
    _changeSetPointTimer?.cancel();
    _changeSetPointTimer = null;

    _sliderColorsAnimationTimer?.cancel();
    _sliderColorsAnimationTimer = null;
    
    _changeActionModeTimer?.cancel();
    _changeActionModeTimer = null;

    remoteSubscription?.cancel();
    localSubscription?.cancel();

    if (localSubscription != null) {
      Future(() async{
        String? fabricId = await FabricHelper.getFabricId(mcThingName);
        if (fabricId == null || fabricId.isEmpty) {
          log.e('onClose() -> fabricId is null.');
          return;
        }
        if (nodeId.value.isEmpty) {
          log.e('onClose() -> nodeId is empty.');
          return;
        }
        CtFlutterMatterPlugin.getInstance()
            .unsubscribes(fabricId, nodeId.value);
      });
    }

    super.onClose();
  }

  Future<void> setMqttListener() async {
    remoteSubscription = globalService.getMqttStream().listen((shadow) async {
      if (shadow.thingName == thingName) {
        try {
          it850 = it850.mapJson(shadow.thingShadow);

          bool thatIsConnected = it850.isConnected();
          if (thatIsConnected != isConnected.value) {
            log.i('setMqttListener() -> 刷新connected');
            isConnected.value = thatIsConnected;
          }

          double thatLocalTemp = it850.getLocalTempValue();
          if (thatLocalTemp != currentTemp.value) {
            log.i('setMqttListener() -> 刷新localTemp');
            currentTemp.value = thatLocalTemp;
          }

          int runningStateValue = it850.getRunningState();
          if (runningStateValue != runningState.value) {
            log.i('setMqttListener() -> 刷新runningState');
            runningState.value = runningStateValue;
          }

          String systemModeKey = 'ep1:sTherS:SystemMode';
          if (_manualPropertyMap.containsKey(systemModeKey)) {
            log.i('setMqttListener() -> 包含systemModeKey');
            int setSystemModeValue = _manualPropertyMap[systemModeKey] as int;
            int systemModeValue = it850.getSystemMode();
            if (systemModeValue == setSystemModeValue) {
              log.i('setMqttListener() -> 取消systemMode定时器');
              _manualPropertyMap.remove(systemModeKey);

              _changeActionModeTimer?.cancel();
              _changeActionModeTimer = null;

              systemMode.value = systemModeValue;

              updateLoading(false);
            }
          } else {
            int systemModeValue = it850.getSystemMode();
            if (systemModeValue != systemMode.value) {
              log.i('setMqttListener() ->刷新systemMode');
              systemMode.value = systemModeValue;
            }
          }

          if (systemMode.value == SystemModes.cool) {
            String coolingSpKey = 'ep1:sTherS:CoolingSp';
            if (_manualPropertyMap.containsKey(coolingSpKey)) {
              log.i('setMqttListener() -> 包含coolingSpKey');
              int setCoolingSpValue = _manualPropertyMap[coolingSpKey] as int;
              int coolingSp = it850.getCoolingSp();
              if (coolingSp == setCoolingSpValue) {
                log.i('setMqttListener() -> 取消setPoint定时器');
                _manualPropertyMap.remove(coolingSpKey);

                _changeSetPointTimer?.cancel();
                _changeSetPointTimer = null;

                _sliderColorsAnimationTimer?.cancel();
                _sliderColorsAnimationTimer = null;

                colorIndex = 0;
                update([ThermostatControlPage.thermostatSliderId]);
              }
            } else {
              double value = it850.getCoolingSpValue();
              if (value != desiredTemp) {
                log.i('setMqttListener() -> 刷新coolingSp');
                desiredTemp = value;
                desiredTempForSlider = desiredTemp;
                highlightIndex = getHighLightIndex();
                update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
              }
            }
          } else {
            String heatingSpKey = 'ep1:sTherS:HeatingSp';
            if (_manualPropertyMap.containsKey(heatingSpKey)) {
              log.i('setMqttListener() -> 包含heatingSpKey');
              int setHeatingSpValue = _manualPropertyMap[heatingSpKey] as int;
              int heatingSp = it850.getHeatingSp();
              if (heatingSp == setHeatingSpValue) {
                log.i('setMqttListener() -> 取消setPoint定时器');
                _manualPropertyMap.remove(heatingSpKey);

                _changeSetPointTimer?.cancel();
                _changeSetPointTimer = null;

                _sliderColorsAnimationTimer?.cancel();
                _sliderColorsAnimationTimer = null;

                colorIndex = 0;
                update([ThermostatControlPage.thermostatSliderId]);
              }
            } else {
              double value = it850.getHeatingSpValue();
              if (value != desiredTemp) {
                log.i('setMqttListener() -> 刷新heatingSp');
                desiredTemp = value;
                desiredTempForSlider = desiredTemp;
                highlightIndex = getHighLightIndex();
                update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
              }
            }
          }

          String scheduleEnKey = 'ep1:sTimeH:ScheduleEn';
          if (_manualPropertyMap.containsKey(scheduleEnKey)) {
            log.i('setMqttListener() -> 包含scheduleEnKey');
            int setScheduleEnValue = _manualPropertyMap[scheduleEnKey] as int;
            int scheduleEnValue = it850.getScheduleEnableValue();
            if (scheduleEnValue == setScheduleEnValue) {
              log.i('setMqttListener() -> 取消scheduleMode定时器');
              _manualPropertyMap.remove(scheduleEnKey);

              _changeActionModeTimer?.cancel();
              _changeActionModeTimer = null;

              scheduleMode.value = scheduleEnValue;

              updateLoading(false);
            }
          } else {
            int scheduleEnValue = it850.getScheduleEnableValue();
            if (scheduleEnValue != scheduleMode.value) {
              log.i('setMqttListener() ->刷新scheduleMode');
              scheduleMode.value = scheduleEnValue;
            }
          }

          String thatDeviceName = it850.getDeviceName();
          if (thatDeviceName != deviceName.value) {
            log.i('setMqttListener() ->刷新deviceName');
            deviceName.value = thatDeviceName;
          }

        } catch (e) {
          log.e('setMqttListener() ->failed: $e');
        }
      }
    });
  }

  Future<void> loadDataWithRemote() async {
    updateLoading(true);

    try {
      AppShadow shadow = deviceShadowService.getDeviceShadow(thingName);
      it850 = IT850.fromJson(shadow.thingShadow, thingName);
      it850Initialized = true;
      await setupData();

      Map<String, dynamic> thingShadow = await deviceShadowService.fetchDeviceShadow(thingName);
      it850 = IT850.fromJson(thingShadow, thingName);
      it850Initialized = true;
      await setupData();
    } catch (e) {
      log.e('loadDataWithRemote() -> failed: $e');
    }

    updateLoading(false);
  }

  Future<void> setupData() async {
    isConnected.value = it850.isConnected();
    deviceName.value = it850.getDeviceName();
    nodeId.value = it850.getNodeId();
    systemMode.value = it850.getSystemMode();
    scheduleMode.value = it850.getScheduleEnableValue();
    runningState.value = it850.getRunningState();
    currentTemp.value = it850.getLocalTempValue();

    if (systemMode.value == SystemModes.cool) {
      desiredTemp = it850.getCoolingSpValue();
    } else {
      desiredTemp = it850.getHeatingSpValue();
    }

    desiredTempForSlider = desiredTemp;
    lastDesiredTemp = desiredTemp;
    highlightIndex = getHighLightIndex();
    update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId,ThermostatControlPage.thermostatSliderId]);
  }

  Future<void> loadDataWithLocal() async {
    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    if (fabricId == null || fabricId.isEmpty) {
      log.e('fabricId is null.');
      return;
    }

    AppShadow appShadow = deviceShadowService.getDeviceShadow(thingName);
    it850 = IT850.fromJson(appShadow.thingShadow, mcThingName);
    String thatNodeId = it850.getNodeId();

    if (thatNodeId.isEmpty) {
      log.e('nodeId is empty.');
      return;
    }

    nodeId.value = thatNodeId;
    updateLoading(true);

    try {
      var systemModeMap = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId.value, 1, 513, 28);
      log.i('systemModeMap=$systemModeMap');
      int thatSystemMode = systemModeMap['SystemMode'] as int;
      systemMode.value = thatSystemMode;
      if (thatSystemMode == 3) {
        var occupiedCoolingSetpointMap =
            await CtFlutterMatterPlugin.getInstance()
                .readAttribute(fabricId, nodeId.value, 1, 513, 17);
        log.i('occupiedCoolingSetpointMap=$occupiedCoolingSetpointMap');
        int occupiedCoolingSetpoint =
            occupiedCoolingSetpointMap['OccupiedCoolingSetpoint'] as int;
        desiredTemp =
            AppNumberHelper.roundDouble(occupiedCoolingSetpoint / 100, 1);
        desiredTempForSlider = desiredTemp;
        highlightIndex = getHighLightIndex();
        update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
      } else if (thatSystemMode == 4) {
        var occupiedHeatingSetpointMap =
            await CtFlutterMatterPlugin.getInstance()
                .readAttribute(fabricId, nodeId.value, 1, 513, 18);
        log.i('occupiedHeatingSetpointMap=$occupiedHeatingSetpointMap');
        int occupiedHeatingSetpoint =
            occupiedHeatingSetpointMap['OccupiedHeatingSetpoint'] as int;
        desiredTemp =
            AppNumberHelper.roundDouble(occupiedHeatingSetpoint / 100, 1);
        desiredTempForSlider = desiredTemp;
        highlightIndex = getHighLightIndex();
        update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
      } else if (thatSystemMode == 0) {}

      var localTemperatureMap = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId.value, 1, 513, 0);
      log.i('localTemperatureMap=$localTemperatureMap');
      int localTemperature = localTemperatureMap['LocalTemperature'] as int;
      currentTemp.value =
          AppNumberHelper.roundDouble(localTemperature / 100, 1);

      localSubscription = CtFlutterMatterPlugin.getInstance()
          .onAttributesStream
          .listen((attributeMap) {
        log.i('onAttributesStream() -> $attributeMap');
        try {
          if (attributeMap["OccupiedCoolingSetpoint"] != null) {
            int occupiedCoolingSetpoint =
                attributeMap["OccupiedCoolingSetpoint"] as int;
            desiredTemp =
                AppNumberHelper.roundDouble(occupiedCoolingSetpoint / 100, 1);
            desiredTempForSlider = desiredTemp;
            highlightIndex = getHighLightIndex();
            update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
          }

          if (attributeMap["OccupiedHeatingSetpoint"] != null) {
            int occupiedHeatingSetpoint =
                attributeMap["OccupiedHeatingSetpoint"] as int;
            desiredTemp =
                AppNumberHelper.roundDouble(occupiedHeatingSetpoint / 100, 1);
            desiredTempForSlider = desiredTemp;
            highlightIndex = getHighLightIndex();
            update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
          }

          if (attributeMap['LocalTemperature'] != null) {
            int localTemperature = attributeMap['LocalTemperature'] as int;
            currentTemp.value =
                AppNumberHelper.roundDouble(localTemperature / 100, 1);
          }
        } catch (e) {
          log.e('onAttributesStream() -> error:$e');
        }
      });

      var isSubscribed = await CtFlutterMatterPlugin.getInstance()
          .subscribeAttributes(fabricId, nodeId.value);
      log.i('loadDataWithLocal() -> isSubscribed=$isSubscribed');
    } catch (e) {
      log.e('loadDataWithLocal() -> failed: $e');
    }

    updateLoading(false);
  }

  Future<void> changeSetPoint(int value) async {
    int controlMode = await localStorageService.getControlMode() ?? ControlModes.remote;
    if (controlMode == ControlModes.remote) {
      await changeSetPointWithRemote(value);
    } else {
      await changeSetPointWithLocal(value);
    }
  }

  Future<void> changeSetPointWithRemote(int value) async {
    int thatSystemMode = systemMode.value;
    log.i("changeSetPointWithRemote() -> systemMode=$thatSystemMode");

    Map<String, dynamic> property = {};
    String getKey = '';
    String setKey = '';

    if (thatSystemMode == SystemModes.heat) {
      setKey = 'ep1:sTherS:sHeatingSp';
      getKey = 'ep1:sTherS:HeatingSp';
      property[setKey] = value;
      _manualPropertyMap[getKey] = value;
    } else if (thatSystemMode == SystemModes.cool){
      setKey = 'ep1:sTherS:sCoolingSp';
      getKey = 'ep1:sTherS:CoolingSp';
      property[setKey] = value;
      _manualPropertyMap[getKey] = value;
    } else {
      log.e("changeSetPointWithRemote() -> unknown mode");
    }

    if (property.isEmpty) {
      log.e("changeSetPointWithRemote() -> property is empty.");
      return;
    }

    _changeSetPointTimer?.cancel();
    _changeSetPointTimer = Timer(const Duration(seconds: 30), () {
      log.i("changeSetPointWithRemote() -> timeout.");
      _manualPropertyMap.remove(getKey);

      _changeSetPointTimer?.cancel();
      _changeSetPointTimer = null;

      _sliderColorsAnimationTimer?.cancel();
      _sliderColorsAnimationTimer = null;

      colorIndex = 0;
      desiredTemp = lastDesiredTemp;
      desiredTempForSlider = lastDesiredTemp;
      highlightIndex = getHighLightIndex();
      update([ThermostatControlPage.desiredTempId, ThermostatControlPage.highlightIndexId, ThermostatControlPage.thermostatSliderId]);
      log.i("changeSetPointWithRemote() -> desiredTemp=$desiredTemp，desiredTempForSlider=$desiredTempForSlider");
    });

    colorIndex = 0;

    _sliderColorsAnimationTimer?.cancel();
    _sliderColorsAnimationTimer = Timer.periodic(const Duration(milliseconds: 250), (timer) {
      colorIndex = colorIndex + 1;
      update([ThermostatControlPage.thermostatSliderId]);
      if (colorIndex == 4) {
        colorIndex = 0;
        update([ThermostatControlPage.thermostatSliderId]);
      }
    });

    try {
      await deviceShadowService.updateDeviceProperties(
          thingName: thingName,
          property: property,
          subId: IT850.subId
      ).timeout(const Duration(seconds: 30));
      log.i('changeSetPointWithRemote() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('changeSetPointWithRemote() -> failed to publish data to cloud: $e, $r');
      _manualPropertyMap.remove(getKey);

      _changeSetPointTimer?.cancel();
      _changeSetPointTimer = null;

      _sliderColorsAnimationTimer?.cancel();
      _sliderColorsAnimationTimer = null;

      colorIndex = 0;
      update([ThermostatControlPage.thermostatSliderId]);
      showErrorSnackBar('Failed to change set point.');
    }

  }

  Future<void> changeSetPointWithLocal(int value) async {
    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    if (fabricId == null || fabricId.isEmpty) {
      log.e('changeSetPointWithLocal() -> fabricId = null');
      return;
    }

    int thatSystemMode = systemMode.value;
    log.i("changeSetPointWithLocal() -> mode = $thatSystemMode");
    if (thatSystemMode == SystemModes.heat) {
      updateLoading(true);
      try {
        var result = await CtFlutterMatterPlugin.getInstance()
            .writeAttribute(fabricId, nodeId.value, 1, 513, 18, "SignedInteger", value.toString(), 0);
        log.i("changeSetPointWithLocal() -> result: $result");
      } catch (e) {
        log.e("changeSetPointWithLocal() -> error: $e");
      } finally {
        updateLoading(false);
      }
    } else if (thatSystemMode == SystemModes.cool) {
      updateLoading(true);
      try {
        var result = await CtFlutterMatterPlugin.getInstance()
            .writeAttribute(fabricId, nodeId.value, 1, 513, 17, "SignedInteger", value.toString(), 0);
        log.i("changeSetPointWithLocal() -> result: $result");
      } catch (e) {
        log.e("changeSetPointWithLocal() -> error: $e");
      } finally {
        updateLoading(false);
      }
    } else {

    }
  }

  Future<void> changeSystemMode(int value) async {
    int controlMode = await localStorageService.getControlMode() ?? ControlModes.remote;
    if (controlMode == ControlModes.remote) {
      await changeSystemModeWithRemote(value);
    } else {
      await changeSystemModeWithLocal(value);
    }
  }

  Future<void> changeSystemModeWithRemote(int value) async {
    if (isLoading()) {
      return;
    }

    updateLoadingMessage('changing...');

    String setKey = 'ep1:sTherS:sSystemMode';
    String getKey = 'ep1:sTherS:SystemMode';

    _manualPropertyMap[getKey] = value;

    _changeActionModeTimer?.cancel();
    _changeActionModeTimer = Timer(const Duration(seconds: 30), () {
      log.i("changeSystemWithRemote() -> timeout.");
      _manualPropertyMap.remove(getKey);

      _changeActionModeTimer?.cancel();
      _changeActionModeTimer = null;

      updateLoading(false);
    });

    try {
      await deviceShadowService.updateDeviceProperties(
          thingName: thingName,
          property: {
            setKey: value,
          },
          subId: IT850.subId
      ).timeout(const Duration(seconds: 30));
      log.i('changeSetPointWithRemote() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('changeSetPointWithRemote() -> failed to publish data to cloud: $e, $r');
      _manualPropertyMap.remove(getKey);

      _changeActionModeTimer?.cancel();
      _changeActionModeTimer = null;

      updateLoading(false);
      showErrorSnackBar('Failed to change system mode.');
    }
  }

  Future<void> changeSystemModeWithLocal(int value) async {
    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    if (fabricId == null || fabricId.isEmpty) {
      log.e('changeSystemWithLocal() -> fabricId is null.');
      return;
    }

    updateLoading(true);

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .writeAttribute(fabricId, nodeId.value, 1, 513, 28, "UnsignedInteger", value.toString(), 0);
      log.i("changeSystemWithLocal() -> result=$result");
    } catch (e) {
      log.e("changeSystemWithLocal() -> error: $e");
    } finally {
      updateLoading(false);
    }
  }


  Future<void> changeScheduleModeWithRemote() async {
    if (isLoading()) {
      return;
    }

    updateLoadingMessage('changing...');

    String setKey = 'ep1:sTimeH:sScheduleEn';
    String getKey = 'ep1:sTimeH:ScheduleEn';

    int value = it850.isScheduleEnable() ? 0 : 1;

    log.i("changeScheduleModeWithRemote() -> value: $value");

    _manualPropertyMap[getKey] = value;

    _changeActionModeTimer?.cancel();
    _changeActionModeTimer = Timer(const Duration(seconds: 30), () {
      log.i("changeScheduleModeWithRemote() -> timeout.");
      _manualPropertyMap.remove(getKey);

      _changeActionModeTimer?.cancel();
      _changeActionModeTimer = null;

      updateLoading(false);
    });

    try {
      await deviceShadowService.updateDeviceProperties(
          thingName: thingName,
          property: {
            setKey: value,
          },
          subId: IT850.subId
      ).timeout(const Duration(seconds: 30));
      log.i('changeScheduleModeWithRemote() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('changeScheduleModeWithRemote() -> failed to publish data to cloud: $e, $r');
      _manualPropertyMap.remove(getKey);

      _changeActionModeTimer?.cancel();
      _changeActionModeTimer = null;

      updateLoading(false);
      showErrorSnackBar('Failed to change schedule mode.');
    }
  }

  int getHighLightIndex() {
    if (UserAttributesService.to.temperatureUnit == TemperatureUnitStatus.fahrenheit) {
      return ThermostatConstants.tempToIndexForFahrenheitMap[desiredTemp] ?? 0;
    } else {
      return ThermostatConstants.tempToIndexForCelsiusMap[desiredTemp] ?? 0;
    }
  }


}