import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gaimon/gaimon.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/system_modes.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/extensions/it850_extension.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/device_type/thermostat/setting/thermostat_settings_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/setting/thermostat_settings_page.dart';
import 'package:habi_app/widgets/slider_ticks_widget.dart';
import 'package:habi_app/widgets/thermostat_slider.dart';
import 'package:habi_app/widgets/thermostat_action_mode_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/device_type/thermostat/control/thermostat_control_controller.dart';

class ThermostatControlPage extends BasePage<ThermostatControlController> {

  static const String thermostatSliderId = 'thermostatSlider';
  static const String desiredTempId = 'desiredTemp';
  static const String highlightIndexId = 'highlightIndex';

  ThermostatControlPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Obx(() => Text(
            controller.deviceName.value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          )),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.more_vert, color: AppColors.ff01A796),
              onPressed: () {
                Get.to(() => ThermostatSettingsPage(),
                    binding: ThermostatSettingsBindings(
                        homeId: controller.homeId,
                        roomId: controller.roomId,
                        thingName: controller.thingName,
                    ));
              },
            ),
          ],
        ),
        body: Stack(
          children: [
            _buildMainBody(context),
            Positioned(
              left: (500.w / 2) - 20.w,
              bottom: 75.h,
              top: 0,
              child: Container(
                alignment: Alignment.center,
                child: GetBuilder<ThermostatControlController>(
                  init: controller,
                  id: highlightIndexId,
                  builder: (controller) {
                    return CustomPaint(
                      size: Size(560.w, 560.w),
                      painter: SliderTicksWidget(
                        tickCount: 150,
                        highlightIndex: controller.highlightIndex,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!
                            .firstColor,
                      ),
                    );
                  },
                ),
              ),
            ),

            Positioned(
                left: (500.w / 2) + 8.w,
                bottom: 75.h,
                top: 0,
              child: GetBuilder<ThermostatControlController>(
                init: controller,
                id: thermostatSliderId,
                builder: (controller) {
                  double min = 5;
                  double max = 35;

                  if (controller.it850Initialized) {
                    min = controller.it850.getSliderMinHeatSpValue();
                    max = controller.it850.getSliderMaxHeatSpValue();
                  }

                  if (controller.desiredTempForSlider < min) {
                    controller.desiredTempForSlider = min;
                  }

                  if (controller.desiredTempForSlider > max) {
                    controller.desiredTempForSlider = max;
                  }

                  Color dotColor = AppColors.ff01A796;
                  Color progressBarColor = AppColors.ff01A796;
                  if (!controller.isConnected.value) {
                    Color offlineColor = Theme.of(context)
                        .extension<AppThemeExtension>()!
                        .firstColor;
                    dotColor = offlineColor;
                    progressBarColor = offlineColor;
                  }

                  return ThermostatSlider(
                    size: 500.w,
                    dotColor: dotColor,
                    progressBarColor: progressBarColor,
                    progressBarColors: getProgressBarColors(controller.colorIndex),
                    initialValue: controller.desiredTempForSlider,
                    min: min,
                    max: max,
                    onChange: (value) {
                      log.i("ThermostatSlider{} -> onChange() -> value=$value");
                      var newValue = controller.it850.roundSliderValue(value);
                      log.i("ThermostatSlider{} -> onChange() -> newValue=$newValue");
                      if (controller.desiredTemp != newValue) {
                        controller.desiredTemp = newValue;
                        controller.highlightIndex = controller.getHighLightIndex();
                        controller.update([desiredTempId, highlightIndexId]);
                        log.i("ThermostatSlider{} -> onChange() -> highlightIndex=${controller.highlightIndex}");
                      }
                      if (controller.desiredTempForSlider != newValue) {
                        controller.desiredTempForSlider = newValue;
                      }
                      _executeHapticFeedback();
                    },
                    onChangeStart: (value) {
                      log.i("ThermostatSlider{} -> onChangeStart() -> value=$value");
                      var lastValue = controller.desiredTemp;
                      log.i("ThermostatSlider{} -> onChangeStart() -> lastValue=$lastValue");
                      controller.lastDesiredTemp = lastValue;
                    },
                    onChangeEnd: (value) {
                      log.i("ThermostatSlider{} -> onChangeEnd() -> value=$value");
                      var newValue = controller.it850.roundSliderValue(value);
                      log.i("ThermostatSlider{} -> onChangeEnd() -> newValue=$newValue");

                      controller.it850.setHeatingSpValue(newValue);
                      var heatingSp = controller.it850.getHeatingSp();
                      log.i("ThermostatSlider{} -> onChangeEnd() -> heatingSp=$heatingSp");
                      controller.changeSetPoint(heatingSp);
                    },
                  );
                },
              ),
            ),

            Obx(() {
              if (!controller.isConnected.value) {
                return Opacity(
                  opacity: 0.5,
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                  ),
                );
              }

              if (controller.isConnected.value && controller.systemMode.value == SystemModes.off) {
                return Opacity(
                  opacity: 0.5,
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                  ),
                );
              }

              return const SizedBox();
            }),

            Obx(() {
              if (controller.isConnected.value && controller.systemMode.value == SystemModes.off) {
                return Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(height: 31.h),

                      Padding(
                        padding: const EdgeInsets.only(left: 26),
                        child: buildActionModeForOffWidget(context),
                      ),

                      const Expanded(child: SizedBox()),

                    ],
                  ),
                );
              }

              return const SizedBox();
            })

          ],
        ),
    );
  }

  Widget _buildFormatTempWidget({
    required double temp,
    required double fontSize,
    required Color color,
  }) {
    final tempInt = temp.toInt();
    final tempStr = temp == tempInt ? tempInt.toString() : temp.toString();
    final parts = tempStr.split('.');
    final intPart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';

    const double lineHeight = 0.8;
    final double height = fontSize * lineHeight;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: fontSize * 0.3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: height,
            child: AutoSizeText(
              intPart,
              style: TextStyles.regular.copyWith(
                fontSize: 100,
                height: lineHeight,
                color: color,
              ),
              maxLines: 1,
            ),
          ),
          if (decimalPart.isNotEmpty)
            SizedBox(
              height: height * 0.5,
              child: AutoSizeText(
                '.$decimalPart',
                style: TextStyles.regular.copyWith(
                  fontSize: 100,
                  height: lineHeight,
                  color: color,
                ),
                maxLines: 1,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMainBody(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(height: 31.h),

        Padding(
          padding: const EdgeInsets.only(left: 26),
          child: buildActionModeWidget(context),
        ),

        SizedBox(height: 110.h),

        Padding(
          padding: const EdgeInsets.only(left: 36),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                  'currentTemp'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  )
              ),

              Align(
                alignment: Alignment.centerLeft,
                child: Obx(() {
                  return _buildFormatTempWidget(
                    temp: controller.currentTemp.value,
                    fontSize: 56,
                    color: AppColorsHelper.firstColor,
                  );
                }),
              ),

              SizedBox(height: 27.h),

              Text(
                  'desiredTemp'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  )
              ),

              Align(
                alignment: Alignment.centerLeft,
                child: GetBuilder<ThermostatControlController>(
                  init: controller,
                  id: desiredTempId,
                  builder: (controller) {
                    Color color = AppColors.ff01A796;
                    if (!controller.isConnected.value) {
                      color = Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor;
                    }
                    return _buildFormatTempWidget(
                      temp: controller.desiredTemp,
                      fontSize: 84,
                      color: color,
                    );
                  },
                ),
              ),

              SizedBox(height: 27.h),

            ],
          ),
        ),

        const Expanded(child: SizedBox()),

      ],
    );
  }

  Widget buildActionModeWidget(BuildContext context) {
    return Row(
      children: [
        Expanded(
            flex: 1,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: Container(
                      // color: Colors.lightBlue,
                      height: 100,
                      alignment: Alignment.center,
                      child: _buildScheduleMode(),
                    )),
                    Expanded(child: Container(
                      // color: Colors.black,
                      height: 100,
                      alignment: Alignment.center,
                      child: _buildOnOffWidget(),
                    ))
                  ],
                ),
                Row(
                    children: [
                      Expanded(child: Container(
                        // color: Colors.red,
                        height: 100,
                        alignment: Alignment.center,
                        child: _buildHeatDemandWidget(),
                      )),
                      Expanded(child: Container())
                    ],
                  ),
              ],
            )
        ),
        const Expanded(flex: 1, child: SizedBox())
      ],
    );
  }

  Widget buildActionModeForOffWidget(BuildContext context) {
    return Row(
      children: [
        Expanded(
            flex: 1,
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: Container(
                      // color: Colors.lightBlue,
                    )),
                    Expanded(child: Container(
                      // color: Colors.black,
                      height: 100,
                      alignment: Alignment.center,
                      child: _buildOnOffWidget(),
                    ))
                  ],
                ),
                Row(
                    children: [
                      Expanded(child: Container(
                        // color: Colors.red,
                        height: 100,
                        alignment: Alignment.center,
                        child: _buildHeatDemandWidget(),
                      )),
                      Expanded(child: Container())
                    ],
                  ),
              ],
            )
        ),
        const Expanded(flex: 1, child: SizedBox())
      ],
    );
  }

  Widget _buildScheduleMode() {
    return Obx(() {
      final isOffline = !controller.isConnected.value;
      final notEnabled = controller.scheduleMode.value == 0;
      return ThermostatActionModeWidget(
        label: 'schedule'.tr,
        icon: (isOffline || notEnabled) ? AppImagePaths.deviceSchedule
            : AppImagePaths.deviceScheduleEnable,
        isConnected: controller.isConnected.value,
        onTap: () {
          controller.changeScheduleModeWithRemote();
        },
      );
    });
  }

  Widget _buildHeatDemandWidget() {
    return Obx(() {
      final isOffline = !controller.isConnected.value;
      final noHeat = controller.runningState.value == 0;
      return ThermostatActionModeWidget(
        label: 'demandForHeat'.tr,
        icon: (isOffline || noHeat) ? AppImagePaths.thermostatNoHeat
            : AppImagePaths.thermostatHeat,
        isConnected: controller.isConnected.value,
        onTap: () {
          //no op
        },
      );
    });
  }

  Widget _buildOnOffWidget() {
    return Obx(() {
      final isOffline = !controller.isConnected.value;
      final isOff = controller.systemMode.value == SystemModes.off;
      return ThermostatActionModeWidget(
        label: (isOffline || isOff ? 'off'.tr : 'on'.tr).toUpperCase(),
        icon: (isOffline || isOff)
            ? AppImagePaths.thermostatToggleOff
            : AppImagePaths.thermostatToggleOn,
        isConnected: controller.isConnected.value,
        onTap: () {
          controller.changeSystemMode(
            isOff ? SystemModes.heat : SystemModes.off,
          );
        },
      );
    });
  }

  List<Color>? getProgressBarColors(int index) {
    List<Color>? list;
    switch (index) {
      case 1:
        list = [
          Colors.orange,
          Colors.green,
          Colors.lightBlueAccent,
          Colors.red
        ];
        break;
      case 2:
        list = [
          Colors.lightBlueAccent,
          Colors.orange,
          Colors.green,
          Colors.red
        ];
        break;
      case 3:
        list = [
          Colors.lightBlueAccent,
          Colors.green,
          Colors.orange,
          Colors.red
        ];
        break;
      case 4:
        list = [
          Colors.lightBlueAccent,
          Colors.green,
          Colors.red,
          Colors.orange,
        ];
        break;
    }
    return list;
  }

  Future<void> _executeHapticFeedback() async {
    try {
      if(await Gaimon.canSupportsHaptic) {
        Gaimon.selection();
        log.i("executeHapticFeedback() -> 执行触觉反馈成功");
      } else {
        log.d("executeHapticFeedback() -> 设备不支持触觉反馈");
      }
    } catch (e) {
      log.e("executeHapticFeedback() -> 执行触觉反馈失败: $e");
    }
  }
}
