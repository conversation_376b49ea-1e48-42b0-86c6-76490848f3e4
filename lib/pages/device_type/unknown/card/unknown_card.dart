import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_card_widget.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/device_type/unknown/setting/unknown_settings_bindings.dart';
import 'package:habi_app/pages/device_type/unknown/setting/unknown_settings_page.dart';
import 'package:habi_app/widgets/habi_shadow_card.dart';

class UnknownCard extends BaseCardWidget {

  final String homeId;
  final String roomId;

  UnknownCard({
    super.key,
    required super.thingName,
    required this.homeId,
    required this.roomId,
  });

  @override
  State<UnknownCard> createState() {
    return UnknownCardState();
  }
}

class UnknownCardState extends BaseCardState<UnknownCard> {

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.to(() => UnknownSettingsPage(),
          binding: UnknownSettingsBindings(
            homeId: widget.homeId,
            roomId: widget.roomId,
            thingName: widget.thingName,
          ),
        );
      },
      child: HabiShadowCard(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Center(
            child: Text(
              'unknownDevice'.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            ),
          ),
        ),
      ),
    );
  }

}