import 'package:get/instance_manager.dart';
import 'unknown_settings_controller.dart';

class UnknownSettingsBindings extends Bindings {

  final String homeId;
  final String roomId;
  final String thingName;

  UnknownSettingsBindings({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => UnknownSettingsController(
      homeId: homeId,
      roomId: roomId,
      thingName: thingName,
    ));
  }
}
