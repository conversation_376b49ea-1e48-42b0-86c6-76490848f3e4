import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/app_map_helper.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/value_completer.dart';
import 'package:habi_app/pages/change_room/change_room_bindings.dart';
import 'package:habi_app/pages/change_room/change_room_page.dart';
import 'package:habi_app/pages/device_type/thermostat/device_pairing_mode/device_pairing_mode_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/device_pairing_mode/device_pairing_mode_page.dart';
import 'package:habi_app/pages/device_type/thermostat/view_schedule/it850_schedule_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/Iot_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/home_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';
import 'package:habi_app/utility/home_utils.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:network_info_plus/network_info_plus.dart';

class UnknownSettingsController extends BaseController {
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;

  final editNameController = TextEditingController(text: 'unknownDevice'.tr);

  final String homeId;
  final String roomId;
  final String thingName;

  late String gwThingName;
  late String mcThingName;

  final subId = '11';
  final connected = false.obs;
  final editName = false.obs;
  final deviceName = 'unknownDevice'.tr.obs;
  final nodeId = ''.obs;
  final enableParingMode = false.obs;

  Worker? _deviceShadowWorker;
  ValueCompleter<void, String>? _setDeviceNameCompleter;
  bool get isOnline => connected.value;

  Map<Object?, Object?> openPairingWindowData = {};

  UnknownSettingsController({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  }) {
    var result = Sait85rHelper.parseThingName(thingName);
    gwThingName = result['gwThingName'] ?? '';
    mcThingName = Sait85rHelper.getMBRCThingName(gwThingName);
    log.i('gwThingName=$gwThingName, mcThingName=$mcThingName');
  }

  String get connectionStatus {
    return connected.value ? 'online'.tr : 'offline'.tr;
  }

  @override
  void onInit() {
    super.onInit();
    _deviceShadowWorker = deviceShadowService.watchDeviceShadow(
      thingName,
      onDeviceShadowUpdated,
    );

    AppShadow shadow = deviceShadowService.getDeviceShadow(thingName);
    onDeviceShadowUpdated(shadow);

    _tryReadVid();
  }

  void onDeviceShadowUpdated(AppShadow shadow) {
    log.i('shadow updated: $shadow');

    String connectedKey = 'connected';
    String deviceNameKey = 'ep1:sMDO:DeviceName';
    String nodeIdKey = 'ep1:sMDO:NodeID';

    final thatConnected = AppMapHelper.getDeepValueForKey(shadow.thingShadow, connectedKey);
    final thatDeviceName = AppMapHelper.getDeepValueForKey(shadow.thingShadow, deviceNameKey);
    final thatNodeId = AppMapHelper.getDeepValueForKey(shadow.thingShadow, nodeIdKey);

    log.i('thatConnected=$thatConnected');
    log.i('thatNodeId=$thatNodeId');
    log.i('thatDeviceName=$thatDeviceName');;

    connected.value = thatConnected == 'true';
    nodeId.value = thatNodeId ?? '';

    if (thatDeviceName != null && thatDeviceName.isNotEmpty) {
      deviceName.value = thatDeviceName;

      if (_setDeviceNameCompleter?.isCompleted == false &&
          _setDeviceNameCompleter?.extraValue == thatDeviceName) {
        _setDeviceNameCompleter!.complete();
      }
    }

  }

  @override
  void onClose() {
    if (_setDeviceNameCompleter?.isCompleted == false) {
      _setDeviceNameCompleter?.completeError(Exception('canceled'));
    }
    _deviceShadowWorker?.dispose();
    editNameController.dispose();
    super.onClose();
  }

  Future<void> deleteDevice() async {
    updateLoadingMessage('deleting...');

    /*String? fabricId = await FabricHelper.getFabricIdFromLocalStorage(mcThingName);
    if (fabricId != null && fabricId.isNotEmpty) {
      CtFlutterMatterPlugin.getInstance()
          .unpairDevice(fabricId, nodeId.value)
          .then((result) {
        log.i('deleteDevice() -> unpairDevice succeeded');
      }).catchError((error) {
        log.e('deleteDevice() -> unpairDevice failed: $error');
      }).timeout(const Duration(seconds: 15));
    }*/

    GlobalService.to
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.prepareDeleteDevice));

    int retryCount = 0;
    const maxRetries = 3;
    while (retryCount < maxRetries) {
      try {
        await deviceShadowService.updateDeviceProperties(
          thingName: thingName,
          property: {
            'ep1:sMDO:sLeavNetw': 1,
          },
          subId: subId,
        ).timeout(const Duration(seconds: 60));
        log.i('deleteDevice() -> 发送 sLeavNetw 给云端成功!');
        retryCount = maxRetries;
      } catch (e, r) {
        retryCount++;
        if (retryCount < maxRetries) {
          log.w('deleteDevice() -> 发送 sLeavNetw 给云端失败：$e，$r');
          log.w('deleteDevice() -> 发送 sLeavNetw 给云端重试中..., retryCount: $retryCount');
        } else {
          log.e('deleteDevice() -> 发送 sLeavNetw 给云端$maxRetries次重试全部失败：$e，$r');
          updateLoading(false);
          showErrorDialog('deleteDeviceFailed'.tr, 'requestFailed'.tr);
          return;
        }
      }
    }

    try {
      log.i('deleteDevice() -> checkThingList start.');
      await checkThingList();
    } catch (e) {
      log.e('deleteDevice() -> checkThingList failed: $e');
    }

    try {
      String deviceId = thingName;
      await DevService.to.removeDeviceFromRoom(roomId, deviceId);
      log.i('deleteDevice() -> removeDeviceFromRoom succeeded');
    } catch (e) {
      log.e('deleteDevice() -> removeDeviceFromRoom failed: $e');
    }

    await HomeService.to.removeSingleDeviceRoom(
      roomId: roomId,
      thingName: thingName,
    );

    await clearLocalData();

    updateLoading(false);

    Get.until((route) {
      return route.settings.name == Routes.homeManagement;
    });

    GlobalService.to
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.deleteDevice));
  }

  Future<void> clearLocalData() async {
    try {
      await localStorageService.clearThingShadow(thingName);
    } catch (e) {
      log.e('deleteDevice() -> clear thing data failed: $e');
    }

    try {
      await HomeUtils.removeCachedRoomDevice(homeId, roomId, thingName);
    } catch (e) {
      log.e('deleteDevice() -> clear room device data failed: $e');
    }
  }

  Future<void> checkThingList() async {
    IotService service = IotService.to;
    bool isTimeout = false;
    await Future.doWhile(() async {
      if (isTimeout) {
        return false; // 停止检查
      }
      String thingGroupName = Sait85rHelper.getThingGroupName(gwThingName);
      log.i('checkThingList() -> thingGroupName=$thingGroupName');

      List<String>? things = await service.listThingsInThingGroup(thingGroupName);
      log.i('checkThingList() -> things=$things');

      if (things == null || things.isEmpty) {
        return false; // 停止检查
      }

      if (things.contains(thingName)) {
        log.i('checkThingList() -> thing还存在于服务器上，继续检查...');
        await Future.delayed(const Duration(seconds: 3)); // 等待3秒
        return true; // 继续检查
      } else {
        log.i('checkThingList() -> thing不存在于服务器上，删除成功！');
        return false; // 停止检查
      }
    }).timeout(
      const Duration(seconds: 60),
      onTimeout: () {
        isTimeout = true;
        log.e('checkThingList() -> 检查超时（60秒）');
        throw TimeoutException('Check thing list timeout');
      },
    );
  }

  void openPairingWindow() async {
    if (!enableParingMode.value) {
      showSnackBar('enablePairingModeSmallInfo'.tr);
      return;
    }

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    if (fabricId == null || fabricId.isEmpty) {
      log.e('openPairingWindow() -> 未获取到fabricId');
      showErrorSnackBar('failedToOpenPairingWindow'.tr);
      return;
    }

    updateLoadingMessage('open pairing window...');

    try {
      AppShadow shadow = deviceShadowService.getDeviceShadow(thingName);
      String vidKey = 'ep1:sMBasicS:VendorID';
      String pidKey = 'ep1:sMBasicS:ProductID';

      int vendorId = AppMapHelper.getDeepValueForKey(shadow.thingShadow, vidKey) ?? -1;
      int productId = AppMapHelper.getDeepValueForKey(shadow.thingShadow, pidKey) ?? -1;
      log.i('openPairingWindow() -> vid=$vendorId, pid=$productId');

      int setupPinCode = 43717650;
      int discriminator = 4071;

      if (vendorId == -1 || productId == -1) {
        vendorId = await _readVid(fabricId)
            .timeout(const Duration(seconds: 60));
        productId = await _readPid(fabricId)
            .timeout(const Duration(seconds: 60));

        if (vendorId == -1 || productId == -1) {
          log.e('openPairingWindow() -> 读取vid pid失败');
          updateLoading(false);
          showErrorSnackBar('failedToReadVidAndPid'.tr);
          return;
        }
      }

      int status = await CtFlutterMatterPlugin.getInstance()
          .readPairingWindowStatus(fabricId, nodeId.value)
          .timeout(const Duration(seconds: 60));
      log.i('openPairingWindow() -> status=$status');
      if (status == 1) {
        updateLoading(false);
        Get.to(() => DevicePairingModePage(),
          binding: DevicePairingModeBindings(
            openPairingWindowData: openPairingWindowData,
            vendorId: vendorId,
            productId: productId,
            discriminator: discriminator,
            setupPinCode: setupPinCode,
          ),
        );
        return;
      }

      if (PlatformUtils.isAndroid) {
        log.i('openECMPairingWindow() -> begin...');
        setupPinCode = await CtFlutterMatterPlugin.getInstance()
            .generateRandomSetupPasscode();
        discriminator = await CtFlutterMatterPlugin.getInstance()
            .generateRandomDiscriminator();
        log.i('openECMPairingWindow() -> discriminator=$discriminator, setupPinCode=$setupPinCode');
        int commissioningTimeout = 300;
        openPairingWindowData = {};
        await CtFlutterMatterPlugin.getInstance()
            .openECMPairingWindow(fabricId, nodeId.value, commissioningTimeout, setupPinCode, discriminator)
            .timeout(const Duration(seconds: 60));

        log.i('openECMPairingWindow() -> succeeded!');
      } else if (PlatformUtils.isIOS) {
        log.i('openPairingWindow() -> begin...');

        int duration = 300;
        openPairingWindowData = await CtFlutterMatterPlugin.getInstance()
            .openPairingWindow(fabricId, nodeId.value, duration)
            .timeout(const Duration(seconds: 60));

        log.i('openPairingWindow() -> succeeded!');
      }

      updateLoading(false);

      Get.to(() => DevicePairingModePage(),
        binding: DevicePairingModeBindings(
          openPairingWindowData: openPairingWindowData,
          vendorId: vendorId,
          productId: productId,
          discriminator: discriminator,
          setupPinCode: setupPinCode,
        ),
      );
    } catch (e) {
      log.e('openPairingWindow() -> failed: $e');
      updateLoading(false);
      showErrorSnackBar('failedToOpenPairingWindow'.tr);
    }
  }

  void onEditDeviceNameTap() {
    if (editName.value) {
      editName.value = false;
    } else {
      editNameController.text = deviceName.value;
      editName.value = true;
    }
  }

  Future<void> changeName(String name) async {
    if (name.isEmpty) {
      log.e('changeName() -> name cannot be empty');
      return;
    }

    if (deviceName.value == name) {
      log.e('changeName() -> name is the same as before');
      return;
    }

    updateLoadingMessage('changing...');

    try {
      final completer = ValueCompleter<void, String>(name);
      _setDeviceNameCompleter = completer;

      Future(() async {
        try {
          await deviceShadowService.updateDeviceProperties(
            thingName: thingName,
            property: {
              'ep1:sMDO:sDeviceName': name,
            },
            subId: subId,
          );

          await Future.delayed(const Duration(seconds: 30));

          throw Exception('timeout');
        } catch (e) {
          if (!completer.isCompleted) {
            completer.completeError(e);
          }
        }
      });

      await completer.future;

      log.i('changeName() -> successfully publish name to cloud!');
      showSuccessSnackBar('successfully changed name!');
      updateLoading(false);
    } catch (e, r) {
      log.e('changeName() -> failed to publish name to cloud: $e, $r');
      showErrorSnackBar('failed to change name!');
      updateLoading(false);
    }
  }

  void onScheduleTap() {
    log.i('onScheduleTap() -> thingName=$thingName');
    Get.to(() => It850SchedulePage(thingName: thingName));
  }

  void onMatterPairingModeTap() {
    openPairingWindow();
  }

  void onChangeRoomTap() async {
    Get.to(
      () => const ChangeRoomPage(),
      binding: ChangeRoomBindings(
        homeId: homeId,
        roomId: roomId,
        thingName: thingName,
        gwThingName: gwThingName,
      ),
    );
  }

  void onDeleteDeviceTap() async {
    DialogUtils.showConfirmDialog(
      content: 'areYouSureYouWantToDeleteThisDevice'.tr,
      primaryButtonText: 'cancel'.tr.toUpperCase(),
      secondaryButtonText: 'delete'.tr.toUpperCase(),
      barrierDismissible: true,
      onSecondaryButtonPressed: () {
        deleteDevice();
      },
    );
  }

  Future<bool> _isConnectedToWifi() async {
    final Connectivity connectivity = Connectivity();
    final List<ConnectivityResult> connectivityResult = await connectivity.checkConnectivity();
    if (!connectivityResult.contains(ConnectivityResult.wifi)) {
      return false;
    }
    return true;
  }

  Future<String> getWiFiSsid() async {
    AppShadow appShadow = deviceShadowService.getDeviceShadow(gwThingName);
    Sait85rGW sait85rGW = Sait85rGW.fromJson(appShadow.thingShadow, gwThingName);
    String networkSSID = sait85rGW.shadow?.state?.reported?.model
        ?.properties?.sGateway?.networkSSID ?? '';
    log.i('getWiFiSsid() -> networkSSID=$networkSSID');
    if (networkSSID.isNotEmpty) {
      return networkSSID;
    }
    return '';
  }

  Future<String> getWifiName() async {
    String wifiName = '';

    try {
      NetworkInfo networkInfo = NetworkInfo();
      wifiName = await networkInfo.getWifiName() ?? '';
      log.i('getWifiName() -> 获取wifi名称成功：$wifiName');
    } catch (e, r) {
      log.e('getWifiName() -> 获取wifi名称失败，e=$e, r=$r');
    }

    if (wifiName.isEmpty) {
      log.w('getWifiName() -> wifi名称为空');
      return '';
    }

    String newWifiName = '';
    if (PlatformUtils.isAndroid) {
      // 使用正则表达式去除可能的多余字符
      // final match = RegExp(r'^"(.*?)"$').firstMatch(wifiName);
      // newWifiName = match?.group(1) ?? wifiName;
      newWifiName = wifiName.substring(1, wifiName.length - 1);
    } else {
      newWifiName = wifiName;
    }

    log.i('getWifiName() -> newWifiName：$newWifiName');

    return newWifiName;
  }

  Future<bool> _validateWiFi() async {
    String wifiName = await getWifiName();
    String wifiSsid = await getWiFiSsid();

    log.i('_validateWiFi() -> wifiName=$wifiName, wifiSsid=$wifiSsid');

    if (wifiName.isNotEmpty && wifiSsid.isNotEmpty) {
      if (wifiName == wifiSsid) {
        return true;
      }
    }

    return false;
  }


  Future<void> _tryReadVid() async {
    try {
      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId == null || fabricId.isEmpty) {
        log.e('tryReadVid() -> 获取fabricId失败!');
        return;
      }

      if (!await _isConnectedToWifi()) {
        log.e('tryReadVid() -> 当前不是wifi网络');
        return;
      }

      if (!await _validateWiFi()) {
        log.e('tryReadVid() -> wifi网络不匹配');
        return;
      }

      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId.value, 0, 40, 2)
          .timeout(const Duration(seconds: 60));
      int vendorId = result['VendorID'] as int;

      if (vendorId != -1) {
        enableParingMode.value = true;
      }

      log.i('tryReadVid() -> 读取vid成功: $vendorId');
    } catch (e, r) {
      log.e('tryReadVid() -> 读取vid失败: $e, $r');
    }
  }

  Future<int> _readVid(String fabricId) async {
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId.value, 0, 40, 2);
      int vendorId = result['VendorID'] as int;
      log.i('_readVid() -> 读取vid成功!');
      return vendorId;
    } catch (e, r) {
      log.e('_readVid() -> 读取vid失败: $e, $r');
      return -1;
    }
  }

  Future<int> _readPid(String fabricId) async {
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId.value, 0, 40, 4);
      int productId = result['ProductID'] as int;
      log.i('_readPid() -> 读取pid成功!');
      return productId;
    } catch (e, r) {
      log.e('_readPid() -> 读取pid失败: $e, $r');
      return -1;
    }
  }

  void showErrorDialog(String title, String content) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                content,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

}
