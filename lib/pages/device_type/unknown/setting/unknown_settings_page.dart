import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/device_type/unknown/setting/unknown_settings_controller.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';

class UnknownSettingsPage extends BasePage<UnknownSettingsController> {
  const UnknownSettingsPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'deviceSettings'.tr,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(
            top: 0,
            bottom: 20,
            left: 20,
            right: 20,
          ),
          child: Obx(() {
            return Column(
              children: [
                _buildDeviceName(),
                Opacity(
                  opacity: controller.isOnline ? 1.0 : 0.5,
                  child: Column(
                    children: [
                      _buildDeviceSettings(),
                      _buildDeviceStatus(),
                    ],
                  ),
                ),
                _buildDeleteButton(),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildDeviceName() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: SvgPicture.asset(
              AppImagePaths.iconThermostat,
              width: 53,
              height: 53,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Obx(() {
                if (controller.editName.value) {
                  return TextFormField(
                    onFieldSubmitted: (value) async {
                      final name = value.trim();
                      log.i('onFieldSubmitted() -> name: $name');
                      await controller.changeName(name);
                      controller.editName.value = false;
                    },
                    style: TextStyles.regular14.copyWith(
                      color: AppColorsHelper.firstColor,
                    ),
                    controller: controller.editNameController,
                  );
                }

                return Text(
                  controller.deviceName.value,
                  style: TextStyles.regular24.copyWith(
                    color: AppColorsHelper.firstColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }),
            ),
          ),
          if (controller.isOnline)
            InkWell(
              onTap: controller.onEditDeviceNameTap,
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: SvgPicture.asset(
                  AppImagePaths.edit,
                  width: 24,
                  height: 24,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDeviceSettings() {
    return Column(
      children: [
        Opacity(
          opacity: controller.enableParingMode.value ? 1.0 : 0.5,
          child: _buildSettingItem(
            'matterPairingMode'.tr,
            onTap: () => controller.onMatterPairingModeTap(),
          ),
        ),
        _buildSettingItem(
          'changeRoom'.tr,
          onTap: () => controller.onChangeRoomTap(),
        ),
      ],
    );
  }

  Widget _buildSettingItem(String title, {VoidCallback? onTap}) {
    return InkWell(
      onTap: controller.isOnline ? onTap : null,
      child: Padding(
        padding: const EdgeInsets.only(left: 10),
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title.tr,
                  style: TextStyles.medium18.copyWith(
                    color: AppColorsHelper.firstColor,
                  ),
                ),
              ),
              SizedBox(
                width: 40,
                height: 50,
                child: Center(
                  child: SvgPicture.asset(
                    AppImagePaths.chevronForward,
                    colorFilter: ColorFilter.mode(
                      AppColorsHelper.firstColor,
                      BlendMode.srcIn,
                    ),
                    width: 18,
                    height: 18,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceStatus() {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          _buildStatusItem(
            'nodeId'.tr,
            content: _buildNodeId(),
          ),
          const SizedBox(height: 20),
          _buildStatusItem(
            'thingName'.tr,
            content: _buildThingName(),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String title, {required Widget content}) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Opacity(
            opacity: 0.5,
            child: Text(
              title.tr,
              style: TextStyles.regular16.copyWith(
                color: AppColorsHelper.firstColor,
              ),
            ),
          ),
          const SizedBox(height: 5),
          content,
        ],
      ),
    );
  }

  Widget _buildNodeId() {
    return Obx(() {
      return Text(
        controller.nodeId.value,
        style: TextStyles.regular16.copyWith(
          color: AppColorsHelper.firstColor,
        ),
      );
    });
  }

  Widget _buildThingName() {
    return Text(
      controller.thingName,
      style: TextStyles.regular16.copyWith(
        color: AppColorsHelper.firstColor,
      ),
    );
  }

  Widget _buildDeleteButton() {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double calculatedWidth = constraints.maxWidth * 0.8;
        if (calculatedWidth > 360) {
          calculatedWidth = 360;
        }
        return Container(
          width: calculatedWidth,
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: OutlinedButton(
            onPressed: () => controller.onDeleteDeviceTap(),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.ffFF4E4E,
              side: const BorderSide(color: AppColors.ffFF4E4E),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24.0),
              ),
              padding: const EdgeInsets.all(12.0),
            ),
            child: Text(
              'deleteDevice'.tr,
              style: TextStyles.medium14.copyWith(
                color: AppColors.ffFF4E4E,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }
}
