import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';

class LightControlController extends GetxController {

  final String homeId;
  final String roomId;
  final String thingName;
  var onOff = false.obs;

  late String mcThingName;
  late String nodeId;

  LightControlController({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
  }

  @override
  void onClose() {
    super.onClose();
  }


  Future<void> controlOnOff(bool value) async {
    try {
      log.i("controlOnOff() -> value=$value");

      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId == null || fabricId.isEmpty) {
        log.e('未获取到fabricId');
        showErrorSnackBar('Failed!');
        return;
      }

      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .writeAttribute(nodeId, 1, 6, 0, "SignedInteger", "2", 0);

      var result = await CtFlutterMatterPlugin.getInstance()
          .lightOnOff(fabricId, nodeId, value ? 1 : 0);

      onOff.value = value;
      log.i("controlOnOff() -> result=$result");
    } catch (e) {
      showErrorSnackBar('Failed!');
    }
  }

  Future<void> toggle() async{
    try {
      log.i("toggle() -> onOff=$onOff");

      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId == null || fabricId.isEmpty) {
        log.e('未获取到fabricId');
        showErrorSnackBar('Failed!');
        return;
      }

      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .writeAttribute(nodeId, 1, 6, 0, "SignedInteger", "0", 0);

      var result = await CtFlutterMatterPlugin.getInstance()
          .lightToggle(fabricId, nodeId);

      log.i("toggle() -> result=$result");
      onOff.value = !onOff.value;
    } catch (e) {
      showErrorSnackBar('Failed!');
    }
  }

}
