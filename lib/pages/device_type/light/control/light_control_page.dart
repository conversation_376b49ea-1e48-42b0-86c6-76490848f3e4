import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/device_type/light/control/light_control_controller.dart';
import 'package:habi_app/pages/device_type/light/setting/light_settings_bindings.dart';
import 'package:habi_app/pages/device_type/light/setting/light_settings_page.dart';

class LightControlPage extends GetView<LightControlController> {

  const LightControlPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'My Light',
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: AppColors.ff01A796),
            onPressed: () {
              Get.to(() => LightSettingsPage(),
                  binding: LightSettingsBindings(
                    homeId: controller.homeId,
                    roomId: controller.roomId,
                    thingName: controller.thingName,
                  ));
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            const SizedBox(height: 30),

            Expanded(
              flex: 2,
              child: Obx((){
                return IconButton(
                    icon: SvgPicture.asset(
                      controller.onOff.value ? AppImagePaths.lightOn : AppImagePaths.lightOff,
                      width: 226,
                      height: 226,
                    ),
                    onPressed: () {
                      controller.toggle();
                    }
                );
              }
              ),
            ),

            Expanded(
              flex: 1,
              child: const SizedBox(),
            ),

            Expanded(
              flex: 1,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Opacity(
                      opacity: 0.5,
                      child: Text(
                        'on/off'.tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      ),
                    ),

                    Obx((){
                      return Switch(
                          value: controller.onOff.value,
                          onChanged: (value) {
                            controller.controlOnOff(value);
                          }
                      );
                    }
                    )
                  ]
              ),
            ),

            const SizedBox(height: 30),

          ],
        ),
      ),
    );
  }



}

