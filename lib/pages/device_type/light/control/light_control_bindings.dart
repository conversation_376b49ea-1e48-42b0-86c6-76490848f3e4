import 'package:get/get.dart';
import 'package:habi_app/pages/device_type/light/control/light_control_controller.dart';

class LightControlBindings extends Bindings {
  final String homeId;
  final String roomId;
  final String thingName;

  LightControlBindings({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => LightControlController(
      homeId: homeId,
      roomId: roomId,
      thingName: thingName,
    ));
  }
}
