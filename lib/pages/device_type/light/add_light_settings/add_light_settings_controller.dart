import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/models/app_home.dart';
class AddLightSettingsController extends BaseController {

  final String thingName;
  late AppHome appHome;

  AddLightSettingsController({
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }





}
