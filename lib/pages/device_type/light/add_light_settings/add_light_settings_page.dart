import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'add_light_settings_controller.dart';

class AddLightSettingsPage extends BasePage<AddLightSettingsController> {

  AddLightSettingsPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'addLight'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 56.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppImagePaths.receiver,
                        width: 56.w,
                        height: 56.h,
                      ),

                      Expanded(
                        child: Center(
                          child: Text('My Light',
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: AppFontWeights.regular,
                                color: Theme.of(context)
                                    .extension<AppThemeExtension>()!.firstColor,
                              )
                          ),
                        ),
                      ),

                      SizedBox(
                        width: 56.w,
                        height: 56.h,
                        child: Center(
                          child: SvgPicture.asset(
                            AppImagePaths.edit,
                            width: 22,
                            height: 22,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),


                SizedBox(height: 51.h),
          
          
              ],
            ),
          ),
        )
    );
  }


}
