import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_card_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/device_type/light/control/light_control_bindings.dart';
import 'package:habi_app/pages/device_type/light/control/light_control_page.dart';
import 'package:habi_app/widgets/habi_shadow_card.dart';

class LightCard extends BaseCardWidget {

  final String homeId;
  final String roomId;

  LightCard({
    super.key,
    required super.thingName,
    required this.homeId,
    required this.roomId,
  });

  @override
  State<LightCard> createState() {
    return LightCardState();
  }
}

class LightCardState extends BaseCardState<LightCard> {

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.to(() => LightControlPage(),
            binding: LightControlBindings(
              homeId: widget.homeId,
              roomId: widget.roomId,
              thingName: widget.thingName,
            ));
      },
      child: HabiShadowCard(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Light1',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
              const SizedBox(height: 25),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Opacity(
                          opacity: 0.5,
                          child: Text(
                            'onOff'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.regular,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            ),
                          ),
                        ),
                      )
                  ),
                  const SizedBox(height: 5),
                  Expanded(
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Switch(
                            value: true,
                            onChanged: (value) {
                            }
                        ),
                      )
                  )
                ],
              ),


            ],
          ),
        ),
      ),
    );
  }

}