import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/device_type/light/setting/light_settings_controller.dart';
import 'package:habi_app/widgets/delete_widget.dart';

class LightSettingsPage extends BasePage<LightSettingsController> {

  LightSettingsPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'lightSetting'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 56.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppImagePaths.receiver,
                        width: 56.w,
                        height: 56.h,
                      ),

                      Expanded(
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text('My Light',
                              style: TextStyle(
                                fontSize: 28,
                                fontWeight: AppFontWeights.regular,
                                color: Theme.of(context)
                                    .extension<AppThemeExtension>()!.firstColor,
                              )
                          ),
                        ),
                      ),

                      SizedBox(
                        width: 56.w,
                        height: 56.h,
                        child: Center(
                          child: SvgPicture.asset(
                            AppImagePaths.edit,
                            width: 22,
                            height: 22,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 40.h),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                      'nodeId'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      )
                  ),
                ),
                SizedBox(height: 12.h,),

                Text(
                  controller.nodeId,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),

                SizedBox(height: 22.h),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                      'thirdPartyServices'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      )
                  ),
                ),
                SizedBox(height: 12.h,),

                InkWell(
                  onTap: onDevicePairingModeClick,
                  child: Text(
                    'devicePairingMode'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),

                SizedBox(height: 53.h),

                SizedBox(
                  width: 96.w,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () => _onDelete(context),
                    child: Text(
                        'delete'.tr.toUpperCase(),
                        style: const TextStyle(
                            fontSize: 14,
                            fontWeight: AppFontWeights.bold,
                            color: Colors.white
                        )
                    ),
                  ),
                ),
          
          
              ],
            ),
          ),
        )
    );
  }

  void _onDelete(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return DeleteWidget(
            title: 'areYouSureYouWantToDeleteThisDevice'.tr,
            onDelete: () async {
              controller.deleteDevice();
            },
          );
        }
    );
  }

  void onDevicePairingModeClick() {
    controller.openPairingWindow();
  }

}
