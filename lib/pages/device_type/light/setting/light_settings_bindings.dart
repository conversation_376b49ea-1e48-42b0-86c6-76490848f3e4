import 'package:get/instance_manager.dart';
import 'light_settings_controller.dart';

class LightSettingsBindings extends Bindings {

  final String homeId;
  final String roomId;
  final String thingName;

  LightSettingsBindings({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => LightSettingsController(
      homeId: homeId,
      roomId: roomId,
      thingName: thingName,
    ));
  }
}
