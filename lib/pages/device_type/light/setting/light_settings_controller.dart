import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/pages/device_type/thermostat/device_pairing_mode/device_pairing_mode_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/device_pairing_mode/device_pairing_mode_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/global_service.dart';

class LightSettingsController extends BaseController {

  final String homeId;
  final String roomId;
  final String thingName;

  late String nodeId;
  late String mcThingName;

  LightSettingsController({
    required this.homeId,
    required this.roomId,
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }


  Future<void> deleteDevice() async {
    updateLoading(true);
    try {
      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId != null && fabricId.isNotEmpty) {
        CtFlutterMatterPlugin.getInstance()
            .unpairDevice(fabricId, nodeId)
            .then((result) {
          log.i('deleteDevice() -> unpairDevice succeeded');
        })
            .catchError((error) {
          log.e('deleteDevice() -> unpairDevice failed: $error');
        })
            .timeout(const Duration(seconds: 15));
      }

      await DevService.to.removeDeviceFromRoom(roomId, thingName);
      log.i('deleteDevice() -> removeDeviceFromRoom succeeded');

      Get.until((route) {
        return route.settings.name == Routes.homeManagement;
      });

      GlobalService.to
          .getEventStreamController()
          .add(AppEvent(name: AppEvents.deleteDevice));
    } catch (e) {
      log.e('deleteDevice() -> failed: $e');
    } finally {
      updateLoading(false);
    }
  }

  void openPairingWindow() async {
    updateLoading(true);
    try {
      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId == null || fabricId.isEmpty) {
        log.e('openPairingWindow() -> 未获取到fabricId');
        updateLoading(false);
        showErrorSnackBar('Failed to open the pairing window.');
        return;
      }

      int vendorId = -1;
      int productId = -1;
      int setupPinCode = 43717650;
      int discriminator = 4071;

      try {
        var result = await CtFlutterMatterPlugin.getInstance()
            .readAttribute(fabricId, nodeId, 0, 40, 2);
        vendorId = result['VendorID'] as int;
        log.i('openPairingWindow() -> 读取vid成功!');
      } catch (e, r) {
        log.e('openPairingWindow() -> 读取vid失败: $e, $r');
      }

      try {
        var result = await CtFlutterMatterPlugin.getInstance()
            .readAttribute(fabricId, nodeId, 0, 40, 4);
        productId = result['ProductID'] as int;
        log.i('openPairingWindow() -> 读取pid成功!');
      } catch (e, r) {
        log.e('openPairingWindow() -> 读取pid失败: $e, $r');
      }

      if (vendorId == -1 || productId == -1) {
        log.e('openPairingWindow() -> 读取vid pid失败');
        updateLoading(false);
        showErrorSnackBar('Failed to read VID and PID.');
        return;
      }

      int status = await CtFlutterMatterPlugin.getInstance()
          .readPairingWindowStatus(fabricId, nodeId)
          .timeout(const Duration(seconds: 15));
      log.i('openPairingWindow() -> status=$status');
      if (status == 1) {
        updateLoading(false);
        Get.to(() => DevicePairingModePage(), binding: DevicePairingModeBindings(
            openPairingWindowData: {},
            vendorId: vendorId,
            productId: productId,
            discriminator: discriminator,
            setupPinCode: setupPinCode,
        ));
        return;
      }

      await CtFlutterMatterPlugin.getInstance()
          .openECMPairingWindow(fabricId, nodeId, 300, setupPinCode, discriminator)
          .timeout(const Duration(seconds: 15));
      log.i('openPairingWindow() -> succeeded!');

      updateLoading(false);

      Get.to(() => DevicePairingModePage(), binding: DevicePairingModeBindings(
        openPairingWindowData: {},
        vendorId: vendorId,
        productId: productId,
        discriminator: discriminator,
        setupPinCode: setupPinCode,
      ));
    } catch (e) {
      log.e('openPairingWindow() -> failed: $e');
      updateLoading(false);
      showErrorSnackBar('Failed to open the pairing window.');
    }
  }



}
