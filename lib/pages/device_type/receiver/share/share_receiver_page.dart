import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/device_type/receiver/share/share_receiver_controller.dart';

class ShareReceiverPage extends GetView<ShareReceiverController> {

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  ShareReceiverPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'shareReceiver'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 36),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[

              SizedBox(height: 107.h,),

              Text(
                textAlign: TextAlign.center,
                'PleaseEnterTheEmailAddressYouWantToShare'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
        
              SizedBox(height: 37.h,),

              _buildForm(context),

              SizedBox(height: 37.h,),

              // InkWell(
              //   onTap: _onResend,
              //   child: SizedBox(
              //     width: double.infinity,
              //     child: Center(
              //       child: Text(
              //         'resend'.tr,
              //         style: const TextStyle(
              //           fontSize: 16,
              //           fontWeight: AppFontWeights.regular,
              //           color: AppColors.ff01A796,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),

              SizedBox(height: 329.h,),

              SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: Obx(() {
                    return FilledButton(
                      onPressed: _onContinue,
                      child: controller.isLoading.value ?
                      const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ) : Text(
                        'continue'.tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.secondColor,
                        ),
                      ),
                    );
                  })
              ),

              SizedBox(height: 113.h,),

        
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
        key: _formKey,
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'email'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 14,),
            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.emailController,
                keyboardType: TextInputType.emailAddress,
                validator: _validateEmail,
              ),
            ),

          ],
        )
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (!value.isEmail) {
      return 'emailAddressInvalid'.tr;
    }

    return null;
  }


  void _onContinue() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.shareReceiver();
  }

  // void _onResend() async {
  //   bool validate = _formKey.currentState?.validate() ?? false;
  //   if (!validate) {
  //     return;
  //   }
  //   await controller.shareReceiver();
  // }

}
