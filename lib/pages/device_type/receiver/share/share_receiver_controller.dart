import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/device_provision/device_provision_result.dart';
import 'package:habi_app/services/device_provision_service.dart';

class ShareReceiverController extends GetxController {

  final TextEditingController emailController = TextEditingController();
  final String thingName;
  var isLoading = false.obs;

  ShareReceiverController({
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
  }

  Future<void> shareReceiver() async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      DeviceProvisionResult result = await DeviceProvisionService.to.shareDeviceByOwner(
          deviceId: thingName,
          username: emailController.text
      ).timeout(const Duration(seconds: 30));

      if (!result.isSuccess()) {
        if(result.body.contains('User has access already!')) {
          showErrorSnackBar('User has access already!');
          return;
        }
        showErrorSnackBar('share receiver failed.');
        return;
      }

      Get.back();
      showSuccessSnackBar('share receiver success.');
    } catch (e, r) {
      log.e("shareReceiver() -> Exception{} -> e=$e, r=$r");
      showErrorSnackBar('share receiver failed.');
    } finally {
      isLoading.value = false;
    }
  }

}
