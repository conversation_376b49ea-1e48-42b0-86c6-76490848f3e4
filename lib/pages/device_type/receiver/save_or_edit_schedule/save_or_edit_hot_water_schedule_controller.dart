import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/widgets/time_wheel.dart';

import 'save_or_edit_hot_water_schedule_page.dart';

class SaveOrEditHotWaterScheduleController extends GetxController {
  static const String defaultActionValue = '0000';
  final Schedule schedule;
  final int action;
  late FixedExtentScrollController hourController;
  late FixedExtentScrollController minuteController;
  var repeat = false.obs;
  var isAM = true.obs;
  var desiredTemperature = 21.0.obs;
  var onOrOff = defaultActionValue.obs;
  String hourValue = '08';
  String minuteValue = '00';
  TimeWheel? timeWheel;

  SaveOrEditHotWaterScheduleController({
    required this.schedule,
    required this.action,
  });

  @override
  void onInit() {
    super.onInit();
    hourController = FixedExtentScrollController(initialItem: 0);
    minuteController = FixedExtentScrollController(initialItem: 0);
  }

  @override
  void onReady() {
    super.onReady();
    onOrOff.value = schedule.onOrOff ?? defaultActionValue;

    if (action == SaveOrEditHotWaterSchedulePage.saveAction) {
      setCurrentTimeToSchedule();
    } else {
      parseScheduleTime();
    }

  }

  @override
  void onClose() {
    hourController.dispose();
    minuteController.dispose();
    super.onClose();
  }

  Widget createTimeWheelWidget() {
    timeWheel ??= TimeWheel(
      onHourChanged: (int index, String value) {
        log.i('hour-index: $index');
        log.i('hour-value: $value');
        hourValue = value;
      },
      onMinuteChanged: (int index, String value) {
        log.i('minute-index: $index');
        log.i('minute-value: $value');
        minuteValue = value;
      },
      hourController: hourController,
      minuteController: minuteController,
      is24Hour: schedule.is24HourFormat(),
    );
    return timeWheel!;
  }

  void setOnOrOff(String value) {
    onOrOff.value = value;
  }

  void deleteSchedule() {
    Get.back(result: ('delete', schedule));
  }

  void saveSchedule() {
    log.i('saveSchedule() -> onOrOff: ${onOrOff.value}, hourStr: $hourValue, minuteStr: $minuteValue');

    schedule.onOrOff = onOrOff.value;

    if (isAM.value) {
      schedule.setTime('$hourValue:$minuteValue AM');
    } else {
      schedule.setTime('$hourValue:$minuteValue PM');
    }

    log.i('saveSchedule() -> getTime: ${schedule.getTime()}');

    Get.back(result: ('save', schedule));
  }

  void setCurrentTimeToSchedule() {
    try {
      DateTime now = DateTime.now();
      String hourStr = now.hour.toString().padLeft(2, '0');
      String minuteStr = now.minute.toString().padLeft(2, '0');

      log.i('setCurrentTimeToSchedule() -> now: $now');
      log.i('setCurrentTimeToSchedule() -> hourStr: $hourStr');
      log.i('setCurrentTimeToSchedule() -> minuteStr: $minuteStr');

      if (schedule.is24HourFormat()) {
        schedule.time = '$hourStr$minuteStr';
        hourValue = hourStr;
        minuteValue = minuteStr;
      } else {
        String period = now.hour < 12 ? 'am' : 'pm';
        int hour12 = now.hour % 12 == 0 ? 12 : now.hour % 12;

        log.i('setCurrentTimeToSchedule() -> period: $period');
        log.i('setCurrentTimeToSchedule() -> hour12: $hour12');

        String newHourStr = hour12.toString().padLeft(2, '0');
        log.i('setCurrentTimeToSchedule() -> newHourStr: $newHourStr');

        schedule.time = '$newHourStr$minuteStr $period';
        isAM.value = period.toLowerCase().contains('am');

        hourValue = newHourStr;
        minuteValue = minuteStr;
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);

      log.i('setCurrentTimeToSchedule() -> hourValue: $hourValue');
      log.i('setCurrentTimeToSchedule() -> minuteValue: $minuteValue');
      log.i('setCurrentTimeToSchedule() -> schedule.time: ${schedule.time}');
    } catch (e) {
      log.e('setCurrentTimeToSchedule() -> error: $e');
      schedule.time = '0800';
      hourValue = '08';
      minuteValue = '00';

      if (!schedule.is24HourFormat()) {
        isAM.value = true;
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);
    }
  }

  void parseScheduleTime() {
    try {
      schedule.time ??= '0800';
      String scheduleTime = schedule.getTime().trim();
      log.i('parseScheduleTime() -> scheduleTime: $scheduleTime');
      log.i('parseScheduleTime() -> is24HourFormat: ${schedule.is24HourFormat()}');

      if (schedule.is24HourFormat()) {
        List timeSplit = scheduleTime.split(':');
        log.i('parseScheduleTime() -> timeSplit: $timeSplit');
        String hourStr = timeSplit[0];
        String minuteStr = timeSplit[1];
        log.i('parseScheduleTime() -> hourStr: $hourStr');
        log.i('parseScheduleTime() -> minuteStr: $minuteStr');
        hourValue = hourStr;
        minuteValue = minuteStr;
      } else {
        List parts = scheduleTime.split(' ');
        log.i('parseScheduleTime() -> parts: $parts');
        String timePart = parts[0];
        log.i('parseScheduleTime() -> timePart: $timePart');
        String amPm = parts[1].trim();
        log.i('parseScheduleTime() -> amPm: $amPm');
        List timeSplit = timePart.split(':');
        log.i('parseScheduleTime() -> timeSplit: $timeSplit');
        String hourStr = timeSplit[0];
        String minuteStr = timeSplit[1];
        log.i('parseScheduleTime() -> hourStr: $hourStr');
        log.i('parseScheduleTime() -> minuteStr: $minuteStr');
        hourValue = hourStr;
        minuteValue = minuteStr;
        isAM.value = amPm.toLowerCase().contains('am');
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);
    } catch (e) {
      log.e('parseScheduleTime() -> error: $e');
      schedule.time = '0800';
      hourValue = '08';
      minuteValue = '00';

      if (!schedule.is24HourFormat()) {
        isAM.value = true;
      }

      timeWheel?.selectedHourValue(hourValue);
      timeWheel?.selectedMinuteValue(minuteValue);
    }
  }

}
