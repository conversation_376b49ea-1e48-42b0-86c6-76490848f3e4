import 'package:get/get.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'save_or_edit_hot_water_schedule_controller.dart';

class SaveOrEditHotWaterScheduleBindings extends Bindings {
  final Schedule schedule;
  final int action;

  SaveOrEditHotWaterScheduleBindings({
    required this.schedule,
    required this.action,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => SaveOrEditHotWaterScheduleController(
        schedule: schedule,
        action: action,
    ));
  }
}
