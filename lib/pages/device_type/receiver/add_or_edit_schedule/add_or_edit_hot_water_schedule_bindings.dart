import 'package:get/get.dart';
import 'package:habi_app/models/schedule/hot_water_schedule.dart';
import 'package:habi_app/pages/device_type/receiver/add_or_edit_schedule/add_or_edit_hot_water_schedule_controller.dart';

class AddOrEditHotWaterScheduleBindings extends Bindings {
  final HotWaterSchedule schedule;
  final bool isEdit;

  AddOrEditHotWaterScheduleBindings({
    required this.schedule,
    required this.isEdit,
  });

  @override
  void dependencies() {
    Get.lazyPut(
      () => AddOrEditHotWaterScheduleController(
        schedule: schedule,
        isEdit: isEdit,
      ),
    );
  }
}
