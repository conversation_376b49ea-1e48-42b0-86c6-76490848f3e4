import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/pages/device_type/receiver/add_or_edit_schedule/add_or_edit_hot_water_schedule_controller.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'package:habi_app/widgets/habi_button.dart';

class AddOrEditHotWaterSchedulePage
    extends GetView<AddOrEditHotWaterScheduleController> {
  const AddOrEditHotWaterSchedulePage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: controller.isEdit ? 'editSlot'.tr : 'addSlot'.tr,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 30),
              _buildStartTime(),
              const SizedBox(height: 50),
              _buildEndTime(),
              const SizedBox(height: 67),
              _buildButtons(),
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStartTime() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'startTime'.tr,
          style: TextStyles.medium14FirstColor,
        ),
        const SizedBox(height: 10),
        _buildTimeWheelTitle(),
        Row(
          children: [
            Expanded(
              child: controller.startTimeWheel,
            ),
            SizedBox(
              width: 88,
              child: _buildStartTimeAmPmOptions(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEndTime() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Opacity(
          opacity: 0.5,
          child: Text(
            'endTime'.tr,
            style: TextStyles.medium14FirstColor,
          ),
        ),
        const SizedBox(height: 10),
        // _buildTimeWheelTitle(),
        Row(
          children: [
            Expanded(
              child: controller.endTimeWheel,
            ),
            SizedBox(
              width: 88,
              child: _buildEndTimeAmPmOptions(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeWheelTitle() {
    return Opacity(
      opacity: 0.5,
      child: Row(
        children: [
          Expanded(
            child: Text(
              'hours'.tr,
              style: TextStyles.medium14FirstColor,
            ),
          ),
          SizedBox(width: 38.w),
          Expanded(
            child: Text(
              'minutes'.tr,
              style: TextStyles.medium14FirstColor,
            ),
          ),
          const SizedBox(width: 88),
        ],
      ),
    );
  }

  Widget _buildStartTimeAmPmOptions() {
    if (controller.onSchedule.is24HourFormat()) {
      return Container();
    }

    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildAmOrPmOption(
            text: 'am'.tr,
            isChecked: !controller.isStartTimePM.value,
            onPressed: () {
              controller.isStartTimePM.value = false;
            },
          ),
          const SizedBox(height: 29),
          _buildAmOrPmOption(
            text: 'pm'.tr,
            isChecked: controller.isStartTimePM.value,
            onPressed: () {
              controller.isStartTimePM.value = true;
            },
          ),
        ],
      );
    });
  }

  Widget _buildEndTimeAmPmOptions() {
    if (controller.offSchedule.is24HourFormat()) {
      return Container();
    }

    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildAmOrPmOption(
            text: 'am'.tr,
            isChecked: !controller.isEndTimePM.value,
            onPressed: () {
              controller.isEndTimePM.value = false;
            },
          ),
          const SizedBox(height: 29),
          _buildAmOrPmOption(
            text: 'pm'.tr,
            isChecked: controller.isEndTimePM.value,
            onPressed: () {
              controller.isEndTimePM.value = true;
            },
          ),
        ],
      );
    });
  }

  Widget _buildAmOrPmOption({
    required String text,
    required bool isChecked,
    required VoidCallback? onPressed,
  }) {
    Color textColor;
    Color backgroundColor;
    if (isChecked) {
      textColor = Colors.white;
      backgroundColor = AppColors.ff01A796;
    } else {
      textColor = AppColors.ff01A796;
      backgroundColor = Colors.transparent;
    }

    return SizedBox(
      width: 66,
      height: 37,
      child: HabiButton(
        label: Text(
          text,
          style: TextStyles.regular18.copyWith(color: textColor),
          textAlign: TextAlign.center,
        ),
        borderColor: AppColors.ff01A796,
        backgroundColor: backgroundColor,
        padding: const EdgeInsets.only(),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            HabiButton(
              onPressed: controller.onCancelButtonPressed,
              text: 'cancel'.tr.toUpperCase(),
              textColor: AppColors.ff01A796,
              borderColor: AppColors.ff01A796,
            ),
            HabiButton(
              onPressed: controller.onDeleteButtonPressed,
              text: 'delete'.tr.toUpperCase(),
              textColor: AppColors.ffFF4E4E,
              borderColor: AppColors.ffFF4E4E,
            ),
          ],
        ),
        const SizedBox(height: 28),
        HabiButton(
          text: 'save'.tr.toUpperCase(),
          textColor: Colors.white,
          backgroundColor: AppColors.ff01A796,
          onPressed: controller.onSaveButtonPressed,
        ),
      ],
    );
  }
}
