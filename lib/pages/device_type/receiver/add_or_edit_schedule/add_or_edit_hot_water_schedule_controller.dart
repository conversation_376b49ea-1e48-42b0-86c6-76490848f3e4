import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/schedule/hot_water_schedule.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/utility/date_time_utils.dart';
import 'package:habi_app/widgets/time_wheel.dart';

enum ScheduleAction {
  delete,
  save,
}

class AddOrEditHotWaterScheduleResult {
  final ScheduleAction action;
  final HotWaterSchedule schedule;

  AddOrEditHotWaterScheduleResult({
    required this.action,
    required this.schedule,
  });
}

class AddOrEditHotWaterScheduleController extends GetxController {
  final bool isEdit;
  final HotWaterSchedule schedule;

  AddOrEditHotWaterScheduleController({
    required this.isEdit,
    required this.schedule,
  });

  final isStartTimePM = false.obs;
  String startTimeHour = '08';
  String startTimeMinute = '00';
  late FixedExtentScrollController startTimeHourController;
  late FixedExtentScrollController startTimeMinuteController;
  late TimeWheel startTimeWheel;

  final isEndTimePM = false.obs;
  String endTimeHour = '08';
  String endTimeMinute = '00';
  late FixedExtentScrollController endTimeHourController;
  late FixedExtentScrollController endTimeMinuteController;
  late TimeWheel endTimeWheel;

  Schedule get onSchedule => schedule.onSchedule;

  Schedule get offSchedule => schedule.offSchedule;

  @override
  void onInit() {
    super.onInit();

    startTimeWheel = _createStartTimeWheelWidget();
    endTimeWheel = _createEndTimeWheelWidget();
  }

  @override
  void onReady() {
    super.onReady();

    if (!isEdit) {
      _setCurrentTimeToSchedule();
    } else {
      _parseScheduleTime();
    }
  }

  @override
  void onClose() {
    startTimeHourController.dispose();
    startTimeMinuteController.dispose();
    endTimeHourController.dispose();
    endTimeMinuteController.dispose();
    super.onClose();
  }

  TimeWheel _createStartTimeWheelWidget() {
    startTimeHourController = FixedExtentScrollController();
    startTimeMinuteController = FixedExtentScrollController();

    return TimeWheel(
      onHourChanged: (int index, String value) {
        log.i('hour-index: $index');
        log.i('hour-value: $value');
        startTimeHour = value;
      },
      onMinuteChanged: (int index, String value) {
        log.i('minute-index: $index');
        log.i('minute-value: $value');
        startTimeMinute = value;
      },
      hourController: startTimeHourController,
      minuteController: startTimeMinuteController,
      is24Hour: onSchedule.is24HourFormat(),
    );
  }

  TimeWheel _createEndTimeWheelWidget() {
    endTimeHourController = FixedExtentScrollController();
    endTimeMinuteController = FixedExtentScrollController();

    return TimeWheel(
      onHourChanged: (int index, String value) {
        log.i('hour-index: $index');
        log.i('hour-value: $value');
        endTimeHour = value;
      },
      onMinuteChanged: (int index, String value) {
        log.i('minute-index: $index');
        log.i('minute-value: $value');
        endTimeMinute = value;
      },
      hourController: endTimeHourController,
      minuteController: endTimeMinuteController,
      is24Hour: offSchedule.is24HourFormat(),
    );
  }

  String _validateTime() {
    try {
      final startTime = DateTimeUtils.convertHHmmToMinutes(onSchedule.time!);
      final endTime = DateTimeUtils.convertHHmmToMinutes(offSchedule.time!);

      if (endTime <= startTime) {
        return 'endTimeMustBeAfterStartTime'.tr;
      }
    } catch (e) {
      log.e('validateTime() -> error: $e');
      return 'invalidTimeFormat'.tr;
    }

    return '';
  }

  void onCancelButtonPressed() {
    Get.back(
      closeOverlays: true,
    );
  }

  void onDeleteButtonPressed() {
    Get.back(
      closeOverlays: true,
      result: AddOrEditHotWaterScheduleResult(
        action: ScheduleAction.delete,
        schedule: schedule,
      ),
    );
  }

  void onSaveButtonPressed() {
    if (isStartTimePM.value) {
      onSchedule.setTime('$startTimeHour:$startTimeMinute PM');
    } else {
      onSchedule.setTime('$startTimeHour:$startTimeMinute AM');
    }

    if (isEndTimePM.value) {
      offSchedule.setTime('$endTimeHour:$endTimeMinute PM');
    } else {
      offSchedule.setTime('$endTimeHour:$endTimeMinute AM');
    }

    final errorMessage = _validateTime();
    if (errorMessage.isNotEmpty) {
      if (!Get.isSnackbarOpen) {
        showErrorSnackBar(errorMessage);
      }
      return;
    }

    Get.back(
      closeOverlays: true,
      result: AddOrEditHotWaterScheduleResult(
        action: ScheduleAction.save,
        schedule: schedule,
      ),
    );
  }

  void _setCurrentTimeToSchedule() {
    try {
      final now = DateTime.now();
      final hour = now.hour.toString().padLeft(2, '0');
      final minute = now.minute.toString().padLeft(2, '0');
      final currentTime = '$hour:$minute';

      _setTimeToOnSchedule(currentTime);
      _setTimeToOffSchedule(currentTime);
    } catch (e) {
      log.e('setCurrentTimeToSchedule() -> error: $e');
      _setDefaultTimeToSchedule();
    }
  }

  /// Get schedule time
  /// Return format HH:mm, Ex: 08:00
  String _getScheduleTime(Schedule schedule) {
    try {
      final time = schedule.time?.trim().toLowerCase();
      if (time != null && time != "" && !time.contains("f")) {
        return "${time.substring(0, 2)}:${time.substring(2, 4)}";
      }
    } catch (e) {
      //
    }
    return "08:00";
  }

  void _parseScheduleTime() {
    try {
      final onScheduleTime = _getScheduleTime(onSchedule);
      final offScheduleTime = _getScheduleTime(offSchedule);
      _setTimeToOnSchedule(onScheduleTime);
      _setTimeToOffSchedule(offScheduleTime);
    } catch (e) {
      log.e('parseScheduleTime() -> error: $e');
      _setDefaultTimeToSchedule();
    }
  }

  void _setDefaultTimeToSchedule() {
    _setTimeToOnSchedule('08:00');
    _setTimeToOffSchedule('08:00');
  }

  /// Set time to on schedule
  ///
  /// [time] is in format HH:mm, Ex: 08:00
  void _setTimeToOnSchedule(String time) {
    final timeSplit = time.split(':');
    final hour24 = int.parse(timeSplit[0]);
    final minute = int.parse(timeSplit[1]);

    if (onSchedule.is24HourFormat()) {
      final hourStr = hour24.toString().padLeft(2, '0');
      final minuteStr = minute.toString().padLeft(2, '0');

      startTimeHour = hourStr;
      startTimeMinute = minuteStr;
      isStartTimePM.value = false;
      onSchedule.time = '$hourStr$minuteStr';
    } else {
      final hour12 = hour24 % 12 == 0 ? 12 : hour24 % 12;
      final hourStr = hour12.toString().padLeft(2, '0');
      final minuteStr = minute.toString().padLeft(2, '0');

      startTimeHour = hourStr;
      startTimeMinute = minuteStr;

      if (hour24 < 12) {
        isStartTimePM.value = false;
        onSchedule.time = '$hourStr$minuteStr am';
      } else {
        isStartTimePM.value = true;
        onSchedule.time = '$hourStr$minuteStr pm';
      }
    }

    startTimeWheel.selectedHourValue(startTimeHour);
    startTimeWheel.selectedMinuteValue(startTimeMinute);
  }

  /// Set time to off schedule
  ///
  /// [time] is in format HH:mm, Ex: 08:00
  void _setTimeToOffSchedule(String time) {
    final timeSplit = time.split(':');
    final hour24 = int.parse(timeSplit[0]);
    final minute = int.parse(timeSplit[1]);

    if (offSchedule.is24HourFormat()) {
      final hourStr = hour24.toString().padLeft(2, '0');
      final minuteStr = minute.toString().padLeft(2, '0');

      endTimeHour = hourStr;
      endTimeMinute = minuteStr;
      isEndTimePM.value = false;
      offSchedule.time = '$hourStr$minuteStr';
    } else {
      final hour12 = hour24 % 12 == 0 ? 12 : hour24 % 12;
      final hourStr = hour12.toString().padLeft(2, '0');
      final minuteStr = minute.toString().padLeft(2, '0');

      endTimeHour = hourStr;
      endTimeMinute = minuteStr;

      if (hour24 < 12) {
        isEndTimePM.value = false;
        offSchedule.time = '$hourStr$minuteStr am';
      } else {
        isEndTimePM.value = true;
        offSchedule.time = '$hourStr$minuteStr pm';
      }
    }

    endTimeWheel.selectedHourValue(endTimeHour);
    endTimeWheel.selectedMinuteValue(endTimeMinute);
  }
}
