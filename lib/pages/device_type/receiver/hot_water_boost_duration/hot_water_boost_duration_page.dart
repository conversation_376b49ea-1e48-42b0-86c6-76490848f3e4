import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/widgets/hour_minute_picker.dart';
import 'hot_water_boost_duration_controller.dart';

class HotWaterBoostDurationPage extends BasePage<HotWaterBoostDurationController> {

  HotWaterBoostDurationPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'boostDuration'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 30.h),

                Text(
                    'duration'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!
                          .firstColor,
                    )
                ),

                SizedBox(height: 30.h),

                Row(
                  children: [
                    Expanded(
                      child: Center(
                        child: Text(
                            'hours'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.medium,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!
                                  .firstColor
                                  .withOpacity(0.5),
                            )
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 38.w,
                    ),
                    Expanded(
                      child: Center(
                        child: Text(
                            'minutes'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.medium,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!
                                  .firstColor
                                  .withOpacity(0.5),
                            )
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 5.h),

                HourMinutePicker(
                  hourInitialItem: 1,
                  minuteInitialItem: 0,
                  hourMax: 6,
                  onHourChanged: (int value) {
                    log.i('hour: $value');
                    controller.setHour(value);
                  },
                  onMinuteChanged: (int value) {
                    log.i('minute: $value');
                    controller.setMinute(value);
                  },
                ),

                SizedBox(height: 30.h),

                Center(
                  child: Opacity(
                    opacity: 0.5,
                    child: Text(
                        'boostHoursLimit'.tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .fourthColor,
                        )
                    ),
                  ),
                ),

                SizedBox(height: 152.h),

                _buildBoostHoursButton(),

                SizedBox(height: 28.h),

                _buildCancelButton(),

                SizedBox(height: 52.h),
              ],
            ),
          ),
        )
    );
  }

  Widget _buildBoostHoursButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: () {
          controller.saveBoostDuration();
        },
        child: Text(
            'boostHours'.tr.toUpperCase(),
            style: const TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.bold,
                color: Colors.white
            )
        ),
      ),
    );
  }

  Widget _buildCancelButton() {
    return SizedBox(
        width: double.infinity,
        height: 48,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(
              color: Colors.red,
              width: 1,
            ),
          ),
          onPressed: () {
            Get.back();
          },
          child: Text(
            'cancelHours'.tr.toUpperCase(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Colors.red,
            ),
          ),
        )
    );
  }



}
