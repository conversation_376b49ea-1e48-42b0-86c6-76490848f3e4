import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';

class HotWaterBoostDurationController extends BaseController {

  final String thingName;
  int hour = 1;
  int minute = 0;

  HotWaterBoostDurationController({
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void setHour(int value) {
    hour = value;
  }

  void setMinute(int value) {
    minute = value;
  }

  Future<void> saveBoostDuration() async {
    try {
      int totalMinutes = hour * 60 + minute;
      log.e('saveBoostDuration() -> Total Boost Duration in minutes: $totalMinutes');
      if (totalMinutes <= 0) {
        showErrorSnackBar('boostDurationNotSet'.tr);
        return;
      }
      Get.back(result: totalMinutes);
    } catch (e, s) {
      log.e('saveBoostDuration() -> $e, $s');
    }
  }

}
