import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_home/add_home_bindings.dart';
import 'package:habi_app/pages/add_home/add_home_page.dart';
import 'add_receiver_settings_controller.dart';

class AddReceiverSettingsPage extends BasePage<AddReceiverSettingsController> {

  AddReceiverSettingsPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'addReceiver'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 56.h,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [

                      SizedBox(
                        width: 56.w,
                        height: 56.h,
                        child: Center(
                          child: SvgPicture.asset(
                            AppImagePaths.receiver,
                            width: 56,
                            height: 56,
                          ),
                        ),
                      ),

                      Expanded(
                        child: Center(
                          child: Obx(() {
                            if (controller.editName.value) {
                              return ConstrainedBox(
                                constraints: const BoxConstraints(
                                    maxHeight: 148,
                                    minHeight: 48
                                ),
                                child: TextFormField(
                                  onFieldSubmitted: (value) {
                                    log.i('onFieldSubmitted() -> value: $value');
                                    controller.deviceName.value = value;
                                    controller.editName.value = false;
                                  },
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: AppFontWeights.regular,
                                    color: Theme.of(context)
                                        .extension<AppThemeExtension>()!.firstColor,
                                  ),
                                  controller: controller.editNameController,
                                ),
                              );
                            }

                            return Text(controller.deviceName.value,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: AppFontWeights.regular,
                                  overflow: TextOverflow.ellipsis,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!.firstColor,
                                )
                            );
                          }),
                        ),
                      ),

                      SizedBox(
                        width: 56.w,
                        height: 56.h,
                        child: InkWell(
                          onTap: () {
                            controller.editName.value = !controller.editName.value;
                          },
                          child: Center(
                            child: SvgPicture.asset(
                              AppImagePaths.edit,
                              width: 22,
                              height: 22,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 40.h),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                      'setTimeZone'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      )
                  ),
                ),

                SizedBox(height: 14.h),

                Obx(() {
                  if (controller.timeZoneList.isEmpty) {
                    return const SizedBox();
                  }
                  return DropdownMenu(
                    width: Get.width - 72,
                    menuHeight: 350.h,
                    initialSelection: controller.selectedTimeZone.value,
                    dropdownMenuEntries: controller.timeZoneList,
                    onSelected: _onTimeZoneSelected,
                  );
                }),

                Obx(() {
                  if (controller.homeEntries.isEmpty) {
                    return const SizedBox();
                  }
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 14.h),
                      Opacity(
                        opacity: 0.5,
                        child: Text(
                          'location'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),

                      SizedBox(height: 14.h),
                      DropdownMenu(
                        width: Get.width - 72,
                        menuHeight: 350.h,
                        dropdownMenuEntries: controller.homeEntries,
                        initialSelection: controller.selectedHomeEntry.value,
                        onSelected: _onHomeSelected,
                      ),
                    ],
                  );
                }),

                Obx(() {
                  if (!controller.isDeviceInHome.value) {
                    return Column(
                      children: [
                        SizedBox(height: 14.h),
                        SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: OutlinedButton.icon(
                            onPressed: _addToNewHome,
                            icon: const Icon(
                              Icons.add,
                              color: AppColors.ff01A796,
                            ),
                            label: Text('addNewLocation'.tr,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: AppFontWeights.regular,
                                  color: AppColors.ff01A796,
                                )),
                          ),
                        )
                      ],
                    );
                  }
                  return const SizedBox();
                }),

                SizedBox(height: 28.h),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                      '2ndRelayFunction'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      )
                  ),
                ),

                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                          'hotWaterControl'.tr,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          )
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Obx(() => Switch(
                          value: controller.isHotWaterControlEnabled.value,
                          onChanged: (value) {
                            controller.isHotWaterControlEnabled.value = value;
                          },
                        )),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 104.h),

                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _onSave,
                    child: Text(
                      'save'.tr.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                SizedBox(height: 51.h),

              ],
            ),
          ),
        )
    );
  }

  void _onTimeZoneSelected(value) {
    log.i('selected time zone: $value');
    controller.selectedTimeZone.value = value;
  }

  void _onHomeSelected(String? value) {
    if (value == null || value.isEmpty) {
      return;
    }
    controller.selectedHomeEntry.value = value;
  }

  void _addToNewHome() async {
    var result = await Get.to(() => AddHomePage(),
        binding: AddHomeBindings());
    if (result != null) {
      await controller.loadAllData();
    }
  }

  void _onSave() {
    if (controller.selectedHomeEntry.isEmpty) {
      showSnackBar('pleaseSelectLocationOfYourReceiver'.tr);
      return;
    }
    controller.handelAddReceiver();
  }


}
