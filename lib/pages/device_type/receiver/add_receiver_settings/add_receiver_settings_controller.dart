import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_time_zones.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';

class AddReceiverSettingsController extends BaseController {
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;

  final String thingName;
  final TextEditingController editNameController = TextEditingController();

  late Map<String, String> timeZonesMap;
  late String mcThingName;

  var timeZoneList = <DropdownMenuEntry<String>>[].obs;
  var selectedTimeZone = ''.obs;
  var homeEntries = <DropdownMenuEntry<String>>[].obs;
  var selectedHomeEntry = ''.obs;
  var isHotWaterControlEnabled = false.obs;
  var editName = false.obs;
  var deviceName = 'myReceiver'.tr.obs;
  var isDeviceInHome = false.obs;

  AddReceiverSettingsController({
    required this.thingName,
  });

  @override
  void onInit() {
    super.onInit();
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
    editNameController.text = 'myReceiver'.tr;
  }

  @override
  void onReady() {
    super.onReady();
    final dropdownMenuEntries = <DropdownMenuEntry<String>>[];
    final timeZoneConvertedMap = AppTimeZones.getTimeZoneConvertedMap();
    timeZonesMap = AppTimeZones.getTimeZoneMap();
    DropdownMenuEntry? defaultEntry;

    timeZoneConvertedMap.forEach((key, value){
      var entry = DropdownMenuEntry<String>(value: key, label: value);
      dropdownMenuEntries.add(entry);
      if (AppTimeZones.defaultTimeZoneKey == key) {
        defaultEntry = entry;
      }
    });

    selectedTimeZone.value = defaultEntry?.value;
    timeZoneList.value = dropdownMenuEntries;

    loadAllData();
  }

  @override
  void onClose() {
    editNameController.dispose();
    super.onClose();
  }

  Future<void> loadAllData() async {
    updateLoading(true);

    if (await _tryGetHomeByDeviceId()) {
      return;
    }

    if (await _tryGetUserHomes()) {
      return;
    }

    updateLoading(false);
    showLoadDataErrorDialog();
  }

  Future<bool> _tryGetHomeByDeviceId() async {
    try {
      final response = await DevService.to.getHomeByDeviceID(thingName)
          .timeout(const Duration(seconds: 60));

      if (!(response.success ?? false)) {
        log.e('_tryGetHomeByDeviceId() -> API call failed');
        return false;
      }

      final presenceHome = response.home;
      if (presenceHome == null) {
        log.e('_tryGetHomeByDeviceId() -> No presence home');
        return false;
      }

      log.i('_tryGetHomeByDeviceId() -> Found device in home: ${presenceHome.name}');
      _setupSingleHomeOption(AppHome.fromHBHome(presenceHome));
      isDeviceInHome.value = true;
      updateLoading(false);
      return true;
    } catch (e) {
      log.e('_tryGetHomeByDeviceId() -> error: $e');
      return false;
    }
  }

  Future<bool> _tryGetUserHomes() async {
    try {
      final response = await DevService.to.getUserHomes()
          .timeout(const Duration(seconds: 60));

      if (!(response.success ?? false)) {
        log.e('_tryGetUserHomes() -> API call failed');
        updateLoading(false);
        return true;
      }

      final homes = response.homes ?? [];
      if (homes.isEmpty) {
        log.e('_tryGetUserHomes() -> No homes found');
        updateLoading(false);
        return true;
      }

      final appHomes = homes.map((home) => AppHome.fromHBHome(home)).toList();

      try {
        AppHome? existingHome = appHomes.firstWhere(
              (home) => home.deviceList?.contains(thingName) ?? false,
          orElse: () => throw Exception('Device not found in any home'),
        );

        log.i('_tryGetUserHomes() -> Device found in home: ${existingHome.name}');
        _setupSingleHomeOption(existingHome);
        isDeviceInHome.value = true;
        updateLoading(false);
        return true;
      } catch (e){
        log.i('_tryGetUserHomes() -> Device not found in any home, '
            'proceeding with available homes');
      }

      var availableHomes = appHomes.where((home) => home.deviceList == null
          || home.deviceList!.isEmpty).toList();

      if (availableHomes.isEmpty) {
        log.e('_tryGetUserHomes() -> no available home found after filtering');
        updateLoading(false);
        return true;
      }

      log.i('_tryGetUserHomes() -> availableHomes length: ${availableHomes.length}');
      _setupHomeOptions(availableHomes);
      updateLoading(false);
      return true;
    } catch (e) {
      log.e('_tryGetUserHomes() -> error: $e');
      return false;
    }
  }

  void _setupSingleHomeOption(AppHome home) {
    List<DropdownMenuEntry<String>> homeOptions = [
      DropdownMenuEntry(
        label: home.name!,
        value: home.homeId!,
      )
    ];
    homeEntries.value = homeOptions;
    selectedHomeEntry.value = homeOptions.first.value;
  }

  void _setupHomeOptions(List<AppHome> homes) {
    List<DropdownMenuEntry<String>> homeOptions = homes.map((home) =>
        DropdownMenuEntry(
          label: home.name!,
          value: home.homeId!,
        )
    ).toList();

    if (homeOptions.isNotEmpty) {
      homeEntries.value = homeOptions;
      selectedHomeEntry.value = homeOptions.first.value;
    }
  }

  Future<void> handelAddReceiver() async {
    try {
      updateLoadingMessage('saving...');

      bool result;
      if (isDeviceInHome.value) {
        result = await _addUserToHome();
      } else {
        result = await _addDeviceToHome();
      }

      if (!result) {
        showSaveErrorDialog();
        return;
      }

      await localStorageService.setThingGroup(thingName);
      await _publishSettings();
      await _navigateBack();
      
    } catch (e, s) {
      log.e('handelAddReceiver() -> error: $e\n$s');
    } finally {
      updateLoading(false);
    }
  }

  Future<bool> _addUserToHome() async {
    try {
      final session = await AuthService.to.fetchCognitoAuthSession();
      final userId = session.identityIdResult.value;
      final homeId = selectedHomeEntry.value;
      
      await DevService.to.addUserToHome(
        homeId, 
        [userId], 
        true // isOwner
      ).timeout(const Duration(seconds: 60));
      log.i('_addUserToHome() -> succeeded');
      return true;
    } catch (e, s) {
      log.e('_addUserToHome() -> failed: $e\n$s');
      return false;
    }
  }

  Future<bool> _addDeviceToHome() async {
    try {
      final homeId = selectedHomeEntry.value;
      await DevService.to.addDeviceToHome(
        homeId, 
        thingName
      ).timeout(const Duration(seconds: 60));
      
      log.i('_addDeviceToHome() -> succeeded');
      return true;
    } catch (e, s) {
      log.e('_addDeviceToHome() -> failed: $e\n$s');
      return false;
    }
  }

  Future<void> _publishSettings() async {
    try {
      await Future.wait([
        publishProperty(),
        publishHotWaterControlEnabledProperty()
      ]);
    } catch (e, s) {
      log.e('_publishSettings() -> failed: $e\n$s');
    }
  }

  Future<void> _navigateBack() async {
    Get.until((route) => route.settings.name == Routes.homeManagement);
    
    GlobalService.to
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.addReceiver));
  }

  Future<void> publishProperty() async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        String timeZone = timeZonesMap[selectedTimeZone.value] ?? AppTimeZones
            .defaultTimeZoneValue;
        await deviceShadowService.updateDeviceProperties(
            thingName: thingName,
            property: {
              'ep0:sGateway:SetDeviceName': deviceName.value,
              'ep0:sGateway:SetTimeZone': timeZone,
            },
            subId: Sait85rGW.subId
        ).timeout(const Duration(seconds: 60));
        log.i('publishProperty() -> successfully publish data to cloud!');
        return;
      } catch (e, r) {
        retryCount++;
        if (retryCount < maxRetries) {
          log.w('publishProperty() -> attempt $retryCount failed, retrying...');
        } else {
          log.e('publishProperty() -> failed to publish data to cloud after $maxRetries attempts: $e, $r');
        }
      }
    }
  }

  Future<void> publishHotWaterControlEnabledProperty() async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        int enableDHW = isHotWaterControlEnabled.value ? 1 : 0;
        await deviceShadowService.updateDeviceProperties(
            thingName: mcThingName,
            property: {
              "ep0:sBoiler:sEnableDHW": enableDHW,
            },
            subId: Sait85rMC.subId
        ).timeout(const Duration(seconds: 60));
        log.i('publishHotWaterControlEnabledProperty() -> successfully published data to cloud!');
        return;
      } catch (e, r) {
        retryCount++;
        if (retryCount < maxRetries) {
          log.w('publishHotWaterControlEnabledProperty() -> attempt $retryCount failed, retrying...');
        } else {
          log.e('publishHotWaterControlEnabledProperty() -> failed to publish data to cloud after $maxRetries attempts: $e, $r');
        }
      }
    }
  }

  void showSaveErrorDialog() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                'saveFailed'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                'saveFailedPrompt'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'cancel'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  handelAddReceiver();
                },
                child: Text(
                  'tryAgain'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

  void showLoadDataErrorDialog() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                'failedToLoadData'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                'unableToFetchDataFromServer'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'cancel'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  loadAllData();
                },
                child: Text(
                  'tryAgain'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

}
