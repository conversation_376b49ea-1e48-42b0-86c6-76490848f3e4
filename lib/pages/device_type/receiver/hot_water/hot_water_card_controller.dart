import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_card_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/extensions/sait85r_gw_extension.dart';
import 'package:habi_app/extensions/sait85r_mc_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/widgets/change_receiver_mode_warning_widget.dart';
import 'package:habi_app/widgets/receiver_running_mode_widget.dart';
import 'package:habi_app/pages/device_type/receiver/hot_water_boost_duration/hot_water_boost_duration_bindings.dart';
import 'package:habi_app/pages/device_type/receiver/hot_water_boost_duration/hot_water_boost_duration_page.dart';
import 'package:intl/intl.dart';

class HotWaterCardController {
  static const String dhwRelayKey = 'ep0:sBoiler:DHWRelay';
  static const String holdTypeKey = 'ep0:sBoiler:HoldType';
  static const String boostDHWKey = 'ep0:sBoiler:BoostDHW';

  final deviceShadowService = DeviceShadowService.to;
  final globalService = GlobalService.to;
  final String thingName;

  HotWaterCardController({
    required this.thingName,
  });

  late String mcThingName;
  late Sait85rGW sait85rGW;
  late Sait85rMC sait85rMC;
  late ValueNotifier<CardUIType> uiType;
  late Map<String, dynamic> _manualPropertyMap;

  bool isConnected = false;
  bool isLoading = false;
  bool hotWater = false;
  int runningMode = ReceiverRunningModeWidget.permanentHold;
  String statusText = '${'status'.tr}: ${'continuousOff'.tr}';

  StreamSubscription? _thingShadowSubscription;
  Timer? _changePropertyTimer;

  void init() {
    _manualPropertyMap = {};
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
    uiType = ValueNotifier<CardUIType>(CardUIType.copyLoading());
    setMqttListener();
    Future(() async {
      await initData();
    });
  }

  void dispose() {
    uiType.dispose();
    _thingShadowSubscription?.cancel();
    _changePropertyTimer?.cancel();
    _changePropertyTimer = null;
  }

  void setMqttListener() {
    _thingShadowSubscription = globalService
        .getMqttStream()
        .listen((shadow) async {
      if (shadow.thingName == mcThingName) {
        _handleMcBusiness(shadow.thingShadow);
      }
      if (shadow.thingName == thingName) {
        _handleGWBusiness(shadow.thingShadow);
      }
    });
  }

  Future<void> initData() async {
    await Future.wait([fetchGWShadow(), fetchMCShadow()]);
    uiType.value = CardUIType.copyContent();
  }

  Future<void> fetchGWShadow() async {
    try {
      AppShadow gwShadow = deviceShadowService.getDeviceShadow(thingName);
      if (gwShadow.thingShadow.isEmpty) {
        Map<String, dynamic> gwThingShadow = await deviceShadowService.fetchDeviceShadow(thingName);
        log.i('fetchGWShadow() -> getShadow success: $thingName');
        gwShadow = AppShadow(thingShadow: gwThingShadow, thingName: thingName);
      }
      sait85rGW = Sait85rGW.fromJson(gwShadow.thingShadow, thingName);
      isConnected = sait85rGW.isConnected();
    } catch (e) {
      log.e('fetchGWShadow() -> getShadow error: $thingName, $e');
    }
  }

  Future<void> fetchMCShadow() async {
    try {
      AppShadow mcShadow = deviceShadowService.getDeviceShadow(mcThingName);
      if (mcShadow.thingShadow.isEmpty) {
        Map<String, dynamic> mcThingShadow = await deviceShadowService.fetchDeviceShadow(mcThingName);
        log.i('fetchMCShadow() -> getMCShadow success: $mcThingName');
        mcShadow = AppShadow(thingShadow: mcThingShadow, thingName: mcThingName);
      }

      sait85rMC = Sait85rMC.fromJson(mcShadow.thingShadow, mcThingName);
      sait85rMC.parseSchedules();

      int dhwRelay = sait85rMC.getDHWRelay();
      hotWater = dhwRelay == 1;

      int boostDHW = sait85rMC.getBoostDHW();
      if (boostDHW != 0) {
        runningMode = ReceiverRunningModeWidget.boost;

        //从云上面拿boost的更新时间做计算
        int boostDHWMillisecond = sait85rMC.shadow?.metadata?.reported?.model?.properties?.sBoiler?.boostDHW ?? 0;
        if (boostDHWMillisecond <= 0) {
          log.w('boostDHWMillisecond <= 0');
          statusText = '${'status'.tr}: ${'onBoost'}';
          return;
        }

        boostDHWMillisecond = boostDHWMillisecond * 1000;
        log.i('boostDHWMillisecond: $boostDHWMillisecond');

        DateTime boostDataTime = DateTime.fromMillisecondsSinceEpoch(boostDHWMillisecond);
        //结束时间
        DateTime boostEndTime = boostDataTime.add(Duration(minutes: boostDHW));
        // 格式化时间为字符串，例如 '7:50 AM'
        String formattedTime = DateFormat('h:mm a').format(boostEndTime);
        statusText = '${'status'.tr}: ${'onBoostUntil'.trParams({
          'time': formattedTime,
        })}';

      } else {
        int holdType = sait85rMC.getHoldType();
        if (holdType == 0) {
          runningMode = ReceiverRunningModeWidget.schedule;
          statusText = '${'status'.tr}: ${'followingSchedule'.tr}';
        } else if (holdType == 2){
          runningMode = ReceiverRunningModeWidget.permanentHold;
          if (hotWater) {
            statusText = '${'status'.tr}: ${'continuousOn'.tr}';
          } else {
            statusText = '${'status'.tr}: ${'continuousOff'.tr}';
          }
        }
      }

    } catch (e) {
      log.e('fetchMCShadow() -> getMCShadow error: $mcThingName, $e');
    }
  }

  Future<void> changeRunningMode(String value) async {
    switch (value) {
      case 'turnOn':
        await turnOn();
        break;
      case 'turnOff':
        await turnOff();
        break;
      case 'schedule':
        await scheduleMode();
        break;
      case 'boost':
        await boostMode();
        break;
      default:
       break;
     }
  }

  Future<void> turnOn() async {
    if (isLoading) {
      log.i('turnOn() -> 正在处理...');
      return;
    }

    if (hotWater) {
      log.i('turnOn() -> hot-water已经是on，无需处理...');
      return;
    }

    int dhwSwitch = sait85rMC.getDHWSwitch();
    if (dhwSwitch != 2) {
      log.i('turnOn() -> 当前不是auto模式，无法切换...');
      _showTipsDialog('switchNotInAutoMode'.tr);
      return;
    }

    isLoading = true;
    uiType.value = CardUIType.copyContent();

    _manualPropertyMap[dhwRelayKey] = 1;
    _manualPropertyMap[holdTypeKey] = 2;
    _manualPropertyMap[boostDHWKey] = 0;

    _startPropertyTimer();
    await sait85rMC.setHotWater(true);
  }

  Future<void> turnOff() async {
    if (isLoading) {
      log.i('turnOff() -> 正在处理...');
      return;
    }

    if (!hotWater) {
      log.i('turnOff() -> hot-water已经是off，无需处理...');
      return;
    }

    int dhwSwitch = sait85rMC.getDHWSwitch();
    if (dhwSwitch != 2) {
      log.i('turnOff() -> 当前不是auto模式，无法切换...');
      _showTipsDialog('switchNotInAutoMode'.tr);
      return;
    }

    isLoading = true;
    uiType.value = CardUIType.copyContent();

    _manualPropertyMap[dhwRelayKey] = 0;
    _manualPropertyMap[holdTypeKey] = 2;
    _manualPropertyMap[boostDHWKey] = 0;

    _startPropertyTimer();
    await sait85rMC.setHotWater(false);
  }

  Future<void> scheduleMode() async {
    if (isLoading) {
      log.i('scheduleMode() -> 正在处理...');
      return;
    }

    if (!hotWater) {
      log.i('turnOn() -> hot-water是off，不可以打开schedule模式...');
      _showTipsDialog('pleaseActivateTurnOnButtonFirst'.tr);
      return;
    }

    int dhwSwitch = sait85rMC.getDHWSwitch();
    if (dhwSwitch != 2) {
      log.i('boostMode() -> 当前不是auto模式，无法切换...');
      _showTipsDialog('switchNotInAutoMode'.tr);
      return;
    }

    if (runningMode == ReceiverRunningModeWidget.schedule) {
      log.i('scheduleMode() -> 运行模式为schedule，无需切换...');
      return;
    }

    if (runningMode == ReceiverRunningModeWidget.boost) {
      showChangeModeWarningDialog(ReceiverRunningModeWidget.schedule);
      return;
    }

    await executeScheduleMode();
  }

  Future<void> executeScheduleMode() async {
    isLoading = true;
    uiType.value = CardUIType.copyContent();

    _manualPropertyMap[holdTypeKey] = 0;
    _manualPropertyMap[boostDHWKey] = 0;

    _startPropertyTimer();
    await sait85rMC.setScheduleMode();
  }

  Future<void> boostMode() async {
    if (isLoading) {
      log.i('boostMode() -> 正在处理...');
      return;
    }

    int dhwSwitch = sait85rMC.getDHWSwitch();
    if (dhwSwitch != 2) {
      log.i('boostMode() -> 当前不是auto模式，无法切换...');
      _showTipsDialog('switchNotInAutoMode'.tr);
      return;
    }

    if (runningMode == ReceiverRunningModeWidget.boost) {
      log.i('boostMode() -> 运行模式为boost，无需切换...');
      return;
    }

    if (runningMode == ReceiverRunningModeWidget.schedule) {
      showChangeModeWarningDialog(ReceiverRunningModeWidget.boost);
      return;
    }

    await executeBoostMode();
  }

  Future<void> executeBoostMode() async {
    int? totalMinutes = await Get.to(() => HotWaterBoostDurationPage(),
        binding: HotWaterBoostDurationBindings(thingName: thingName));
    if (totalMinutes != null && totalMinutes > 0) {
      isLoading = true;
      uiType.value = CardUIType.copyContent();

      _manualPropertyMap[boostDHWKey] = totalMinutes;
      _startPropertyTimer();

      await sait85rMC.setBoostMode(totalMinutes);
    }
  }

  void showChangeModeWarningDialog(int runningMode) {
    String warningText;
    if (runningMode == ReceiverRunningModeWidget.schedule) {
      warningText = 'endBoostTimerAndResumeSchedule'.tr;
    } else {
      warningText = 'endScheduleAndResumeBoostTimer'.tr;
    }
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return ChangeReceiverModeWarningWidget(
              onYes: () {
                Get.back();
                if (runningMode == ReceiverRunningModeWidget.schedule) {
                  executeScheduleMode();
                } else {
                  executeBoostMode();
                }
              },
              onCancel: () {
                Get.back();
              },
              warningText: warningText
          );
        }
    );
  }
  
  void _startPropertyTimer() {
    _changePropertyTimer?.cancel();
    _changePropertyTimer = null;
    _changePropertyTimer = Timer(const Duration(seconds: 30), () {
      log.i("startPropertyTimer() -> timeout.");
      _manualPropertyMap.clear();
      _changePropertyTimer?.cancel();
      _changePropertyTimer = null;
      isLoading = false;
      uiType.value = CardUIType.copyContent();
    });
  }


  Future<void> _handleMcBusiness(Map<String, dynamic> thingShadow) async {
    try {
      sait85rMC.mapJson(thingShadow);

      if (_manualPropertyMap.containsKey(dhwRelayKey)
          || _manualPropertyMap.containsKey(holdTypeKey)
          || _manualPropertyMap.containsKey(boostDHWKey)
      ) {
        log.i('刷新区域1...');
        _changePropertyTimer?.cancel();
        _changePropertyTimer = null;
        _manualPropertyMap.clear();
        return;
      }

      int dhwRelay = sait85rMC.getDHWRelay();
      hotWater = dhwRelay == 1;

      int boostDHW = sait85rMC.getBoostDHW();
      if (boostDHW != 0) {
        runningMode = ReceiverRunningModeWidget.boost;

        // 获取当前时间
        DateTime now = DateTime.now();
        // 计算boostDHW分钟后的时间
        DateTime boostEndTime = now.add(Duration(minutes: boostDHW));
        // 格式化时间为字符串，例如 '7:50 AM'
        String formattedTime = DateFormat('h:mm a').format(boostEndTime);

        statusText = '${'status'.tr}: ${'onBoostUntil'.trParams({
          'time': formattedTime,
        })}';

      } else {
        int holdType = sait85rMC.getHoldType();
        if (holdType == 0) {
          runningMode = ReceiverRunningModeWidget.schedule;
          statusText = '${'status'.tr}: ${'followingSchedule'.tr}';
        } else if (holdType == 2){
          runningMode = ReceiverRunningModeWidget.permanentHold;
          if (hotWater) {
            statusText = '${'status'.tr}: ${'continuousOn'.tr}';
          } else {
            statusText = '${'status'.tr}: ${'continuousOff'.tr}';
          }
        }
      }

      log.i('刷新区域2...');
      isLoading = false;
      uiType.value = CardUIType.copyContent();
    } catch (e, r) {
      log.e('_handleMcBusiness() -> 失败: $e, $r');
    }
  }

  Future<void> _handleGWBusiness(Map<String, dynamic> thingShadow) async {
    try {
      sait85rGW.mapJson(thingShadow);
      bool thatIsConnected = sait85rGW.isConnected();
      if (thatIsConnected != isConnected) {
        log.i('刷新isConnected');
        isConnected = thatIsConnected;
        uiType.value = CardUIType.copyContent();
      }
    } catch (e, r) {
      log.e('_handleGWBusiness() -> 失败: $e, $r');
    }
  }

  void _showTipsDialog(String msg) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return Dialog(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(28.0),
              ),
            ),
            insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                    borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: Text(
                          msg,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.regular,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 30,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: OutlinedButton(
                            onPressed: () {
                              Get.back();
                            },
                            child: Text(
                              'ok'.tr,
                              style: const TextStyle(
                                  color: AppColors.ff01A796
                              ),
                            ),
                          ),
                        ),
                      ),


                      const SizedBox(height: 50,),
                    ],
                  ),
                ),
              ),
            ),

          );
        }
    );
  }

}