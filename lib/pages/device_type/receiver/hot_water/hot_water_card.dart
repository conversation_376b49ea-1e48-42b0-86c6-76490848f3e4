import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_card_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/device_type/receiver/view_schedule/hot_water_schedule_page.dart';
import 'package:habi_app/widgets/receiver_running_mode_widget.dart';
import 'package:habi_app/widgets/habi_shadow_card.dart';
import 'hot_water_card_controller.dart';

class HotWaterCard extends BaseCardWidget {

  HotWaterCard({
    super.key,
    required super.thingName
  });

  @override
  State<HotWaterCard> createState() {
    return HotWaterCardState();
  }
}

class HotWaterCardState extends BaseCardState<HotWaterCard> {

  late HotWaterCardController controller;

  @override
  void initState() {
    super.initState();
    log.i('HotWaterCard{} -> initState');
    controller = HotWaterCardController(thingName: widget.thingName);
    controller.init();
  }

  @override
  void dispose() {
    log.i('HotWaterCard{} -> dispose');
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    log.i('HotWaterCard{} -> build');
    return ValueListenableBuilder(
      valueListenable: controller.uiType,
      builder: (context, value, child) {
        // log.i('HotWaterCard{} -> 检测到内部 build...');
          if (value.value == CardUIType.loading) {
            return buildLoading();
          }
          return _buildContent(context);
        },
    );
  }

  Widget _buildContent(BuildContext context) {
    return Opacity(
      opacity: controller.isConnected ? 1.0 : 0.5,
      child: AbsorbPointer(
        absorbing: !controller.isConnected,
        child: HabiShadowCard(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'hotWater'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      ),
                    ),
                    Expanded(
                        child: GestureDetector(
                          onTap: () {
                            Get.to(() => HotWaterSchedulePage(thingName: widget.thingName));
                          },
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: SvgPicture.asset(
                              AppImagePaths.hotWaterSchedule,
                              width: 22.58,
                              height: 21.58,
                              colorFilter: ColorFilter.mode(controller.runningMode == ReceiverRunningModeWidget.schedule
                                  ? AppColors.ff01A796 : Theme.of(context)
                                  .extension<AppThemeExtension>()!
                                  .fourthColor
                                  .withOpacity(0.5), BlendMode.srcIn),
                            ),
                          ),
                        ),
                    )
                  ]
                ),

                const SizedBox(height: 17,),

                Stack(
                  children: [
                    ReceiverRunningModeWidget(
                      hotWater: controller.hotWater,
                      runningMode: controller.runningMode,
                      onSelected: (String value) {
                        controller.changeRunningMode(value);
                      },
                    ),
                    if (controller.isLoading)
                      const Center(
                        child: SizedBox(
                            width: 65,
                            height: 65,
                            child: CircularProgressIndicator(
                              color: AppColors.ff01A796,
                            ),
                        ),
                      )
                  ],
                ),

                const SizedBox(height: 17,),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                    controller.statusText,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),

              ],
            ),
          ),
        ),
      ),
    );
  }



}