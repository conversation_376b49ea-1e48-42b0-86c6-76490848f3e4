import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_stateful_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/constants/app_schedules.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/models/schedule/hot_water_schedule.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/pages/device_type/receiver/add_or_edit_schedule/add_or_edit_hot_water_schedule_bindings.dart';
import 'package:habi_app/pages/device_type/receiver/add_or_edit_schedule/add_or_edit_hot_water_schedule_controller.dart';
import 'package:habi_app/pages/device_type/receiver/add_or_edit_schedule/add_or_edit_hot_water_schedule_page.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/extensions/sait85r_mc_extension.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:habi_app/utility/date_time_utils.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'package:habi_app/widgets/habi_button.dart';
import 'package:habi_app/widgets/schedule_widgets/hot_water_schedule_data.dart';
import 'package:habi_app/widgets/schedule_widgets/schedule_tab_bar.dart';

class HotWaterSchedulePage extends BaseStatefulWidget {
  final String thingName;
  late final String mcThingName;

  HotWaterSchedulePage({
    required this.thingName,
    super.key,
  }) {
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
  }

  @override
  HotWaterSchedulePageState createState() => HotWaterSchedulePageState();
}

class HotWaterSchedulePageState extends BaseStatefulState<HotWaterSchedulePage> with SingleTickerProviderStateMixin {
  final localStorageService = LocalStorageService.to;
  final deviceShadowService = DeviceShadowService.to;
  final userAttributesService = UserAttributesService.to;

  Sait85rMC? sait85rMC;

  final _mon = <HotWaterSchedule>[].obs;
  final _tue = <HotWaterSchedule>[].obs;
  final _wed = <HotWaterSchedule>[].obs;
  final _thu = <HotWaterSchedule>[].obs;
  final _fri = <HotWaterSchedule>[].obs;
  final _sat = <HotWaterSchedule>[].obs;
  final _sun = <HotWaterSchedule>[].obs;
  final _monSun = <HotWaterSchedule>[].obs;
  final _monFri = <HotWaterSchedule>[].obs;
  final _satSun = <HotWaterSchedule>[].obs;

  final _initializing = true.obs;
  final _isSending = false.obs;
  final _isScheduleUpdate = false.obs;
  final _isDeleteAllVisible = false.obs;
  final _isModifyEnable = false.obs;

  late TabController _tabController;

  int _totalIntervals = 0;
  int _initScheduleType = 2;

  // Avoid external padding to prevent shadow clipping on HabiShadowCard
  final _pageHorizontalPadding = const EdgeInsets.symmetric(horizontal: 30);

  @override
  void initState() {
    super.initState();
    AppShadow appShadow = deviceShadowService.getDeviceShadow(widget.mcThingName);
    Map<String, dynamic> mcThingShadow = appShadow.thingShadow;

    sait85rMC = Sait85rMC.fromJson(mcThingShadow, widget.mcThingName);
    sait85rMC?.parseSchedules();

    _isModifyEnable.value = sait85rMC?.shadow.state?.reported?.connected == "true";
    _isDeleteAllVisible.value = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.isDeleteAllIntervalVisible() ?? false;
    _initScheduleType = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.sTimeGh?.scheduleType ?? 2;

    _getSchedules();

    _tabController = TabController(length: 3, vsync: this, initialIndex: 1);
    _tabController.addListener(_onTabChanged);
    _tabController.index = _initScheduleType == 0 ? 0 : _initScheduleType - 1;

    _initializing.value = false;
  }

  @override
  void dispose() {
    if (_isScheduleUpdate.value) {
      sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.cancel();
      sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
    }
    sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.changeBackScheduleType();
    sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.clearOtherTypeSchedules();
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.setScheduleType(_tabController.index + 1);
    sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
    setState(() {});
    log.i('当前选中的Tab索引：${_tabController.index}');
  }

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'schedule'.tr,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          SizedBox(height: 20.h),
          Padding(
            padding: _pageHorizontalPadding,
            child: TabBar(
              controller: _tabController,
              tabs: _getTabs(),
              labelColor: AppColorsHelper.firstColor,
              unselectedLabelColor: AppColorsHelper.firstColor.withOpacity(0.4),
              indicatorColor: AppColors.ff01A796,
              indicatorWeight: 2,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: AppColors.ff707070.withOpacity(0.5),
              dividerHeight: 2,
            ),
          ),
          SizedBox(height: 20.h),
          Obx(() {
            if (_initializing.value) {
              return Center(
                  child: CircularProgressIndicator(
                color: AppColorsHelper.firstColor,
              ));
            }
            return Expanded(
              child: TabBarView(
                controller: _tabController,
                children: _getTabPages(),
              ),
            );
          }),
          SizedBox(height: 20.h),
          Padding(
            padding: _pageHorizontalPadding,
            child: Obx(() {
              if (_isScheduleUpdate.value) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    HabiButton(
                      onPressed: () {
                        if (sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getScheduleType() != _initScheduleType) {
                          sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.clearSchedules();
                        }
                        sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.setIsScheduleUpdate(isScheduleUpdate: false);
                        sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.cancel();
                        _getSchedules();
                        _tabController.index = _initScheduleType == 0 ? 0 : _initScheduleType - 1;
                        _isScheduleUpdate.value = false;
                        _isDeleteAllVisible.value = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.isDeleteAllIntervalVisible() ?? true;
                      },
                      text: 'cancel'.tr,
                      textColor: Colors.white,
                      backgroundColor: AppColors.ff01A796,
                    ),
                    const Spacer(),
                    _buildSaveSchedule(),
                  ],
                );
              } else {
                if (_isDeleteAllVisible.value) {
                  return _buildDeleteAllSlot();
                }
              }
              return Container();
            }),
          ),
          SizedBox(height: 30.h),
        ],
      ),
    );
  }

  List<Widget> _getTabs() {
    final List<Tab> tabs = [];
    final networkScanText = 'all'.tr;
    final pairingCodeText = 'workWeek'.tr;
    final qrCodeText = 'individual'.tr;

    tabs.add(Tab(text: networkScanText));
    tabs.add(Tab(text: pairingCodeText));
    tabs.add(Tab(text: qrCodeText));

    return tabs;
  }

  List<Widget> _getTabPages() {
    return [
      ScheduleTabBar(
        tabBarPadding: _pageHorizontalPadding,
        scheduleHeader: _buildScheduleHeader(),
        scheduleType: 1,
        initialIndex: 0,
        allDay: _buildFullWeekContainers(context),
        weekDay: _buildWeekDayContainers(context),
        daily: _buildDailyContainers(context),
        key: UniqueKey(),
      ),
      ScheduleTabBar(
        tabBarPadding: _pageHorizontalPadding,
        scheduleHeader: _buildScheduleHeader(),
        scheduleType: 2,
        initialIndex: 0,
        allDay: _buildFullWeekContainers(context),
        weekDay: _buildWeekDayContainers(context),
        daily: _buildDailyContainers(context),
        key: UniqueKey(),
      ),
      ScheduleTabBar(
        tabBarPadding: _pageHorizontalPadding,
        scheduleHeader: _buildScheduleHeader(),
        scheduleType: 3,
        initialIndex: 0,
        allDay: _buildFullWeekContainers(context),
        weekDay: _buildWeekDayContainers(context),
        daily: _buildDailyContainers(context),
        key: UniqueKey(),
      ),
    ];
  }

  List<Widget> _buildWeekDayContainers(BuildContext context) {
    return [
      Obx(
        () => _buildScheduleData(
          context,
          _monFri,
          getDefaultHotWaterSchedule(1),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _satSun,
          getDefaultHotWaterSchedule(6),
        ),
      ),
    ];
  }

  List<Widget> _buildFullWeekContainers(BuildContext context) {
    return [
      Obx(
        () => _buildScheduleData(
          context,
          _monSun,
          getDefaultHotWaterSchedule(1),
        ),
      ),
    ];
  }

  List<Widget> _buildDailyContainers(BuildContext context) {
    return [
      Obx(
        () => _buildScheduleData(
          context,
          _sun,
          getDefaultHotWaterSchedule(7),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _mon,
          getDefaultHotWaterSchedule(1),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _tue,
          getDefaultHotWaterSchedule(2),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _wed,
          getDefaultHotWaterSchedule(3),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _thu,
          getDefaultHotWaterSchedule(4),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _fri,
          getDefaultHotWaterSchedule(5),
        ),
      ),
      Obx(
        () => _buildScheduleData(
          context,
          _sat,
          getDefaultHotWaterSchedule(6),
        ),
      ),
    ];
  }

  Widget _buildScheduleData(BuildContext context, List<HotWaterSchedule> objList, HotWaterSchedule defaultSchedule) {
    _setTotalInterval(objList.length);
    return HotWaterScheduleData(
      padding: _pageHorizontalPadding,
      emptyMessage: "noSlot".tr,
      isModifyEnable: _isModifyEnable.value,
      objList: objList,
      onTabShowDialog: _onTabShowDialog,
      defaultSchedule: defaultSchedule,
    );
  }

  void _setTotalInterval(int interval) {
    _totalIntervals = _totalIntervals + interval;
  }

  Widget _buildScheduleHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: Opacity(
              opacity: 0.5,
              child: Text(
                'state'.tr,
                style: TextStyles.medium14FirstColor,
              ),
            ),
          ),
          Expanded(
            child: Opacity(
              opacity: 0.5,
              child: Text(
                'time'.tr,
                style: TextStyles.medium14FirstColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  int getTimeFormat24Hour() {
    if (userAttributesService.hourFormat == HourFormatStatus.format24h) {
      return 1;
    }
    return 0;
  }

  Schedule _getDefaultSchedule({
    required int dayIndex,
    required String onOrOff,
  }) {
    return Schedule(
      dayIndex: dayIndex,
      timeFormat24Hour: getTimeFormat24Hour(),
      scheduleMode: ScheduleMode.onOrOff,
      onOrOff: onOrOff,
      isNew: true,
    );
  }

  HotWaterSchedule getDefaultHotWaterSchedule(int dayIndex) {
    return HotWaterSchedule(
      onSchedule: _getDefaultSchedule(
        dayIndex: dayIndex,
        onOrOff: HotWaterScheduleStatus.on.value,
      ),
      offSchedule: _getDefaultSchedule(
        dayIndex: dayIndex,
        onOrOff: HotWaterScheduleStatus.off.value,
      ),
    );
  }

  List<HotWaterSchedule> _convertSchedules(List<Schedule> schedules) {
    final validSchedules = schedules.where((s) => s.time != null && s.onOrOff != null).toList();

    validSchedules.sort((a, b) => a.time!.compareTo(b.time!));

    final List<HotWaterSchedule> result = [];
    Schedule? pendingOnSchedule;

    for (final schedule in validSchedules) {
      if (schedule.onOrOff == HotWaterScheduleStatus.on.value) {
        if (pendingOnSchedule != null) {
          result.add(HotWaterSchedule(
            onSchedule: pendingOnSchedule,
            offSchedule: _getDefaultSchedule(
              dayIndex: pendingOnSchedule.dayIndex,
              onOrOff: HotWaterScheduleStatus.off.value,
            ),
          ));
        }
        pendingOnSchedule = schedule;
      } else if (schedule.onOrOff == HotWaterScheduleStatus.off.value) {
        if (pendingOnSchedule != null) {
          result.add(HotWaterSchedule(
            onSchedule: pendingOnSchedule,
            offSchedule: schedule,
          ));
          pendingOnSchedule = null;
        } else {
          result.add(HotWaterSchedule(
            onSchedule: _getDefaultSchedule(
              dayIndex: schedule.dayIndex,
              onOrOff: HotWaterScheduleStatus.on.value,
            ),
            offSchedule: schedule,
          ));
        }
      }
    }

    if (pendingOnSchedule != null) {
      result.add(HotWaterSchedule(
        onSchedule: pendingOnSchedule,
        offSchedule: _getDefaultSchedule(
          dayIndex: pendingOnSchedule.dayIndex,
          onOrOff: HotWaterScheduleStatus.off.value,
        ),
      ));
    }

    return result;
  }

  void _getSchedules() {
    _mon.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getMon() ?? []);
    _tue.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getTue() ?? []);
    _wed.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getWed() ?? []);
    _thu.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getThu() ?? []);
    _fri.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getFri() ?? []);
    _sat.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getSat() ?? []);
    _sun.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getSun() ?? []);
    _monFri.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getMonToFri() ?? []);
    _satSun.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getSatToSun() ?? []);
    _monSun.value = _convertSchedules(sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getMonToSun() ?? []);
  }

  Widget _buildDeleteAllSlot() {
    return SizedBox(
      width: double.infinity,
      child: HabiButton(
        text: 'deleteAllSlots'.tr,
        textColor: _isModifyEnable.value ? Colors.white : Colors.white.withOpacity(0.5),
        backgroundColor: _isModifyEnable.value ? AppColors.ff01A796 : AppColorsHelper.firstColor.withOpacity(0.5),
        onPressed: () {
          if (!_isModifyEnable.value) {
            return;
          }
          sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.clearSchedulesWithScheduleUpdateFlag();
          _isScheduleUpdate.value = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getIsScheduleUpdate() ?? true;
          _getSchedules();
        },
      ),
    );
  }

  Widget _buildSaveSchedule() {
    return Stack(
      alignment: Alignment.center,
      children: [
        HabiButton(
          onPressed: () async {
            if (_isSending.value || !_isModifyEnable.value) {
              return;
            }
            _isSending.value = true;
            await sait85rMC?.setSchedule();
            _initScheduleType = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getScheduleType() ?? _initScheduleType;
            _isScheduleUpdate.value = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getIsScheduleUpdate() ?? false;
            _isDeleteAllVisible.value = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.isDeleteAllIntervalVisible() ?? true;
            _isSending.value = false;
          },
          text: 'save'.tr,
          textColor: _isModifyEnable.value ? Colors.white : Colors.white.withOpacity(0.5),
          backgroundColor: _isModifyEnable.value ? AppColors.ff01A796 : AppColorsHelper.firstColor.withOpacity(0.5),
        ),
        if (_isSending.value)
          CircularProgressIndicator(
            color: AppColorsHelper.firstColor,
          ),
      ],
    );
  }

  bool _hasOverlappingTime(List<HotWaterSchedule> schedules, HotWaterSchedule newSchedule) {
    final newOnScheduleTime = newSchedule.onSchedule.time!;
    final newOffScheduleTime = newSchedule.offSchedule.time!;
    final newStart = DateTimeUtils.convertHHmmToMinutes(newOnScheduleTime);
    final newEnd = DateTimeUtils.convertHHmmToMinutes(newOffScheduleTime);

    for (final schedule in schedules) {
      final existingOnScheduleTime = schedule.onSchedule.time;
      final existingOffScheduleTime = schedule.offSchedule.time;

      if (existingOnScheduleTime == null) {
        if (existingOffScheduleTime == newOnScheduleTime || existingOffScheduleTime == newOffScheduleTime) {
          return true;
        }
        continue;
      }
      if (existingOffScheduleTime == null) {
        if (existingOnScheduleTime == newOnScheduleTime || existingOnScheduleTime == newOffScheduleTime) {
          return true;
        }
        continue;
      }

      final existingStart = DateTimeUtils.convertHHmmToMinutes(existingOnScheduleTime);
      final existingEnd = DateTimeUtils.convertHHmmToMinutes(existingOffScheduleTime);

      if (newStart <= existingEnd && newEnd >= existingStart) {
        return true;
      }
    }
    return false;
  }

  void _onTabShowDialog(HotWaterSchedule schedule, List<HotWaterSchedule> objList, bool isEdit) async {
    final otherHotWaterSchedules = objList.where((item) => item != schedule).toList();
    final data = await Get.to<AddOrEditHotWaterScheduleResult>(
      () => const AddOrEditHotWaterSchedulePage(),
      binding: AddOrEditHotWaterScheduleBindings(
        schedule: schedule.copyWith(),
        isEdit: isEdit,
      ),
    );

    if (data == null) {
      log.i('empty data.');
      return;
    }

    if (data.action == ScheduleAction.save) {
      if (_hasOverlappingTime(otherHotWaterSchedules, data.schedule)) {
        showErrorSnackBar("scheduleConflicts".tr);
        return;
      }

      // Must delete Off Schedule first because its position is higher than On Schedule
      // If On Schedule is deleted first, the actual position of Off Schedule in the array would decrease by 1, leading to deleting the wrong schedule
      if (data.schedule.offSchedule.position != null) {
        log.d('HotWaterSchedulePageState._onTabShowDialog() -> delete offSchedule: ${data.schedule.offSchedule.time}');
        sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.removeSchedule(data.schedule.offSchedule);
        data.schedule.offSchedule.position = null;
      }
      if (data.schedule.onSchedule.position != null) {
        log.d('HotWaterSchedulePageState._onTabShowDialog() -> delete onSchedule: ${data.schedule.onSchedule.time}');
        sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.removeSchedule(data.schedule.onSchedule);
        data.schedule.onSchedule.position = null;
      }
      sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.addUpdateSchedules(data.schedule.onSchedule);
      sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.addUpdateSchedules(data.schedule.offSchedule);
      sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.buildScheduleStrings();
      _getSchedules();

      try {
        updateLoadingMessage('saving...');
        await sait85rMC?.setSchedule().timeout(const Duration(seconds: 60));
        _initScheduleType = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getScheduleType() ?? _initScheduleType;
        await Future.delayed(const Duration(seconds: 1));
      } catch (e, s) {
        log.i('error:$e, stack:$s');
      } finally {
        updateLoading(false);
      }
    } else if (data.action == ScheduleAction.delete) {
      if (!isEdit) {
        return;
      }

      // Must delete Off Schedule first because its position is higher than On Schedule
      // If On Schedule is deleted first, the actual position of Off Schedule in the array would decrease by 1, leading to deleting the wrong schedule
      if (data.schedule.offSchedule.position != null) {
        log.d('HotWaterSchedulePageState._onTabShowDialog() -> delete offSchedule: ${data.schedule.offSchedule.time}');
        sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.removeSchedule(data.schedule.offSchedule);
      }
      if (data.schedule.onSchedule.position != null) {
        log.d('HotWaterSchedulePageState._onTabShowDialog() -> delete onSchedule: ${data.schedule.onSchedule.time}');
        sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.removeSchedule(data.schedule.onSchedule);
      }
      _getSchedules();

      try {
        updateLoadingMessage('deleting...');
        await sait85rMC?.setSchedule().timeout(const Duration(seconds: 60));
        _initScheduleType = sait85rMC?.shadow.state?.reported?.model?.properties?.schedules?.getScheduleType() ?? _initScheduleType;
        await Future.delayed(const Duration(seconds: 1));
      } catch (e, s) {
        log.i('error:$e, stack:$s');
      } finally {
        updateLoading(false);
      }
    }
  }
}
