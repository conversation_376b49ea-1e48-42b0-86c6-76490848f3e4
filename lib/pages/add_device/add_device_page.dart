import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_stateful_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_device/add_device_controller.dart';
import 'package:habi_app/pages/add_device/network_scan/add_device_network_scan_page.dart';
import 'package:habi_app/pages/add_device/pairing_code/add_device_pairing_code_page.dart';
import 'package:habi_app/pages/add_device/qr_code/add_device_qr_code_page.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';

class AddDevicePage extends BaseStatefulWidget {

  const AddDevicePage({
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _AddDevicePagePageState();
  }
}

class _AddDevicePagePageState extends BaseStatefulState<AddDevicePage>
    with SingleTickerProviderStateMixin {

  late AddDeviceController _addDeviceController;
  late TabController _tabController;
  late List<Widget> _tabs;
  late List<Widget> _tabPages;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _addDeviceController = Get.find<AddDeviceController>();
    _addDeviceController.setUpdateLoadingCallback((value) {
      updateLoading(value);
    });
    _addDeviceController.setUpdateLoadingMessageCallback((value) {
      updateLoadingMessage(value);
    });
    _addDeviceController.setUpdateTransparentLoadingCallback((value) {
      updateTransparentLoading(value);
    });
    _addDeviceController.setIsLoadingCallback(() {
      return isLoading;
    });
    _addDeviceController.setIsTransparentLoadingCallback(() {
      return isTransparentLoading;
    });

    _tabs = _getTabs();
    _tabPages = _getTabPages();
    _tabController =  TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    log.i('当前选中的Tab索引：${_tabController.index}');
    if (_currentIndex == _tabController.index) {
      return;
    }

    UIDelegateBridge lastBridge = _tabPages[_currentIndex] as UIDelegateBridge;
    lastBridge
        .getUIDelegateManager()
        .delegateLifeCycle
        ?.onHide();

    UIDelegateBridge nextBridge = _tabPages[_tabController.index] as UIDelegateBridge;
    nextBridge
        .getUIDelegateManager()
        .delegateLifeCycle
        ?.onShow();

    _currentIndex = _tabController.index;
  }

  @override
  Widget buildBody(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        bool isShowPairingPw = _addDeviceController.isShowPairingProgressWidget.value;
        log.i('onPopInvoked()-> didPop: $didPop, isShowPairingPw: $isShowPairingPw');
        if(!didPop) {
          if (isShowPairingPw) {
            log.d('正在添加Device，不能强制退出...');
          } else {
            Get.back();
          }
        }
      },
      child: Stack(
        children: [
          _buildContent(),
          Obx(() {
            if (!_addDeviceController.isShowPairingProgressWidget.value) {
              return const SizedBox();
            }
            return _buildPairingProgressWidget();
          }),
        ]
      ),
    );
  }

  Widget _buildPairingProgressWidget() {
    return Material(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Container(
          width: double.infinity,
          height: 500.h,
          alignment: Alignment.center,
          margin: const EdgeInsets.symmetric(horizontal: 30),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.secondColor,
            borderRadius: const BorderRadius.all(
              Radius.circular(28.0),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'pairingInProgress'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.medium,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 20,),

              Text(
                'pleaseAllowUpTo15Minutes'.tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 20,),

              Obx(() => CircularStepProgressIndicator(
                totalSteps: 100,
                currentStep: _addDeviceController.pairingProgressStep.value,
                selectedStepSize: 8,
                stepSize: 2,
                selectedColor: AppColors.ff01A796,
                unselectedColor: Colors.grey,
                padding: 0,
                width: 135,
                height: 135,
                child: Center(
                  child: Text(
                    '${_addDeviceController.pairingProgressStep.value}%',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ff868788,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              )),

              const SizedBox(height: 20,),

              Obx(() => Text(
                _addDeviceController.pairingProgressStatus.value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                textAlign: TextAlign.center,
              )),
            ],
          )
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'addDevice'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 30.0, right: 30.0),
        child: Column(
          children: [
            const SizedBox(height: 20,),

            SizedBox(
              width: double.infinity,
              height: 48,
              child: TabBar(
                controller: _tabController,
                tabs: _tabs,
                padding: EdgeInsets.zero,
                labelPadding: EdgeInsets.zero,
                indicatorColor: AppColors.ff01A796,
                indicatorWeight: 2,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: AppColors.ff707070,
                dividerHeight: 2,
                labelStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()
                    !.firstColor
                ),
                unselectedLabelStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!
                        .firstColor
                        .withOpacity(0.5)
                ),

              ),
            ),

            Expanded(
                child: TabBarView(
                    controller: _tabController,
                    children: _tabPages
                )
            )
          ],
        ),
      ),
    );
  }

  List<Widget> _getTabs() {
    final List<Tab> tabs = [];

    final qrCodeText = 'qrCode'.tr;
    final networkScanText = 'networkScan'.tr;
    final pairingCodeText = 'pairingCode'.tr;

    tabs.add(Tab(text: qrCodeText));
    tabs.add(Tab(text: networkScanText));
    tabs.add(Tab(text: pairingCodeText));

    return tabs;
  }

  List<Widget> _getTabPages() {
    final List<Widget> pages = [];

    pages.add(AddDeviceQrCodePage(delegateManager: UIDelegateManager()));
    pages.add(AddDeviceNetworkScanPage(delegateManager: UIDelegateManager()));
    pages.add(AddDevicePairingCodePage(delegateManager: UIDelegateManager()));

    return pages;
  }


}



