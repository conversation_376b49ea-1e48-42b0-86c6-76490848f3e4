import 'package:get/get.dart';

class AddDeviceQrCodeController extends GetxController {

  final String thingName;

  AddDeviceQrCodeController({
    required this.thingName,
  });

  var cameraInit = false.obs;
  var clicked = false.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }


}
