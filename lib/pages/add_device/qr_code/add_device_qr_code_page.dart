import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/pages/add_device/add_device_controller.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_device/qr_code/add_device_qr_code_controller.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/widgets/matter_code_sticker_widget.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class AddDeviceQrCodePage extends StatefulWidget implements UIDelegateBridge {
  final UIDelegateManager delegateManager;

  const AddDeviceQrCodePage({super.key, required this.delegateManager});

  @override
  State<AddDeviceQrCodePage> createState() {
    return _AddDeviceQrCodePageState();
  }

  @override
  UIDelegateManager getUIDelegateManager() => delegateManager;
}

class _AddDeviceQrCodePageState extends State<AddDeviceQrCodePage>
    with AutomaticKeepAliveClientMixin implements UIDelegateLifeCycle {

  final GlobalKey _scanQRCodeKey = GlobalKey();
  late AddDeviceController addDeviceController;
  late AddDeviceQrCodeController qrCodeController;
  QRViewController? qrViewController;
  StreamSubscription? subscription;
  bool isShowSelectDeviceTypeDialog = false;
  StreamSubscription? eventSubscription;

  @override
  void initState() {
    super.initState();
    addDeviceController = Get.find<AddDeviceController>();
    qrCodeController = Get.find<AddDeviceQrCodeController>();
    widget.delegateManager.setUIDelegateLifecycle(this);

    GlobalService globalService = GlobalService.to;
    eventSubscription = globalService.getEventStream().listen((event) {
      log.i('AddDeviceQrCodePage{} -> event=$event');
      if (event.name == AppEvents.resumeCamera) {
        isShowSelectDeviceTypeDialog = false;
        qrViewController?.resumeCamera();
      }
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    qrViewController?.dispose();
    widget.delegateManager.setUIDelegateLifecycle(null);
    eventSubscription?.cancel();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onHide() {
    log.i('UIDelegate{} -> QrCodePage onHide');
    qrViewController?.pauseCamera();
  }

  @override
  void onShow() {
    log.i('UIDelegate{} -> QrCodePage onShow');
    isShowSelectDeviceTypeDialog = false;
    qrViewController?.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      width: 342.w,
      height: 534.h,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(50))
      ),
      child: Obx(() {
        if (!qrCodeController.clicked.value) {
          return buildMatterQRCodeStickerWidget();
        }
        return Stack(
          children: [
            buildScanQRCodeWidget(),
            buildPrepareWidget(),
          ],
        );
      }),
    );
  }

  Widget buildMatterQRCodeStickerWidget() {
    return MatterCodeStickerWidget(
      title: 'pairYourDevice'.tr,
      subTitle: 'matterQRCodeSticker'.tr,
      onClicked: () {
        qrCodeController.clicked.value = true;
      },
    );
  }

  Widget buildPrepareWidget() {
    return Obx(() {
      if (qrCodeController.cameraInit.value) {
        return Container();
      }
      return Container(
        alignment: Alignment.center,
        decoration: const BoxDecoration(
            color: AppColors.ffE7F9F7,
            borderRadius: BorderRadius.all(Radius.circular(50))
        ),
        margin: const EdgeInsets.only(top: 60, bottom: 60),
        child: SvgPicture.asset(
          AppImagePaths.camera,
        ),
      );
    });
  }

  Widget buildScanQRCodeWidget() {
    return Padding(
      padding: const EdgeInsets.only(top: 60, bottom: 60),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(50.0),
        child: QRView(
            key: _scanQRCodeKey,
            // overlay: QrScannerOverlayShape(
            //     borderColor: widget.borderColor,
            //     borderRadius: 50,
            //     borderLength: 1,
            //     borderWidth: 1,
            //     cutOutWidth: widget.width ?? 260,
            //     cutOutHeight: widget.height ?? 260
            // ),
            onQRViewCreated: _onQRViewCreated,
        ),
      ),
    );
  }

  void _onQRViewCreated(controller) {
    qrViewController = controller;
    subscription = qrViewController?.scannedDataStream.listen((data) {
      String qrcode = data.code ?? '';
      log.i('qrcode=$qrcode');
      if (qrcode.isEmpty) {
        return;
      }
      if (!isShowSelectDeviceTypeDialog) {
        isShowSelectDeviceTypeDialog = true;
        qrViewController?.pauseCamera();
        addDeviceController.requestPermission(qrcode);
      }
    });
    Future.delayed(const Duration(milliseconds: 500), () {
      qrCodeController.cameraInit.value = true;
    });
  }


}