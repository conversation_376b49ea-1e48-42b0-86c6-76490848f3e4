import 'dart:async';
import 'dart:convert';
import 'package:aws_iot_data_api/iot-data-2015-05-28.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_loading_callback_controller.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_status_code.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/hex_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/models/matter/setup_payload.dart';
import 'package:habi_app/models/matter/support_device.dart';
import 'package:habi_app/pages/device_type/thermostat/add_thermostat_settings/add_thermostat_settings_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/add_thermostat_settings/add_thermostat_settings_page.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/utility/sentry_utils.dart';
import 'package:habi_app/widgets/add_device_failed_widget.dart';
import 'package:habi_app/widgets/add_device_succeeded_widget.dart';
import 'package:habi_app/widgets/enter_ssid_and_password_widget.dart';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:habi_app/widgets/select_device_type_widget.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class AddDeviceController extends BaseLoadingCallbackController
    implements OnIssueOperationalCertificateCallback {
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;
  final globalService = GlobalService.to;

  final String thingName;
  late String mcThingName;

  var isShowPairingProgressWidget = false.obs;
  var pairingProgressStep = 0.obs;
  var pairingProgressStatus = 'checking...'.obs;

  Sait85rMC? sait85rMC;
  bool _interrupt = false;
  Map<Object?, Object?>? _commissioningInfo;
  ISentrySpan? _commissioningSpan;
  ISentrySpan? _nodeNocSpan;
  ISentrySpan? _caseSpan;
  DateTime? _nodeNocStartTime;

  AddDeviceController({
    required this.thingName,
  }) {
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
    log.i('mcThingName: $mcThingName');
  }

  @override
  void onInit() {
    super.onInit();
    CtFlutterMatterPlugin.getInstance()
        .setOnDeviceAttestationCallback((attestationInfo, errorCode) {
          log.i('onDeviceAttestationCallback() -> attestationInfo: $attestationInfo，errorCode: $errorCode');
          updatePairingProgress(20);
          if (errorCode != 0) {
            showUncertifiedDeviceDialog();
          }
    });

    CtFlutterMatterPlugin.getInstance()
        .setOnIssueOperationalCertificateCallback(this);

    CtFlutterMatterPlugin.getInstance()
        .setOnReadCommissioningInfoCallback((commissioningInfo) {
      log.i('OnReadCommissioningInfoCallback() -> commissioningInfo: $commissioningInfo');
      _commissioningInfo = commissioningInfo;
    });
  }

  @override
  void onClose() {
    closeBle();
    _interrupt = true;
    _commissioningInfo = null;
    super.onClose();
  }

  void showPairingProgressWidget() {
    isShowPairingProgressWidget.value = true;
  }

  void hidePairingProgressWidget() {
    isShowPairingProgressWidget.value = false;
  }

  void updatePairingProgressStatus(String status) {
    pairingProgressStatus.value = status;
  }

  void updatePairingProgress(int progress) {
    if (progress < 0) progress = 0;
    if (progress > 100) progress = 100;
    pairingProgressStep.value = progress;
  }

  Future<void> closeBle() async {
    String fabricId = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";
    if (fabricId.isEmpty) {
      log.e('closeBle() -> fabricId is empty.');
      return;
    }

    try {
      await CtFlutterMatterPlugin.getInstance()
          .close(fabricId: fabricId);
      log.i('closeBle() -> success');
    } catch (e, r) {
      log.e('closeBle() -> failed，e=$e, r=$r');
    }
  }

  //申请权限
  Future<void> requestPermission(String code) async {
    if (PlatformUtils.isAndroid) {
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.location
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetoothScan] == PermissionStatus.granted
          && result[Permission.bluetoothConnect] == PermissionStatus.granted
          && result[Permission.location] == PermissionStatus.granted
      ) {
        addMatterDevice(code);
      } else {
        log.i('_requestPermission() -> 权限不足');
        openAppSettings();
      }
    } else if (PlatformUtils.isIOS){
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetooth,
        Permission.locationWhenInUse,
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetooth] == PermissionStatus.granted
          && result[Permission.locationWhenInUse] == PermissionStatus.granted
      ) {
        addMatterDevice(code);
      } else {
        log.i('_requestPermission() -> 权限不足');
        openAppSettings();
      }
    } else {
      showSnackBar('Platform not supported');
    }

  }

  Future<bool> _validateBluetooth() async {
    BluetoothAdapterState adapterState;
    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('requestPermission() -> 获取蓝牙状态=$adapterState');
    } catch (e, r) {
      log.e('requestPermission() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      showSnackBar('pleaseTurnOnBluetooth'.tr);
      hidePairingProgressWidget();
      return false;
    }

    return true;
  }

  Future<bool> _isConnectedToWifi() async {
    final Connectivity connectivity = Connectivity();
    final List<ConnectivityResult> connectivityResult = await connectivity.checkConnectivity();
    if (!connectivityResult.contains(ConnectivityResult.wifi)) {
      hidePairingProgressWidget();
      showErrorDialog('pleaseConnectToAWiFiNetwork'.tr, AppStatusCode.notConnectedToWiFi);
      return false;
    }
    return true;
  }

  Future<bool> _validateWiFi() async {
    String wifiName = await getWifiName();
    String wifiSsid = await getWiFiSsid();

    log.i('_validateWiFi() -> wifiName=$wifiName, wifiSsid=$wifiSsid');

    if (wifiName.isNotEmpty && wifiSsid.isNotEmpty) {
      if (wifiName == wifiSsid) {
        return true;
      }
    }

    hidePairingProgressWidget();
    showErrorDialog('wifiMismatchError'.trParams({
      'phoneWiFi': wifiName,
      'receiverWiFi': wifiSsid,
    }), AppStatusCode.wifiMismatchError);

    return false;
  }

  Future<SetupPayload?> _parseCode(String code) async {
    // Parse QR code or manual pairing code
    try {
      Map<Object?, Object?> map;
      if (code.startsWith("MT:")) {
        map = await CtFlutterMatterPlugin.getInstance()
            .parseQrCode(code);
      } else {
        map = await CtFlutterMatterPlugin.getInstance()
            .parseManualPairingCode(code);
      }
      SetupPayload setupPayload = SetupPayload.fromNativeMap(map);
      log.i('_validateCode() -> Successfully parsed code, setupPayload=$setupPayload');
      return setupPayload;
    } catch (e, r) {
      log.e('_validateCode() -> Failed to parse code: $e, $r');
      hidePairingProgressWidget();
      showErrorDialog('Failed to parse code.', AppStatusCode.parsingQrcodeError);
    }
    return null;
  }

  Future<String> getWiFiSsid() async {
    AppShadow appShadow = deviceShadowService.getDeviceShadow(thingName);
    Sait85rGW sait85rGW = Sait85rGW.fromJson(appShadow.thingShadow, thingName);
    String networkSSID = sait85rGW.shadow?.state?.reported?.model
        ?.properties?.sGateway?.networkSSID ?? '';
    log.i('getWiFiSsid() -> networkSSID=$networkSSID');
    if (networkSSID.isNotEmpty) {
      return networkSSID;
    }
    return '';
  }

  Future<String> getWifiName() async {
    String wifiName = '';

    try {
      NetworkInfo networkInfo = NetworkInfo();
      wifiName = await networkInfo.getWifiName() ?? '';
      log.i('getWifiName() -> 获取wifi名称成功：$wifiName');
    } catch (e, r) {
      log.e('getWifiName() -> 获取wifi名称失败，e=$e, r=$r');
    }

    if (wifiName.isEmpty) {
      log.w('getWifiName() -> wifi名称为空');
      return '';
    }

    String newWifiName = '';
    if (PlatformUtils.isAndroid) {
      // 使用正则表达式去除可能的多余字符
      // final match = RegExp(r'^"(.*?)"$').firstMatch(wifiName);
      // newWifiName = match?.group(1) ?? wifiName;
      newWifiName = wifiName.substring(1, wifiName.length - 1);
    } else {
      newWifiName = wifiName;
    }

    log.i('getWifiName() -> newWifiName：$newWifiName');

    return newWifiName;
  }

  Future<bool> validateAddMatterDeviceConditions(ISentrySpan? transaction) async {
    updatePairingProgressStatus('fetch device shadow...');
    Map<String, dynamic>? thingShadow = await _fetchDeviceShadow();
    if (thingShadow == null) {
      log.e('validateAddMatterDeviceConditions() -> device shadow为空');
      hidePairingProgressWidget();
      showErrorDialog('Unable to get device shadow.', AppStatusCode.unableToGetThingShadow);
      return false;
    }

    try {
      sait85rMC = Sait85rMC.fromJson(thingShadow, mcThingName);
    } catch (e, r) {
      log.e('validateAddMatterDeviceConditions() -> 解析设备shadow失败，e=$e, r=$r');
    }

    if (sait85rMC == null) {
      log.e('validateAddMatterDeviceConditions() -> 解析设备shadow失败');
      hidePairingProgressWidget();
      showErrorDialog('Unable to parse device shadow.', AppStatusCode.unableToParseThingShadow);
      return false;
    }

    String connected = sait85rMC?.shadow?.state?.reported?.connected ?? 'false';
    if (connected == 'false') {
      log.e('validateAddMatterDeviceConditions() -> controller不在线');
      hidePairingProgressWidget();
      showErrorDialog('Receiver is offline.', AppStatusCode.receiverIsOffline);
      return false;
    }

    String fabricId = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";
    if (fabricId.isEmpty) {
      log.e('validateAddMatterDeviceConditions() -> fabricId为空');
      hidePairingProgressWidget();
      showErrorDialog('fabricId cannot be empty', AppStatusCode.fabricIdCannotBeEmptyError);
      return false;
    }

    bool hasMatterClient = await CtFlutterMatterPlugin.getInstance().hasMatterClient(fabricId);
    if (!hasMatterClient) {
      log.i('validateAddMatterDeviceConditions() -> 检测到matter client没创建...');
      updatePairingProgressStatus('create matter client...');

      await _createMatterClient(fabricId, transaction);

      hasMatterClient = await CtFlutterMatterPlugin.getInstance().hasMatterClient(fabricId);
      if (!hasMatterClient) {
        hidePairingProgressWidget();
        showErrorDialog('Unable to create matter client.', AppStatusCode.unableToCreateMatterClientError);
        return false;
      }
    }

    return true;
  }

  Future<void> addMatterDevice(String code) async {
    if (isShowPairingProgressWidget.value) {
      log.e('addMatterDevice() -> 重复点击添加设备');
      return;
    }

    updatePairingProgress(0);
    showPairingProgressWidget();
    updatePairingProgressStatus('checking...');

    if(!await _validateBluetooth()){
      return;
    }

    if (!await _isConnectedToWifi()) {
      return;
    }

    if (!await _validateWiFi()) {
      return;
    }

    SetupPayload? setupPayload = await _parseCode(code);
    if (setupPayload == null) {
      return;
    }

    SupportDevice supportDevice = globalService.config.supportDevices.firstWhere(
          (item) => item.vid == setupPayload.vendorId
          && item.pid == setupPayload.productId,
      orElse: () => SupportDevice.unknown(),
    );

    if (supportDevice.capacity == SupportDevice.capacityWiFi) {
      await _handleAddWiFiDevice(code);
    } else if (supportDevice.capacity == SupportDevice.capacityThread) {
      await _handleAddThreadDevice(code);
    } else {
      showSelectDeviceTypeDialog(code);
    }
  }

  Future<void> _handleAddWiFiDevice(String code) async {
    if (PlatformUtils.isIOS) {
      await _executeAddWiFiDevice(code, '', '');
      return;
    }

    Map<String, dynamic> allWifiMap = await localStorageService
        .getWiFiMap() ?? <String, dynamic>{};
    Map<String, dynamic> thatWiFiMap = allWifiMap[thingName]
        ?? <String, dynamic>{};
    log.i('addMatterDevice() -> allWifiMap: $allWifiMap');

    // String ssid = thatWiFiMap['ssid'] ?? await getWiFiSsid();
    String ssid = await getWiFiSsid();
    String pwd = thatWiFiMap['password'] ?? '';

    showSsidAndPasswordDialog(code, ssid, pwd);
  }

  Future<void> _handleAddThreadDevice(String code) async {
    ISentrySpan? transaction;
    try {
      transaction = SentryUtils.startTransaction(
        'addMatterOverThreadDevice()',
        'task',
        bindToScope: true,
      );

      await addMatterOverThreadDevice(code, transaction)
          .timeout(const Duration(minutes: 5));
    } catch (e, s) {
      log.e('addMatterOverThreadDevice() -> e=$e, s=$s');

      transaction?.throwable = e;
      transaction?.status = const SpanStatus.unknownError();

      SentryUtils.captureMessage('addDevice() -> failed, type=thread, errorMessage=$e', level: SentryLevel.error);

      hidePairingProgressWidget();
      int errorCode = -1;
      if (e is TimeoutException) {
        showErrorDialog('commissioningPeriodTimeoutDisconnectReconnectWifiRetry'.tr, AppStatusCode.timeoutError);
      } else {
        showErrorDialog('Unknown cause', errorCode);
      }

      _interrupt = true;

    } finally {
      await transaction?.finish();
    }
  }

  Future<void> addMatterOverWiFiDevice(
      String code,
      String ssid,
      String pwd,
      ISentrySpan? transaction,
      bool saveSsidAndPassword
  ) async {
    log.i('addMatterOverWiFiDevice() -> ssid=$ssid, pwd=$pwd');

    if (!await validateAddMatterDeviceConditions(transaction)) {
      return;
    }

    updatePairingProgress(10);

    String fabricId = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";

    updatePairingProgressStatus('commissioning...');
    String nodeId;
    int errorCode = -1;
    final startTime = DateTime.now();
    ISentrySpan? span;

    try {
      span = SentryUtils.startChild(transaction, 'commissioning()');
      _commissioningSpan = span;

      Map<Object?, Object?> addDeviceResult = await CtFlutterMatterPlugin.getInstance()
          .addWiFiDevice(fabricId, code, ssid, pwd);

      nodeId = addDeviceResult["nodeId"] as String;
      errorCode = addDeviceResult["errorCode"] as int;

      if (errorCode != 0 || nodeId.isEmpty) {
        span?.throwable = Exception('commissioning failed, errorCode=$errorCode');
        span?.status = const SpanStatus.unknownError();
        log.e('addMatterOverWiFiDevice() -> commissioning failed, errorCode=$errorCode');
        SentryUtils.captureMessage('addDevice() -> commissioning() -> failed, type=wifi, errorCode=$errorCode', level: SentryLevel.error);

        hidePairingProgressWidget();
        showErrorDialog(getErrorMassage(errorCode), errorCode);
        return;
      }

      await localStorageService.setNodeId(nodeId);

      span?.status = const SpanStatus.ok();
      log.i('addMatterOverWiFiDevice() -> commissioning success, nodeId=$nodeId');

      if (saveSsidAndPassword) {
        log.i('addMatterOverWiFiDevice() -> 保存ssid和密码');
        Map<String, dynamic> allWifiMap = await localStorageService
            .getWiFiMap() ?? <String, dynamic>{};
        allWifiMap[thingName] = <String, dynamic>{
          'ssid': ssid,
          'password': pwd,
        };
        await localStorageService.setWiFiMap(allWifiMap);
      }
    } catch (e, r) {
      span?.throwable = e;
      span?.status = const SpanStatus.unknownError();
      log.e('addMatterOverWiFiDevice() -> commissioning failed: $e, $r');
      SentryUtils.captureMessage('addDevice() -> commissioning() -> failed, type=wifi, errorMessage=$e', level: SentryLevel.error);

      hidePairingProgressWidget();

      if (e is TimeoutException) {
        showErrorDialog('commissioningPeriodTimeoutDisconnectReconnectWifiRetry'.tr, AppStatusCode.timeoutError);
      } else if (e is PlatformException) {
        try {
          errorCode = int.parse(e.code);
        } catch (e) {
          log.e('addMatterOverWiFiDevice() -> parse errorCode failed: $e');
        }
        showErrorDialog(e.message ?? getErrorMassage(errorCode), errorCode);
      } else {
        showErrorDialog('Unknown cause', errorCode);
      }

      return;
    } finally {
      await _caseSpan?.finish();
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('addMatterOverWiFiDevice() -> The total time taken for commissioning is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }

    if (GlobalService.to.config.factoryParingMode == false) {
      await handleAddDeviceEnd(fabricId, nodeId, transaction);
    } else {
      await handleFactoryPairingAddDeviceEnd(fabricId, nodeId, transaction);
    }
  }

  Future<void> addMatterOverThreadDevice(String code, ISentrySpan? transaction) async {
    if (!await validateAddMatterDeviceConditions(transaction)) {
      return;
    }

    updatePairingProgress(10);

    String fabricId = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";

    String thdDataset = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.thdDataset ?? "";
    if (thdDataset.isEmpty) {
      log.e('addMatterOverThreadDevice() -> dataset为空');
      hidePairingProgressWidget();
      showErrorDialog('Dataset cannot be empty', AppStatusCode.datasetCannotBeEmptyError);
      return;
    }

    String thdBaId = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.thdBaId ?? "";
    String thdExtendedAddress = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.thdExtendedAddress ?? "";

    if (PlatformUtils.isIOS) {
      if (thdExtendedAddress.isEmpty) {
        log.e('addMatterOverThreadDevice() -> extendedAddress为空');
        hidePairingProgressWidget();
        showErrorDialog('Extended address cannot be empty', AppStatusCode.extendedAddressCannotBeEmptyError);
        return;
      }
      updatePairingProgressStatus('saving iOS thread credentials...');
      await _saveIOSThreadCredentials(thdExtendedAddress, thdDataset, transaction);
    }

    String newDataset;

    try {
      newDataset = base64Encode(HexHelper.hexToBytes(thdDataset));
    } catch (e, r) {
      log.e('addMatterOverThreadDevice() -> 解析dataset失败: $e, $r');
      hidePairingProgressWidget();
      showErrorDialog('Parsing dataset failed', AppStatusCode.parsingDatasetFailedError);
      return;
    }

    if (newDataset.isEmpty) {
      log.e('addMatterOverThreadDevice() -> newDataset为空');
      hidePairingProgressWidget();
      showErrorDialog('Parsing dataset failed', AppStatusCode.parsingDatasetFailedError);
      return;
    }

    log.i('addMatterOverThreadDevice() -> newDataset=$newDataset');

    updatePairingProgressStatus('commissioning...');
    String nodeId;
    int errorCode = -1;
    final startTime = DateTime.now();
    ISentrySpan? span;

    try {
      span = SentryUtils.startChild(transaction, 'commissioning()');
      _commissioningSpan = span;

      String agentId = PlatformUtils.isAndroid ? thdBaId : thdExtendedAddress;

      Map<Object?, Object?> addDeviceResult = await CtFlutterMatterPlugin.getInstance()
          .addThreadDeviceWithTBRDataset(fabricId, code, agentId, newDataset);

      nodeId = addDeviceResult["nodeId"] as String;
      errorCode = addDeviceResult["errorCode"] as int;

      if (PlatformUtils.isIOS) {
        try {
          Map<Object?, Object?> stepMap = addDeviceResult["stepDict"] as Map<Object?, Object?>;
          log.i('addMatterOverThreadDevice() -> stepMap=$stepMap');
        } catch (e, r) {
          log.e('addMatterOverThreadDevice() -> parse stepDict failed: $e, $r');
        }
      }

      if (errorCode != 0 || nodeId.isEmpty) {
        span?.throwable = Exception('commissioning failed, errorCode=$errorCode');
        span?.status = const SpanStatus.unknownError();
        log.e('addMatterOverThreadDevice() -> commissioning failed, errorCode=$errorCode');
        SentryUtils.captureMessage('addDevice() -> commissioning() -> failed, type=thread, errorCode=$errorCode', level: SentryLevel.error);

        hidePairingProgressWidget();
        showErrorDialog(getErrorMassage(errorCode), errorCode);
        return;
      }

      await localStorageService.setNodeId(nodeId);

      span?.status = const SpanStatus.ok();
      log.i('addMatterOverThreadDevice() -> commissioning success, nodeId=$nodeId');
    } catch (e, r) {
      span?.throwable = e;
      span?.status = const SpanStatus.unknownError();
      log.e('addMatterOverThreadDevice() -> commissioning failed: $e, $r');
      SentryUtils.captureMessage('addDevice() -> commissioning() -> failed, type=thread, errorMessage=$e', level: SentryLevel.error);

      hidePairingProgressWidget();

      if (e is TimeoutException) {
        showErrorDialog('commissioningPeriodTimeoutDisconnectReconnectWifiRetry'.tr, AppStatusCode.timeoutError);
      } else if (e is PlatformException) {
        try {
          errorCode = int.parse(e.code);
        } catch (e) {
          log.e('addMatterOverThreadDevice() -> parse errorCode failed: $e');
        }

        showErrorDialog(e.message ?? getErrorMassage(errorCode), errorCode);
      } else {
        showErrorDialog('Unknown cause', errorCode);
      }

      return;
    } finally {
      await _caseSpan?.finish();
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('addMatterOverThreadDevice() -> The total time taken for commissioning is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }

    if (GlobalService.to.config.factoryParingMode == false) {
      await handleAddDeviceEnd(fabricId, nodeId, transaction);
    } else {
      await handleFactoryPairingAddDeviceEnd(fabricId, nodeId, transaction);
    }
  }

  Future<Map<String, dynamic>?> _fetchDeviceShadow() async {
    AppShadow appShadow = deviceShadowService.getDeviceShadow(mcThingName);
    if (appShadow.thingShadow.isEmpty) {
      log.i('fetchDeviceShadow() -> 本地device shadow为空，需要从服务器获取');
      try {
        log.i('fetchDeviceShadow() -> 开始从服务器获取device shadow');
        final thingShadow = await deviceShadowService.fetchDeviceShadow(mcThingName)
            .timeout(const Duration(seconds: 60));
        return thingShadow;
      } catch (e, r) {
        log.e('fetchDeviceShadow() -> 无法从服务器获取device shadow：$e, $r');
        SentryUtils.captureMessage('addDevice() -> fetchDeviceShadow() -> failed, errorMessage=$e', level: SentryLevel.error);
        return null;
      }
    } else {
      log.i('fetchDeviceShadow() -> 本地device shadow不为空，直接使用');
      return appShadow.thingShadow;
    }
  }

  Future<bool> _saveIOSThreadCredentials(String thdExtendedAddress, String thdDataset, ISentrySpan? transaction) async {
    ISentrySpan? span;
    final startTime = DateTime.now();

    try {
      span = SentryUtils.startChild(transaction, 'saveThreadCredentials()');
      await CtFlutterMatterPlugin.getInstance()
          .saveThreadOperationalCredentials(thdExtendedAddress, thdDataset)
          .timeout(const Duration(seconds: 30));
      log.i('saveThreadCredentials() -> 保存ThreadOperationalCredentials成功!');
      span?.status = const SpanStatus.ok();
      return true;
    } catch (e, r) {
      log.e('saveThreadCredentials() -> 保存ThreadOperationalCredentials失败: $e, $r');
      SentryUtils.captureMessage('addDevice() -> saveThreadCredentials() -> failed, errorMessage=$e', level: SentryLevel.error);
      span?.throwable = e;
      span?.status = const SpanStatus.unknownError();
      return false;
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('saveThreadCredentials() -> The total time taken to save thread-operational-credentials is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }
  }

  Future<bool> _createMatterClient(String fabricId, ISentrySpan? transaction) async {
    ISentrySpan? span;
    final startTime = DateTime.now();

    try {
      log.i('_createMatterClient() -> 开始创建matter client');
      span = SentryUtils.startChild(transaction, 'createMatterClient()');
      await FabricHelper.checkFabric(sait85rMC!, mcThingName);
      span?.status = const SpanStatus.ok();
    } catch (e, r) {
      log.e('_createMatterClient() -> 尝试创建matter client失败: $e, $r');
      span?.throwable = e;
      span?.status = const SpanStatus.unknownError();
      SentryUtils.captureMessage('addDevice() -> createMatterClient() -> failed, errorMessage=$e', level: SentryLevel.error);
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('_createMatterClient() -> The total time taken to create the matter client is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }

    return true;
  }

  Future<void> handleAddDeviceEnd(String fabricId, String nodeId, ISentrySpan? transaction) async {
    updatePairingProgress(75);
    updatePairingProgressStatus('writing cat value...');
    bool writeCatValueFlag = await writeCatValue(fabricId, nodeId, transaction);
    if (!writeCatValueFlag) {
      hidePairingProgressWidget();
      showErrorDialog('Failed to write cat value.', AppStatusCode.writeCatValueError);
      return;
    }

    updatePairingProgressStatus('publishing node id to cloud...');
    bool publishNodeIdToCloudFlag = await publishNodeIdToCloud(nodeId, transaction);
    if (!publishNodeIdToCloudFlag) {
      hidePairingProgressWidget();
      showErrorDialog('Failed to publish nodeId to cloud.', AppStatusCode.publishNodeIdToCloudError);
      return;
    }

    updatePairingProgressStatus('read vid..');
    int vendorId = await readVid(fabricId, nodeId, transaction);
    if (vendorId == -1) {
      hidePairingProgressWidget();
      showErrorDialog('Failed to read vid.', AppStatusCode.readVidError);
      return;
    }

    updatePairingProgressStatus('read pid...');
    int productId = await readPid(fabricId, nodeId, transaction);
    if (productId == -1) {
      hidePairingProgressWidget();
      showErrorDialog('Failed to read pid.', AppStatusCode.readPidError);
      return;
    }

    updatePairingProgressStatus('unregister icd client...');
    await unregisterICDClient(fabricId, nodeId, transaction);

    updatePairingProgressStatus('generate thing name...');
    String? newThingName = await generateThingName(fabricId, nodeId, vendorId, productId, transaction);
    if (newThingName == null || newThingName.isEmpty) {
      hidePairingProgressWidget();
      showErrorDialog('Failed to generate thing-name.', AppStatusCode.failedToGenerateThingName);
      return;
    }

    updatePairingProgress(90);
    updatePairingProgressStatus('checking device shadow...');
    var (errorCode, errorMessage) = await checkDeviceShadow(fabricId, nodeId, newThingName, transaction);
    if (errorCode != 0) {
      hidePairingProgressWidget();
      if (errorCode == -1) {
        showDetailsErrorDialog('Api request failed.', AppStatusCode.deviceShadowIsNotPresent, errorMessage);
      } else if (errorCode == -2) {
        showErrorDialog(errorMessage, AppStatusCode.deviceShadowIsNotPresent);
      } else if (errorCode == -3) {
        showErrorDialog(errorMessage, AppStatusCode.nodeIdVerificationDidNotPass);
      }
      return;
    }

    updatePairingProgress(100);

    hidePairingProgressWidget();

    showSuccessDialog(newThingName);
  }

  Future<(int, String)> checkDeviceShadow(String fabricId, String nodeId, String newThingName, ISentrySpan? transaction) async {
    bool isThingExist = false;
    bool isNodeIdExist = false;
    String? checkingShadowErrorMessage;
    final startTimeCheckShadow = DateTime.now();
    ISentrySpan? span = SentryUtils.startChild(transaction, 'checkingShadow()');

    try {
      for (int i = 0; i < 60; i++) {
        if (_interrupt) {
          log.i('checkDeviceShadow() -> 终止...');
          break;
        }

        try {
          Map<String, dynamic> thingShadow = await deviceShadowService.fetchDeviceShadow(newThingName)
              .timeout(const Duration(seconds: 60));
          isThingExist = true;

          IT850 it850 = IT850.fromJson(thingShadow, newThingName);
          String thatNodeId = it850.shadow?.state?.reported?.model?.properties?.sMdo?.nodeID ?? "";
          log.i('checkDeviceShadow() -> shadow上的nodeId: $thatNodeId');

          if (thatNodeId == nodeId) {
            log.i('checkDeviceShadow() -> 检测到nodeId相同...');
            isNodeIdExist = true;
            span?.status = const SpanStatus.ok();
            break;
          } else {
            log.i('checkDeviceShadow() -> nodeId不同，需要继续检测...');
          }
        } catch (e, r) {
          if (e is ResourceNotFoundException) {
            log.e('checkDeviceShadow() -> 服务器上暂时没有这个thing...');
          } else {
            log.e('checkDeviceShadow() -> 获取shadow失败: $e, $r');
            checkingShadowErrorMessage = e.toString();
          }
        }
        await Future.delayed(const Duration(seconds: 5));
      }

      if (_interrupt) {
        return (-4, '终止checkDeviceShadow.');
      }

      if (!isThingExist) {
        log.e('checkDeviceShadow() -> 检测到device shadow不存在');
        if (checkingShadowErrorMessage != null) {
          SentryUtils.captureMessage('addDevice() -> '
              'checkDeviceShadow() -> failed, errorMessage=$checkingShadowErrorMessage', level: SentryLevel.error);
          span?.throwable = Exception(checkingShadowErrorMessage);
          span?.status = const SpanStatus.unknownError();
          return (-1, checkingShadowErrorMessage);
        } else {
          String errorMessage = 'Device shadow is not present.';
          SentryUtils.captureMessage('addDevice() -> '
              'checkDeviceShadow() -> failed, errorMessage=$errorMessage', level: SentryLevel.error);
          span?.throwable = Exception(errorMessage);
          span?.status = const SpanStatus.unknownError();
          return (-2, errorMessage);
        }
      }

      if (!isNodeIdExist) {
        log.e('checkDeviceShadow() -> 检测到存在device shadow，但是nodeId不同');
        String errorMessage = 'Node ID verification did not pass.';
        SentryUtils.captureMessage('addDevice() -> '
            'checkDeviceShadow() -> failed, errorMessage=$errorMessage', level: SentryLevel.error);
        span?.throwable = Exception(errorMessage);
        span?.status = const SpanStatus.unknownError();
        return (-3, errorMessage);
      }

      return (0, '');

    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTimeCheckShadow);
      log.i('checkDeviceShadow() -> The total time taken to check the device shadow is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }
  }

  Future<String?> generateThingName(String fabricId, String nodeId, int vendorId, int productId, ISentrySpan? transaction) async {
    const int maxRetries = 3;
    const int retryDelay = 2;
    final startTimeMacAddress = DateTime.now();

    ISentrySpan? span = SentryUtils.startChild(transaction, 'generateThingName()');

    try {
      for (int i = 0; i < maxRetries; i++) {
        try {
          log.i('generateThingName() -> getting mac-address, try=${i + 1}');
          List<Object?> networkInterfaces = await CtFlutterMatterPlugin.getInstance()
              .getNetworkInterfaces(fabricId, nodeId);

          if (networkInterfaces.isNotEmpty) {
            Map<Object?, Object?> map = networkInterfaces.first as Map<Object?, Object?>;
            List<int> bytes = map["hardwareAddress"] as List<int>;
            log.i('generateThingName() -> address字节列表:$bytes');

            String hexString = bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0'))
                .join()
                .padLeft(16, '0')
                .toUpperCase();

            log.i('generateThingName() -> address十六进制:$hexString');

            String newThingName = Sait85rHelper.formatThingName(thingName, vendorId, productId, hexString);
            log.i('generateThingName() -> newThingName:$newThingName');

            span?.status = const SpanStatus.ok();
            return newThingName;
          }
        } catch (e, r) {
          log.e('generateThingName() -> Failed to get mac-address: $e, $r');
          if (i < maxRetries - 1) {
            await Future.delayed(const Duration(seconds: retryDelay));
          } else {
            SentryUtils.captureMessage('addDevice() -> '
                'generateThingName() -> failed, errorMessage=$e', level: SentryLevel.error);
            span?.throwable = e;
            span?.status = const SpanStatus.unknownError();
          }
        }
      }
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTimeMacAddress);
      log.i('generateThingName() -> Total time taken to get generate thing-name: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }

    return null;
  }

  Future<bool> unregisterICDClient(String fabricId, String nodeId, ISentrySpan? transaction) async {
    final startTimeUnregister = DateTime.now();
    ISentrySpan? span = SentryUtils.startChild(transaction, 'unregisterICDClient()');

    try {
      var registeredClients = await CtFlutterMatterPlugin.getInstance()
          .readRegisteredClients(fabricId, nodeId);
      if (registeredClients.isEmpty) {
        log.i('unregisterICDClient() -> No registered icd-clients.');
        return false;
      }
      log.i('unregisterICDClient() -> Successfully read registered icd-clients.');

      Map<Object?, Object?> map = registeredClients.first as Map<Object?, Object?>;
      int checkInNodeID = map["checkInNodeID"] as int;
      log.i('unregisterICDClient() -> checkInNodeID=$checkInNodeID');

      BigInt bigInt = BigInt.from(checkInNodeID);
      if (bigInt.isNegative) {
        bigInt = bigInt + (BigInt.one << 64);
      }

      String unregisterNodeId = bigInt.toRadixString(16).toUpperCase();
      log.i('unregisterICDClient() -> unregisterNodeId=$unregisterNodeId');

      try {
        await CtFlutterMatterPlugin.getInstance()
            .unregisterICDClient(fabricId, nodeId, unregisterNodeId);
        log.i('unregisterICDClient() -> Successfully deregistered icd-client.');
        span?.status = const SpanStatus.ok();
        return true;
      } catch (e, r) {
        log.e('unregisterICDClient() -> Failed to deregister icd-client: $e, $r');
      }

    } catch(e, r) {
      log.e('unregisterICDClient() -> Failed to read registered icd-clients: $e, $r');
      SentryUtils.captureMessage('addDevice() -> '
          'unregisterICDClient() -> failed', level: SentryLevel.error);
      span?.throwable = e;
      span?.status = const SpanStatus.unknownError();
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTimeUnregister);
      log.i('unregisterICDClient() -> The total time taken to read registered icd-clients is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }

    return false;
  }

  Future<int> readPid(String fabricId, String nodeId, ISentrySpan? transaction) async {
    int productId = -1;
    if (_commissioningInfo != null && _commissioningInfo!.containsKey('productId')) {
      productId = _commissioningInfo!['productId'] as int;
      log.i('readPid() -> Using productId from commissioningInfo.');
      return productId;
    }

    const int maxRetries = 3;
    const int retryDelay = 2;
    final startTimeReadPid = DateTime.now();
    ISentrySpan? span = SentryUtils.startChild(transaction, 'readPid()');

    try {
      for (int i = 0; i < maxRetries; i++) {
        try {
          log.i('readPid() -> read pid, try=$i');
          var result = await CtFlutterMatterPlugin.getInstance()
              .readAttribute(fabricId, nodeId, 0, 40, 4)
              .timeout(const Duration(seconds: 30));
          log.i('readPid() -> Successfully read pid.');

          productId = result["ProductID"] as int;
          log.i('readPid() -> productID=$productId');

          span?.status = const SpanStatus.ok();
          return productId;
        } catch (e, r) {
          log.e('readPid() -> Failed to read pid: $e, $r');
          if (i < maxRetries - 1) {
            await Future.delayed(const Duration(seconds: retryDelay));
          } else {
            SentryUtils.captureMessage('addDevice() -> '
                'readPid() -> failed, errorMessage=$e', level: SentryLevel.error);
            span?.throwable = e;
            span?.status = const SpanStatus.unknownError();
          }
        }
      }

      return productId;
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTimeReadPid);
      log.i('readPid() -> The total time taken to read pid is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }
  }


  Future<int> readVid(String fabricId, String nodeId, ISentrySpan? transaction) async {
    int vendorId = -1;
    if (_commissioningInfo != null && _commissioningInfo!.containsKey('vendorId')) {
      vendorId = _commissioningInfo!['vendorId'] as int;
      log.i('readVid() -> Using vendorId from commissioningInfo.');
      return vendorId;
    }

    const int maxRetries = 3;
    const int retryDelay = 2;
    final startTimeReadPid = DateTime.now();
    ISentrySpan? span = SentryUtils.startChild(transaction, 'readVid()');

    try {
      for (int i = 0; i < maxRetries; i++) {
        try {
          log.i('readVid() -> read vid, try=$i');
          var result = await CtFlutterMatterPlugin.getInstance()
              .readAttribute(fabricId, nodeId, 0, 40, 2)
              .timeout(const Duration(seconds: 30));

          log.i('readVid() -> Successfully read vid.');
          vendorId = result["VendorID"] as int;
          log.i('readVid() -> VendorID=$vendorId');

          span?.status = const SpanStatus.ok();
          return vendorId;
        } catch (e, r) {
          log.e('readVid() -> Failed to read vid: $e, $r');
          if (i < maxRetries - 1) {
            await Future.delayed(const Duration(seconds: retryDelay));
          } else {
            SentryUtils.captureMessage('addDevice() -> '
                'readVid() -> failed, errorMessage=$e', level: SentryLevel.error);
            span?.throwable = e;
            span?.status = const SpanStatus.unknownError();
          }
        }
      }

      return vendorId;
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTimeReadPid);
      log.i('readVid() -> The total time taken to read vid is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }
  }

  Future<bool> publishNodeIdToCloud(String nodeId, ISentrySpan? transaction) async {
    const int maxRetries = 3;
    const int retryDelay = 2;
    final startTime = DateTime.now();

    ISentrySpan? span = SentryUtils.startChild(transaction, 'publishNodeIdToCloud()');

    try {
      for (int i = 0; i < maxRetries; i++) {
        try {
          log.i('publishNodeIdToCloud() -> publishing nodeId, try ${i + 1}');
          await deviceShadowService.updateDeviceProperties(
              thingName: mcThingName,
              property: {'ep0:sMCtlr:sNewNodeID': nodeId},
              subId: '11'
          );

          log.i('publishNodeIdToCloud() -> Successfully sent nodeId to the cloud');
          span?.status = const SpanStatus.ok();
          return true;
        } catch (e, r) {
          log.e('publishNodeIdToCloud() -> Failed to send nodeId to the cloud: $e, $r');
          if (i < maxRetries - 1) {
            await Future.delayed(const Duration(seconds: retryDelay));
          } else {
            SentryUtils.captureMessage('addDevice() -> '
                'publishNodeIdToCloud() -> failed, errorMessage=$e', level: SentryLevel.error);
            span?.throwable = e;
            span?.status = const SpanStatus.unknownError();
          }
        }
      }

      return false;
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('publishNodeIdToCloud() -> The total time taken to send nodeId to the cloud is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }
  }

  Future<bool> writeCatValue(String fabricId, String nodeId, ISentrySpan? transaction) async {
    const String catIdOperate = 'AB120003';
    const int maxRetries = 3;
    final startTime = DateTime.now();

    ISentrySpan? span = SentryUtils.startChild(transaction, 'writeCatValue()');

    try {
      for (int i = 0; i < maxRetries; i++) {
        try {
          log.i('writeCatValue() -> writing cat, try ${i + 1}');
          await CtFlutterMatterPlugin.getInstance().writeCatValue(fabricId, nodeId, catIdOperate);

          log.i('writeCatValue() -> write cat success.');
          span?.status = const SpanStatus.ok();
          return true;
        } catch (e, r) {
          log.e('writeCatValue() -> write cat failed: $e, $r');
          if (i == maxRetries - 1) {
            SentryUtils.captureMessage('addDevice() -> '
                'writeCatValue() -> failed, errorMessage=$e', level: SentryLevel.error);
            span?.throwable = e;
            span?.status = const SpanStatus.unknownError();
          }
        }
      }

      return false;
    } finally {
      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('writeCatValue() -> The total time taken to write cat is: ${executionTime.inMilliseconds} ms');
      await span?.finish();
    }
  }

  Future<void> handleFactoryPairingAddDeviceEnd(String fabricId, String nodeId, ISentrySpan? transaction) async {
    hidePairingProgressWidget();

    Get.back();

    showSuccessSnackBar('PASS');
  }

  Future<void> showSsidAndPasswordDialog(String code, String ssid, String pwd) async {
    var result = await showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return EnterSsidAndPasswordWidget(
            ssid: ssid,
            pwd: pwd,
            obscureText: true,
          );
        });
    log.i('showSsidAndPasswordDialog() -> result=$result');
    if (result != null) {
      var inputSsid = result['ssid'];
      var inputPwd = result['password'];
      await _executeAddWiFiDevice(code, inputSsid, inputPwd);
    } else {
      hidePairingProgressWidget();
      GlobalService.to.getEventStreamController()
          .add(AppEvent(name: AppEvents.resumeCamera));
    }
  }

  Future<void> _executeAddWiFiDevice(String code, String inputSsid, String inputPwd) async {
    ISentrySpan? transaction;
    try {
      transaction = SentryUtils.startTransaction(
        'addMatterOverWiFiDevice()',
        'task',
        bindToScope: true,
      );

      await addMatterOverWiFiDevice(code, inputSsid, inputPwd, transaction, true)
          .timeout(const Duration(minutes: 5));
    } catch (e, s) {
      log.e('addMatterOverWiFiDevice() -> error=$e, $s');

      transaction?.throwable = e;
      transaction?.status = const SpanStatus.unknownError();

      SentryUtils.captureMessage('addDevice() -> failed, type=wifi, errorMessage=$e', level: SentryLevel.error);

      hidePairingProgressWidget();
      int errorCode = -1;
      if (e is TimeoutException) {
        showErrorDialog('commissioningPeriodTimeoutDisconnectReconnectWifiRetry'.tr, AppStatusCode.timeoutError);
      } else {
        showErrorDialog('Unknown cause', errorCode);
      }

      _interrupt = true;

    } finally {
      await transaction?.finish();
    }
  }

  void showSuccessDialog(String newThingName) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AddDeviceSucceededWidget(
              onOK: () {
                Get.to(() => AddThermostatSettingsPage(),
                    binding: AddThermostatSettingsBindings(
                      gwThingName: thingName,
                      thingName: newThingName,
                    ));
              }
          );
        }
    );
  }

  void showSelectDeviceTypeDialog(String code) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return SelectDeviceTypeWidget(
            onDeviceTypeSelected: (int type) {
              if (type == 0) {
                _handleAddWiFiDevice(code);
              } else {
                _handleAddThreadDevice(code);
              }
            },
          );
        }
    );
  }

  void showErrorDialog(String errorDescription, int errorCode) {
    if (_interrupt) {
      log.i('showErrorDialog() -> 添加Device操作已被中断，不显示错误弹窗...');
      return;
    }
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AddDeviceFailedWidget(
              errorDescription: errorDescription,
              onTryAgain: () {
                Get.back();
                GlobalService.to.getEventStreamController()
                    .add(AppEvent(name: AppEvents.resumeCamera));
              }
          );
        }
    );
  }

  void showDetailsErrorDialog(String errorDescription, int errorCode, String errorDescriptionDetails) {
    if (_interrupt) {
      log.i('showDetailsErrorDialog() -> 添加Device操作已被中断，不显示错误弹窗...');
      return;
    }
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AddDeviceFailedWidget(
              errorDescription: errorDescription,
              errorDescriptionDetails: errorDescriptionDetails,
              onTryAgain: () {
                Get.back();
                GlobalService.to.getEventStreamController()
                    .add(AppEvent(name: AppEvents.resumeCamera));
              }
          );
        }
    );
  }

  String getErrorMassage(int errorCode) {
    switch (errorCode) {
      case -10:
        return 'deviceNotFoundPowerOnFactoryResetPairingRetry'.tr;
      case -8:
        return 'Unable to Add Accessory';
      case -7:
        return 'commissioningErrorFactoryResetAppRestartRetry'.tr;
      case -6:
        return 'Device Attestation Error';
      case -5:
        return 'Non Support Error';
      case -4:
        return 'Pairing Device Error';
      case -3:
        return 'Parsing Dataset Error';
      case -2:
        return 'Parsing Code Error';
      case 1:
        if (PlatformUtils.isIOS) {
          return 'General error';
        }
        return 'Sending blocked';
      case 2:
        if (PlatformUtils.isIOS) {
          return 'Invalid string length';
        }
        return 'Connection aborted';
      case 3:
        if (PlatformUtils.isIOS) {
          return 'Invalid integer value';
        }
        return 'Incorrect state';
      case 4:
        if (PlatformUtils.isIOS) {
          return 'Invalid argument';
        }
        return 'Message too long';
      case 5:
        if (PlatformUtils.isIOS) {
          return 'Invalid message length';
        }
        return 'Recursion depth limit';
      case 6:
        if (PlatformUtils.isIOS) {
          return 'Invalid state';
        }
        return 'Too many unsolicited message handlers';
      case 7:
        if (PlatformUtils.isIOS) {
          return 'Wrong address type';
        }
        return 'No unsolicited message handler';
      case 8:
        if (PlatformUtils.isIOS) {
          return 'Integrity check failed';
        }
        return 'no connection handler';
      case 9:
        if (PlatformUtils.isIOS) {
          return 'Transaction timed out';
        }
        return 'Too many peer nodes';
      case 47:
        return 'Invalid argument';
      case 50:
        return 'commissioningPeriodTimeoutDisableVPNAntivirusRetry'.tr;
      case 172:
        return 'commissioningErrorFactoryResetRetry'.tr;
      case 219:
        return 'BUSY';
      case 1417:
        return 'Resource exhausted';
      case 33554533:
        return 'Network is unreachable';
      default:
        return 'Unknown cause';
     }
  }

  void showUncertifiedDeviceDialog() {
    String fabricId = sait85rMC?.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";
    Get.dialog(
      AlertDialog(
        title: Text(
          'uncertifiedDevice'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(Get.context!)
                  .extension<AppThemeExtension>()!.firstColor,
            )
        ),
        content: Text(
          'uncertifiedDeviceSecurityRisk'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(Get.context!)
                  .extension<AppThemeExtension>()!.firstColor,
            )
        ),
        actions: [
          TextButton(
            onPressed: () async {
              CtFlutterMatterPlugin.getInstance()
                  .continueCommissioningDevice(fabricId, true);
              Get.back();
            },
            child: Text(
              'setUpAnyway'.tr,
              style: const TextStyle(
                  color: AppColors.ff01A796
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              CtFlutterMatterPlugin.getInstance()
                  .continueCommissioningDevice(fabricId, false);
              Get.back();
            },
            child: Text(
              'cancel'.tr.toUpperCase(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  @override
  void onIssueNocBegin() {
    _nodeNocStartTime = DateTime.now();
    _nodeNocSpan = SentryUtils.startChild(_commissioningSpan, 'getNodeNoc()');

    updatePairingProgress(40);
    updatePairingProgressStatus('fetch node noc...');
  }

  @override
  void onIssueNocFailure() async {
    DateTime startTime = _nodeNocStartTime ?? DateTime.now();
    ISentrySpan? span = _nodeNocSpan;

    span?.throwable = 'issue noc failed.';
    span?.status = const SpanStatus.unknownError();

    final endTime = DateTime.now();
    final executionTime = endTime.difference(startTime);
    log.i('onIssueNocFailure() -> The total time taken to write cat is: ${executionTime.inMilliseconds} ms');
    await span?.finish();

    updatePairingProgress(60);
    updatePairingProgressStatus('case...');
    _caseSpan = SentryUtils.startChild(_commissioningSpan, 'case()');
  }

  @override
  void onIssueNocSuccess() async {
    DateTime startTime = _nodeNocStartTime ?? DateTime.now();
    ISentrySpan? span = _nodeNocSpan;

    span?.status = const SpanStatus.ok();

    final endTime = DateTime.now();
    final executionTime = endTime.difference(startTime);
    log.i('onIssueNocSuccess() -> The total time taken to write cat is: ${executionTime.inMilliseconds} ms');
    await span?.finish();

    updatePairingProgress(60);
    updatePairingProgressStatus('case...');
    _caseSpan = SentryUtils.startChild(_commissioningSpan, 'case()');
  }

}
