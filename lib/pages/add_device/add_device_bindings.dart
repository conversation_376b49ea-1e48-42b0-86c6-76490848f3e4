import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/add_device/add_device_controller.dart';
import 'package:habi_app/pages/add_device/network_scan/add_device_network_scan_controller.dart';
import 'package:habi_app/pages/add_device/pairing_code/add_device_pairing_code_controller.dart';
import 'package:habi_app/pages/add_device/qr_code/add_device_qr_code_controller.dart';

class AddDeviceBindings extends Bindings {

  final String thingName;

  AddDeviceBindings({required this.thingName});

  @override
  void dependencies() {
    Get.lazyPut(() => AddDeviceController(thingName: thingName));
    Get.lazyPut(() => AddDeviceNetworkScanController(thingName: thingName));
    Get.lazyPut(() => AddDevicePairingCodeController(thingName: thingName));
    Get.lazyPut(() => AddDeviceQrCodeController(thingName: thingName));
  }
}
