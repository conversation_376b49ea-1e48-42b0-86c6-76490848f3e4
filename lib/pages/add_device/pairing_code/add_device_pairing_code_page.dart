import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_device/add_device_controller.dart';
import 'package:habi_app/pages/add_device/pairing_code/add_device_pairing_code_controller.dart';
import 'package:habi_app/widgets/matter_code_sticker_widget.dart';

class AddDevicePairingCodePage extends StatefulWidget implements UIDelegateBridge {
  final UIDelegateManager delegateManager;

  const AddDevicePairingCodePage({super.key, required this.delegateManager});

  @override
  State<AddDevicePairingCodePage> createState() => _AddDevicePairingCodePageState();

  @override
  UIDelegateManager getUIDelegateManager() => delegateManager;

}

class _AddDevicePairingCodePageState extends State<AddDevicePairingCodePage>
    with AutomaticKeepAliveClientMixin implements UIDelegateLifeCycle {

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late AddDeviceController addDeviceController;
  late AddDevicePairingCodeController pairingCodeController;

  @override
  void initState() {
    super.initState();
    addDeviceController = Get.find<AddDeviceController>();
    pairingCodeController = Get.find<AddDevicePairingCodeController>();
    widget.delegateManager.setUIDelegateLifecycle(this);
  }

  @override
  void dispose() {
    widget.delegateManager.setUIDelegateLifecycle(null);
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onHide() {
    log.i('UIDelegate{} -> pairingCodePage onHide');
  }

  @override
  void onShow() {
    log.i('UIDelegate{} -> pairingCodePage onShow');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Obx(() {
      if (!pairingCodeController.clicked.value) {
        return _buildMatterQRCodeStickerWidget();
      }
      return _buildBody();
    });
  }

  Widget _buildMatterQRCodeStickerWidget() {
    return MatterCodeStickerWidget(
      title: 'pairYourDevice'.tr,
      subTitle: 'matterPairingCodeSticker'.tr,
      onClicked: () {
        pairingCodeController.clicked.value = true;
      },
    );
  }

  Widget _buildBody() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(height: 30.h,),

        Text(
          textAlign: TextAlign.center,
          'pairYourDevice'.tr,
          style: TextStyle(
            fontSize: 22,
            fontWeight: AppFontWeights.medium,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        const SizedBox(height: 5,),
        Text(
          textAlign: TextAlign.center,
          'enterPairingCode'.tr,
          style: TextStyle(
            fontSize: 16,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),

        SizedBox(height: 42.h,),

        _buildForm(context),

        SizedBox(height: 42.h,),

        Expanded(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: FilledButton(
                onPressed: _onContinue,
                child: Text(
                  'continue'.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.medium,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 37,),

      ],
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
        key: _formKey,
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'pairingCode'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 14,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                textAlign: TextAlign.center,
                textAlignVertical: TextAlignVertical.center,
                controller: pairingCodeController.pairingCodeController,
                keyboardType: TextInputType.emailAddress,
                validator: _validatePairingCode,
              ),
            ),

          ],
        )
    );
  }

  String? _validatePairingCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }
    return null;
  }


  void _onContinue() {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    KeyboardHelper.dismissKeyboard(Get.context!);
    String pairingCode = pairingCodeController.pairingCodeController.text;
    addDeviceController.requestPermission(pairingCode);
  }



}