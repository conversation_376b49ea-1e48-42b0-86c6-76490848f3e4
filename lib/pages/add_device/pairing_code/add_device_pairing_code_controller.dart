import 'package:flutter/material.dart';
import 'package:get/get.dart';


class AddDevicePairingCodeController extends GetxController {

  final String thingName;

  AddDevicePairingCodeController({
    required this.thingName,
  });

  final pairingCodeController = TextEditingController();
  var clicked = false.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    pairingCodeController.dispose();
    super.onClose();
  }


}
