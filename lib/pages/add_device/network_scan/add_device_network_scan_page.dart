import 'package:ct_flutter_matter_plugin/model/ct_matter_device.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_device/add_device_controller.dart';
import 'package:habi_app/pages/add_device/network_scan/add_device_network_scan_controller.dart';
import 'package:habi_app/widgets/enter_pairing_code_widget.dart';
import 'package:habi_app/widgets/scan_device_ripple_widget.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class AddDeviceNetworkScanPage extends StatefulWidget implements UIDelegateBridge{
  final UIDelegateManager delegateManager;

  const AddDeviceNetworkScanPage({super.key, required this.delegateManager});

  @override
  State<AddDeviceNetworkScanPage> createState() => _AddDeviceNetworkScanPageState();

  @override
  UIDelegateManager getUIDelegateManager() => delegateManager;
}

class _AddDeviceNetworkScanPageState extends State<AddDeviceNetworkScanPage>
    with AutomaticKeepAliveClientMixin implements UIDelegateLifeCycle {

  late AddDeviceController addDeviceController;
  late AddDeviceNetworkScanController controller;

  @override
  void initState() {
    super.initState();
    addDeviceController = Get.find<AddDeviceController>();
    controller = Get.find<AddDeviceNetworkScanController>();
    widget.delegateManager.setUIDelegateLifecycle(this);
  }

  @override
  void dispose() {
    widget.delegateManager.setUIDelegateLifecycle(null);
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onHide() {
    log.i('UIDelegate{} -> networkScanPage onHide');
    controller.stopScanAllDevices();
    controller.showTryAgain.value = true;
  }

  @override
  void onShow() {
    log.i('UIDelegate{} -> networkScanPage onShow');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(height: 20.h,),

        Text(
          'lookingForDevices'.tr,
          style: TextStyle(
            fontSize: 22,
            fontWeight: AppFontWeights.medium,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),

        SizedBox(height: 28.h,),

        Expanded(
            child: Center(
                child: ScanDeviceRippleWidget(
                  key: controller.rippleWidgetKey,
                  width: 342.w,
                  height: 342.h,
                )
            )
        ),

        SizedBox(height: 39.h,),

        Opacity(
          opacity: 0.5,
          child: Text(
            'discoveredDevices'.tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
        ),

        SizedBox(height: 21.h,),

        ConstrainedBox(
          constraints: const BoxConstraints(
            maxHeight: 120,
            minHeight: 100,
          ),
          child: Obx(() {
            return ListView.separated(
              itemBuilder: _buildSanDeviceItem,
              scrollDirection: Axis.horizontal,
              itemCount: controller.scanDeviceList.length,
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(width: 16,);
              },
            );
          }),
        ),

        SizedBox(height: 21.h,),

        Obx(() {
          return Visibility(
            visible: controller.showTryAgain.value,
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: OutlinedButton(
                onPressed: _onTryAgain,
                child: Text(
                  'tryAgain'.tr.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.ff01A796,
                  ),
                ),
              ),
            ),
          );
        }),

        SizedBox(height: 14.h,),

      ],
    );
  }


  Widget _buildSanDeviceItem(BuildContext context, int index) {
    var device = controller.scanDeviceList[index];
    return InkWell(
      onTap: () => _onScanDeviceItemTap(device),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 71,
            height: 61,
            child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                    left: 4,
                    child: SvgPicture.asset(
                      AppImagePaths.receiver,
                      width: 56,
                      height: 56,
                    ),
                  ),
                  Positioned(
                    right: 4,
                    bottom: 0,
                    child: SvgPicture.asset(
                      AppImagePaths.addDevice,
                      width: 30,
                      height: 30,
                    ),
                  ),
                ]
            ),
          ),

          const SizedBox(height: 7),

          Text(
            'Device-${index + 1}\n(${device.getInstanceName()})',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),

        ],
      ),
    );
  }

  void _onScanDeviceItemTap(CtMatterDevice device) async {
    /*if (device.discoveryCapabilities != 'BLE') {
      if (PlatformUtils.isAndroid) {
        if (device.commissioningWindowStatus == 0) {
          log.i('_onScanDeviceItemTap() -> 没有开启paring window');
          return;
        }
      } else if (PlatformUtils.isIOS) {
        if (device.didRemoveCommissionableDevice == true) {
          log.i('_onScanDeviceItemTap() -> 没有开启paring window');
          return;
        }
      }
    }*/

    String? result = await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return EnterPairingCodeWidget();
        }
    );

    if (result == null || result.isEmpty) {
      return;
    }

    controller.rippleWidgetKey.currentState?.stopAnimation();
    controller.stopScanDevices();
    controller.stopDiscoverCommissionableNodes();
    addDeviceController.requestPermission(result);
  }

  void _onTryAgain() {
    controller.reStartScanAllDevices();
  }


}
