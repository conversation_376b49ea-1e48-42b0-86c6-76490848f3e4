import 'dart:async';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:ct_flutter_matter_plugin/model/ct_matter_device.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/widgets/scan_device_ripple_widget.dart';

class AddDeviceNetworkScanController extends GetxController {

  final String thingName;
  late String mcThingName;

  AddDeviceNetworkScanController({
    required this.thingName,
  }) {
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
  }

  final GlobalKey<ScanDeviceRippleWidgetState> rippleWidgetKey = GlobalKey<ScanDeviceRippleWidgetState>();
  var scanDeviceList = <CtMatterDevice>[].obs;
  var showTryAgain = false.obs;
  StreamSubscription? _onScanDevicesSubscription;
  bool isScanning = false;
  bool isDiscoverCommissionableNodes = false;
  Timer? _scanDeviceTimer;
  Timer? _stopDeviceTimer;

  @override
  void onInit() {
    super.onInit();
    _onScanDevicesSubscription = CtFlutterMatterPlugin.getInstance()
        .onScanDevicesStream.listen((scanDevice) {
      log.i('onScanDevicesStream() -> 扫描到的matter设备: $scanDevice');
      bool deviceExist = scanDeviceList.any((itemDevice) {
        return itemDevice.discriminator == scanDevice.discriminator;
      });
      bool isOpenCommissioningWindow = _isOpenCommissioningWindow(scanDevice);
      log.i('onScanDevicesStream() -> deviceExist: $deviceExist, '
          'isOpenCommissioningWindow: $isOpenCommissioningWindow');
      if (!deviceExist && isOpenCommissioningWindow) {
        scanDeviceList.add(scanDevice);
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    startScanAllDevices();
  }

  @override
  void onClose() {
    stopScanAllDevices();
    _onScanDevicesSubscription?.cancel();
    super.onClose();
  }

  Future<void> startScanAllDevices() async {
    await startScanDevices();
    if (PlatformUtils.isAndroid) {
      _scanDeviceTimer = Timer.periodic(const Duration(seconds: 8), (timer) async {
        await discoverCommissionableNodes();
      });
    }

    _stopDeviceTimer = Timer(const Duration(seconds: 60), () async {
      await stopScanDevices();
      if (PlatformUtils.isAndroid) {
        await stopDiscoverCommissionableNodes();
      }
      showTryAgain.value = true;
      rippleWidgetKey.currentState?.stopAnimation();
      log.i('60s时间已到，停止扫描ble和onNetwork设备...');
    });
  }

  Future<void> reStartScanAllDevices() async {
    showTryAgain.value = false;
    scanDeviceList.value = [];
    rippleWidgetKey.currentState?.startAnimation();
    Future.delayed(const Duration(milliseconds: 500), () {
      startScanAllDevices();
    });
  }

  Future<void> stopScanAllDevices() async {
    log.i('停止扫描ble和onNetwork设备...');
    _scanDeviceTimer?.cancel();
    _stopDeviceTimer?.cancel();
    rippleWidgetKey.currentState?.stopAnimation();
    stopScanDevices();
    stopDiscoverCommissionableNodes();
  }

  Future<void> startScanDevices() async {
    if (!isScanning) {
      try {
        log.i('startScanDevices() -> 开始扫描...');
        String? fabricId = await FabricHelper.getFabricId(mcThingName);
        if (fabricId == null || fabricId.isEmpty) {
          log.e('startScanDevices() -> 未获取到fabricId');
          return;
        }
        await CtFlutterMatterPlugin.getInstance().startScanDevices(fabricId);
        isScanning = true;
      } catch(e) {
        log.e('startScanDevices() -> 开始扫描失败: $e');
      }
    }
  }

  Future<void> stopScanDevices() async {
    if (isScanning) {
      try {
        log.i('stopScanDevices() -> 退出扫描...');
        String? fabricId = await FabricHelper.getFabricId(mcThingName);
        if (fabricId == null || fabricId.isEmpty) {
          log.e('stopScanDevices() -> 未获取到fabricId');
          return;
        }
        await CtFlutterMatterPlugin.getInstance().stopScanDevices(fabricId);
        isScanning = false;
      } catch (e){
        log.e('stopScanDevices() -> 退出扫描失败: $e');
      }
    }
  }

  Future<void> discoverCommissionableNodes() async {
    if (isDiscoverCommissionableNodes) {
      log.i('discoverCommissionableNodes() -> isDiscoverCommissionableNodes=true, 不执行发现');
      return;
    }
    isDiscoverCommissionableNodes = true;

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    if (fabricId == null || fabricId.isEmpty) {
      log.e('discoverCommissionableNodes() -> 未获取到fabricId, 不执行发现设备');
      Future.delayed(const Duration(milliseconds: 8000), () async {
        isDiscoverCommissionableNodes = false;
      });
      return;
    }

    try {
      log.i('discoverCommissionableNodes() -> 开始discoverNodes...');
      await CtFlutterMatterPlugin.getInstance()
          .discoverCommissionableNodes(fabricId: fabricId);
      Future.delayed(const Duration(milliseconds: 8000), () async {
        await CtFlutterMatterPlugin.getInstance()
            .getDiscoveredDevice(fabricId: fabricId);
        isDiscoverCommissionableNodes = false;
        log.i('discoverCommissionableNodes() -> discoverNodes结束');
      });
    } catch(e) {
      log.e('discoverCommissionableNodes() -> discoverNodes失败');
      Future.delayed(const Duration(milliseconds: 8000), () async {
        isDiscoverCommissionableNodes = false;
      });
    }
  }

  Future<void> stopDiscoverCommissionableNodes() async {
    _scanDeviceTimer?.cancel();
  }

  bool _isOpenCommissioningWindow(CtMatterDevice device) {
    if (PlatformUtils.isAndroid) {
      return device.commissioningWindowStatus != 0;
    } else if (PlatformUtils.isIOS){
      return device.didRemoveCommissionableDevice == false && device.commissioningMode == true;
    } else {
      return false;
    }
  }

}