import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/create_account/create_account_controller.dart';
import 'package:habi_app/pages/forgot_password/forgot_password_controller.dart';
import 'package:habi_app/pages/language_select/language_select_controller.dart';
import 'package:habi_app/pages/login/login_controller.dart';
import 'package:habi_app/pages/root/root_controller.dart';

class ForgotPasswordBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ForgotPasswordController());
  }
}
