import 'dart:async';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_bindings.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_page.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class ForgotPasswordController extends GetxController {
  final emailController = TextEditingController();
  final isLoading = false.obs;
  Completer<void>? _forgotPasswordCompleter;

  @override
  void onClose() {
    if (_forgotPasswordCompleter?.isCompleted == false) {
      _forgotPasswordCompleter?.completeError(Exception('canceled'));
    }
    super.onClose();
    emailController.dispose();
  }

  Future<void> forgotPassword() async {
    if (isLoading.value) {
      return;
    }

    try {
      isLoading.value = true;
      await _forgotPassword();
      Get.to(
        () => TwoFactorAuthenticationPage(
          email: emailController.text,
          authType: TwoFactorAuthenticationPage.authTypeResetPassword,
        ),
        binding: TwoFactorAuthenticationBindings(),
      );
    } catch (e, r) {
      log.e("forgotPassword() -> Exception{} -> e=$e, r=$r");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _forgotPassword() {
    final completer = Completer<void>();
    _forgotPasswordCompleter = completer;

    Future.delayed(Duration.zero, () async {
      try {
        await AuthService.to
            .forgotPassword(emailController.text)
            .timeout(const Duration(seconds: 30));
        if (!completer.isCompleted) {
          completer.complete();
        }
      } on NetworkException catch (e, r) {
        log.e("_forgotPassword() -> NetworkException{} -> e=$e, r=$r");
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(content: 'networkError'.tr);
          completer.completeError(e);
        }
      } on TimeoutException catch (e, r) {
        log.e("_forgotPassword() -> TimeoutException{} -> e=$e, r=$r");
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(content: 'timeoutError'.tr);
          completer.completeError(e);
        }
      } catch (e, r) {
        log.e("_forgotPassword() -> Exception{} -> e=$e, r=$r");
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(
            paragraphs: ['requestFailed'.tr, e.toString()],
          );
          completer.completeError(e);
        }
      }
    });

    return completer.future;
  }
}
