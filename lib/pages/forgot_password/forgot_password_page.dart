import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/forgot_password/forgot_password_controller.dart';
import 'package:habi_app/widgets/expanded_scroll_view.dart';

class ForgotPasswordPage extends GetView<ForgotPasswordController> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'forgotPassword'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context).extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: ExpandedScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        topContent: Column(
          children: [
            SizedBox(height: 107.h),
            Text(
              textAlign: TextAlign.center,
              'sendLinkToChangeYourPassword'.tr,
              style: TextStyle(
                fontSize: 18,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),
            SizedBox(height: 37.h),
            _buildForm(context),
            SizedBox(height: 37.h),
          ],
        ),
        bottomContent: Column(
          children: [
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: 'emailCheckOrResendSpan1'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ff868788,
                    ),
                  ),
                  TextSpan(
                    text: 'resend'.tr,
                    recognizer: TapGestureRecognizer()..onTap = _onResend,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ff01A796,
                      decoration: TextDecoration.underline,
                      decorationColor: AppColors.ff01A796,
                    ),
                  ),
                  TextSpan(
                    text: 'emailCheckOrResendSpan2'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ff868788,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 25.h),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: Obx(() {
                return FilledButton(
                  onPressed: _onReset,
                  child: controller.isLoading.value
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          'reset'.tr.toUpperCase(),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .secondColor,
                          ),
                        ),
                );
              }),
            ),
            SizedBox(height: 113.h),
          ],
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: Opacity(
              opacity: 0.5,
              child: Text(
                'email'.tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: AppFontWeights.medium,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!
                      .firstColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 14),
          ConstrainedBox(
            constraints: const BoxConstraints(
              maxHeight: 148,
              minHeight: 48,
            ),
            child: TextFormField(
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
              controller: controller.emailController,
              keyboardType: TextInputType.emailAddress,
              validator: _validateEmail,
            ),
          ),
        ],
      ),
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (!value.isEmail) {
      return 'emailAddressInvalid'.tr;
    }

    return null;
  }

  void _onReset() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.forgotPassword();
  }

  void _onResend() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.forgotPassword();
  }
}
