import 'dart:async';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_bindings.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_page.dart';
import 'package:habi_app/services/local_storage_service.dart';

class CreateAccountController extends GetxController {

  final GlobalKey emailGlobalKey = GlobalKey();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();
  var isLoading = false.obs;
  var errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
  }

  Future<void> signUpUser(String country, String language) async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;
    KeyboardHelper.dismissKeyboard(Get.context!);

    try {
      String email = emailController.text;
      String password = passwordController.text;
      String firstName = '';
      String lastName = '';

      try {
        var array = email.split('@');
        if (array.length > 1) {
          firstName = array[0];
          lastName = firstName;
        }
      } catch (e) {
        log.e("signUpUser() -> e=$e");
      }

      SignUpResult result = await AuthService.to.signUpUser(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        country: country,
        language: language,
      ).timeout(const Duration(seconds: 30));

      log.i("signUpUser() -> signUpStep=${result.nextStep.signUpStep}");

      switch (result.nextStep.signUpStep) {
        case AuthSignUpStep.confirmSignUp:
          log.i("signUpUser() -> confirmSignUp");
          var localStorageService = LocalStorageService.to;
          await localStorageService.initUser(emailController.text);
          await localStorageService.setLanguage(language);

          Get.to(
              () => TwoFactorAuthenticationPage(
                email: emailController.text,
                authType: TwoFactorAuthenticationPage.authTypeSignUp,
              ),
              binding: TwoFactorAuthenticationBindings()
          );
          break;
        case AuthSignUpStep.done:
          log.i("signUpUser() -> done");
          break;
        default:
          log.i("signUpUser() -> default");
          break;
      }
    } catch(e, r) {
      log.e("signUpUser() -> Exception{} -> e=$e, r=$r");

      if (e is NetworkException) {
        errorMessage.value = 'networkError'.tr;
      } else if (e is TimeoutException) {
        errorMessage.value = 'timeoutError'.tr;
      } else if (e is UsernameExistsException) {
        errorMessage.value = 'emailAddressHasAlreadyBeenTaken'.tr;
      } else {
        errorMessage.value = 'requestFailed'.tr;
      }

    } finally {
      isLoading.value = false;
    }

  }

}
