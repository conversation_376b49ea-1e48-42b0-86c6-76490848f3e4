import 'dart:core';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/reg_exp_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/pages/create_account/create_account_controller.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:url_launcher/url_launcher_string.dart';

class CreateAccountPage extends GetView<CreateAccountController> {

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final String country;
  final String language;

  CreateAccountPage({
    super.key,
    required this.country,
    required this.language,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'createAnAccount'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 36),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 33.h,),

              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  'createAccountTitle'.tr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 40,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.ff01A796,
                  ),
                ),
              ),

              SizedBox(height: 18.h,),

              Text(
                'createAccountBody'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!
                      .firstColor,
                ),
              ),
        
              SizedBox(height: 37.h,),

              _buildForm(context),

              SizedBox(height: 12.h,),

              Obx((){
                return Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    controller.errorMessage.value,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: AppFontWeights.regular,
                      color: AppColors.ffFF4E4E,
                    ),
                  ),
                );
              }),

              SizedBox(height: 12.h,),

              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  children: [
                    TextSpan(
                        text: 'privacyPolicySpan1'.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: AppColors.ff868788,
                        ),
                    ),
                    TextSpan(
                      text: 'privacyPolicySpan2'.tr,
                      recognizer: TapGestureRecognizer()..onTap = _onPrivacyPolicyTap,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff01A796,
                        decoration: TextDecoration.underline,
                        decorationColor: AppColors.ff01A796,
                      ),
                    ),
                    TextSpan(
                      text: 'privacyPolicySpan3'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff868788,
                      ),
                    ),
                    TextSpan(
                      text: 'privacyPolicySpan4'.tr,
                      recognizer: TapGestureRecognizer()..onTap = _onTermsConditionsTap,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff01A796,
                        decoration: TextDecoration.underline,
                        decorationColor: AppColors.ff01A796,
                      ),
                    ),
                  ]
                ),
              ),

              SizedBox(height: 27.h,),


              SizedBox(
                width: double.infinity,
                height: 48,
                child: Obx(() {
                  return FilledButton(
                    onPressed: _onContinue,
                    child: controller.isLoading.value ?
                    const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                      ),
                    ) : Text(
                      'continue'.tr,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.medium,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.secondColor,
                      ),
                    ),
                  );
                })
              ),


              SizedBox(height: 28.h,),

              /*SizedBox(
                width: double.infinity,
                height: 48,
                child: OutlinedButton.icon(
                  onPressed: _onConnectWithGoogle,
                  icon: Image.asset(
                    AppImagePaths.googleLogo,
                    width: 22,
                    height: 22,
                  ),
                  label: Text(
                    'connectWithGoogle'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 24.h,),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: OutlinedButton.icon(
                  onPressed: _onConnectWithAppleId,
                  icon: Image.asset(
                    AppImagePaths.appleLogo,
                    width: 22,
                    height: 22,
                  ),
                  label: Text(
                    'connectWithAppleId'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ), */

              SizedBox(height: 47.h,),
        
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
        key: _formKey,
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'email'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                maxHeight: 148,
                minHeight: 48
              ),
              child: TextFormField(
                key: controller.emailGlobalKey,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.emailController,
                keyboardType: TextInputType.emailAddress,
                validator: _validateEmail,
              ),
            ),


            SizedBox(height: 14.h,),

            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'password'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.passwordController,
                obscureText: true,
                validator: _validatePassword,
              ),
            ),

            SizedBox(height: 14.h,),

            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'confirmPassword'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.confirmPasswordController,
                obscureText: true,
                validator: _validatePassword,
              ),
            ),
          ],
        )
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (!value.isEmail) {
      return 'incorrectEmail'.tr;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 8) {
      return 'passwordTooShort'.tr;
    }

    if (value.length > 14) {
      return 'passwordTooLong'.tr;
    }

    if (!RegExp(RegExpHelper.userPassword).hasMatch(value)) {
      return 'passwordTooSimple'.tr;
    }

    if(!RegExpHelper.specialSymbol.any(value.contains)){
      return 'passwordMustContain'.tr;
    }

    if(controller.passwordController.text != controller.confirmPasswordController.text){
      return 'passwordNotMatching'.tr;
    }

    return null;
  }

  void _onContinue() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.signUpUser(country, language);
  }

  // void _onConnectWithGoogle() {
  //
  // }
  //
  // void _onConnectWithAppleId() {
  //
  // }

  void _onPrivacyPolicyTap() {
    Future(() async {
      try {
        GlobalService service = GlobalService.to;
        if (!await launchUrlString(service.config.privacyUrl, mode: LaunchMode.externalApplication)) {
          showErrorSnackBar('Could not launch ${service.config.privacyUrl}');
        }
      } catch (e) {
        showErrorSnackBar('Could not launch $e');
      }
    });
  }

  void _onTermsConditionsTap() {
    Future(() async {
      try {
        GlobalService service = GlobalService.to;
        if (!await launchUrlString(service.config.termsUrl, mode: LaunchMode.externalApplication)) {
          showErrorSnackBar('Could not launch ${service.config.termsUrl}');
        }
      } catch (e) {
        showErrorSnackBar('Could not launch $e');
      }
    });
  }

}
