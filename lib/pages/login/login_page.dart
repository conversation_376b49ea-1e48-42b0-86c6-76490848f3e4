import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/forgot_password/forgot_password_bindings.dart';
import 'package:habi_app/pages/forgot_password/forgot_password_page.dart';
import 'package:habi_app/pages/login/login_controller.dart';

class LoginPage extends GetView<LoginController> {

  static const String routeName = '/login';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final bool showBackIcon;

  LoginPage({
    super.key,
    this.showBackIcon = false
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'login'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: showBackIcon ? IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ) : const SizedBox(),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 36),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[

              SizedBox(height: 33.h,),

              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  'loginTitle'.tr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 40,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.ff01A796,
                  ),
                ),
              ),

              SizedBox(height: 18.h,),

              Text(
                'loginBody'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!
                      .firstColor,
                ),
              ),
        
              SizedBox(height: 37.h,),

              _buildForm(context),

              SizedBox(height: 12.h,),

              Obx((){
                bool displayErrorMessage = controller.errorMessage.value.isNotEmpty;
                return Column(
                  children: [
                    if (displayErrorMessage) _buildErrorMessage(context),
                    if (displayErrorMessage) SizedBox(height: 6.h,),
                    _buildKeepMeLoggedIn(context),
                  ],
                );
              }),

              SizedBox(height: 16.h,),

              InkWell(
                onTap: _onForgotPasswordTap,
                child: SizedBox(
                  width: double.infinity,
                  child: Center(
                    child: Text(
                      'forgotPassword'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff01A796,
                      ),
                    ),
                  ),
                ),
              ),
        
              SizedBox(height: 39.h,),

              SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: Obx(() {
                    return FilledButton(
                      onPressed: _onContinue,
                      child: controller.isLoading.value ?
                      const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ) : Text(
                        'continue'.tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.secondColor,
                        ),
                      ),
                    );
                  })
              ),

              SizedBox(height: 28.h,),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: Obx(() {
                  return FilledButton(
                    onPressed: controller.signInDemo,
                    child: controller.isLoadingForDemo.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : Text(
                            'demo'.tr,
                            style: TextStyles.medium14SecondColor,
                          ),
                  );
                }),
              ),

              SizedBox(height: 28.h,),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: Obx(() {
                  return Stack(
                    fit: StackFit.expand,
                    children: [
                      OutlinedButton.icon(
                        onPressed: _onConnectWithGoogle,
                        icon: Image.asset(
                          AppImagePaths.googleLogo,
                          width: 22,
                          height: 22,
                        ),
                        label: Text(
                          'connectWithGoogle'.tr,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          ),
                        ),
                      ),
                      if (controller.isLoadingForGoogle.value) const Center(
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: AppColors.ff01A796,
                          ),
                        ),
                      )
                    ]
                  );
                }),
              ),

              SizedBox(height: 24.h,),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: Obx(() {
                  return Stack(
                      fit: StackFit.expand,
                      children: [
                        OutlinedButton.icon(
                          onPressed: _onConnectWithAppleId,
                          icon: Image.asset(
                            AppImagePaths.appleLogo,
                            width: 22,
                            height: 22,
                          ),
                          label: Text(
                            'connectWithAppleId'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.medium,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            ),
                          ),
                        ),
                        if (controller.isLoadingForApple.value) const Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: AppColors.ff01A796,
                            ),
                          ),
                        )
                      ]
                  );
                }),
              ),

              SizedBox(height: 47.h,),
        
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
        key: _formKey,
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'email'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                maxHeight: 148,
                minHeight: 48
              ),
              child: TextFormField(
                key: controller.emailGlobalKey,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.emailController,
                keyboardType: TextInputType.emailAddress,
                validator: _validateEmail,
              ),
            ),


            SizedBox(height: 14.h,),

            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'password'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 14.h,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.passwordController,
                obscureText: true,
                validator: _validatePassword,
              ),
            ),

          ],
        )
    );
  }

  Widget _buildErrorMessage(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        controller.errorMessage.value,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: AppFontWeights.regular,
          color: AppColors.ffFF4E4E,
        ),
      ),
    );
  }

  Widget _buildKeepMeLoggedIn(BuildContext context) {
    return Row(
      children: [
        Checkbox(
          value: controller.keepMeLoggedIn.value,
          activeColor: AppColors.ff01A796,
          fillColor: controller.keepMeLoggedIn.value == false? null : const WidgetStatePropertyAll(AppColors.ff01A796),
          visualDensity: VisualDensity.compact, // 减少内部紧凑度
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap, // 缩小点击区
          onChanged: (value) {
            controller.setKeepMeLoggedIn(value ?? false);
          },
        ),
        GestureDetector(
          onTap: () {
            controller.setKeepMeLoggedIn(!controller.keepMeLoggedIn.value);
          },
          child: Opacity(
            opacity: 0.5,
            child: Text(
              'keepMeLoggedIn'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (!value.isEmail) {
      return 'emailAddressInvalid'.tr;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 8) {
      return 'passwordTooShort'.tr;
    }

    return null;
  }

  Future<void> _onContinue() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.signIn();
  }

  void _onForgotPasswordTap() {
    Get.to(
        () => ForgotPasswordPage(),
        binding: ForgotPasswordBindings()
    );
  }

  Future<void>  _onConnectWithGoogle() async {
    await controller.signInWithGoogle();
  }
  
  Future<void> _onConnectWithAppleId() async {
    await controller.signInWithApple();
  }

}
