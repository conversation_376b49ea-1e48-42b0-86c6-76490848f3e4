import 'dart:async';
import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_bindings.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/push_notification_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:habi_app/utility/amplify_utils.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class LoginController extends GetxController {
  final authService = AuthService.to;
  final globalService = GlobalService.to;
  final demoModeService = DemoModeService.to;
  final deviceListService = DeviceListService.to;
  final localStorageService = LocalStorageService.to;
  final userAttributesService = UserAttributesService.to;
  final pushNotificationService = PushNotificationService.to;

  final GlobalKey emailGlobalKey = GlobalKey();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  var isLoading = false.obs;
  var isLoadingForDemo = false.obs;
  var isLoadingForGoogle = false.obs;
  var isLoadingForApple = false.obs;
  var errorMessage = ''.obs;
  var keepMeLoggedIn = false.obs;

  bool get isSignInLoading =>
      isLoading.value ||
      isLoadingForDemo.value ||
      isLoadingForGoogle.value ||
      isLoadingForApple.value;

  @override
  void onInit() {
    super.onInit();
    deviceListService.clearAll();
    demoModeService.isDemo = false;

    if (globalService.config.amplifyConfigured) {
      pushNotificationService.disablePinpointNotifications();
    }

    log.i("LoginController -> onInit() -> end");
  }

  @override
  void onReady() {
    super.onReady();
    Future(() async {
      emailController.text = await localStorageService.getEmail() ?? "";
      passwordController.text = await localStorageService.getPassword() ?? "";
    });
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
    passwordController.dispose();
  }

  Future<void> signIn() async {
    if (isSignInLoading) {
      log.e("signIn() -> login in...");
      return;
    }

    isLoading.value = true;
    KeyboardHelper.dismissKeyboard(Get.context!);

    try {
      log.i("signIn() -> start signOut...");
      SignOutResult signOutResult =
          await authService.signOut().timeout(const Duration(seconds: 30));
      if (signOutResult is CognitoCompleteSignOut) {
        log.i("signIn() -> signOut Success.");
      } else {
        log.e("signIn() -> signOut Failed.");
      }

      log.i("signIn() -> start signIn...");
      SignInResult result = await authService
          .signIn(
            username: emailController.text,
            password: passwordController.text,
          )
          .timeout(const Duration(seconds: 30));

      log.i("signIn() -> signInResult=${result.nextStep.signInStep}");

      switch (result.nextStep.signInStep) {
        case AuthSignInStep.confirmSignInWithNewPassword:
          break;
        case AuthSignInStep.confirmSignUp:
          Get.to(
              () => TwoFactorAuthenticationPage(
                    email: emailController.text,
                    authType: TwoFactorAuthenticationPage.authTypeSignIn,
                  ),
              binding: TwoFactorAuthenticationBindings());
          break;
        case AuthSignInStep.done:
          await userAttributesService.fetchAttributes();
          await _saveUserData();
          await _handleLoginSuccess();
          break;
        default:
          break;
      }
    } catch (e, r) {
      log.e("signIn() -> Exception{} -> e=$e, r=$r");
      if (e is NotAuthorizedServiceException) {
        Scrollable.ensureVisible(emailGlobalKey.currentContext!);
        errorMessage.value = 'incorrectEmailOrPassword'.tr;
      } else if (e is TimeoutException) {
        errorMessage.value = 'timeoutError'.tr;
      } else if (e is NetworkException) {
        errorMessage.value = 'networkError'.tr;
      } else {
        errorMessage.value = 'requestFailed'.tr;
      }
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> signInDemo() async {
    if (isSignInLoading) {
      log.e("signInDemo() -> login in...");
      return;
    }

    isLoadingForDemo.value = true;
    KeyboardHelper.dismissKeyboard(Get.context!);

    // try {
    //   log.i("signInDemo() -> start signOut...");
    //   final signOutResult =
    //       await authService.signOut().timeout(const Duration(seconds: 30));
    //   if (signOutResult is CognitoCompleteSignOut) {
    //     log.i("signInDemo() -> signOut Success.");
    //   } else {
    //     log.e("signInDemo() -> signOut Failed.");
    //   }
    // } catch (e) {
    //   log.e("signInDemo() -> signOut Error");
    // }

    try {
      await authService.signInDemo().timeout(const Duration(seconds: 30));
      await userAttributesService.fetchAttributes();
      
      final email = userAttributesService.attributes.value?.email ?? '';
      if (email.isNotEmpty) {
        await localStorageService.initUser(email);
        await localStorageService.initThingShadow(email);

        final user = await authService.getCurrentUser();
        await localStorageService.setUserId(user.userId);
        log.i("signInDemo() -> userId=${user.userId}");
      }

      Get.offAllNamed(Routes.homeManagement);
    } catch (e, r) {
      log.e("signInDemo() -> Exception{} -> e=$e, r=$r");
    } finally {
      isLoadingForDemo.value = false;
    }
  }

  Future<void> signInWithGoogle() async {
    if (isSignInLoading) {
      log.e("signInWithGoogle() -> login in...");
      return;
    }
    isLoadingForGoogle.value = true;
    try {
      SignInResult result = await authService.signInWithGoogle();
      if (result.isSignedIn) {
        await userAttributesService.fetchAttributes();
        await _saveUserDataOAuth();
        await _handleLoginSuccess();
      }
    } catch (e, r) {
      log.e("signInWithGoogle() -> e=$e, r=$r");
    } finally {
      isLoadingForGoogle.value = false;
    }
  }

  Future<void> signInWithApple() async {
    if (isSignInLoading) {
      log.e("signInWithApple() -> login in...");
      return;
    }
    isLoadingForApple.value = true;
    try {
      SignInResult result = await authService.signInWithApple();
      if (result.isSignedIn) {
        await userAttributesService.fetchAttributes();
        await _saveUserDataOAuth();
        await _handleLoginSuccess();
      }
    } catch (e, r) {
      log.e("signInWithApple() -> e=$e, r=$r");
    } finally {
      isLoadingForApple.value = false;
    }
  }

  Future<void> _saveUserData() async {
    await localStorageService.initUser(emailController.text);
    await localStorageService.initThingShadow(emailController.text);
    await localStorageService.setEmail(emailController.text);
    await localStorageService.setPassword(passwordController.text);

    var user = await authService.getCurrentUser();
    await localStorageService.setUserId(user.userId);
    log.i("_saveUserData() -> userId=${user.userId}");
  }

  Future<void> _sentryConfigureScope() async {
    try {
      var user = await authService.getCurrentUser();
      Sentry.configureScope(
        (scope) => scope.setUser(
          SentryUser(id: user.userId, email: emailController.text),
        ),
      );
    } catch (e, r) {
      log.e("_sentryConfigureScope() -> e=$e, r=$r");
    }
  }

  Future<void> _saveUserDataOAuth() async {
    String email = userAttributesService.attributes.value?.email ?? '';

    if (email.isNotEmpty) {
      await localStorageService.initUser(email);
      await localStorageService.initThingShadow(email);
      await localStorageService.setEmail(email);
      // There is no password for logging in to OAuth, so after a successful
      // login, the information of the last login needs to be cleared
      await localStorageService.setPassword('');

      var user = await authService.getCurrentUser();
      await localStorageService.setUserId(user.userId);
      log.i("_saveUserDataOAuth() -> userId=${user.userId}");
    } else {
      log.e('_saveUserDataOAuth() -> email is empty.');
    }
  }

  Future<void> setKeepMeLoggedIn(bool value) async {
    keepMeLoggedIn.value = value;
    await localStorageService.setKeepMeLoggedIn(value);
  }

  Future<void> _handleLoginSuccess() async {
    if (!kDebugMode) {
      await _sentryConfigureScope();
    }

    if (!globalService.config.amplifyConfigured) {
      bool configured = await AmplifyUtils.reinitConfigure();
      globalService.config.amplifyConfigured = configured;
    }

    Get.offAllNamed(Routes.homeManagement);
  }
}
