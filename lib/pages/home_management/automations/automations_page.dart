import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';

class AutomationsPage extends StatefulWidget {

  const AutomationsPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _AutomationsPageState();
  }

}

class _AutomationsPageState extends State<AutomationsPage> with AutomaticKeepAliveClientMixin {

  final controller = Get.find<HomeManagementController>();

  @override
  Widget build(BuildContext context) {
    //必须调用 super 方法
    super.build(context);
    return Container(
      padding: const EdgeInsets.only(left: 36.0, right: 36.0),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(50),
              bottomRight: Radius.circular(50))
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

}