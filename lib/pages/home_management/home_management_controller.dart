import 'dart:async';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/push_notification_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:habi_app/utility/platform_utils.dart';

class HomeManagementController extends BaseController {
  final deviceShadowService = DeviceShadowService.to;
  final pushNotificationService = PushNotificationService.to;
  final demoModeService = DemoModeService.to;
  late List<String> appBarTitleList;
  late PageController pageController;
  AppLifecycleListener? appLifecycleListener;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  var currentIndex = 0.obs;
  var appBarTitle = 'navigationBarDevices'.tr.obs;

  @override
  void onInit() async{
    super.onInit();
    pageController = PageController(initialPage: 0);
    appBarTitleList = [];
    appBarTitleList.add('navigationBarDevices'.tr);
    // appBarTitleList.add('navigationBarAutomations'.tr);
    appBarTitleList.add('navigationBarNotifications'.tr);
    appBarTitleList.add('navigationBarSettings'.tr);
    appLifecycleListener = AppLifecycleListener(
      onResume: () {
        log.i('app resumed...');
        startDnssd();
      },
      onPause: () {
        log.i('app paused...');
        stopDnssd();
      },
      onDetach: () {
        log.i('app detached...');
      },
    );

    _checkAndListenNetworkStatus();

    deviceShadowService.startSync();

    if (!demoModeService.isDemo) {
      pushNotificationService.initializePushNotificationListeners();
      pushNotificationService.enableAppNotifications();
      pushNotificationService.updatePinpointNotifications();
    }
  }

  @override
  void onClose() {
    pageController.dispose();
    appLifecycleListener?.dispose();
    _connectivitySubscription?.cancel();
    deviceShadowService.stopSync();
    super.onClose();
  }

  void changeAppTitle(int index) {
    appBarTitle.value = appBarTitleList[index];
  }

  void _checkAndListenNetworkStatus() async {
    try {
      final Connectivity connectivity = Connectivity();
      final List<ConnectivityResult> connectivityResult = await connectivity.checkConnectivity();
      handleConnectivityResult(connectivityResult);
      _connectivitySubscription = connectivity.onConnectivityChanged
          .listen((List<ConnectivityResult> result) {
        // Received changes in available connectivity types!
        handleConnectivityResult(result);
      });
    } catch (e) {
      log.e('_checkAndListenNetworkStatus() -> error=$e');
    }
  }

  void handleConnectivityResult(List<ConnectivityResult> result) {
    // This condition is for demo purposes only to explain every connection type.
    // Use conditions which work for your requirements.
    if (result.contains(ConnectivityResult.mobile)) {
      // Mobile network available.
      log.i('handleConnectivityResult() -> mobile');
    } else if (result.contains(ConnectivityResult.wifi)) {
      // Wi-fi is available.
      // Note for Android:
      // When both mobile and Wi-Fi are turned on system will return Wi-Fi only as active network type
      log.i('handleConnectivityResult() -> wifi');
    } else if (result.contains(ConnectivityResult.ethernet)) {
      // Ethernet connection available.
      log.i('handleConnectivityResult() -> ethernet');
    } else if (result.contains(ConnectivityResult.vpn)) {
      // Vpn connection active.
      // Note for iOS and macOS:
      // There is no separate network interface type for [vpn].
      // It returns [other] on any device (also simulator)
      log.i('handleConnectivityResult() -> vpn');
    } else if (result.contains(ConnectivityResult.bluetooth)) {
      // Bluetooth connection available.
      log.i('handleConnectivityResult() -> bluetooth');
    } else if (result.contains(ConnectivityResult.other)) {
      // Connected to a network which is not in the above mentioned networks.
      log.i('handleConnectivityResult() -> other');
    } else if (result.contains(ConnectivityResult.none)) {
      // No available network types
      log.i('handleConnectivityResult() -> none');
    }
  }

  Future<void> startDnssd() async {
    await _performDnssdAction(true);
  }

  Future<void> stopDnssd() async {
    await _performDnssdAction(false);
  }

  Future<void> _performDnssdAction(bool isStart) async {
    if (!PlatformUtils.isAndroid) {
      log.i('_performDnssdAction() -> not Android');
      return;
    }
    try {
      String? thingGroup = await LocalStorageService.to.getThingGroup();
      if (thingGroup != null && thingGroup.isNotEmpty) {
        String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
        String? fabricId = await FabricHelper.getFabricId(mcThingName);
        if (fabricId != null && fabricId.isNotEmpty) {
          log.i('_performDnssdAction() -> fabricId=$fabricId');
          if (isStart) {
            await CtFlutterMatterPlugin.getInstance().startDnssd(fabricId);
          } else {
            await CtFlutterMatterPlugin.getInstance().stopDnssd(fabricId);
          }
        }
      }
    } catch (e, s) {
      log.e('_performDnssdAction() -> error=$e, stackTrace=$s');
    }
  }

}
