import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/system_modes.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_notifications.dart';
import 'package:habi_app/models/app_notifications_item.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/dynamo_db/push_notification.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/thing_name_utils.dart';
import 'package:intl/intl.dart';
import 'package:flutter_app_badge_control/flutter_app_badge_control.dart';

class NotificationsController extends GetxController {
  final deviceListService = DeviceListService.to;
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;

  HomeManagementController? homeManagementController;
  DateFormat dateFormat = DateFormat('dd MMMM-HH:mm:ss');
  var notificationList = <AppNotifications>[].obs;
  Worker? _worker;

  @override
  void onInit() {
    super.onInit();
    homeManagementController = Get.find<HomeManagementController>();
  }

  @override
  void onReady() {
    super.onReady();

    Future.delayed(Duration.zero, () {
      _worker?.dispose();
      _worker = everAll([
        deviceListService.deviceListChangedEvent,
        deviceShadowService.deviceShadowChangedEvent,
      ], (_) => loadNotifications(showLoading: false));
      loadNotifications();
    });
  }

  @override
  void onClose() {
    _worker?.dispose();
    super.onClose();
  }

  Future<void> loadNotifications({bool showLoading = true}) async {
    if (showLoading) {
      homeManagementController?.updateLoading(true);
    }
    try {
      final List<AppNotifications> thatNotificationList = [];
      String? thingGroup = await localStorageService.getThingGroup();

      log.i('loadNotifications => thingGroup: $thingGroup');

      if (thingGroup != null && thingGroup.isNotEmpty) {
        String gwThingName = thingGroup;

        Map<String, dynamic>? gwShadow = deviceShadowService.getDeviceShadow(gwThingName).thingShadow;

        if (gwShadow != null) {
          Sait85rGW sait85rGW = Sait85rGW.fromJson(gwShadow, gwThingName);
          String connected = sait85rGW.shadow?.state?.reported?.connected ?? 'false';
          int? connectedTimestamp =
              sait85rGW.shadow?.metadata?.reported?.connected;
          log.i('loadNotifications ->  GW connectedTimestamp: $connectedTimestamp');

          if (connectedTimestamp != null && connectedTimestamp > 0) {
            DateTime connectedDateTime =
                DateTime.fromMillisecondsSinceEpoch(connectedTimestamp * 1000, isUtc: true).toLocal();
            String connectedFormattedDate =
                dateFormat.format(connectedDateTime);
            log.i('loadNotifications -> GW connectedFormattedDate$connectedFormattedDate');
            List<String> connectedFormattedList =
                connectedFormattedDate.split('-');
            if (connectedFormattedList.length > 1) {
              String deviceName = sait85rGW.shadow?.state?.reported?.model
                      ?.properties?.sGateway?.deviceName ??
                  'Receiver';
              List<AppNotificationsItem> items = [];
              String date = connectedFormattedList[0];
              String time = connectedFormattedList[1];
              items.add(AppNotificationsItem(
                  time: time,
                  message: connected == 'true'
                      ? "$deviceName is online"
                      : "$deviceName is offline",
              ));
              thatNotificationList.add(AppNotifications(date: date, timestamp: connectedTimestamp, items: items));
            }
          }
        }

        List<String>? thingList = deviceListService.getDevicesByGatewayId(gwThingName);

        log.i('loadNotifications => thingList: $thingList');

        if (thingList != null && thingList.isNotEmpty) {
          for (String thingName in thingList) {

            Map<String, String> parsedResult = Sait85rHelper.parseThingName(thingName);
            log.i('loadNotifications -> parsedResult: $parsedResult');

            final deviceModel = ThingNameUtils.extractDeviceModel(thingName);

            if (deviceModel == 'MCTLR') {
              continue;
            }

            Map<String, dynamic>? deviceShadow = deviceShadowService.getDeviceShadow(thingName).thingShadow;

            if (deviceShadow != null) {
              IT850 it850 = IT850.fromJson(deviceShadow, thingName);
              String connected = it850.shadow?.state?.reported?.connected ?? 'false';
              String deviceName = it850.shadow?.state?.reported?.model
                      ?.properties?.sMdo?.deviceName ??
                  deviceModel;
              int systemMode = it850.shadow?.state?.reported?.model?.properties?.
                  sTherS?.systemMode ?? SystemModes.off;
              int? connectedTimestamp =
                  it850.shadow?.metadata?.reported?.connected;
              int? systemModeTimestamp =
                  it850.shadow?.metadata?.reported?.model?.properties?.sTherSTS?.systemMode;
              log.i(
                  'loadNotifications ->  Device connectedTimestamp: $connectedTimestamp');
              log.i(
                  'loadNotifications ->  Device systemModeTimestamp: $systemModeTimestamp');
              if (connectedTimestamp != null && connectedTimestamp > 0) {
                DateTime connectedDateTime = DateTime.fromMillisecondsSinceEpoch(
                    connectedTimestamp * 1000, isUtc: true).toLocal();
                String connectedFormattedDate = dateFormat.format(connectedDateTime);
                log.i(
                    'loadNotifications -> Dev connectedFormattedDate$connectedFormattedDate');
                List<String> connectedFormattedList = connectedFormattedDate.split('-');
                if (connectedFormattedList.length > 1) {
                  List<AppNotificationsItem> items = [];
                  String date = connectedFormattedList[0];
                  String time = connectedFormattedList[1];
                  items.add(AppNotificationsItem(
                    time: time,
                    message: connected == 'true'
                        ? "$deviceName is online"
                        : "$deviceName is offline",
                  ));
                  thatNotificationList
                      .add(AppNotifications(date: date, timestamp: connectedTimestamp, items: items));
                }
              }

              if (systemModeTimestamp != null && systemModeTimestamp > 0) {
                DateTime systemModeDateTime = DateTime.fromMillisecondsSinceEpoch(
                    systemModeTimestamp * 1000, isUtc: true).toLocal();
                String systemModeFormattedDate = dateFormat.format(systemModeDateTime);
                List<String> systemModeFormattedList = systemModeFormattedDate.split('-');
                if (systemModeFormattedList.length > 1) {
                  List<AppNotificationsItem> items = [];
                  String date = systemModeFormattedList[0];
                  String time = systemModeFormattedList[1];
                  items.add(AppNotificationsItem(
                    time: time,
                    message: systemMode == SystemModes.heat
                        ? "$deviceName is heating"
                        : "$deviceName is off",
                  ));
                  thatNotificationList
                      .add(AppNotifications(date: date, timestamp: systemModeTimestamp, items: items));
                }
              }
            }
          }
        }

        try {
          //TODO: Notifications handle from db
          List<PushNotification> notificationList = await DynamoDBService.to.fetchNotifications();
          notificationList = await DynamoDBService.to.updateReadNotifications(notificationList);
          FlutterAppBadgeControl.removeBadge();
        } catch (e) {
          log.e('loadNotifications() -> Notification handle error: $e');
        }
      }

      thatNotificationList.sort((a,b) => b.timestamp.compareTo(a.timestamp));

      notificationList.value = thatNotificationList;
      updateNotificationUI();
    } catch (e) {
      debugPrint('loadNotifications() -> Error: $e');
      log.i('loadNotifications() -> Error: $e');
    } finally {
      if (showLoading) {
        homeManagementController?.updateLoading(false);
      }
    }
  }

  void updateNotificationUI() {
    update();
  }

}