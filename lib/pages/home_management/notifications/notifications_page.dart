import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/pages/home_management/notifications/notifications_controller.dart';

class NotificationsPage extends StatefulWidget {

  const NotificationsPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _NotificationsPageState();
  }

}

class _NotificationsPageState extends State<NotificationsPage> with AutomaticKeepAliveClientMixin {

  final homeManagementController = Get.find<HomeManagementController>();
  final notificationsController = Get.find<NotificationsController>();
  late EasyRefreshController _easyRefreshController;

  @override
  void initState() {
    super.initState();
    _easyRefreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    _easyRefreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //必须调用 super 方法
    super.build(context);
    return Container(
      padding: const EdgeInsets.only(left: 36.0, right: 36.0),
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(50),
              bottomRight: Radius.circular(50))),
      child: EasyRefresh(
        controller: _easyRefreshController,
        header: const MaterialHeader(color: AppColors.ff01A796),
        footer: const MaterialFooter(color: AppColors.ff01A796),
        onRefresh: () async {
          await notificationsController.loadNotifications(showLoading: false);
          _easyRefreshController.finishRefresh();
          _easyRefreshController.resetFooter();
        },
        child: GetBuilder<NotificationsController>(
            builder: (controller) {
              log.i('build notifications page');
              if (notificationsController.notificationList.isEmpty) {
                return Container();
              }
              return buildContent(context);
            }
        ),
      ),
    );
  }

  Widget buildContent(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: notificationsController.notificationList.map((notification) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:[
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  notification.date,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
              Column(
                  children: notification.items.map((notificationItem) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            notificationItem.time,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.regular,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            ),
                          ),
                          Text(
                            notificationItem.message,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: AppFontWeights.regular,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList()
              )
            ],
          );
        }).toList(),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

}