import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/aws_cognito_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_device.dart';
import 'package:habi_app/pages/add_device/add_device_bindings.dart';
import 'package:habi_app/pages/add_device/add_device_page.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_bindings.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_page.dart';
import 'package:habi_app/pages/device_type/receiver/hot_water/hot_water_card.dart';
import 'package:habi_app/pages/device_type/thermostat/card/thermostat_card.dart';
import 'package:habi_app/pages/device_type/unknown/card/unknown_card.dart';
import 'package:habi_app/pages/home_management/devices/devices_controller.dart';
import 'package:habi_app/pages/home_management/devices/widgets/room_name_editor.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/pages/location_settings/location_settings_bindings.dart';
import 'package:habi_app/pages/location_settings/location_settings_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:permission_handler/permission_handler.dart';

class DevicesPageUIType {
  static const int loading = 0;
  static const int home = 1;
  static const int notReceiver = 2;
  static const int tryAgain = 3;
  int value = loading;

  DevicesPageUIType(this.value);

  factory DevicesPageUIType.copyLoading() {
    return DevicesPageUIType(DevicesPageUIType.loading);
  }

  factory DevicesPageUIType.copyHome() {
    return DevicesPageUIType(DevicesPageUIType.home);
  }

  factory DevicesPageUIType.copyNotReceiver() {
    return DevicesPageUIType(DevicesPageUIType.notReceiver);
  }

  factory DevicesPageUIType.copyTryAgain() {
    return DevicesPageUIType(DevicesPageUIType.tryAgain);
  }

  @override
  String toString() {
    return 'DevicesPageUIType(value: $value)';
  }

}

class DevicesPage extends StatefulWidget {

  const DevicesPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return DevicesPageState();
  }

}

class DevicesPageState extends State<DevicesPage> with AutomaticKeepAliveClientMixin {
  final demoModeService = DemoModeService.to;
  final deviceListService = DeviceListService.to;
  final devicesController = Get.find<DevicesController>();
  final homeManagementController = Get.find<HomeManagementController>();
  late EasyRefreshController _easyRefreshController;
  static const EdgeInsets _padding = EdgeInsets.symmetric(horizontal: 35);

  @override
  void initState() {
    super.initState();
    _easyRefreshController = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true,
    );
  }

  @override
  void dispose() {
    _easyRefreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //必须调用 super 方法
    super.build(context);
    return Container(
      height: double.infinity,
      decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(50),
              bottomRight: Radius.circular(50))
      ),
      child: EasyRefresh(
        header: const MaterialHeader(color: AppColors.ff01A796),
        footer: const MaterialFooter(color: AppColors.ff01A796),
        onRefresh: () async {
          await devicesController.fetchAllDataFromServer(
              showLoading: false,
              showTransparentLoading: true
          );
          _easyRefreshController.finishRefresh();
          _easyRefreshController.resetHeader();
        },
        child: GetBuilder<DevicesController>(
            builder: (controller) {
              DevicesPageUIType uiType = devicesController.uiType;
              // log.i('build devices page, ui type: $uiType');
              if (uiType.value == DevicesPageUIType.loading){
                return Padding(
                  padding: _padding,
                  child: _buildLoading(),
                );
              } else if (uiType.value == DevicesPageUIType.home){
                return _buildBody();
              } else if (uiType.value == DevicesPageUIType.notReceiver) {
                return Padding(
                  padding: _padding,
                  child: _buildNotReceiver(),
                );
              } else if (uiType.value == DevicesPageUIType.tryAgain){
                return Padding(
                  padding: _padding,
                  child: _buildTryAgain(),
                );
              } else {
                return const SizedBox();
              }
            }
        ),
      ),
    );
  }

  Widget _buildNotReceiver() {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 20,),

          Text(
            'allLocations'.tr,
            style: TextStyle(
              fontSize: 22,
              fontWeight: AppFontWeights.medium,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),

          const SizedBox(height: 20,),

          _buildAddButton(isAddReceiver: true),

          Expanded(child: SingleChildScrollView(child: Container())),

        ]
    );
  }

  Widget _buildTryAgain() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            devicesController.tryAgainErrorMsg,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          const SizedBox(height: 20,),
          ElevatedButton(
            onPressed: () {
              devicesController.fetchAllDataFromServer();
            },
            child: Text(
              'tryAgain'.tr,
              style: const TextStyle(
                  color: Colors.white
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoading() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Center(
          child: CircularProgressIndicator(
            color: AppColors.ff01A796,
          ),
        ),
        const SizedBox(height: 5,),
        Text(
          'loading...',
          style: TextStyle(
            fontSize: 16,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 10,),

        Padding(
          padding: _padding,
          child: _buildLocationBar(),
        ),

        const SizedBox(height: 6,),

        Expanded(
          child: devicesController.appHome != null ? _buildLocation()
              : SingleChildScrollView(child: Container()),
        ),

        const SizedBox(height: 20,),

        Padding(
          padding: _padding,
          child: _buildAddButton(),
        ),

        const SizedBox(height: 25,),

      ],
    );
  }

  Widget _buildAddButton({bool isAddReceiver = false}) {
    return InkWell(
      onTap: () => _requestPermission(isAddReceiver: isAddReceiver),
      child: Obx(() {
        return Container(
          width: double.infinity,
          height: 48,
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            color: AppColors.ff01A796,
            borderRadius: BorderRadius.all(Radius.circular(24)),
          ),
          child: devicesController.requestPermissions.value
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      AppImagePaths.add,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                      width: 18,
                      height: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isAddReceiver
                          ? 'addReceiver'.tr.toUpperCase()
                          : 'addDevice'.tr.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.medium,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
        );
      }),
    );
  }

  Widget _buildLocationBar() {
    return Row(
        children: [
          Expanded(
              flex: 1,
              child: Text(
                'allLocations'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.medium,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              )
          ),
          Expanded(
              flex: 1,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      _requestPermission(isAddReceiver: true);
                    },
                    child: Container(
                      color: Colors.transparent,
                      width: 50,
                      height: 30,
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: SvgPicture.asset(
                          AppImagePaths.addBlack,
                          width: 16,
                          height: 16,
                          colorFilter: ColorFilter.mode(
                              Theme.of(context)
                                  .extension<AppThemeExtension>()!
                                  .firstColor,
                              BlendMode.srcIn
                          ),
                        ),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(Routes.locations);
                    },
                    child: Container(
                      color: Colors.transparent,
                      width: 50,
                      height: 30,
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: SvgPicture.asset(
                          AppImagePaths.arrowRightBlack,
                          width: 16,
                          height: 16,
                          colorFilter: ColorFilter.mode(
                              Theme.of(context)
                                  .extension<AppThemeExtension>()!
                                  .firstColor,
                              BlendMode.srcIn
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              )
          )
        ]
    );
  }

  Widget _buildLocation() {
    String homeId = devicesController.appHome!.homeId!;
    String homeName = devicesController.appHome!.name!;
    List<String> deviceList = devicesController.appHome!.deviceList!;
    String receiverId = deviceList.first;

    return SingleChildScrollView(
        padding: _padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                    flex:4,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          homeName,
                          style: TextStyles.medium18FirstColor,
                        ),
                      ),
                    )
                ),

                const SizedBox(width: 11,),

                Expanded(
                  flex: 1,
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: IconButton(
                      icon: const Icon(
                          Icons.more_vert,
                          color: AppColors.ff01A796
                      ),
                      onPressed: () {
                        Get.to(() => LocationSettingsPage(),
                            routeName: LocationSettingsPage.routeName,
                            binding: LocationSettingsBindings(
                              homeId: homeId,
                              homeName: homeName,
                              thingName: receiverId,
                            ));
                      },
                    ),
                  ),
                ),
              ],
            ),

            if (devicesController.isHotWaterEnable) ...[
              const SizedBox(height: 10),
              HotWaterCard(thingName: receiverId),
            ],

            const SizedBox(height: 17.5),
            // Loop through rooms
            Column(
              children: devicesController.appHome!.rooms!.map<Widget>((room) {
                var deviceList = room.deviceList ?? [];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RoomNameEditor(
                      homeId: homeId,
                      room: room,
                    ),

                    const SizedBox(height: 10),

                    LayoutBuilder(
                      builder: (context, constraints) {
                        double screenWidth = constraints.maxWidth;
                        int crossAxisCount = screenWidth ~/ 150;
                        crossAxisCount = crossAxisCount < 2 ? 2 : crossAxisCount;
                        // double childAspectRatio = screenWidth / (crossAxisCount * 200);

                        return GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: crossAxisCount,
                              crossAxisSpacing: 10.0,
                              mainAxisSpacing: 15.0,
                              childAspectRatio: 1.1
                            // childAspectRatio: childAspectRatio, // Adjust ratio to fit your needs
                          ),
                          itemCount: deviceList.length,
                          itemBuilder: (context, deviceIndex) {
                            var deviceId = deviceList[deviceIndex];
                            var device = AppDevice.fromDeviceId(deviceId);
                            if ((device.vid == 'FFF1' && device.pid == '8000')
                                || (device.vid == '1527' && device.pid == '0001')
                                || (device.vid == '131B' && device.pid == '1A1B')
                                || (device.vid == '1078' && device.pid == '0001')
                            ) {
                              return ThermostatCard(
                                thingName: deviceId,
                                homeId: homeId,
                                roomId: room.roomId!,
                              );
                            } else if ((device.vid == 'FFF2' && device.pid == '8001')
                                || (device.vid == '1527' && device.pid == '0002')
                            ) {
                              return ThermostatCard(
                                thingName: deviceId,
                                homeId: homeId,
                                roomId: room.roomId!,
                              );
                            } else {
                              return UnknownCard(
                                thingName: deviceId,
                                homeId: homeId,
                                roomId: room.roomId!,
                              );
                            }
                          },
                        );
                      },
                    ),

                    const SizedBox(height: 30),
                  ],
                );
              }).toList(),
            ),
          ],
        )
    );
  }

  //申请权限
  Future<void> _requestPermission({bool isAddReceiver = false}) async {
    if (demoModeService.isDemo) {
      showSnackBar('featureNotAvailableInDemoMode'.tr);
      return;
    }

    if (devicesController.requestPermissions.value) {
      return;
    }

    devicesController.requestPermissions.value = true;

    if (PlatformUtils.isAndroid) {
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.location
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetoothScan] == PermissionStatus.granted
          && result[Permission.bluetoothConnect] == PermissionStatus.granted
          && result[Permission.location] == PermissionStatus.granted
      ) {
        await checkPermissionsAndNavigate(isAddReceiver);
      } else {
        log.i('_requestPermission() -> 权限不足');
        devicesController.requestPermissions.value = false;
        openAppSettings();
      }
    } else if (PlatformUtils.isIOS){
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetooth,
        Permission.locationWhenInUse,
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetooth] == PermissionStatus.granted
          && result[Permission.locationWhenInUse] == PermissionStatus.granted
      ) {
        await checkPermissionsAndNavigate(isAddReceiver);
      } else {
        log.i('_requestPermission() -> 权限不足');
        devicesController.requestPermissions.value = false;
        openAppSettings();
      }
    } else {
      showSnackBar('Platform not supported');
      devicesController.requestPermissions.value = false;
    }

  }

  Future<void> checkPermissionsAndNavigate(bool isAddReceiver) async {
    BluetoothAdapterState adapterState;

    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('requestPermission() -> 获取蓝牙状态=$adapterState');
    } catch (e, r) {
      log.e('requestPermission() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      showSnackBar('pleaseTurnOnBluetooth'.tr);
      devicesController.requestPermissions.value = false;
      return;
    }

    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      showSnackBar('pleaseTurnOnLocation'.tr);
      devicesController.requestPermissions.value = false;
      return;
    }

    devicesController.requestPermissions.value = false;

    if (isAddReceiver) {
      bool isAdminUser = await AwsCognitoHelper.isAdminUser();
      if (isAdminUser) {
        showRestrictAdminUsersDialog();
        return;
      }
      Get.to(() => AddReceiverPage(), binding: AddReceiverBindings());
    } else {
      String? thingGroup = await LocalStorageService.to.getThingGroup();
      if (thingGroup == null || thingGroup.isEmpty) {
        List<String> thingGroupList = deviceListService.gatewayIds;
        if (thingGroupList.isNotEmpty) {
          thingGroup = thingGroupList.first;
        }
      }

      if (thingGroup != null && thingGroup.isNotEmpty) {
        Get.to(() => AddDevicePage(), binding: AddDeviceBindings(thingName: thingGroup));
      } else {
        showNoReceiverDialog();
      }
    }
  }

  @override
  bool get wantKeepAlive => true;

  void showNoReceiverDialog() {
    showDialog(
        context: context,
        builder: (context){
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Text(
                'noReceiver'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                'noReceiverDesc'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                    'ok'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

  void showRestrictAdminUsersDialog() {
    showDialog(
        context: context,
        builder: (context){
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Text(
                'receiverAddLimitTitle'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                'receiverAddLimitMsg'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                    'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

}