import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/retry_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_room.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/services/dev_service.dart';

class _CustomException implements Exception {
  final String message;

  _CustomException(this.message);

  @override
  String toString() {
    return message;
  }
}

class RoomNameEditor extends StatefulWidget {
  final String homeId;
  final AppRoom room;

  const RoomNameEditor({
    super.key,
    required this.homeId,
    required this.room,
  });

  @override
  State<RoomNameEditor> createState() => _RoomNameEditorState();
}

class _RoomNameEditorState extends State<RoomNameEditor> {
  static const String _tag = '[RoomNameEditor]';

  TextEditingController? _textController;
  bool _isEditing = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    log.d('$_tag initialized');
  }

  @override
  void dispose() {
    log.d('$_tag disposed');
    _textController?.dispose();
    super.dispose();
  }

  void _startEditing() {
    if (_textController == null) {
      log.d('$_tag Starting editing');
      _textController = TextEditingController(text: widget.room.name);
    } else {
      log.d('$_tag Resuming editing');
      _textController!.text = widget.room.name ?? '';
    }
    setState(() {
      _isEditing = true;
    });
  }

  Future<List<HBRoom>> _getRooms(String homeId) async {
    return RetryHelper.execute<List<HBRoom>>(
      operation: () async {
        final result = await DevService.to
            .getHomeRooms(homeId)
            .timeout(const Duration(seconds: 30));
        if (!(result.success ?? false)) {
          throw Exception('Failed to get home rooms: ${result.errorCode}');
        }
        return result.rooms ?? [];
      },
      operationName: 'Get home rooms ',
      maxRetries: 1,
    );
  }

  Future<void> _changeRoomName({
    required String roomId,
    required String roomName,
  }) async {
    return RetryHelper.execute<void>(
      operation: () async {
        await DevService.to
            .changeRoomName(roomId, roomName)
            .timeout(const Duration(seconds: 15));
      },
      operationName: 'Change room name ',
      maxRetries: 1,
    );
  }

  Future<void> _saveRoomName() async {
    if (!mounted) {
      log.w('$_tag Save room name called after dispose');
      return;
    }

    final roomId = widget.room.roomId;
    final oldName = widget.room.name;
    final newName = _textController!.text.trim();
    log.d('$_tag New room name: $newName');
    if (newName.isEmpty || newName == oldName) {
      log.d('$_tag Room name is empty or unchanged');
      if (mounted) {
        setState(() {
          _isEditing = false;
        });
      }
      return;
    }

    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      log.d('$_tag Saving new room name: $roomId, $newName');

      final rooms = await _getRooms(widget.homeId);

      if (!mounted) return;

      bool isUnchanged = false;

      for (final room in rooms) {
        if (room.name == newName) {
          if (room.roomId == roomId) {
            log.d('$_tag Room name is unchanged');
            isUnchanged = true;
            break;
          } else {
            throw _CustomException('roomNameExists'.tr);
          }
        }
      }

      if (!isUnchanged) {
        await _changeRoomName(
          roomId: roomId!,
          roomName: newName,
        );
      }

      if (!mounted) return;

      widget.room.name = newName;

      showSuccessSnackBar('roomNameUpdated'.tr);
    } catch (e) {
      if (!mounted) return;

      log.e('$_tag Failed to update room name', error: e);
      if (e is _CustomException) {
        showErrorSnackBar(e.message);
      } else {
        showErrorSnackBar('failedToUpdateRoomName'.tr);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEditing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isEditing) {
      return _buildEditMode();
    } else {
      return _buildViewMode();
    }
  }

  Widget _buildViewMode() {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.room.name ?? '',
            style: TextStyles.medium18FirstColor,
          ),
        ),
        const SizedBox(width: 8),
        InkWell(
          onTap: _startEditing,
          child: _buildEditIcon(),
        ),
      ],
    );
  }

  Widget _buildEditMode() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            readOnly: _isLoading,
            controller: _textController,
            style: TextStyles.medium18FirstColor,
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 4,
              ),
            ),
            autofocus: true,
            onSubmitted: (_) => _saveRoomName(),
          ),
        ),
        const SizedBox(width: 8),
        if (_isLoading)
          _buildLoadingIndicator()
        else
          InkWell(
            onTap: _saveRoomName,
            child: _buildEditIcon(),
          ),
      ],
    );
  }

  Widget _buildEditIcon() {
    return SizedBox.square(
      dimension: 28,
      child: Center(
        child: SvgPicture.asset(
          AppImagePaths.edit,
          width: 22,
          height: 22,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const SizedBox.square(
      dimension: 28,
      child: Center(
        child: SizedBox.square(
          dimension: 22,
          child: CircularProgressIndicator(
            color: AppColors.ff01A796,
          ),
        ),
      ),
    );
  }
}
