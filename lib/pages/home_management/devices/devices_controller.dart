import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/extensions/sait85r_gw_extension.dart';
import 'package:habi_app/helpers/app_map_helper.dart';
import 'package:habi_app/helpers/device_ota_helper.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/load_home_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/dev/hb_version.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/Iot_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/utility/home_utils.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/widgets/device_ota_widget.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/models/app_room.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_provision_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/extensions/sait85r_mc_extension.dart';
import 'devices_page.dart';
// import 'package:habi_app/services/thing_shadow_service.dart';
// import 'package:aws_iot_data_api/iot-data-2015-05-28.dart';

class DevicesController extends GetxController {
  final deviceListService = DeviceListService.to;
  final globalService = GlobalService.to;
  final demoModeService = DemoModeService.to;
  final localStorageService = LocalStorageService.to;
  final deviceShadowService = DeviceShadowService.to;
  final homeManagementController = Get.find<HomeManagementController>();
  StreamSubscription? eventSubscription;
  StreamSubscription? routeSubscription;
  StreamSubscription? mqttThingShadowSubscription;
  bool needRefreshPage = false;
  AppHome? appHome;
  DevicesPageUIType uiType = DevicesPageUIType.copyLoading();
  var requestPermissions = false.obs;
  Set<String> leaveNetSet = {};
  String tryAgainErrorMsg = 'failedToLoadData'.tr;
  bool isHotWaterEnable = true;
  bool startDnssdFirst = false;
  bool isManualDelete = false;

  @override
  void onInit() {
    super.onInit();
    CtFlutterMatterPlugin.getInstance()
        .init(globalService.config.fabric.toMap());
  }

  @override
  void onReady() {
    super.onReady();
    setEventListener();
    setRouteListener();
    setMqttListener();
    Future(() async {
      await fetchAllDataFromServer();
    });
  }

  @override
  void onClose() {
    eventSubscription?.cancel();
    routeSubscription?.cancel();
    mqttThingShadowSubscription?.cancel();
    leaveNetSet.clear();

    Future(() async{
      String? thingGroup = await localStorageService.getThingGroup();
      if (thingGroup != null && thingGroup.isNotEmpty) {
        await shutdownMatterClient(thingGroup);
      }
    });

    super.onClose();
  }

  Future<bool> fetchAllDataFromServer({
    bool showLoading = true,
    bool showTransparentLoading = false
  }) async {
    log.i('fetchAllData -> start, showLoading:$showLoading, '
        'showTransparentLoading:$showTransparentLoading');

    appHome = null;
    updateUIType(DevicesPageUIType.copyLoading());

    if (showLoading){
      homeManagementController.updateLoading(true);
    }
    if (showTransparentLoading){
      homeManagementController.updateTransparentLoading(true);
    }

    try {
      await deviceListService.refresh()
          .timeout(const Duration(seconds: 120));

      List<String> allName = deviceListService.gatewayIds;
      if (allName.isNotEmpty) {
        String? thingGroup = await localStorageService.getThingGroup();
        log.i('fetchAllData -> localStorage thingGroup=$thingGroup');

        if (thingGroup == null || thingGroup.isEmpty) {
          await localStorageService.setThingGroup(allName.first);
          thingGroup = allName.first;
        } else {
          if (!allName.contains(thingGroup)) {
            await localStorageService.setThingGroup(allName.first);
            thingGroup = allName.first;
          }
        }

        log.i('fetchAllData -> current thingGroup=$thingGroup');

        await deviceListService.refreshGatewayDevices(thingGroup)
            .timeout(const Duration(seconds: 120));

        //这里要优先获取mc-shadow去初始化matter-client，否则会影响依赖matter功能的模块
        await fetchMcShadow(thingGroup)
            .timeout(const Duration(seconds: 120));

        LoadHomeHelper loadHomeHelper = LoadHomeHelper();
        appHome = await loadHomeHelper.load(thingGroup)
            .timeout(const Duration(seconds: 120));
        updateUIType(DevicesPageUIType.copyHome());

        log.i('fetchAllData -> current home: ${appHome?.name}');
      } else {
        await createUserRecord()
            .timeout(const Duration(seconds: 120));
        updateUIType(DevicesPageUIType.copyNotReceiver());
      }

      homeManagementController.updateLoading(false);
      homeManagementController.updateTransparentLoading(false);

      return true;
    } catch(e) {
      log.e('fetchAllData -> error: $e');
      updateUIType(DevicesPageUIType.copyTryAgain());
      homeManagementController.updateLoading(false);
      homeManagementController.updateTransparentLoading(false);

      if (e is TimeoutException) {
        tryAgainErrorMsg = 'loadDataTimeout'.tr;
      } else {
        tryAgainErrorMsg = 'failedToLoadData'.tr;
      }

      showErrorSnackBar(tryAgainErrorMsg);

      return false;
    }
  }

  Future<void> createUserRecord() async {
    try {
      final result = await DeviceProvisionService.to.createUserRecord();
      log.i('createUserRecord -> result: ${result.isSuccess()}');
    } catch(e) {
      log.e('createUserRecord -> error: $e');
    }
  }

  Future<void> fetchMcShadow(String gwThingName) async {
    try {
      log.i('fetchMcShadow() -> start: $gwThingName');
      String mcThingName = Sait85rHelper.getMBRCThingName(gwThingName);

      Map<String, dynamic> mcThingShadow = await deviceShadowService
          .fetchDeviceShadow(mcThingName);
      Sait85rMC sait85rMC = Sait85rMC.fromJson(mcThingShadow, mcThingName);

      bool thatHotWaterEnable = sait85rMC.isHotWaterEnable();
      if (thatHotWaterEnable != isHotWaterEnable) {
        isHotWaterEnable = thatHotWaterEnable;
      }

      log.i('fetchMcShadow() -> hotWaterEnable: $isHotWaterEnable');

      if (!demoModeService.isDemo) {
        await initMatterClient(sait85rMC, mcThingName);

        if (!startDnssdFirst) {
          startDnssdFirst = true;
          homeManagementController.startDnssd();
        }
      }
    } catch (e) {
      log.e('fetchMcShadow -> error: $e');
    }
  }


  Future<void> shutdownMatterClient(String gwThingName) async {
    try {
      String mcThingName = Sait85rHelper.getMBRCThingName(gwThingName);
      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId == null || fabricId.isEmpty) {
        log.e('shutdownMatterClient() -> fabricId = null');
        return;
      }
      CtFlutterMatterPlugin.getInstance()
          .shutdown(fabricId);
      log.i('shutdownMatterClient() -> succeeded');
    } catch (e) {
      log.e('shutdownMatterClient() -> failed: $e');
    }
  }

  Future<void> deleteCredentials(String gwThingName) async {
    try {
      String mcThingName = Sait85rHelper.getMBRCThingName(gwThingName);
      AppShadow appShadow = deviceShadowService.getDeviceShadow(mcThingName);
      Sait85rMC sait85rMC = Sait85rMC.fromJson(appShadow.thingShadow, mcThingName);

      if (PlatformUtils.isIOS) {
        String? extendedAddress = sait85rMC.shadow?.state?.reported?.model
            ?.properties?.sMcTlr?.thdExtendedAddress;
        if (extendedAddress != null && extendedAddress.isNotEmpty) {
          for (int i = 0; i < 3; i++) {
            try {
              await CtFlutterMatterPlugin.getInstance()
                  .deleteCredentials(extendedAddress)
                  .timeout(const Duration(seconds: 30));
              log.i('deleteCredentials() -> succeeded');
              break;
            } catch (e, s) {
              log.e('deleteCredentials() -> failed: $e, $s');
            }
          }
        } else {
          log.e('deleteCredentials() -> failed: thdExtendedAddress=null');
        }
      }

    } catch (e) {
      log.e('deleteCredentials() -> failed: $e');
    }
  }

  Future<void> clearLocalData(String gwThingName) async {
    String mcThingName = Sait85rHelper.getMBRCThingName(gwThingName);

    try {
      await localStorageService.clearThingShadow(gwThingName);
    } catch (e) {
      log.e('clearLocalData() -> clear thing data failed: $gwThingName $e');
    }

    try {
      await localStorageService.clearThingShadow(mcThingName);
    } catch (e) {
      log.e('clearLocalData() -> clear thing data failed: $mcThingName $e');
    }

    try {
      if (appHome != null) {
        await HomeUtils.removeCachedHome(appHome!);
      }
    } catch (e) {
      log.e('clearLocalData() -> clear home data failed: $e');
    }

    try {
      Map<String, dynamic>? allFabricMap = await localStorageService.getFabricMap();
      log.i('clearLocalData() -> before allFabricMap: $allFabricMap');
      if (allFabricMap != null && allFabricMap.isNotEmpty) {
        if (allFabricMap.containsKey(mcThingName)) {
          allFabricMap.remove(mcThingName);
          await localStorageService.setFabricMap(allFabricMap);
        }
      }
      log.i('clearLocalData() -> after allFabricMap: $allFabricMap');
    } catch (e) {
      log.e('clearLocalData() -> clear fabric data failed: $e');
    }

    try {
      Map<String, dynamic>? allWiFiMap = await localStorageService.getWiFiMap();
      log.i('clearLocalData() -> before allWiFiMap: $allWiFiMap');
      if (allWiFiMap != null && allWiFiMap.isNotEmpty) {
        if (allWiFiMap.containsKey(gwThingName)) {
          allWiFiMap.remove(gwThingName);
          await localStorageService.setWiFiMap(allWiFiMap);
        }
      }
      log.i('clearLocalData() -> after allWiFiMap: $allWiFiMap');
    } catch (e) {
      log.e('clearLocalData() -> clear wifi data failed: $e');
    }
  }

  Future<void> removeDeviceFromRoom(String deviceThingName) async {
    AppRoom? room;

    try {
      if (appHome != null) {
        if (appHome!.rooms != null && appHome!.rooms!.isNotEmpty) {
          room = appHome!.rooms!.firstWhere((room) {
            return room.deviceList!.contains(deviceThingName);
          });
        }
      }
    } catch (e) {
      log.e('removeDeviceFromRoom() -> find room failed: $e');
    }

    if (room == null || room.roomId == null || room.roomId!.isEmpty) {
      log.e('removeDeviceFromRoom() -> failed: room is null');
      return;
    }

    try {
      await DevService.to.removeDeviceFromRoom(room.roomId!, deviceThingName);
      log.i('removeDeviceFromRoom() -> succeeded');
    } catch (e) {
      log.e('removeDeviceFromRoom() -> failed: $e');
    }
  }

  Future<void> clearLocalDataByDevice(String gwThingName, String deviceThingName) async {
    try {
      await localStorageService.clearThingShadow(deviceThingName);
    } catch (e) {
      log.e('clearLocalData() -> clear thing data failed: $deviceThingName $e');
    }
  }

  void updateUIType(DevicesPageUIType uiType) {
    this.uiType = uiType;
    update();
  }

  void setEventListener() {
    eventSubscription = globalService.getEventStream().listen((event) {
      log.i('setEventListener() -> event=$event');
      switch(event.name) {
        case AppEvents.refreshDevicePage:
          needRefreshPage = true;
          break;
        case AppEvents.addReceiver:
          Future(() async {
            await fetchAllDataFromServer();
            await checkReceiverOTA();
          });
          break;
        case AppEvents.addDevice:
          Future(() async {
            await fetchAllDataFromServer();
          });
          break;
        case AppEvents.deleteReceiver:
          Future(() async {
            isManualDelete = false;
            await fetchAllDataFromServer();
            showDeletedLocationDialog();
          });
          break;
        case AppEvents.deleteDevice:
          Future(() async {
            isManualDelete = false;
            await fetchAllDataFromServer();
          });
          break;
        case AppEvents.prepareDeleteReceiver:
        case AppEvents.prepareDeleteDevice:
          isManualDelete = true;
          break;
        case AppEvents.changeHomeName:
        case AppEvents.changeLocation:
          Future(() async {
            await fetchAllDataFromServer();
          });
          break;
      }
    });
  }

  void setRouteListener() {
    routeSubscription = globalService.getRouteStream().listen((route) {
      log.i('setRouteListener() -> route=$route');
      if (route.name == Routes.homeManagement) {
        if (needRefreshPage == true) {
          needRefreshPage = false;
          Future(() async {
            await fetchAllDataFromServer();
          });
        }
      }
    });
  }

  void setMqttListener() {
    mqttThingShadowSubscription = globalService.getRealTimeMqttStream().listen((shadow) async {
      try {
        Map<String, dynamic>? reportedMap = shadow.thingShadow['state']?['reported'];
        if (reportedMap != null) {

          Map<String, dynamic>? propertiesMap = reportedMap[Sait85rMC.subId]?['properties'];
          if (propertiesMap != null) {
            String enableDHWKey = "ep0:sBoiler:EnableDHW";
            if (propertiesMap.containsKey(enableDHWKey)) {
              int enableDHWValue = propertiesMap[enableDHWKey];
              bool thatHotWaterEnable = enableDHWValue == 1;
              if (thatHotWaterEnable != isHotWaterEnable) {
                isHotWaterEnable = thatHotWaterEnable;
                log.i('setMqttListener() -> 刷新enableDHW...');
                updateUIType(DevicesPageUIType.copyHome());
              }
            }
          }

          if (isManualDelete) {
            log.i('setMqttListener() -> 检测到是手动删除，不需要处理监听删除逻辑...');
            return;
          }

          String leaveNetKey = 'ep0:sMDO:LeaveNetw';
          String leaveNetKeyForDevice = 'ep1:sMDO:LeaveNetw';

          bool isContainsLeaveNetKey = AppMapHelper.containsDeepKey(
              reportedMap, leaveNetKey);
          bool isContainsLeaveNetKeyForDevice = AppMapHelper.containsDeepKey(
              reportedMap, leaveNetKeyForDevice);

          if (isContainsLeaveNetKey || isContainsLeaveNetKeyForDevice) {
            log.i('setMqttListener() -> The Factory Reset command was received...');

            int leaveNetValue = 0;
            if (isContainsLeaveNetKey) {
              leaveNetValue = AppMapHelper.getDeepValueForKey(reportedMap,
                  leaveNetKey);
            } else if (isContainsLeaveNetKeyForDevice) {
              leaveNetValue = AppMapHelper.getDeepValueForKey(reportedMap,
                  leaveNetKeyForDevice);
            }

            if (leaveNetValue == 1) {
              await handleDeleteReceiverAndDevice(shadow);
            }
          }
        }
      } catch (e) {
        log.e('setMqttListener() -> parse reportedMap error: $e');
      }
    });
  }

  Future<void> handleDeleteReceiverAndDevice(AppShadow shadow) async {
    log.i('setMqttListener() -> leaveThingName: ${shadow.thingName}');
    log.i('setMqttListener() -> leaveNetSet: $leaveNetSet');

    bool isAdded = leaveNetSet.add(shadow.thingName);
    if (!isAdded) {
      log.i('setMqttListener() -> leaveNetSet already contains thingName...');
      return;
    }

    String? thingGroup = await localStorageService.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      log.i('setMqttListener() -> thingGroup is null or empty...');
      leaveNetSet.remove(shadow.thingName);
      return;
    }

    List<String>? thingList = deviceListService.getDevicesByGatewayId(thingGroup);
    if (thingList == null || thingList.isEmpty) {
      log.i('setMqttListener() -> thingList is null or empty...');
      leaveNetSet.remove(shadow.thingName);
      return;
    }

    String? leaveNetThing = thingList.firstWhere((thing) => shadow.thingName == thing,
      orElse: () => '',
    );

    log.i('setMqttListener() -> leaveNetThing=$leaveNetThing');
    if (leaveNetThing.isEmpty) {
      log.i('setMqttListener() -> leaveNetThing is null or empty...');
      leaveNetSet.remove(shadow.thingName);
      return;
    }

    log.i('setMqttListener() -> rawRoute=${Get.rawRoute}');
    if (Get.rawRoute != null) {
      String routeName = Get.rawRoute?.settings.name ?? '';
      log.i('setMqttListener() -> routeName=$routeName');
      if (routeName.isNotEmpty && routeName != Routes.homeManagement) {
        Get.until((route) {
          return route.settings.name == Routes.homeManagement;
        });
      }
    }

    log.i('setMqttListener() -> before leaveNetSet: $leaveNetSet');
    leaveNetSet.remove(shadow.thingName);
    log.i('setMqttListener() -> after leaveNetSet: $leaveNetSet');

    if (leaveNetThing.contains('MCTLR')) {
      log.i('setMqttListener() -> 检测到是删除Matter Controller');
      homeManagementController.updateLoading(true);
      // await shutdownMatterClient(thingGroup);
      await deleteCredentials(thingGroup);
      await clearLocalData(thingGroup);

      // thing没这么快删除，这里延迟一下
      Future.delayed(const Duration(seconds: 15), () async {
        await fetchAllDataFromServer(showLoading: false);
      });

      // 这种检测逻辑有问题，暂时注释掉
      // try {
      //   log.i('setMqttListener() -> checkThingGroup start.');
      //   await checkThingGroup(thingGroup);
      // } catch (e) {
      //   log.e('setMqttListener() -> checkThingGroup error: $e');
      // }
      // await fetchAllDataFromServer(showLoading: false);

    } else {
      log.i('setMqttListener() -> 检测到是删除Matter Device');
      homeManagementController.updateLoading(true);
      await clearLocalDataByDevice(thingGroup, leaveNetThing);
      await removeDeviceFromRoom(leaveNetThing);
      try {
        await checkThingList(leaveNetThing);
      } catch (e) {
        log.e('setMqttListener() -> checkThingList error: $e');
      }
      await fetchAllDataFromServer(showLoading: false);
    }
  }

  /*Future<void> checkThingGroup(String leaveNetThing) async {
    IotService service = IotService.to;
    bool isTimeout = false;
    await Future.doWhile(() async {
      if (isTimeout) {
        return false; // 停止检查
      }
      var thingGroups = await service.listThingGroups();
      log.i('checkThingGroup() -> thingGroups length=${thingGroups?.length}');
      if (thingGroups == null || thingGroups.isEmpty) {
        return false;
      }

      String thingGroupName = Sait85rHelper.getThingGroupName(leaveNetThing);
      log.i('checkThingGroup() -> thingGroupName=$thingGroupName');
      for  (var thingGroup in thingGroups) {
        if (thingGroup.groupName == thingGroupName) {
          log.i('checkThingGroup() -> thingGroup存在于服务器上，继续检查...');
          await Future.delayed(const Duration(seconds: 3)); // 等待3秒
          return true; // 继续检查
        }
      }

      log.i('checkThingGroup() -> thingGroup不存在于服务器上，删除成功！');
      return false; // 停止检查
    }).timeout(
      const Duration(seconds: 60),
      onTimeout: () {
        isTimeout = true;
        log.e('checkThingGroup() -> 检查超时（60秒）');
        throw TimeoutException('Check thing group timeout');
      },
    );
  }*/

  Future<void> checkThingList(String leaveNetThing) async {
    IotService service = IotService.to;
    bool isTimeout = false;
    await Future.doWhile(() async {
      if (isTimeout) {
        return false; // 停止检查
      }
      String? gwThingName = await localStorageService.getThingGroup();
      log.i('checkThingList() -> gwThingName=$gwThingName');
      if (gwThingName == null || gwThingName.isEmpty) {
        return false; // 停止检查
      }

      String thingGroupName = Sait85rHelper.getThingGroupName(gwThingName);
      log.i('checkThingList() -> thingGroupName=$thingGroupName');

      List<String>? things = await service.listThingsInThingGroup(thingGroupName);
      log.i('checkThingList() -> things=$things');

      if (things == null || things.isEmpty) {
        return false; // 停止检查
      }

      if (things.contains(leaveNetThing)) {
        log.i('checkThingList() -> thing还存在于服务器上，继续检查...');
        await Future.delayed(const Duration(seconds: 3)); // 等待3秒
        return true; // 继续检查
      } else {
        log.i('checkThingList() -> thing不存在于服务器上，删除成功！');
        return false; // 停止检查
      }
    }).timeout(
      const Duration(seconds: 60),
      onTimeout: () {
        isTimeout = true;
        log.e('checkThingList() -> 检查超时（60秒）');
        throw TimeoutException('Check thing list timeout');
      },
    );
  }

  Future<void> initMatterClient(Sait85rMC sait85rMC, String mcThingName) async {
    String fabricId = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";
    bool hasMatterClient = await CtFlutterMatterPlugin.getInstance().hasMatterClient(fabricId);
    if (!hasMatterClient) {
      await FabricHelper.checkFabric(sait85rMC, mcThingName);
    } else {
      log.e('initMatterClient() -> The matter client has been initialized!');
    }
  }

  void showDeletedLocationDialog() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return Dialog(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(28.0),
              ),
            ),
            insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                    borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: Text(
                          'locationDeleted'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.regular,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 30,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: OutlinedButton(
                            onPressed: () {
                              Get.back();
                            },
                            child: Text(
                              'close'.tr.toUpperCase(),
                              style: const TextStyle(
                                  color: AppColors.ff01A796
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 50,),
                    ],
                  ),
                ),
              ),
            ),

          );
        }
    );
  }


  Future<void> checkReceiverOTA() async {
    String? thingGroup = await localStorageService.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      log.e('checkReceiverOTA() -> thingGroup is empty!');
      return;
    }

    AppShadow shadow = deviceShadowService.getDeviceShadow(thingGroup);
    if (shadow.thingShadow.isEmpty) {
      log.e('checkReceiverOTA() -> thingShadow is empty!');
      return;
    }

    Sait85rGW sait85rGW = Sait85rGW.fromJson(
      shadow.thingShadow,
      thingGroup,
    );

    String? gatewaySoftwareVersion = sait85rGW.getGatewaySoftwareVersion();
    if (gatewaySoftwareVersion == null || gatewaySoftwareVersion.isEmpty) {
      log.e('checkReceiverOTA() -> gatewaySoftwareVersion is empty!');
      return;
    }

    DeviceOTAHelper deviceOTAHelper = DeviceOTAHelper();

    String deviceId = thingGroup;

    HbVersion currentVersion = HbVersion(
      model: 'SAIT85R',
      version: gatewaySoftwareVersion,
    );

    bool result = await deviceOTAHelper.checkAndUpdate(deviceId, currentVersion);
    log.i('checkReceiverOTA() -> result: $result');
    if (result) {
      showReceiverOTADialog(deviceId);
    }
  }

  void showReceiverOTADialog(String thingName) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return PopScope(
            canPop: false,
            child: Dialog(
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(28.0),
                ),
              ),
              insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
              child: DeviceOTAWidget(
                subId: Sait85rGW.subId,
                thingName: thingName,
                onFinish: () {
                  Get.back();
                  showOTASuccessDialog();
                },
              )
            ),
          );
        }
    );
  }

  void showOTASuccessDialog() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return Dialog(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(28.0),
              ),
            ),
            insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                    borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: Text(
                          'firmwareUpdateSuccessful'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.regular,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 30,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: OutlinedButton(
                            onPressed: () {
                              Get.back();
                            },
                            child: Text(
                              'close'.tr.toUpperCase(),
                              style: const TextStyle(
                                  color: AppColors.ff01A796
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 50,),
                    ],
                  ),
                ),
              ),
            ),

          );
        }
    );
  }

}