import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/home_management/devices/devices_page.dart';
import 'package:habi_app/pages/home_management/automations/automations_page.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/pages/home_management/notifications/notifications_page.dart';
import 'package:habi_app/pages/home_management/settings/settings_page.dart';

class HomeManagementPage extends BasePage<HomeManagementController> {
  const HomeManagementPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          return Text(
            controller.appBarTitle.value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          );
        }),
        automaticallyImplyLeading: false,
      ),
      backgroundColor: Theme.of(context)
          .bottomNavigationBarTheme
          .backgroundColor,
      body: PageView(
        physics: const NeverScrollableScrollPhysics(),
        controller: controller.pageController,
        children: const [DevicesPage(), NotificationsPage(), SettingsPage()],
      ),
      bottomNavigationBar: Obx(() => _getBottomNavigationBar(context)),
    );
  }

  List<BottomNavigationBarItem> _getNavigationBarItems(BuildContext context) {
    final List<BottomNavigationBarItem> items = [];

    final String devicesLabel = 'navigationBarDevices'.tr;
    final String automationsLabel = 'navigationBarAutomations'.tr;
    final String notificationsLabel = 'navigationBarNotifications'.tr;
    final String settingsLabel = 'navigationBarSettings'.tr;

    Color unselectedColor = Theme.of(context).bottomNavigationBarTheme.unselectedIconTheme!.color!;
    Color selectedColor = Theme.of(context).bottomNavigationBarTheme.selectedIconTheme!.color!;

    items.add(BottomNavigationBarItem(
        label: devicesLabel,
        icon: createNavigationBarIcon(AppImagePaths.navHome, unselectedColor),
        activeIcon: createNavigationBarIcon(AppImagePaths.navHome, selectedColor)
    ));
    // items.add(BottomNavigationBarItem(
    //     label: automationsLabel,
    //     icon: createNavigationBarIcon(AppImagePaths.navAutomations, unselectedColor),
    //     activeIcon: createNavigationBarIcon(AppImagePaths.navAutomations, selectedColor)
    // ));
    items.add(BottomNavigationBarItem(
        label: notificationsLabel,
        icon: createNavigationBarIcon(AppImagePaths.navNotifications, unselectedColor),
        activeIcon: createNavigationBarIcon(AppImagePaths.navNotifications, selectedColor)
    ));
    items.add(BottomNavigationBarItem(
        label: settingsLabel,
        icon: createNavigationBarIcon(AppImagePaths.navSettings, unselectedColor),
        activeIcon: createNavigationBarIcon(AppImagePaths.navSettings, selectedColor)
    ));

    return items;
  }

  Widget createNavigationBarIcon(String iconPath, Color color) {
    return SizedBox(
      width: 24,
      height: 31,
      child: Center(
        child: SvgPicture.asset(
          iconPath,
          colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
          width: 24,
          height: 24,
        ),
      ),
    );
  }

  Widget _getBottomNavigationBar(BuildContext context) {
    return BottomNavigationBar(
        items: _getNavigationBarItems(context),
        currentIndex: controller.currentIndex.value,
        onTap: (int index) {
          if (index == controller.currentIndex.value) {
            return;
          }
          controller.changeAppTitle(index);
          controller.currentIndex.value = index;
          controller.pageController.jumpToPage(index);
        }
    );
  }


}
