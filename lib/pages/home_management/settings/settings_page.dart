import 'dart:async';

import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/pages/about_this_app/about_this_app_bindings.dart';
import 'package:habi_app/pages/about_this_app/about_this_app_page.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/pages/home_management/settings/settings_controller.dart';
import 'package:habi_app/pages/personal_information/personal_information_bindings.dart';
import 'package:habi_app/pages/personal_information/personal_information_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:habi_app/pages/test/test_bindings.dart';
import 'package:habi_app/pages/test/test_page.dart';

class SettingsPage extends StatefulWidget {

  const SettingsPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return SettingsPageState();
  }

}

class SettingsPageState extends State<SettingsPage> with AutomaticKeepAliveClientMixin {

  final userAttributesService = UserAttributesService.to;
  final homeManagementController = Get.find<HomeManagementController>();
  final settingsController = Get.find<SettingsController>();

  @override
  Widget build(BuildContext context) {
    //必须调用 super 方法
    super.build(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 36.0),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(50),
          bottomRight: Radius.circular(50),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 33.h),
            Opacity(
              opacity: 0.5,
              child: Text(
                'accountSettings'.tr,
                style: TextStyles.medium16FirstColor,
              ),
            ),
            SizedBox(height: 20.h),
            InkWell(
              onTap: _onPersonalTap,
              child: Row(
                children: [
                  SizedBox(
                    width: 56.w,
                    height: 56.h,
                    child: const CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.white,
                      child: Icon(
                        Icons.person,
                        color: AppColors.ff1C1C1C,
                        size: 30,
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 39.h,
                          child: Obx(() {
                            String name = userAttributesService.name ?? '';
                            if (name.isEmpty) {
                              final email = userAttributesService.email ?? '';
                              if (email.isEmpty) {
                                return const SizedBox();
                              }
                              name = email.split('@')[0];
                            }
                            return FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                name,
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: AppFontWeights.regular,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!
                                      .firstColor,
                                ),
                              ),
                            );
                          }),
                        ),
                        SizedBox(
                          height: 20.h,
                          child: Obx(() {
                            final email = userAttributesService.email ?? '';
                            if (email.isEmpty) {
                              return const SizedBox();
                            }
                            return FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                email,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: AppFontWeights.regular,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!
                                      .firstColor,
                                ),
                              ),
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 46.w,
                    height: 59.h,
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Text(
                        '>',
                        style: TextStyle(
                          fontSize: 34,
                          fontWeight: AppFontWeights.light,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 43.h),
            Opacity(
              opacity: 0.5,
              child: Text(
                'appSettings'.tr,
                style: TextStyles.medium16FirstColor,
              ),
            ),
            _buildAppSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 10),
        /*_buildAppSettingsItem(
                      title: 'deviceUpdate',
                      rightWidget: _buildAppSettingsItemArrows(context)
                  ),*/
        _buildAppSettingsItem(
          title: 'aboutThisApp',
          rightWidget: _buildAppSettingsItemArrows(context),
        ),
        _buildAppSettingsItem(
          title: 'contactSupport',
          rightWidget: _buildAppSettingsItemArrows(context),
        ),
        _buildAppSettingsItem(
          title: 'privacyNotice',
          rightWidget: _buildAppSettingsItemArrows(context),
        ),
        _buildAppSettingsItem(
          title: 'termsConditions',
          rightWidget: _buildAppSettingsItemArrows(context),
        ),
        _buildAppSettingsItem(
          title: 'dataCollection',
          rightWidget: _buildAppSettingsItemArrows(context),
        ),
        Obx(() {
          return _buildAppSettingsItem(
            title: 'logout',
            rightWidget: settingsController.isSignOutLoading.value
                ? SizedBox(
                    height: 34,
                    child: Center(
                      child: SizedBox.square(
                        dimension: 24,
                        child: CircularProgressIndicator(
                          color: AppColorsHelper.firstColor,
                        ),
                      ),
                    ),
                  )
                : _buildAppSettingsItemArrows(context),
          );
        }),
        if (kDebugMode)
          _buildAppSettingsItem(
            title: 'test',
            rightWidget: _buildAppSettingsItemArrows(context),
          ),
        _buildHourFormatSelection(),
        _buildTemperatureUnitSelection(),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildAppSettingsItem({required String title, required Widget rightWidget}) {
    return Builder(
        builder: (context) {
          return InkWell(
            onTap: () => _onAppSettingsItemTap(title),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: Text(
                        title.tr,
                        style: TextStyles.medium18FirstColor,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 50,
                    child: Align(
                        alignment: Alignment.centerRight,
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: rightWidget,
                        )
                    )
                  )
                ],
              ),
            ),
          );
        }
    );
  }

  Widget _buildAppSettingsItemArrows(BuildContext context) {
    return Text(
      '>',
      style: TextStyle(
        fontSize: 34,
        height: 1,
        fontWeight: AppFontWeights.light,
        color: Theme.of(context).extension<AppThemeExtension>()!.firstColor,
      ),
    );
  }

  Widget _buildHourFormatSelection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'hourFormat'.tr,
              style: TextStyles.medium18FirstColor,
            ),
          ),
          Row(
            children: settingsController.hourFormatOptions.map((option) {
              return SizedBox(
                width: 60,
                child: Column(
                  children: [
                    Obx(() {
                      return SizedBox.square(
                        dimension: 30,
                        child: Radio<HourFormatStatus>(
                          value: option.value,
                          groupValue: userAttributesService.hourFormat,
                          onChanged: _onHourFormatChange,
                          fillColor: WidgetStateProperty.all(
                            AppColors.ff01A796,
                          ),
                        ),
                      );
                    }),
                    Text(
                      option.label.tr,
                      style: TextStyles.regular16FirstColor,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTemperatureUnitSelection() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'temperatureUnit'.tr,
              style: TextStyles.medium18FirstColor,
            ),
          ),
          Row(
            children: settingsController.temperatureUnitOptions.map((option) {
              return SizedBox(
                width: 60,
                child: Column(
                  children: [
                    Obx(() {
                      return SizedBox.square(
                        dimension: 30,
                        child: Radio<TemperatureUnitStatus>(
                          value: option.value,
                          groupValue: userAttributesService.temperatureUnit,
                          onChanged: _onTemperatureUnitChange,
                          fillColor: WidgetStateProperty.all(
                            AppColors.ff01A796,
                          ),
                        ),
                      );
                    }),
                    Text(
                      option.label.tr,
                      style: TextStyles.regular16FirstColor,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _onAppSettingsItemTap(String title) async {
    log.i('onAppSettingsItemTap() -> title=$title');
    if (title == 'aboutThisApp') {
      _onAboutThisAppTap();
    } else if (title == 'contactSupport') {
      _onContactSupportTap();
    } else if (title == 'privacyNotice') {
      _onPrivacyNoticeTap();
    } else if (title == 'termsConditions') {
      _onTermsConditionsTap();
    } else if (title == 'dataCollection') {
      _onDataCollectionTap();
    } else if (title == 'logout') {
      await settingsController.signOut();
    } else if (title == 'test') {
      Get.to(() => TestPage(), binding: TestBindings());
    }
  }

  void _onPersonalTap() {
    Get.to(
      () => const PersonalInformationPage(),
      binding: PersonalInformationBindings(),
    );
  }

  void _onAboutThisAppTap() {
    Get.to(() => const AboutThisAppPage(), binding: AboutThisAppBindings());
  }

  void _onContactSupportTap() {
    Future(() async {
      try {
        GlobalService service = GlobalService.to;
        if (!await launchUrlString(service.config.supportUrl, mode: LaunchMode.externalApplication)) {
          showErrorSnackBar('Could not launch ${service.config.supportUrl}');
        }
      } catch (e) {
        showErrorSnackBar('Could not launch $e');
      }
    });
  }

  void _onPrivacyNoticeTap() {
    Future(() async {
      try {
        GlobalService service = GlobalService.to;
        if (!await launchUrlString(service.config.privacyUrl, mode: LaunchMode.externalApplication)) {
          showErrorSnackBar('Could not launch ${service.config.privacyUrl}');
        }
      } catch (e) {
        showErrorSnackBar('Could not launch $e');
      }
    });
  }

  void _onTermsConditionsTap() {
    Future(() async {
      try {
        GlobalService service = GlobalService.to;
        if (!await launchUrlString(service.config.termsUrl, mode: LaunchMode.externalApplication)) {
          showErrorSnackBar('Could not launch ${service.config.termsUrl}');
        }
      } catch (e) {
        showErrorSnackBar('Could not launch $e');
      }
    });
  }

  void _onDataCollectionTap() {
    Get.toNamed(Routes.dataCollection);
  }

  void _onHourFormatChange(HourFormatStatus? hourFormat) async {
    log.i('onHourFormatChange() -> hourFormat=$hourFormat');

    try {
      if (hourFormat == null) {
        throw Exception('Invalid hour format');
      }

      DialogUtils.showLoadingDialog(message: "changing...");

      await userAttributesService
          .updateHourFormat(hourFormat.value)
          .timeout(const Duration(seconds: 30));

      if (userAttributesService.hourFormat != hourFormat) {
        throw Exception('Failed to update hour format');
      }

      if (Get.isDialogOpen == true) {
        Get.back();
      }
    } catch (e) {
      log.e('onHourFormatChange() -> e=$e');

      if (Get.isDialogOpen == true) {
        Get.back();
      }

      _handleError(e);
    }
  }

  void _onTemperatureUnitChange(TemperatureUnitStatus? temperatureUnit) async {
    log.i('onTemperatureUnitChange() -> temperatureUnit=$temperatureUnit');

    try {
      if (temperatureUnit == null) {
        throw Exception('Invalid temperature unit');
      }

      DialogUtils.showLoadingDialog(message: "changing...");

      await userAttributesService
          .updateTemperatureUnit(temperatureUnit.value)
          .timeout(const Duration(seconds: 30));

      if (userAttributesService.temperatureUnit != temperatureUnit) {
        throw Exception('Failed to update temperature unit');
      }

      GlobalService.to
          .getEventStreamController()
          .add(AppEvent(name: AppEvents.changeTemperatureUnit, data: temperatureUnit));

      if (Get.isDialogOpen == true) {
        Get.back();
      }
    } catch (e) {
      log.e('onTemperatureUnitChange() -> e=$e');

      if (Get.isDialogOpen == true) {
        Get.back();
      }

      _handleError(e);
    }
  }

  void _handleError(e) {
    if (e is NetworkException) {
      DialogUtils.showErrorDialog(content: 'networkError'.tr);
    } else if (e is TimeoutException) {
      DialogUtils.showErrorDialog(content: 'timeoutError'.tr);
    } else {
      DialogUtils.showErrorDialog(
        paragraphs: ['requestFailed'.tr, e.toString()],
      );
    }
  }

  @override
  bool get wantKeepAlive => true;

}