import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/option_item.dart';
import 'package:habi_app/pages/login/login_bindings.dart';
import 'package:habi_app/pages/login/login_page.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/local_storage_service.dart';

class SettingsController extends GetxController {
  final localStorageService = LocalStorageService.to;
  var isSignOutLoading = false.obs;

  final List<OptionItem<HourFormatStatus>> hourFormatOptions = [
    const OptionItem(
      value: HourFormatStatus.format24h,
      label: 'hourFormat24',
    ),
    const OptionItem(
      value: HourFormatStatus.format12h,
      label: 'hourFormat12',
    ),
  ];

  final List<OptionItem<TemperatureUnitStatus>> temperatureUnitOptions = [
    const OptionItem(
      value: TemperatureUnitStatus.celsius,
      label: 'temperatureUnitC',
    ),
    const OptionItem(
      value: TemperatureUnitStatus.fahrenheit,
      label: 'temperatureUnitF',
    ),
  ];

  Future<void> signOut() async {
    if (isSignOutLoading.value) {
      return;
    }

    isSignOutLoading.value = true;

    try {
      SignOutResult result = await AuthService.to.signOut()
          .timeout(const Duration(seconds: 60));
      if (result is CognitoCompleteSignOut) {
        showSuccessSnackBar('sign out success.');
        await localStorageService.setKeepMeLoggedIn(false);
        Get.offAll(
          () => LoginPage(),
          routeName: LoginPage.routeName,
          binding: LoginBindings(),
        );
      } else {
        showErrorSnackBar('sign out failed.');
      }
    } catch (e, r) {
      log.e("signOut() -> e=$e, r=$r");
      showErrorSnackBar('sign out failed.');
    } finally {
      isSignOutLoading.value = false;
    }
  }
}
