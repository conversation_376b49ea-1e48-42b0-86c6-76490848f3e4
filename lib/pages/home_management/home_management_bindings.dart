import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/home_management/devices/devices_controller.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/pages/home_management/notifications/notifications_controller.dart';
import 'package:habi_app/pages/home_management/settings/settings_controller.dart';

class HomeManagementBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => HomeManagementController());
    Get.lazyPut(() => DevicesController());
    Get.lazyPut(() => NotificationsController());
    Get.lazyPut(() => SettingsController());
  }
}
