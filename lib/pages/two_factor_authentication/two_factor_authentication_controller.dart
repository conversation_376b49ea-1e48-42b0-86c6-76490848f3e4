import 'dart:async';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/pages/reset_password/reset_password_bindings.dart';
import 'package:habi_app/pages/reset_password/reset_password_page.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_page.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/login/login_bindings.dart';
import 'package:habi_app/pages/login/login_page.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class TwoFactorAuthenticationController extends GetxController {
  final GlobalKey codeGlobalKey = GlobalKey();
  final TextEditingController codeController = TextEditingController();
  var isLoading = false.obs;
  final isResending = false.obs;
  Completer<void>? _resendCompleter;

  void _cancelResendOperation() {
    if (_resendCompleter?.isCompleted == false) {
      _resendCompleter?.completeError(Exception('canceled'));
    }
  }

  @override
  void onClose() {
    _cancelResendOperation();
    super.onClose();
    codeController.dispose();
  }

  Future<void> next(String email, int authType) async {
    if (authType == TwoFactorAuthenticationPage.authTypeResetPassword) {
      _cancelResendOperation();
      Get.to(
        () => ResetPasswordPage(
          email: email,
          confirmationCode: codeController.text,
        ),
        binding: ResetPasswordBindings(),
      );
      return;
    }

    await confirmUser(email, authType);
  }

  Future<void> confirmUser(String email, int authType) async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      SignUpResult result = await AuthService.to
          .confirmUser(username: email, confirmationCode: codeController.text)
          .timeout(const Duration(seconds: 30));

      log.i("confirmUser() -> signUpStep=${result.nextStep.signUpStep}");

      switch (result.nextStep.signUpStep) {
        case AuthSignUpStep.done:
          log.i("confirmUser() -> done");
          if (authType == TwoFactorAuthenticationPage.authTypeSignIn) {
            Get.back();
          } else {
            Get.to(
              () => LoginPage(
                showBackIcon: true,
              ),
              binding: LoginBindings(),
              routeName: LoginPage.routeName,
            );
          }
          break;
        default:
          log.i("confirmUser() -> default");
          break;
      }
    } on CodeMismatchException catch (e, r) {
      log.e("confirmUser() -> CodeMismatchException{} -> e=$e, r=$r");
      showErrorSnackBar('emailAddressHasAlreadyBeenTaken'.tr);
    } on TimeoutException catch (e, r) {
      log.e("confirmUser() -> NetworkException{} -> e=$e, r=$r");
      showErrorSnackBar('timeoutError'.tr);
    } on NetworkException catch (e, r) {
      log.e("confirmUser() -> NetworkException{} -> e=$e, r=$r");
      showErrorSnackBar('networkError'.tr);
    } on Exception catch (e, r) {
      log.e("confirmUser() -> Exception{} -> e=$e, r=$r");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> resendCode(String email, int authType) async {
    if (isResending.value) {
      return;
    }

    try {
      isResending.value = true;
      if (authType == TwoFactorAuthenticationPage.authTypeResetPassword) {
        await _resendForgotPasswordCode(email);
      } else {
        await resendSignUpCode(email);
      }
    } catch (e, r) {
      log.e("resendCode() -> Exception{} -> e=$e, r=$r");
    } finally {
      isResending.value = false;
    }
  }

  Future<void> _resendForgotPasswordCode(String email) {
    final completer = Completer<void>();
    _resendCompleter = completer;

    Future.delayed(Duration.zero, () async {
      try {
        await AuthService.to
            .forgotPassword(email)
            .timeout(const Duration(seconds: 30));
        if (!completer.isCompleted) {
          completer.complete();
        }
      } on NetworkException catch (e, r) {
        log.e(
          "_resendForgotPasswordCode() -> NetworkException{} -> e=$e, r=$r",
        );
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(content: 'networkError'.tr);
          completer.completeError(e);
        }
      } on TimeoutException catch (e, r) {
        log.e(
          "_resendForgotPasswordCode() -> TimeoutException{} -> e=$e, r=$r",
        );
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(content: 'timeoutError'.tr);
          completer.completeError(e);
        }
      } catch (e, r) {
        log.e(
          "_resendForgotPasswordCode() -> Exception{} -> e=$e, r=$r",
        );
        if (!completer.isCompleted) {
          DialogUtils.showErrorDialog(
            paragraphs: ['requestFailed'.tr, e.toString()],
          );
          completer.completeError(e);
        }
      }
    });

    return completer.future;
  }

  Future<void> resendSignUpCode(String username) async {
    await AuthService.to.resendSignUpCode(username: username);
  }
}
