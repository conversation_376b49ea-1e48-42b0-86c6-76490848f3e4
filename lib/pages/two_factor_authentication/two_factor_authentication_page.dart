import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/two_factor_authentication/two_factor_authentication_controller.dart';
import 'package:habi_app/widgets/expanded_scroll_view.dart';
import 'package:pinput/pinput.dart';

class TwoFactorAuthenticationPage
    extends GetView<TwoFactorAuthenticationController> {
  static const int authTypeSignIn = 0;
  static const int authTypeSignUp = 1;
  static const int authTypeResetPassword = 2;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final String email;
  final int authType;

  TwoFactorAuthenticationPage({
    super.key,
    required this.email,
    required this.authType,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'twoFactorAuthentication'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context).extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: ExpandedScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        topContent: Column(
          children: [
            SizedBox(height: 107.h),
            Text(
              textAlign: TextAlign.center,
              'sendConfirmationCodeToEmail'.trParams({'email': email}),
              style: TextStyle(
                fontSize: 18,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),
            SizedBox(height: 37.h),
            _buildForm(context),
            SizedBox(height: 37.h),
            Obx(
              () => InkWell(
                onTap: controller.isResending.value ? null : _onResend,
                child: SizedBox(
                  width: double.infinity,
                  child: Center(
                    child: controller.isResending.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: AppColors.ff01A796,
                            ),
                          )
                        : Text(
                            'resend'.tr,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: AppFontWeights.regular,
                              color: AppColors.ff01A796,
                            ),
                          ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 37.h),
          ],
        ),
        bottomContent: Column(
          children: [
            SizedBox(
              width: double.infinity,
              height: 48,
              child: Obx(() {
                return FilledButton(
                  onPressed: _onContinue,
                  child: controller.isLoading.value
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          'continue'.tr,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .secondColor,
                          ),
                        ),
                );
              }),
            ),
            SizedBox(height: 113.h),
          ],
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Opacity(
          opacity: 0.5,
          child: Text(
            'email'.tr,
            style: TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.medium,
              color:
                  Theme.of(context).extension<AppThemeExtension>()!.firstColor,
            ),
          ),
        ),
        SizedBox(height: 14.h),
        Form(
          key: _formKey,
          child: Pinput(
            length: 6,
            controller: controller.codeController,
            keyboardType: TextInputType.text,
            defaultPinTheme: PinTheme(
              width: 48,
              height: 48,
              textStyle: TextStyle(
                fontSize: 20,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: AppColors.ff01A796),
              ),
            ),
            errorPinTheme: PinTheme(
              width: 48,
              height: 48,
              textStyle: const TextStyle(
                fontSize: 20,
                fontWeight: AppFontWeights.regular,
                color: AppColors.ffFF4E4E,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: AppColors.ffFF4E4E),
              ),
            ),
            errorTextStyle: const TextStyle(
              color: AppColors.ffFF4E4E,
            ),
            onCompleted: _onCompleted,
            validator: _codeValidator,
          ),
        ),
      ],
    );
  }

  void _onContinue() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.next(email, authType);
  }

  void _onCompleted(String? value) {}

  void _onResend() async {
    await controller.resendCode(email, authType);
  }

  String? _codeValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 6) {
      return 'requiredField'.tr;
    }

    return null;
  }
}
