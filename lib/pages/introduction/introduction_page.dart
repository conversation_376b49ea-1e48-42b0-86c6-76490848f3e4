import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/introduction/introduction_controller.dart';
import 'package:habi_app/pages/welcome/welcome_page.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/widgets/habi_logo.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class IntroductionPage extends GetView<IntroductionController> {

  final PageController pageController = PageController(initialPage: 0);
  static const int pageCount = 2;

  IntroductionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const PreferredSize(preferredSize: Size.fromHeight(0), child: Si<PERSON><PERSON><PERSON>()),
      body: Stack(
        children: [
          PageView(
            controller: pageController,
            children: [createFirstPage(context), createSecondPage(context)],
          ),

          Positioned(
            top: 670.h,
            left: 0,
            right: 0,
            bottom: 233.h,
            child: SizedBox(
              width: double.infinity,
              child: Center(
                child: SmoothPageIndicator(
                    controller: pageController,  // PageController
                    count:  2,
                    effect: const WormEffect(
                      dotWidth: 10,
                      dotHeight: 10,
                      activeDotColor: AppColors.ff01A796,
                      dotColor: AppColors.ffA3E4D7,
                    ),  // your preferred effect
                    onDotClicked: (index){

                    }
                ),
              ),
            ),
          )
        ]
      ),
    );
  }

  Widget createFirstPage(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 36),
      child: Column(
        children: [

          SizedBox(height: 34.h),

          HabiLogo(
            height: 47.h,
            width: 80.w,
          ),

          SizedBox(height: 46.h),

          SizedBox(
            height: 372.h,
            child: Center(
              child: Image.asset(
                  AppImagePaths.deviceCardThree,
                  width: 338,
                  height: 372
              ),
            ),
          ),

          SizedBox(
            height: 56.h,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'welcomeTitle'.tr,
                style: const TextStyle(
                  fontSize: 40,
                  fontWeight: AppFontWeights.regular,
                  color: AppColors.ff01A796,
                ),
              ),
            ),
          ),

          SizedBox(height: 18.h,),

          SizedBox(
            height: 49.h,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'welcomeBody'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
            ),
          ),

          SizedBox(height: 140.h,),

          _buildButtons(context),

          SizedBox(height: 85.h,),
        ],
      ),
    );
  }

  Widget createSecondPage(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 36),
      child: Column(
        children: [

          SizedBox(height: 34.h),

          HabiLogo(
            height: 47.h,
            width: 80.w,
          ),

          SizedBox(height: 46.h),

          SizedBox(
            height: 372.h,
            child: Center(
              child: SizedBox(
                width: 338.w,
                height: 372.h,
                child: Stack(
                  children: [
                    Positioned(
                      right: 0,
                      bottom: 43.h,
                      child: Image.asset(
                          AppImagePaths.livingRoom,
                          width: 254.w,
                          height: 272.h
                      ),
                    ),
                    Positioned(
                      left: 0,
                      top: 0,
                      child: Image.asset(
                          AppImagePaths.deviceCardTwo,
                          width: 200.w,
                          height: 218.h
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          SizedBox(
            height: 56.h,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'welcomeTitle'.tr,
                style: const TextStyle(
                  fontSize: 40,
                  fontWeight: AppFontWeights.regular,
                  color: AppColors.ff01A796,
                ),
              ),
            ),
          ),

          SizedBox(height: 18.h,),

          SizedBox(
            height: 49.h,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'welcomeBody'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
            ),
          ),

          SizedBox(height: 140.h,),

          _buildButtons(context),

          SizedBox(height: 85.h,),

        ],
      ),
    );
  }

  Widget _buildButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [

        SizedBox(
          width: 98,
          height: 48,
          child: OutlinedButton(
            onPressed: () {
              _jumpToWelcomePage();
            },
            child: Text(
              'skip'.tr.toUpperCase(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.bold,
                color: AppColors.ff01A796,
              ),
            ),
          ),
        ),

        SizedBox(
          width: 98,
          height: 48,
          child: FilledButton(
            onPressed: () {
              double currentPage = (pageController.page ?? 0) + 1;
              if (currentPage == pageCount) {
                _jumpToWelcomePage();
              } else {
                pageController.nextPage(
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut
                );
              }
            },
            child: Text(
              'next'.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.bold,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.secondColor,
              ),
            ),
          ),
        ),


      ],
    );
  }

  void _jumpToWelcomePage() {
    Future.delayed(Duration.zero, () async {
      await LocalStorageService.to.setFirstStartUp(false);
      Get.to(() => const WelcomePage());
    });
  }

}



