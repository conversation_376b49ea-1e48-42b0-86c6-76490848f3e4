import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/location_settings/location_settings_controller.dart';

class LocationSettingsBindings extends Bindings {

  final String homeId;
  final String homeName;
  final String thingName;

  LocationSettingsBindings({
    required this.homeId,
    required this.homeName,
    required this.thingName
  });

  @override
  void dependencies() {
    Get.lazyPut(() => LocationSettingsController(
      homeId: homeId,
      homeName: homeName,
      thingName: thingName
    ));
  }
}
