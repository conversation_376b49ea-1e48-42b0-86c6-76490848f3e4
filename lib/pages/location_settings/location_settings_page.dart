import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/location_settings/location_settings_controller.dart';
import 'package:habi_app/widgets/change_receiver_wifi_widget.dart';
import 'package:habi_app/widgets/delete_location_widget.dart';
import 'package:habi_app/widgets/delete_receiver_choice_widget.dart';
import 'package:habi_app/widgets/wifi_signal_strength_v2.dart';

class LocationSettingsPage extends BasePage<LocationSettingsController> {

  static const String routeName = '/locationSettings';
  static const String settingsId = 'settings';
  static const String isHotWaterEnabledId = 'isHotWaterEnabled';
  static const String wifiRssiId = 'wifiRssi';
  static const String nodeId = 'nodeId';
  static const String thingNameId = 'thingName';
  static const String softwareVersionId = 'softwareVersion';
  static const String timeZoneId = 'timeZone';
  static const String wifiSSIDId = 'wifiSSID';

  LocationSettingsPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'locationSettings'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: _buildBody(context)
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLocationName(context),

            SizedBox(height: 40.h),

            GetBuilder<LocationSettingsController>(
              id: settingsId,
              builder: (controller) {
                return _buildLocationSettings(context);
              },
            ),

            SizedBox(height: 51.h),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationName(BuildContext context) {
    return SizedBox(
      height: 56.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [

          SizedBox(
            width: 56.w,
            height: 56.h,
            child: Center(
              child: SvgPicture.asset(
                AppImagePaths.locationSettings,
                width: 56,
                height: 56,
              ),
            ),
          ),

          Expanded(
            child: Center(
              child: Obx(() {
                if (controller.editName.value) {
                  return ConstrainedBox(
                    constraints: const BoxConstraints(
                        maxHeight: 148,
                        minHeight: 48
                    ),
                    child: TextFormField(
                      onFieldSubmitted: (value) {
                        log.i('onFieldSubmitted() -> value: $value');
                        controller.editName.value = false;
                        controller.changeName(value);
                      },
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.regular,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      ),
                      controller: controller.editNameController,
                    ),
                  );
                }

                return Text(controller.locationName.value,
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: AppFontWeights.regular,
                      overflow: TextOverflow.ellipsis,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    )
                );
              }),
            ),
          ),

          SizedBox(
            width: 56.w,
            height: 56.h,
            child: InkWell(
              onTap: () {
                controller.editName.value = !controller.editName.value;
              },
              child: Center(
                child: SvgPicture.asset(
                  AppImagePaths.edit,
                  width: 22,
                  height: 22,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSettings(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Opacity(
          opacity: 0.5,
          child: Text(
              'timeZone'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),

        SizedBox(height: 14.h),

        GetBuilder<LocationSettingsController>(
          id: timeZoneId,
          builder: (controller) {
            return _buildTimeZoneDropdown();
          }
        ),

        SizedBox(height: 15.h),

        Text(
            'receiversSetting'.tr,
            style: TextStyle(
              fontSize: 18,
              fontWeight: AppFontWeights.medium,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            )
        ),

        SizedBox(height: 15.h),

        Opacity(
          opacity: 0.5,
          child: Text(
              '2ndRelayFunction'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),

        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                  'hotWaterControl'.tr,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  )
              ),
            ),
            Expanded(
              flex: 1,
              child: Align(
                alignment: Alignment.centerRight,
                child: GetBuilder<LocationSettingsController>(
                  id: isHotWaterEnabledId,
                  builder: (controller) {
                    return Switch(
                      value: controller.isHotWaterEnabled,
                      onChanged: (value) {
                        controller.changeHotWaterEnable(value);
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 10.h),

        Opacity(
          opacity: 0.5,
          child: Text(
              'wifiSsid'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),

        SizedBox(height: 12.h,),

        InkWell(
          onTap: _onWiFiSSIDTap,
          child: Row(
            children: [
              Expanded(
                  flex: 3,
                  child: GetBuilder<LocationSettingsController>(
                    id: wifiSSIDId,
                    builder: (controller) {
                      return Text(
                        controller.networkSSIDF,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      );
                    }
                  )
              ),
              Expanded(
                flex: 1,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Icon(
                    Icons.settings,
                    size: 18.0,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 15.h),

        Opacity(
          opacity: 0.5,
          child: Text(
              'networkStrength'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),

        SizedBox(height: 12.h,),

        GetBuilder<LocationSettingsController>(
            id: wifiRssiId,
            builder: (controller) {
              return WifiSignalStrengthV2(
                  onTap: _showWifiSignalStrengthDialog,
                  signalStrength: controller.wifiRssi
              );
            },
        ),

        SizedBox(height: 15.h),

        Opacity(
          opacity: 0.5,
          child: Text(
              'nodeId'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),

        SizedBox(height: 12.h,),

        GetBuilder<LocationSettingsController>(
          id: nodeId,
          builder: (controller) {
            return Text(
              controller.nodeId,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            );
          },
        ),

        SizedBox(height: 15.h),

        Opacity(
          opacity: 0.5,
          child: Text(
              'thingName'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),
        SizedBox(height: 12.h,),

        GetBuilder<LocationSettingsController>(
          id: thingNameId,
          builder: (controller) {
            return Text(
              controller.thingName,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                   .extension<AppThemeExtension>()!.firstColor,
              ),
            );
          },
        ),

        SizedBox(height: 15.h),

        Opacity(
          opacity: 0.5,
          child: Text(
              'firmwareVersion'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),

        SizedBox(height: 12.h,),

        GetBuilder<LocationSettingsController>(
          id: softwareVersionId,
          builder: (controller) {
            return Text(
              controller.softwareVersion,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
              ),
            );
          },
        ),

        // SizedBox(height: 15.h),
        // TextButton.icon(
        //   onPressed: _shareLocation,
        //   icon: const Icon(
        //     Icons.link,
        //     color: AppColors.ff01A796,
        //   ),
        //   label: Text(
        //       'shareLocation'.tr,
        //       style: const TextStyle(
        //         fontSize: 16,
        //         fontWeight: AppFontWeights.regular,
        //         color: AppColors.ff01A796,
        //       )
        //   ),
        // ),

        SizedBox(height: 51.h),

        SizedBox(
          width: double.infinity,
          height: 48,
          child: OutlinedButton(
            onPressed: () => _onDeleteReceiver(context),
            style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Colors.red),
                overlayColor: Colors.red
            ),
            child: Text(
              'deleteLocation'.tr.toUpperCase(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.medium,
                color: Colors.red,
              ),
            ),
          ),
        ),
      ],
    );
  }
  Widget _buildTimeZoneDropdown() {
    if (controller.timeZoneList.isEmpty) {
      return const SizedBox();
    }
    return DropdownMenu(
      width: Get.width - 72,
      menuHeight: 350.h,
      initialSelection: controller.selectedTimeZone,
      dropdownMenuEntries: controller.timeZoneList,
      onSelected: _onTimeZoneSelected,
    );
  }

  void _onDeleteReceiver(BuildContext context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return DeleteLocationWidget(
            onDelete: () {
              _showDeleteReceiverChoiceDialog(context);
            },
          );
        }
    );
  }

  void _showDeleteReceiverChoiceDialog(BuildContext context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return DeleteReceiverChoiceWidget(
            onConfirm: (int value) {
              controller.deleteReceiver(value);
            },
          );
        }
    );
  }

  // void _shareLocation() {
  //   Get.to(() => ShareReceiverPage(), binding: ShareReceiverBindings(
  //       thingName: controller.thingName));
  // }

  void _onTimeZoneSelected(value) {
    log.i('selected time zone: $value');
    controller.changeTimeZone(value);
  }

  void _onWiFiSSIDTap() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return ChangeReceiverWiFiWidget(
            onConfirm: () {
              controller.findReceiverViaBLE();
            },
          );
        }
    );
  }

  void _showWifiSignalStrengthDialog() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return Dialog(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(28.0),
              ),
            ),
            insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                    borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: Text(
                            'wifiRouterBoilerReceiverSignalStrength'.tr,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: AppFontWeights.regular,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 30,),

                      Padding(
                        padding: const EdgeInsets.only(left: 10, right: 10),
                        child: SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: OutlinedButton(
                            onPressed: () {
                              Get.back();
                            },
                            child: Text(
                              'ok'.tr,
                              style: const TextStyle(
                                  color: AppColors.ff01A796
                              ),
                            ),
                          ),
                        ),
                      ),


                      const SizedBox(height: 50,),
                    ],
                  ),
                ),
              ),
            ),

          );
        }
    );
  }

}

