import 'dart:async';
import 'package:ct_flutter_ble_plugin/ct_ble_plugin.dart';
import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_time_zones.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/models/device_provision/device_provision_result.dart';
import 'package:habi_app/models/dynamo_db/user_to_device_list.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_bindings.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_provision_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/home_utils.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/widgets/delete_receiver_choice_widget.dart';
import 'location_settings_page.dart';

class LocationSettingsController extends BaseController {
  final demoModeService = DemoModeService.to;
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;
  final editNameController = TextEditingController();

  final String homeId;
  final String homeName;
  final String thingName;

  var editName = false.obs;
  var locationName = ''.tr.obs;

  bool isHotWaterEnabled = false;
  int wifiRssi = -120;
  String softwareVersion = '';
  String nodeId = '';
  String networkSSIDF = '';
  List<DropdownMenuEntry<String>> timeZoneList = [];
  String selectedTimeZone = '';

  late String mcThingName;
  late Map<String, String> timeZonesMap;

  Sait85rGW? sait85rGW;
  StreamSubscription? _remoteSubscription;
  StreamSubscription? _listenScanControllerSubscription;
  StreamSubscription? _listenConnectionStateChangeSubscription;
  CtController? _currentController;
  Timer? _startScanControllerTimer;
  bool _isDiscoverServices = false;
  int _reconnectCount = 3;
  Map<String, dynamic>? manualPropertiesMap;

  LocationSettingsController({
    required this.homeId,
    required this.homeName,
    required this.thingName,
  }) {
    mcThingName = Sait85rHelper.getMBRCThingName(thingName);
  }

  @override
  void onInit() {
    super.onInit();
    editNameController.text = homeName;
    locationName.value = homeName;
  }

  @override
  void onReady() {
    super.onReady();
    setMqttListener();
    Future(() async {
      await loadTimeZoneList();
      await initData();
    });
  }

  @override
  void onClose() {
    editNameController.dispose();
    _remoteSubscription?.cancel();

    Future(() async {
      bool isConnected = _currentController?.isConnected ?? false;
      log.i('onClose() -> isConnected:$isConnected');
      if (isConnected) {
        await _currentController?.disconnect();
        log.i('onClose() -> 断开蓝牙连接...');
        _currentController = null;
      }
    });

    _startScanControllerTimer?.cancel();
    _listenScanControllerSubscription?.cancel();
    _listenConnectionStateChangeSubscription?.cancel();
    bool isScan = CtBlePlugin.getInstance().isScanningNow();
    log.i('onClose() -> isScan=$isScan');
    if (isScan) {
      CtBlePlugin.getInstance()
          .stopScanController()
          .then((data) {
        log.i('stopScanController() -> success!');
      }).catchError((error) {
        log.e('stopScanController() -> error: $error');
      });
    }
    super.onClose();
  }

  void setMqttListener() {
    _remoteSubscription = GlobalService.to.getMqttStream().listen((shadow) async {
      Map<String, dynamic>? reportedMap = shadow.thingShadow['state']?['reported'];
      if (reportedMap == null) {
        return;
      }

      String connectedKey = 'connected';
      String connectedValue = 'false';
      if (reportedMap.containsKey(connectedKey)) {
        connectedValue = reportedMap[connectedKey];
        if (connectedValue == 'true') {
          log.d('setMqttListener() -> controller已在线...');
        } else {
          log.d('setMqttListener() -> controller已离线...');
          wifiRssi = -120;
          update([LocationSettingsPage.wifiRssiId]);
          log.i('setMqttListener() -> 刷新wifi-rssi-1...');
        }
      }

      if (shadow.thingName == mcThingName) {
        Map<String, dynamic>? propertiesMap = reportedMap[Sait85rMC.subId]?['properties'];
        if (propertiesMap == null) {
          return;
        }

        String enableDHWKey = "ep0:sBoiler:EnableDHW";
        if (propertiesMap.containsKey(enableDHWKey)) {
          if ((manualPropertiesMap?.containsKey(enableDHWKey) ?? false)) {
            manualPropertiesMap = null;
          } else {
            int enableDHWValue = propertiesMap[enableDHWKey];
            isHotWaterEnabled = enableDHWValue == 1;
            update([LocationSettingsPage.isHotWaterEnabledId]);
            log.i('setMqttListener() -> 刷新EnableDHW...');
          }
        }
      }

      if (shadow.thingName == thingName) {
        Map<String, dynamic>? propertiesMap = reportedMap[Sait85rGW.subId]?['properties'];
        if (propertiesMap == null) {
          return;
        }

        String wifiRssiKey = 'ep0:sGateway:WiFiRSSI';
        if (propertiesMap.containsKey(wifiRssiKey)) {
          int wifiRssiValue = propertiesMap[wifiRssiKey];
          if (connectedValue == 'true' && wifiRssiValue != wifiRssi) {
            wifiRssi = wifiRssiValue;
            update([LocationSettingsPage.wifiRssiId]);
            log.i('setMqttListener() -> 刷新wifi-rssi-2...');
          }
        }

        String ssidKey = 'ep0:sGateway:NetworkSSID';
        if (propertiesMap.containsKey(ssidKey)) {
          String ssidValue = propertiesMap[ssidKey];
          if (ssidValue != networkSSIDF) {
            networkSSIDF = ssidValue;
            update([LocationSettingsPage.wifiSSIDId]);
            log.i('setMqttListener() -> 刷新wifi-ssid...');
          }
        }

        String timeZoneKey = 'ep0:sGateway:TimeZone';
        if (propertiesMap.containsKey(timeZoneKey)) {
          if ((manualPropertiesMap?.containsKey(timeZoneKey) ?? false)) {
            manualPropertiesMap = null;
          } else {
            String timeZoneValue = propertiesMap[timeZoneKey];
            String extractZoneValue = extractZone(timeZoneValue);
            log.i('setMqttListener() -> timeZoneValue: $timeZoneValue, '
                'extractZoneValue: $extractZoneValue');
            if (extractZoneValue != selectedTimeZone) {
              selectedTimeZone = extractZoneValue;
              update([LocationSettingsPage.timeZoneId]);
              log.i('setMqttListener() -> 刷新timeZone...');
            }
          }
        }

        String gatewaySoftwareVersionKey = 'ep0:sGateway:GatewaySoftwareVersion';
        if (propertiesMap.containsKey(gatewaySoftwareVersionKey)) {
          String gatewaySoftwareVersionValue = propertiesMap[gatewaySoftwareVersionKey];
          if (gatewaySoftwareVersionValue != softwareVersion) {
            softwareVersion = gatewaySoftwareVersionValue;
            update([LocationSettingsPage.softwareVersionId]);
            log.i('setMqttListener() -> 刷新softwareVersion...');
          }
        }
      }
    });
  }

  Future<void> initData() async {
    updateLoadingMessage('loading...');
    try {
      AppShadow shadow = deviceShadowService.getDeviceShadow(thingName);
      AppShadow mcShadow = deviceShadowService.getDeviceShadow(mcThingName);

      await setupGWData(shadow.thingShadow);
      await setupMCData(mcShadow.thingShadow);

      await Future.wait([
        fetchGWShadow(),
        fetchMCShadow(),
      ]);
    } catch (e, s) {
      log.e('initData() -> $e, $s');
    } finally {
      updateLoading(false);
    }
  }

  Future<void> fetchGWShadow() async {
    try {
      Map<String, dynamic> shadow = await deviceShadowService.fetchDeviceShadow(thingName);
      // log.i('gwShadow=$shadow');
      setupGWData(shadow);
    } catch (e, s) {
      log.e('fetchGWShadow() -> $e, $s');
    }
  }

  Future<void> fetchMCShadow() async {
    try {
      Map<String, dynamic>? mcShadow = await deviceShadowService.fetchDeviceShadow(mcThingName);
      // log.i('mcShadow=$mcShadow');
      setupMCData(mcShadow);
    } catch (e, s) {
      log.e('fetchMCShadow() -> $e, $s');
    }
  }

  Future<void> setupGWData(Map<String, dynamic> shadow) async {
    try {
      sait85rGW = Sait85rGW.fromJson(shadow, thingName);

      String thatConnected = sait85rGW?.shadow?.state?.reported?.connected?? 'false';
      log.i('thatConnected=$thatConnected');

      String? thatGatewaySoftwareVersion = sait85rGW?.shadow?.state?.reported?.model
          ?.properties?.sGateway?.gatewaySoftwareVersion;
      log.i('thatGatewaySoftwareVersion=$thatGatewaySoftwareVersion');
      if (thatGatewaySoftwareVersion != null && thatGatewaySoftwareVersion.isNotEmpty) {
        softwareVersion = thatGatewaySoftwareVersion;
        update([LocationSettingsPage.softwareVersionId]);
      }

      int thatWifiRssi = sait85rGW?.shadow?.state?.reported?.model
          ?.properties?.sGateway?.wifiRssi ?? -120;
      log.i('thatWifiRssi=$thatWifiRssi');
      if (thatConnected == 'true') {
        wifiRssi = thatWifiRssi;
        update([LocationSettingsPage.wifiRssiId]);
      } else {
        wifiRssi = -120;
        update([LocationSettingsPage.wifiRssiId]);
      }

      String? thatNetworkSSIDF = sait85rGW?.shadow?.state?.reported?.model
          ?.properties?.sGateway?.networkSSID;
      log.i('thatNetworkSSIDF=$thatNetworkSSIDF');
      if (thatNetworkSSIDF != null && thatNetworkSSIDF.isNotEmpty) {
        networkSSIDF = thatNetworkSSIDF;
        update([LocationSettingsPage.wifiSSIDId]);
      } else {
        networkSSIDF = await getNetworkSSID() ?? '********************';
        update([LocationSettingsPage.wifiSSIDId]);
      }

      String? thatTimeZone = sait85rGW?.shadow?.state?.reported?.model
          ?.properties?.sGateway?.timeZone;
      log.i('thatTimeZone=$thatTimeZone');
      if (thatTimeZone != null && thatTimeZone.isNotEmpty) {
        setSelectedTimeZone(thatTimeZone);
      }
    } catch (e, s) {
      log.e('setupGWData() -> $e, $s');
    }
  }

  void setSelectedTimeZone(String timeZone) {
    String zone = extractZone(timeZone);
    log.i('zone=$zone');

    for (var element in timeZoneList) {
      if (element.value == zone) {
        selectedTimeZone = element.value;
        log.i('找到对应的zone: $selectedTimeZone');
        update([LocationSettingsPage.timeZoneId]);
        break;
      }
    }
  }

  String extractZone(String timeZone) {
    List<String> splitList = timeZone.split('/');
    if (splitList.isNotEmpty) {
      return splitList[1];
    } else {
      return timeZone;
    }
  }

  Future<void> setupMCData(Map<String, dynamic> shadow) async {
    try {
      Sait85rMC sait85rMC = Sait85rMC.fromJson(shadow, mcThingName);
      int? thatEnableDhw = sait85rMC.shadow?.state?.reported?.model
          ?.properties?.sBoiler?.enableDhw;
      log.i('thatEnableDhw=$thatEnableDhw');
      if (thatEnableDhw != null) {
        isHotWaterEnabled = thatEnableDhw == 1;
        update([LocationSettingsPage.isHotWaterEnabledId]);
      }

      String? thatNodeId = sait85rMC.shadow?.state?.reported?.model
          ?.properties?.sMcTlr?.nodeID;
      log.i('thatNodeId=$thatNodeId');
      if (thatNodeId != null && thatNodeId.isNotEmpty) {
        nodeId = thatNodeId;
        update([LocationSettingsPage.nodeId]);
      }
    } catch (e, s) {
      log.e('setupMCData() -> $e, $s');
    }
  }

  Future<void> changeName(String name) async {
    if (name.isEmpty) {
      log.e('name cannot be empty');
      return;
    }

    String oldName = locationName.value;
    if (oldName == name) {
      log.e('name is the same as before');
      return;
    }

    updateLoading(true);

    try {
      await DevService.to.changeHomeName(homeId, name);
      log.i('changeName() -> success!');
      locationName.value = name;

      /*List<AppHome>? homeList = await HomeHelper.getHomeList();
      if (homeList != null && homeList.isNotEmpty) {
        for (AppHome home in homeList) {
          if (home.homeId == homeId) {
            home.name = name;
            break;
          }
        }

        String json = jsonEncode(homeList);
        log.i('changeName() -> homeJson: $json');
        await localStorageService.setHomeList(json);
      }*/

      GlobalService.to
          .getEventStreamController()
          .add(AppEvent(name: AppEvents.changeHomeName, data: name));

      showSuccessSnackBar('successfully changed name.');
      updateLoading(false);
    } catch (e, r) {
      log.e('changeName() -> failed to change name: $e, $r');
      showErrorSnackBar('failed to change name.');
      updateLoading(false);
    }
  }

  Future<void> changeHotWaterEnable(bool value) async {
    int enableDHW;
    if (value) {
      enableDHW = 1;
    } else {
      enableDHW = 0;
    }

    manualPropertiesMap = {
      "ep0:sBoiler:EnableDHW": enableDHW,
    };

    isHotWaterEnabled = value;
    update([LocationSettingsPage.isHotWaterEnabledId]);

    updateLoading(true);

    try {
      await deviceShadowService.updateDeviceProperties(
          thingName: mcThingName,
          property: {
            "ep0:sBoiler:sEnableDHW": enableDHW,
          },
          subId: Sait85rMC.subId
      ).timeout(const Duration(seconds: 30));
      log.i('changeHotWaterEnable() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('changeHotWaterEnable() -> failed to publish data to cloud: $e, $r');
    } finally {
      updateLoading(false);
    }
  }

  Future<void> loadTimeZoneList() async {
    final dropdownMenuEntries = <DropdownMenuEntry<String>>[];
    final timeZoneConvertedMap = AppTimeZones.getTimeZoneConvertedMap();
    timeZonesMap = AppTimeZones.getTimeZoneMap();
    DropdownMenuEntry? defaultEntry;

    timeZoneConvertedMap.forEach((key, value){
      var entry = DropdownMenuEntry<String>(value: key, label: value);
      dropdownMenuEntries.add(entry);
      if (AppTimeZones.defaultTimeZoneKey == key) {
        defaultEntry = entry;
      }
    });

    selectedTimeZone = defaultEntry?.value;
    timeZoneList = dropdownMenuEntries;
    update([LocationSettingsPage.timeZoneId]);
  }

  Future<void> changeTimeZone(String value) async {
    String oldTimeZone = selectedTimeZone;
    if (oldTimeZone == value) {
      log.e('changeTimeZone() -> timeZone is the same as before');
      return;
    }

    String? newTimeZone = timeZonesMap[value];
    if (newTimeZone == null || newTimeZone.isEmpty) {
      log.e('changeTimeZone() -> timeZone is not valid.');
      return;
    }

    manualPropertiesMap = {
      "ep0:sGateway:TimeZone": value,
    };
    selectedTimeZone = value;
    update([LocationSettingsPage.timeZoneId]);

    updateLoading(true);

    try {
      await deviceShadowService.updateDeviceProperties(
          thingName: thingName,
          property: {
            'ep0:sGateway:SetTimeZone': newTimeZone
          },
          subId: Sait85rGW.subId
      );
      log.i('changeTimeZone() -> successfully publish timeZone to cloud!');
      showSuccessSnackBar('successfully changed timeZone.');
      updateLoading(false);
    } catch (e, r) {
      log.e('changeTimeZone() -> failed to publish timeZone to cloud: $e, $r');
      showErrorSnackBar('failed to changed timeZone.');
      updateLoading(false);
    }
  }

  Future<String?> getNetworkSSID() async {
    Map<String, dynamic>? allWifiMap = await localStorageService.getWiFiMap();
    log.i('getNetworkSSID() -> allWifiMap: $allWifiMap');
    String? ssid;
    if (allWifiMap != null) {
      Map<String, dynamic>? thatWiFiMap = allWifiMap[thingName];
      ssid = thatWiFiMap?['ssid'];
    }
    return ssid;
  }

  Future<void> findReceiverViaBLE() async {
    if (demoModeService.isDemo) {
      showSnackBar('featureNotAvailableInDemoMode'.tr);
      return;
    }

    BluetoothAdapterState adapterState;

    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('requestPermission() -> 获取蓝牙状态=$adapterState');
    } catch (e, r) {
      log.e('requestPermission() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      log.i('findReceiverViaBLE() -> 蓝牙未打开');
      showSnackBar('pleaseTurnOnBluetooth'.tr);
      return;
    }

    if (isLoading()) {
      return;
    }

    updateLoadingMessage('searching...');

    bool isConnected = _currentController?.isConnected ?? false;
    log.i('findReceiverViaBLE() -> isConnected:$isConnected');
    if (isConnected) {
      log.i('findReceiverViaBLE() -> 已经有一个连接了');
      await Future.delayed(const Duration(milliseconds: 1000));
      await navigateToPage();
      updateLoading(false);
      return;
    }

    try {
      _startScanControllerTimer?.cancel();
      _listenScanControllerSubscription?.cancel();
      _listenConnectionStateChangeSubscription?.cancel();

      _currentController = null;
      _isDiscoverServices = false;
      _reconnectCount = 3;

      String receiverName = Sait85rHelper.getScanGroupName(thingName);
      log.i('findReceiverViaBLE() -> receiverName: $receiverName');

      _listenScanControllerSubscription = CtBlePlugin.getInstance()
          .listenScanController((results) async {
        if (_currentController != null) {
          log.i('findReceiverViaBLE() -> already found receiver, ignore.');
          return;
        }

        CtController? ctController;
        try {
          ctController = results.firstWhere((item) => item.name == receiverName);
        } catch (e) {
          log.e('findReceiverViaBLE() -> no receiver found: $e');
          ctController = null;
        }

        if (ctController == null) {
          return;
        }

        log.i('findReceiverViaBLE() -> receiver found!');
        _currentController = ctController;

        try {
          await CtBlePlugin.getInstance().stopScanController();
        } catch (e, r) {
          log.e('findReceiverViaBLE() -> stop scan failed: $e, $r');
        }

        _listenConnectionStateChangeSubscription =
            _currentController!.listenConnectionStateChange((state) async {
              if (state == BluetoothConnectionState.connected) {
                try {
                  _startScanControllerTimer?.cancel();
                  log.i('findReceiverViaBLE() -> 监听到controller已经连接');
                  log.i('findReceiverViaBLE() -> 开始发现服务...');
                  await ctController!.discoverServices();
                  _isDiscoverServices = true;
                  log.i('findReceiverViaBLE() -> 发现服务成功');
                  await Future.delayed(const Duration(milliseconds: 1000));
                  await navigateToPage();
                  updateLoading(false);
                } catch (e) {
                  log.e('findReceiverViaBLE() -> 发现服务失败');
                }
              } else if (state == BluetoothConnectionState.disconnected) {
                try {
                  log.i('findReceiverViaBLE() -> 监听到controller断开连接');
                  if (!_isDiscoverServices) {
                    log.i('findReceiverViaBLE() -> 检测到还没有连接成功蓝牙ble就已经断开...');
                    await reconnectController();
                  }
                } catch (e) {
                  log.e('findReceiverViaBLE() -> 执行重连失败!!!');
                }
              }
            });

        try {
          await _currentController!.connect();
        } catch (e, r) {
          log.e('findReceiverViaBLE() -> connect failed: $e, $r');
        }

      });

      _startScanControllerTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        timer.cancel();
        CtBlePlugin.getInstance()
            .stopScanController()
            .then((data) {
          log.i('stopScanController() -> success!');
        }).catchError((error) {
          log.e('stopScanController() -> error: $error');
        });
        updateLoading(false);
        showErrorDialog('Failed to add receiver', 'Receiver search timed out, please activate pairing mode and move closer to the device.');
      });

      await CtBlePlugin.getInstance().startScanController();
    } catch (e, r) {
      log.e('findReceiverViaBLE() -> failed to find receiver: $e, $r');
      showErrorDialog('Failed to add receiver', 'Receiver search failed.');
    }
  }

  void showErrorDialog(String title, String content) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                content,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }


  Future<void> navigateToPage() async {
    Get.to(() => SelectNetworkPage(),
        binding: SelectNetworkBindings(
            matterController: _currentController!,
            isOnlyChangeWiFi: true
        ));
  }

  Future<void> reconnectController() async {
    if (_reconnectCount > 0) {
      _reconnectCount--;
      log.i('尝试重新连接蓝牙ble, 剩余重连次数：$_reconnectCount');
      log.i('启动重连蓝牙ble任务，3.5s后执行');
      await Future.delayed(const Duration(milliseconds: 3500));
      await _currentController!.connect();
    } else {
      log.e('3次重连全部失败!!!');
      updateLoading(false);
      showErrorSnackBar('Bluetooth connection failure.');
    }
  }

  Future<void> deleteReceiver(int value) async {
    if (demoModeService.isDemo) {
      showSnackBar('featureNotAvailableInDemoMode'.tr);
      return;
    }

    log.i('deleteReceiver() -> value: $value');
    if (value == DeleteReceiverChoiceWidget.keepAllConfig) {
      await deleteReceiverWithOption1();
    } else if (value == DeleteReceiverChoiceWidget.factoryReset) {
      await deleteReceiverWithOption2();
    }
  }

  Future<void> deleteReceiverWithOption1() async {
    updateLoadingMessage('deleting...');

    bool owner = false;
    bool sharer = false;

    try {
      UserToDeviceList? list = await DynamoDBService.to.fetchUserToDeviceList()
          .timeout(const Duration(seconds: 60));
      owner = list?.ownNames?.contains(thingName) ?? false;
      sharer = list?.shareNames?.contains(thingName) ?? false;
      log.i('deleteReceiver() -> list: $list');
    } catch(e) {
      log.e('deleteReceiver() -> error: $e');
      updateLoading(false);
      showErrorDialog('deleteReceiverFailed'.tr, 'requestFailed'.tr);
      return;
    }

    log.i('deleteReceiver() -> owner: $owner, sharer: $sharer');

    if (sharer) {
      updateLoading(false);
      showErrorDialog('deleteReceiverFailed'.tr, 'cannotDeleteNotOwner'.tr);
      return;
    }

    if (owner) {
      for (int i = 0; i < 3; i++) {
        try {
          DeviceProvisionResult removeDeviceRegistrationResult =
          await DeviceProvisionService.to
              .removeDeviceRegistration(deviceId: thingName)
              .timeout(const Duration(seconds: 30));
          if (removeDeviceRegistrationResult.isSuccess()) {
            log.i('deleteReceiver() -> removeDeviceRegistration succeeded');
            break;
          } else {
            log.e('deleteReceiver() -> removeDeviceRegistration failed: ${removeDeviceRegistrationResult.body}');
          }
        } catch (e, s) {
          log.e('deleteReceiver() -> removeDeviceRegistration failed: $e, $s');
        }
      }

      for (int i = 0; i < 3; i++) {
        try {
          final session = await AuthService.to.fetchCognitoAuthSession();
          final userId = session.identityIdResult.value;
          List<String> userIdList = [userId];
          bool isOwner = true;
          log.i('deleteReceiver() -> removeUserFromHome...');
          await DevService.to.removeUserFromHome(homeId, userIdList, isOwner)
              .timeout(const Duration(seconds: 30));
          log.i('deleteReceiver() -> removeUserFromHome succeeded');
          break;
        } catch (e) {
          log.e('deleteReceiver() -> removeUserFromHome failed: $e');
        }
      }
    }

    /*try {
      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId != null && fabricId.isNotEmpty) {
        CtFlutterMatterPlugin.getInstance()
            .shutdown(fabricId);
        log.i('deleteReceiver() -> shutdown succeeded!');
      } else {
        log.e('deleteReceiver() -> shutdown failed: fabricId = null');
      }
    } catch (e) {
      log.e('deleteReceiver() -> shutdown failed: $e');
    }*/

    await deleteCredentials();

    await clearLocalData();

    updateLoading(false);

    Get.until((route) {
      return route.settings.name == Routes.homeManagement;
    });

    GlobalService.to
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.deleteReceiver));
  }

  Future<void> deleteReceiverWithOption2() async {
    updateLoadingMessage('deleting...');

    bool owner = false;
    bool sharer = false;

    try {
      UserToDeviceList? list = await DynamoDBService.to.fetchUserToDeviceList()
          .timeout(const Duration(seconds: 60));
      owner = list?.ownNames?.contains(thingName) ?? false;
      sharer = list?.shareNames?.contains(thingName) ?? false;
      log.i('deleteReceiver() -> list: $list');
    } catch(e) {
      log.e('deleteReceiver() -> error: $e');
      updateLoading(false);
      showErrorDialog('deleteReceiverFailed'.tr, 'requestFailed'.tr);
      return;
    }

    log.i('deleteReceiver() -> owner: $owner, sharer: $sharer');

    if (sharer) {
      updateLoading(false);
      showErrorDialog('deleteReceiverFailed'.tr, 'cannotDeleteNotOwner'.tr);
      return;
    }

    if (owner) {
      GlobalService.to
          .getEventStreamController()
          .add(AppEvent(name: AppEvents.prepareDeleteReceiver));

      bool sendFactoryResetSuccess = false;
      for (int i = 0; i < 3; i++) {
        log.w('deleteReceiver() -> 发送factory reset给云端，第 ${i + 1} 次');
        try {
          await deviceShadowService.updateDeviceProperties(
              thingName: thingName,
              property: {
                'ep0:sGateway:SetFactoryReset_d': 1
              },
              subId: Sait85rGW.subId
          ).timeout(const Duration(seconds: 60));
          log.i('deleteReceiver() -> 发送factory reset给云端成功!');
          sendFactoryResetSuccess = true;
          break;
        } catch (e, r) {
          log.w('deleteReceiver() -> 发送factory reset给云端失败：$e，$r');
        }
      }

      if (!sendFactoryResetSuccess) {
        log.e('deleteReceiver() -> 3次发送factory reset给云端全部失败');
        updateLoading(false);
        showErrorDialog('deleteReceiverFailed'.tr, 'requestFailed'.tr);
        return;
      }

      for (int i = 0; i < 3; i++) {
        try {
          DeviceProvisionResult removeDeviceRegistrationResult = await DeviceProvisionService.to
              .removeDeviceRegistration(deviceId: thingName)
              .timeout(const Duration(seconds: 30));
          if (removeDeviceRegistrationResult.isSuccess()) {
            log.i('deleteReceiver() -> removeDeviceRegistration succeeded');
            break;
          } else {
            log.e('deleteReceiver() -> removeDeviceRegistration failed: ${removeDeviceRegistrationResult.body}');
          }
        } catch (e, s) {
          log.e('deleteReceiver() -> removeDeviceRegistration failed: $e, $s');
        }
      }

      for (int i = 0; i < 3; i++) {
        try {
          log.i('deleteReceiver() -> deleteHome..., homeId: $homeId');
          await DevService.to.deleteHome(homeId)
              .timeout(const Duration(seconds: 30));
          log.i('deleteReceiver() -> deleteHome succeeded');
          break;
        } catch (e) {
          log.e('deleteReceiver() -> deleteHome failed: $e');
        }
      }
    }

    /*try {
      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId != null && fabricId.isNotEmpty) {
        CtFlutterMatterPlugin.getInstance()
            .shutdown(fabricId);
        log.i('deleteReceiver() -> shutdown succeeded!');
      } else {
        log.e('deleteReceiver() -> shutdown failed: fabricId = null');
      }
    } catch (e) {
      log.e('deleteReceiver() -> shutdown failed: $e');
    }*/

    await deleteCredentials();

    await clearLocalData();

    // thing没这么快删除，这里延迟一下
    await Future.delayed(const Duration(seconds: 15));

    // 这种检测逻辑有问题，暂时注释掉
    /*try {
      log.i('deleteReceiver() -> checkThingGroup start.');
      await checkThingGroup();
    } catch (e) {
      log.e('deleteReceiver() -> checkThingGroup failed: $e');
    }*/

    updateLoading(false);

    Get.until((route) {
      return route.settings.name == Routes.homeManagement;
    });

    GlobalService.to
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.deleteReceiver));
  }

  Future<void> deleteCredentials() async {
    try {
      AppShadow appShadow = deviceShadowService.getDeviceShadow(mcThingName);
      Sait85rMC sait85rMC = Sait85rMC.fromJson(appShadow.thingShadow, mcThingName);

      if (PlatformUtils.isIOS) {
        String? extendedAddress = sait85rMC.shadow?.state?.reported?.model
            ?.properties?.sMcTlr?.thdExtendedAddress;
        if (extendedAddress != null && extendedAddress.isNotEmpty) {
          for (int i = 0; i < 3; i++) {
            try {
              await CtFlutterMatterPlugin.getInstance()
                  .deleteCredentials(extendedAddress)
                  .timeout(const Duration(seconds: 30));
              log.i('deleteCredentials() -> succeeded');
              break;
            } catch (e, s) {
              log.e('deleteCredentials() -> failed: $e, $s');
            }
          }
        } else {
          log.e('deleteCredentials() -> failed: thdExtendedAddress=null');
        }
      }

    } catch (e) {
      log.e('deleteCredentials() -> failed: $e');
    }
  }

  /*Future<void> checkThingGroup() async {
    IotService service = IotService.to;
    bool isTimeout = false;
    await Future.doWhile(() async {
      if (isTimeout) {
        return false; // 如果超时了，停止循环
      }

      var thingGroups = await service.listThingGroups();
      log.i('checkThingGroup() -> thingGroups length=${thingGroups?.length}');
      if (thingGroups == null || thingGroups.isEmpty) {
        return false;
      }

      String thingGroupName = Sait85rHelper.getThingGroupName(thingName);
      log.i('checkThingGroup() -> thingGroupName=$thingGroupName');
      for  (var thingGroup in thingGroups) {
        if (thingGroup.groupName == thingGroupName) {
          log.i('checkThingGroup() -> thingGroup存在于服务器上，继续检查...');
          await Future.delayed(const Duration(seconds: 3)); // 等待3秒
          return true; // 继续检查
        }
      }

      log.i('checkThingGroup() -> thingGroup不存在于服务器上，删除成功！');
      return false; // 停止检查
    }).timeout(
      const Duration(seconds: 60),
      onTimeout: () {
        isTimeout = true;
        log.e('checkThingGroup() -> 检查超时（60秒）');
        throw TimeoutException('Check thing group timeout');
      },
    );
  }*/

  Future<void> clearLocalData() async {
    LocalStorageService localStorageService = LocalStorageService.to;

    try {
      await localStorageService.clearThingShadow(thingName);
    } catch (e) {
      log.e('deleteReceiver() -> clear thing data failed: $thingName $e');
    }

    try {
      await localStorageService.clearThingShadow(mcThingName);
    } catch (e) {
      log.e('deleteReceiver() -> clear thing data failed: $mcThingName $e');
    }

    try {
      await HomeUtils.removeCachedHomeById(homeId);
    } catch (e) {
      log.e('deleteReceiver() -> clear cache failed: $e');
    }

    try {
      Map<String, dynamic>? allFabricMap = await localStorageService.getFabricMap();
      log.i('deleteReceiver() -> before allFabricMap: $allFabricMap');
      if (allFabricMap != null && allFabricMap.isNotEmpty) {
        if (allFabricMap.containsKey(mcThingName)) {
          allFabricMap.remove(mcThingName);
          await localStorageService.setFabricMap(allFabricMap);
        }
      }
      log.i('deleteReceiver() -> after allFabricMap: $allFabricMap');
    } catch (e) {
      log.e('deleteReceiver() -> clear fabric data failed: $e');
    }

    try {
      Map<String, dynamic>? allWiFiMap = await localStorageService.getWiFiMap();
      log.i('deleteReceiver() -> before allWiFiMap: $allWiFiMap');
      if (allWiFiMap != null && allWiFiMap.isNotEmpty) {
        if (allWiFiMap.containsKey(thingName)) {
          allWiFiMap.remove(thingName);
          await localStorageService.setWiFiMap(allWiFiMap);
        }
      }
      log.i('deleteReceiver() -> after allWiFiMap: $allWiFiMap');
    } catch (e) {
      log.e('deleteReceiver() -> clear wifi data failed: $e');
    }
  }

}
