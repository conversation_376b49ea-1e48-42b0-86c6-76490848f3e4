import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/boarding/boarding_bindings.dart';
import 'package:habi_app/pages/boarding/boarding_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:habi_app/utility/amplify_utils.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class RootController extends GetxController {

  final authService = AuthService.to;
  final globalService = GlobalService.to;
  final localStorageService = LocalStorageService.to;
  final userAttributesService = UserAttributesService.to;

  String _email = '';

  Future<void> _sentryConfigureScope() async {
    try {
      var user = await authService.getCurrentUser();
      Sentry.configureScope((scope) => scope.setUser(
        SentryUser(id: user.userId, email: _email),
      ));
    } catch (e, r) {
      log.e("_sentryConfigureScope() -> e=$e, r=$r");
    }
  }


  Future<void> toHomeManagement(String email) async {
    _email = email;

    await localStorageService.initUser(_email);
    await localStorageService.initThingShadow(_email);

    if (!kDebugMode) {
      await _sentryConfigureScope();
    }

    if (!globalService.config.amplifyConfigured) {
      bool configured = await AmplifyUtils.reinitConfigure();
      globalService.config.amplifyConfigured = configured;
    }

    try {
      await userAttributesService.fetchAttributes();
    } on SignedOutException catch (e) {
      // Handle the error of "No user is currently signed in"
      log.e("toHomeManagement() -> e=$e");
      await localStorageService.setKeepMeLoggedIn(false);
      Get.off(() => const BoardingPage(), binding: BoardingBindings());
      return;
    }

    Get.offAllNamed(Routes.homeManagement);
  }




}
