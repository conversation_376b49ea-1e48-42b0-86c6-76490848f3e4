import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/boarding/boarding_bindings.dart';
import 'package:habi_app/pages/boarding/boarding_page.dart';
import 'package:habi_app/pages/welcome/welcome_page.dart';
import 'package:habi_app/services/database_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/platform_utils.dart';

import 'root_controller.dart';

class RootPage extends StatefulWidget {
  const RootPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _RootPageState();
  }

}

class _RootPageState extends State<RootPage> {

  bool _atLeastAndroid12 = true;

  @override
  void initState() {
    super.initState();
    Future(_initData);
  }

  Future<void> _initData() async {
    if (PlatformUtils.isAndroid) {
      _atLeastAndroid12 = await _isAtLeastAndroid12();
      if (_atLeastAndroid12) {
        log.i("_checkJumpLogic() -> android version >= 12");
        await _executeInitTask();
        return;
      } else {
        log.i("_checkJumpLogic() -> android version < 12");
        setState(() {});
        await Future.delayed(const Duration(milliseconds: 1500), _executeInitTask);
        return;
      }
    }
    await _executeInitTask();
  }

  Future<void> _executeInitTask() async {
    log.i("executeInitTask() -> start...");
    await DatabaseService.to.initDb();
    await _checkJumpLogic();
  }

  Future<void> _checkJumpLogic() async {
    var localStorageService = LocalStorageService.to;
    await localStorageService.initApp();
    var isFirstStartUp = await localStorageService.isFirstStartUp() ?? true;
    log.i("_checkJumpLogic() -> isFirstStartUp=$isFirstStartUp");
    if (isFirstStartUp)  {
      Get.off(() => const WelcomePage());
    } else {
      final keepMeLoggedIn = await localStorageService.getKeepMeLoggedIn() ?? false;
      final email = await localStorageService.getEmail() ?? '';
      log.i("_checkJumpLogic() -> keepMeLoggedIn=$keepMeLoggedIn, email=$email");
      if (keepMeLoggedIn && email.isNotEmpty) {
        final rootController = Get.find<RootController>();
        rootController.toHomeManagement(email);
        return;
      }
      Get.off(() => const BoardingPage(), binding: BoardingBindings());
    }
  }

  Future<bool> _isAtLeastAndroid12() async {
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    return androidInfo.version.sdkInt >= 31;
  }

  @override
  Widget build(BuildContext context) {
    if (PlatformUtils.isIOS) {
      return Container(color: AppColors.ff01A796);
    }

    if (PlatformUtils.isAndroid) {
      log.i("_build() -> _atLeastAndroid12=$_atLeastAndroid12");
      if (_atLeastAndroid12) {
        return Container(color: AppColors.ff01A796);
      }
    }

    return Container(
      color: AppColors.ff01A796,
      alignment: Alignment.center,
      child: SvgPicture.asset(
        AppImagePaths.habiLogoWhite,
        height: 82,
        width: 123.9,
      ),
    );
  }

}
