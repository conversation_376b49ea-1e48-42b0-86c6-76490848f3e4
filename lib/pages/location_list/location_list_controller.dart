import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/home_service.dart';
import 'package:habi_app/services/local_storage_service.dart';

class LocationListController extends BaseController {

  final localStorageService = LocalStorageService.to;
  final deviceListService = DeviceListService.to;
  final homeService = HomeService.to;
  List<AppHome> appHomes = [];

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    getAppHomes();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> getAppHomes() async {
    // if (homeService.homes.isNotEmpty) {
    //   appHomes = homeService.homes;
    //   update();
    //   return;
    // }

    updateLoading(true);

    try {
      appHomes = await homeService.loadList(isLoadRooms: false);
      update();
    } catch (e,s) {
      log.e('getAppHomes() -> failed: $e, $s');
    } finally {
      updateLoading(false);
    }
  }

  Future<void> changeLocation(AppHome appHome) async {
    if (appHome.deviceList == null || appHome.deviceList!.isEmpty) {
      log.i('changeLocation() -> no devices found');
      Get.back();
      return;
    }

    try {
      String thingGroup = appHome.deviceList!.firstWhere(
            (deviceId) => deviceListService.gatewayIds.contains(deviceId),
        orElse: () => '',
      );

      if (thingGroup.isEmpty) {
        log.i('changeLocation() -> 未找到 thing group');
        Get.back();
        return;
      }

      String? oldThingGroup = await localStorageService.getThingGroup();
      log.i('changeLocation() -> oldThingGroup: $oldThingGroup');

      if (oldThingGroup == thingGroup){
        log.i('changeLocation() -> same thing group');
        Get.back();
        return;
      }

      if (oldThingGroup != null && oldThingGroup.isNotEmpty) {
        await shutdownMatterClient(oldThingGroup);
      }

      log.i('changeLocation() -> newThingGroup: $thingGroup');
      await localStorageService.setThingGroup(thingGroup);

      AppEvent event = AppEvent(
        name: AppEvents.changeLocation,
        data: thingGroup,
      );

      GlobalService.to
          .getEventStreamController()
          .add(event);

      Get.back();
    } catch (e,s) {
      log.i('changeLocation() -> failed: $e, $s');
    }
  }

  Future<void> shutdownMatterClient(String thingGroup) async {
    try {
      String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
      log.i('shutdownMatterClient() -> mcThingName: $mcThingName');

      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId != null && fabricId.isNotEmpty) {
        CtFlutterMatterPlugin.getInstance()
            .shutdown(fabricId);
        log.i('shutdownMatterClient() -> shutdown succeeded!');
      } else {
        log.e('shutdownMatterClient() -> shutdown failed: fabricId = null');
      }
    } catch (e) {
      log.e('shutdownMatterClient() -> shutdown failed: $e');
    }
  }


}
