import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/models/app_home.dart';
import 'location_list_controller.dart';

class LocationListPage extends BasePage<LocationListController> {

  const LocationListPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'allLocations'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: Icon<PERSON>utton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18),
        child: _buildLocationList(context),
      )
    );
  }

  Widget _buildLocationList(BuildContext context) {
    return GetBuilder<LocationListController>(
        builder: (controller) {
          if (controller.appHomes.isEmpty) {
            return const SizedBox();
          }
          return ListView.builder(
            itemCount: controller.appHomes.length,
            itemBuilder: (context, index) {
              AppHome appHome = controller.appHomes[index];
              String name = appHome.name ?? '';
              return ListTile(
                title: Text(
                  name,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
                trailing: SvgPicture.asset(
                  AppImagePaths.arrowRightBlack,
                  width: 16,
                  height: 16,
                  colorFilter: ColorFilter.mode(
                      Theme.of(context)
                          .extension<AppThemeExtension>()!
                          .firstColor,
                      BlendMode.srcIn
                  ),
                ),
                onTap: () => _onItemClick(appHome),
              );
            },
          );
        },
    );
  }

  void _onItemClick(AppHome appHome) {
    controller.changeLocation(appHome);
  }

}
