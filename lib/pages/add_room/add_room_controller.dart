import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/helpers/retry_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/services/dev_service.dart';

class AddRoomController extends GetxController {
  late final RxList<HBRoom> _rooms;
  final String homeId;
  var isLoading = false.obs;

  List<HBRoom> get rooms => _rooms;

  AddRoomController({
    required this.homeId,
    required List<HBRoom> rooms,
  }) {
    _rooms = rooms.obs;
  }

  final DevService _devService = DevService.to;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();

  @override
  void onClose() {
    nameController.dispose();
    super.onClose();
  }

  Future<void> _getRooms() async {
    try {
      final rooms = await RetryHelper.execute(
        operation: () async {
          final result = await _devService
              .getHomeRooms(homeId)
              .timeout(const Duration(seconds: 30));
          if (!(result.success ?? false)) {
            throw Exception('Failed to get home rooms: ${result.errorCode}');
          }
          return result.rooms;
        },
        operationName: 'getHomeRooms',
        maxRetries: 1,
      );
      log.i(
          'AddRoomController -> getRooms() -> succeeded: ${rooms?.length} rooms');
      _rooms.value = rooms ?? [];
    } catch (e) {
      log.e('AddRoomController -> getRooms() -> failed: $e');
      rethrow;
    }
  }

  bool isRoomNameExists(String name) {
    return rooms.any((room) => room.name == name);
  }

  bool _validate() {
    return formKey.currentState?.validate() ?? false;
  }

  Future<bool> _isRoomNameExistsRemote(String name) async {
    await _getRooms();
    return isRoomNameExists(name);
  }

  Future<bool> _addRoom() async {
    try {
      final roomName = nameController.text;

      if (await _isRoomNameExistsRemote(roomName)) {
        log.i(
            'AddRoomController -> room name already exists in remote: $roomName');
        return true;
      }

      try {
        final result = await _devService
            .addRoom(homeId, roomName)
            .timeout(const Duration(seconds: 30));
        if (!(result.success ?? false)) {
          throw Exception('Failed to add room: ${result.errorCode}');
        }
        log.i('AddRoomController -> addRoom() -> succeeded: $roomName');
      } catch (e) {
        log.e('AddRoomController -> addRoom() -> failed: $e');
        if (await _isRoomNameExistsRemote(roomName)) {
          log.i(
              'AddRoomController -> addRoom() failed, but room name already exists in remote: $roomName');
          return true;
        }
        rethrow;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  Future<void> onAddRoomPressed() async {
    if (!_validate()) {
      return;
    }

    if (isLoading.value) {
      return;
    }

    isLoading.value = true;
    KeyboardHelper.dismissKeyboard(Get.context!);

    final success = await _addRoom();
    if (success) {
      Get.back(result: true);
      showSuccessSnackBar('add room succeeded');
    } else {
      isLoading.value = false;
      showErrorSnackBar('add room failed');
    }
  }
}
