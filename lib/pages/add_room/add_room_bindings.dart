import 'package:get/get.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/pages/add_room/add_room_controller.dart';

class AddRoomBindings extends Bindings {

  final String homeId;
  final List<HBRoom> rooms;

  AddRoomBindings({
    required this.homeId,
    required this.rooms,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => AddRoomController(homeId: homeId, rooms: rooms));
  }
}
