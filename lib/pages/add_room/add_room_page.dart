import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/add_room/add_room_controller.dart';

class AddRoomPage extends GetView<AddRoomController> {

  AddRoomPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'addRoom'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(left: 30.0, right: 30.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              SizedBox(height: 30.h,),


              Form(
                  key: controller.formKey,
                  child: Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: Opacity(
                          opacity: 0.5,
                          child: Text(
                            'name'.tr,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: AppFontWeights.medium,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 14.h,
                      ),
                      ConstrainedBox(
                        constraints:
                        const BoxConstraints(maxHeight: 148, minHeight: 48),
                        child: TextFormField(
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: AppFontWeights.regular,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                          controller: controller.nameController,
                          keyboardType: TextInputType.name,
                          validator: validateName,
                        ),
                      ),
                      SizedBox(
                        height: 14.h,
                      ),
                    ],
                  )
              ),

              SizedBox(
                height: 24.h,
              ),

              SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: Obx(() {
                    return FilledButton(
                      onPressed: controller.onAddRoomPressed,
                      child: controller.isLoading.value ?
                      const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ) : Text(
                        'add'.tr,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.medium,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.secondColor,
                        ),
                      ),
                    );
                  })
              ),


            ],
          ),
        ),
      )
    );
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (controller.isRoomNameExists(value)) {
      return 'roomNameExists'.tr;
    }

    return null;
  }

}

