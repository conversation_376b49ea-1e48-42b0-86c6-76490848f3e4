import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/value_completer.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class InstallerSettingController extends BaseController {
  final String thingName;
  final String gwThingName;

  InstallerSettingController({
    required this.thingName,
    required this.gwThingName,
  });

  static const String _tag = '[InstallerSettingController]';

  final deviceShadowService = DeviceShadowService.to;

  final tempOffset = RxnDouble();
  final minTempOffset = RxDouble(-2.5);
  final maxTempOffset = RxDouble(2.5);
  final tempOffsetStep = RxDouble(0.5);

  final connected = false.obs;

  Worker? _deviceShadowWorker;
  ValueCompleter<void, int>? _setTempOffsetCompleter;

  String get displayTempOffset {
    final offset = tempOffset.value;
    if (offset == null) {
      return '--°';
    }
    return offset == offset.toInt() ? '${offset.toInt()}°' : '$offset°';
  }

  @override
  void onInit() {
    super.onInit();

    _deviceShadowWorker = deviceShadowService.watchDeviceShadow(
      thingName,
      onDeviceShadowUpdated,
    );

    onDeviceShadowUpdated(deviceShadowService.getDeviceShadow(thingName));
  }

  void onDeviceShadowUpdated(AppShadow shadow) {
    log.i('shadow updated: $shadow');
    final it850 = IT850.fromJson(shadow.thingShadow, thingName);

    final thatConnected = it850.shadow?.state?.reported?.connected;
    final thatTempCalibr =
        it850.shadow?.state?.reported?.model?.properties?.sTherS?.tempCalibr;

    connected.value = thatConnected == 'true';

    if (thatTempCalibr != null) {
      tempOffset.value = thatTempCalibr / 10;
      if (_setTempOffsetCompleter?.isCompleted == false &&
          _setTempOffsetCompleter?.extraValue == thatTempCalibr) {
        _setTempOffsetCompleter!.complete();
      }
    }
  }

  @override
  void onClose() {
    if (_setTempOffsetCompleter?.isCompleted == false) {
      _setTempOffsetCompleter?.completeError(Exception('canceled'));
    }
    _deviceShadowWorker?.dispose();
    super.onClose();
  }

  void decreaseOffset() {
    double? currentOffset = tempOffset.value;
    if (currentOffset == null) {
      tempOffset.value = 0;
    } else if (currentOffset > minTempOffset.value) {
      tempOffset.value = currentOffset - tempOffsetStep.value;
    }
  }

  void increaseOffset() {
    double? currentOffset = tempOffset.value;
    if (currentOffset == null) {
      tempOffset.value = 0;
    } else if (currentOffset < maxTempOffset.value) {
      tempOffset.value = currentOffset + tempOffsetStep.value;
    }
  }

  Future<void> save() async {
    if (!connected.value) {
      DialogUtils.showErrorDialog(
        content: 'Device is not connected'.tr,
      );
      return;
    }

    final offset = tempOffset.value;
    if (offset == null) {
      DialogUtils.showErrorDialog(
        content: 'Temperature Offset is required',
      );
      return;
    }

    try {
      updateLoadingMessage('saving'.tr);
      await _saveTempOffset(offset);
      updateLoading(false);
    } catch (e) {
      updateLoading(false);
      DialogUtils.showErrorDialog(
        content: 'saveFailed'.tr,
      );
    }
  }

  Future<void> _saveTempOffset(double offset) async {
    try {
      final int newOffsetX10 = (offset * 10).toInt();
      final completer = ValueCompleter<void, int>(newOffsetX10);
      _setTempOffsetCompleter = completer;

      Future(() async {
        try {
          await deviceShadowService.updateDeviceProperties(
            thingName: thingName,
            property: {
              'ep1:sTherS:sTempCalibr': newOffsetX10,
            },
            subId: IT850.subId,
          );

          await Future.delayed(const Duration(seconds: 30));

          throw Exception('timeout');
        } catch (e) {
          if (!completer.isCompleted) {
            completer.completeError(e);
          }
        }
      });

      await completer.future;
    } catch (e) {
      log.e('$_tag -> save temperature offset failed', error: e);
      rethrow;
    }
  }
}
