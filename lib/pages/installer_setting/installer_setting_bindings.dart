import 'package:get/instance_manager.dart';
import 'installer_setting_controller.dart';

class InstallerSettingBindings extends Bindings {
  final String thingName;
  final String gwThingName;

  InstallerSettingBindings({
    required this.thingName,
    required this.gwThingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(
      () => InstallerSettingController(
        gwThingName: gwThingName,
        thingName: thingName,
      ),
    );
  }
}
