import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/widgets/expanded_scroll_view.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'installer_setting_controller.dart';

class InstallerSettingPage extends BasePage<InstallerSettingController> {
  const InstallerSettingPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'installerSetting'.tr,
      ),
      body: ExpandedScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        topContent: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(height: 33.h),
            Text(
              'installerSetting'.tr,
              style: TextStyles.medium18FirstColor,
            ),
            SizedBox(height: 33.h),
            _buildTemperatureOffset(),
            SizedBox(height: 33.h),
          ],
        ),
        bottomContent: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildButtons(),
            SizedBox(height: 113.h),
          ],
        ),
      ),
    );
  }

  Widget _buildTemperatureOffset() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Opacity(
          opacity: 0.5,
          child: Text(
            'temperatureOffset'.tr,
            style: TextStyles.regular16FirstColor,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          height: 48,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: AppColors.ff01A796),
          ),
          child: Row(
            children: [
              _buildOffsetButton(Icons.remove, controller.decreaseOffset),
              _buildDivider(),
              _buildOffsetValue(),
              _buildDivider(),
              _buildOffsetButton(Icons.add, controller.increaseOffset),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOffsetButton(IconData icon, VoidCallback onPressed) {
    return Expanded(
      child: IconButton(
        icon: Icon(
          icon,
          color: AppColorsHelper.firstColor,
        ),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildOffsetValue() {
    return Expanded(
      flex: 2,
      child: Center(
        child: Obx(
          () => Text(
            controller.displayTempOffset,
            style: TextStyles.regular16FirstColor,
          ),
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return SizedBox(
      height: 24,
      child: VerticalDivider(
        color: AppColorsHelper.firstColor,
        thickness: 1,
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        _buildButton(
          text: 'cancel'.tr.toUpperCase(),
          textColor: AppColors.ffFF4E4E,
          borderColor: AppColors.ffFF4E4E,
          backgroundColor: Colors.transparent,
          onPressed: () => Get.back(),
        ),
        const Spacer(),
        _buildButton(
          text: 'save'.tr.toUpperCase(),
          textColor: Colors.white,
          borderColor: AppColors.ff01A796,
          backgroundColor: AppColors.ff01A796,
          onPressed: () => controller.save(),
        ),
      ],
    );
  }

  Widget _buildButton({
    required String text,
    required Color textColor,
    required Color borderColor,
    required Color backgroundColor,
    required VoidCallback? onPressed,
  }) {
    return Flexible(
      child: OutlinedButton(
        onPressed: () => onPressed?.call(),
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: BorderSide(color: borderColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
          padding: const EdgeInsets.all(12.0),
        ),
        child: ConstrainedBox(
          constraints: const BoxConstraints(minWidth: 80),
          child: Text(
            text,
            style: TextStyles.medium16.copyWith(
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
