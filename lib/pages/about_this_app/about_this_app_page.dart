import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/widgets/habi_logo.dart';
import 'about_this_app_controller.dart';

class AboutThisAppPage extends BasePage<AboutThisAppController> {

  const AboutThisAppPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'aboutThisApp'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 36),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 34.h,),

              HabiLogo(
                height: 94.h,
                width: 160.w,
              ),

              SizedBox(height: 82.h,),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                      flex: 2,
                      child: Text(
                        'company'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      )
                  ),
                  SizedBox(width: 10.w,),
                  Expanded(
                      flex: 3,
                      child: Text(
                        'SALUS Controls, PLC\nwww.salus-tech.com',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      )
                  ),
                ],
              ),

              SizedBox(height: 18.h,),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 2,
                      child: Text(
                        'buildDate'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      )
                  ),
                  SizedBox(width: 10.w,),
                  Expanded(
                    flex: 3,
                      child: Text(
                        controller.buildDate,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      )
                  ),
                ],
              ),

              SizedBox(height: 18.h,),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 2,
                      child: Text(
                        'released'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      )
                  ),
                  SizedBox(width: 10.w,),
                  Expanded(
                    flex: 3,
                      child: Text(
                        controller.buildDate,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      )
                  ),
                ],
              ),

              SizedBox(height: 18.h,),

              GetBuilder<AboutThisAppController>(
                  builder: (controller) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                            flex: 2,
                            child: Text(
                              'version'.tr,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: AppFontWeights.regular,
                                color: Theme.of(context)
                                    .extension<AppThemeExtension>()!.firstColor,
                              ),
                            )
                        ),
                        SizedBox(width: 10.w,),
                        Expanded(
                            flex: 3,
                            child: InkWell(
                              onTap: () {
                                controller.versionCount++;
                                if (controller.versionCount == 5) {
                                  controller.versionCount = 0;
                                  showShareLogsDialog();
                                }
                              },
                              child: Text(
                                controller.appVersion,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: AppFontWeights.regular,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!.firstColor,
                                ),
                              ),
                            )
                        ),
                      ],
                    );
                  }
              ),



            ],
          ),
        ),
      ),
    );
  }

  void showShareLogsDialog() {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                'shareAppLogs'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                'appImprovementLogsRequest'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  controller.shareLogs();
                  Get.back();
                },
                child: Text(
                  'share'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

}

