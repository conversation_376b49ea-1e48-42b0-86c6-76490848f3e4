import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_loading.dart';
import 'package:habi_app/models/log_message.dart';
import 'package:habi_app/plugin/app_plugin.dart';
import 'package:habi_app/services/database_service.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:path/path.dart' as path;

class AboutThisAppController extends BaseController {

  final String buildDate = 'July 10, 2025';
  String appVersion = '';
  int versionCount = 0;

  @override
  void onReady() {
    super.onReady();
    fetchAppVersion();
  }

  Future<void> fetchAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    String buildNumber = packageInfo.buildNumber;
    appVersion = "$version ($buildNumber)";
    log.i('appVersion: $appVersion');
    update();
  }

  Future<void> shareLogs() async {
    updateLoadingMessage('share logs...');

    try {
      File? ctZipFile;
      Directory? ctLogDir;

      if (PlatformUtils.isAndroid) {
        try {
          String ctZipPath = await AppPlugin.getInstance().writeLog();
          debugPrint('shareLogs() -> write ct log success, path: $ctZipPath');
          try {
            ctZipFile = File(ctZipPath);
            String ctLogDirPath = path.dirname(ctZipPath);
            debugPrint('shareLogs() -> ct log dir path: $ctLogDirPath');
            ctLogDir = Directory(ctLogDirPath);
          } catch (e, s) {
            debugPrint('$e, $s');
          }
        } catch (e, s) {
          debugPrint('shareLogs() -> write ct log failed, $e, $s');
        }
      }

      // Get the document directory using path_provider
      final directory = await getApplicationDocumentsDirectory();
      debugPrint('shareLogs() -> directory path: ${directory.path}');

      Directory? appLogDir;
      try {
        appLogDir = Directory('${directory.path}/app_log');
        bool isExists = await appLogDir.exists();
        if (!isExists) {
          await appLogDir.create(recursive: true);
          debugPrint('shareLogs() -> create app log dir success: ${appLogDir.path}');
        }
      } catch (e) {
        debugPrint('shareLogs() -> create app log dir error: $e');
      }

      String dateFormatStr = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      File logFile = File('${directory.path}/app_log/log_$dateFormatStr.txt');
      DateFormat logDateFormat = DateFormat('yyyy-MM-dd HH-mm-ss');
      DatabaseService dbService = DatabaseService.to;
      bool queryFlag = true;
      int limit = 2000;
      int offset = 0;
      IOSink? sink;

      try {
        sink = logFile.openWrite(mode: FileMode.append);
        while(queryFlag) {
          List<LogMessage>? logs = await dbService.logDao?.queryAllLogs(limit, offset);
          if (logs == null || logs.isEmpty) {
            queryFlag = false;
            await sink.close();
            debugPrint('shareLogs() -> 没有数据了，查询结束...');
            break;
          }

          for (var log in logs) {
            final time = logDateFormat.format(log.time);
            final level = log.level.toUpperCase();
            sink.writeln('[$time] [$level] ${log.message}');
          }

          offset += limit;
          debugPrint('shareLogs() -> offset=$offset');
          loading.value = AppLoading(isLoading: true, message: 'share logs... \noffset:$offset');
        }
      } catch (e, s) {
        debugPrint('shareLogs() -> write log file error: $e, $s');
      } finally {
        queryFlag = false;
        await sink?.close();
      }

      // Create device info file
      final deviceInfoFile = File('${directory.path}/app_log/device_info_$dateFormatStr.txt');
      final deviceInfo = await DeviceInfoPlugin().deviceInfo;
      final deviceInfoList = deviceInfo.data.entries
          .map((entry) => '${entry.key}: ${entry.value}')
          .join('\n');
      try {
        await deviceInfoFile.writeAsString(deviceInfoList);
      } catch (e, s) {
        debugPrint('shareLogs() -> write device info file error: $e, $s');
      }

      // Create zip archive
      final appZipFile = File('${directory.path}/app_log/app_logs_$dateFormatStr.zip');
      debugPrint('shareLogs() -> app zip file path: ${appZipFile.path}');

      // Create a zip file with the logs and device info
      try {
        final encoder = ZipFileEncoder();
        encoder.create(appZipFile.path);
        encoder.addFile(logFile);
        encoder.addFile(deviceInfoFile);
        encoder.close();
      } catch (e, s) {
        debugPrint('shareLogs() -> could not create logs zip file: $e, $s');
      }

      List<XFile> xFiles = [];

      if (ctZipFile != null) {
        xFiles.add(XFile(ctZipFile.path));
      }

      xFiles.add(XFile(appZipFile.path));

      ShareResult result;
      if (PlatformUtils.isIOS) {
        final mqSize = MediaQuery.of(Get.context!).size;
        final deviceInfo = await DeviceInfoPlugin().deviceInfo;
        final deviceInfoList = deviceInfo.data.entries;

        var isIpad = deviceInfoList.any((entry) =>
            (entry.key == 'model' && entry.value.toString().contains('iPad')) ||
            (entry.key == 'name' && entry.value.toString().contains('iPad')) ||
            (entry.key == 'systemName' && entry.value.toString().contains('iPadOS'))
        );

        if (isIpad) {
          debugPrint("shareLogs() -> is ipad");
          result = await Share.shareXFiles(
            xFiles,
            subject: 'App Logs',
            // text: 'Attached application logs',
            sharePositionOrigin: Rect.fromLTWH(
                0,
                0,
                mqSize.width,
                mqSize.height / 4
            ),
          );
        } else {
          debugPrint("shareLogs() -> is iPhone");
          result = await Share.shareXFiles(
            xFiles,
            subject: 'App Logs',
            // text: 'Attached application logs',
          );
        }
      } else {
        debugPrint("shareLogs() -> is android");
        result = await Share.shareXFiles(
          xFiles,
          subject: 'App Logs',
          // text: 'Attached application logs',
        );
      }

      debugPrint('shareLogs() -> Share Logs Result: ${result.status}');

      // Delete the exported file
      // 这里的log是原生日志，这部分不管有没有分享成功都要删除
      try {
        debugPrint('shareLogs() -> deleting files...');

        if (ctLogDir != null) {
          await for (var entity in ctLogDir.list()) {
            await entity.delete(recursive: true);
          }
        }

        if (appLogDir != null) {
          await for (var entity in appLogDir.list()) {
            await entity.delete(recursive: true);
          }
        }

        debugPrint('shareLogs() -> files deleted successfully.');
      } catch (e, s) {
        debugPrint('shareLogs() -> failed to delete the files: $e, $s');
      }

      if (result.status == ShareResultStatus.success) {
        // await dbService.logDao?.deleteAllLogs();
        showSuccessSnackBar('Logs shared successfully!');
      }

    } catch (e, s) {
      log.e('shareLogs() -> error: $e, $s');
      showErrorSnackBar('Failed to share logs');
    } finally {
      updateLoading(false);
    }
  }




}
