import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/pages/locations/locations_controller.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'package:habi_app/widgets/habi_button.dart';

class LocationsPage extends GetView<LocationsController> {
  const LocationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'overview'.tr,
      ),
      body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 35),
          child: GetBuilder<LocationsController>(
            builder: (_) {
              if (controller.isLoading) {
                return _buildLoading();
              }
              if (controller.hasError) {
                return _buildTryAgain();
              }
              if (controller.locations.isEmpty) {
                return _buildEmpty();
              }
              return _buildBody();
            },
          )),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: CircularProgressIndicator(
        color: AppColors.ff01A796,
      ),
    );
  }

  Widget _buildTryAgain() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'failedToLoadData'.tr,
            style: TextStyles.regular16FirstColor,
          ),
          const SizedBox(height: 20),
          HabiButton(
            onPressed: controller.tryAgain,
            text: 'tryAgain'.tr,
            textColor: Colors.white,
            backgroundColor: AppColors.ff01A796,
          ),
        ],
      ),
    );
  }

  Widget _buildEmpty() {
    return const SizedBox.shrink();
  }

  Widget _buildBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: _buildLocationList(),
        ),
        const SizedBox(height: 40),
        _buildSaveButton(),
        const SizedBox(height: 45),
      ],
    );
  }

  Widget _buildLocationList() {
    return ListView(
      children: controller.locations.map((location) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildLocationItem(location),
            if (location.expanded) _buildLocationExpanded(location),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildLocationItem(Location location) {
    return InkWell(
      onTap: () => controller.selectLocation(location),
      child: Row(
        children: [
          Radio<String>(
            value: location.id,
            groupValue: controller.selectedLocationId,
            activeColor: AppColors.ff01A796,
            onChanged: null,
          ),
          Expanded(
            child: Text(
              location.name,
              style: TextStyles.medium20FirstColor,
            ),
          ),
          IconButton(
            icon: Icon(
              location.expanded
                  ? Icons.keyboard_arrow_down
                  : Icons.keyboard_arrow_right,
              size: 28,
            ),
            onPressed: () => controller.toggleLocation(location),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationExpanded(Location location) {
    if (location.isLoading) {
      return _buildLoading();
    }
    if (location.hasError) {
      return _buildTryAgain();
    }
    if (location.rooms.isEmpty) {
      return _buildEmpty();
    }
    return Padding(
      padding: const EdgeInsets.only(left: 48.0 + 32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: location.rooms.map((room) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      room.name,
                      style: TextStyles.regular18FirstColor,
                    ),
                  ),
                  _buildDeleteRoomButton(location, room),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(
                  left: 32.0,
                  right: 48.0,
                ),
                child: _buildDeviceList(room),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDeleteRoomButton(Location location, Room room) {
    return IconButton(
      icon: SizedBox.square(
        dimension: 28,
        child: Center(
          child: SvgPicture.asset(
            AppImagePaths.delete,
            theme: SvgTheme(
              currentColor: Get.theme.scaffoldBackgroundColor,
            ),
          ),
        ),
      ),
      onPressed: () => controller.onDeleteRoomPressed(
        location: location,
        room: room,
      ),
    );
  }

  Widget _buildDeviceList(Room room) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: room.devices.map((device) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Text(
            device.getDeviceName(),
            style: TextStyles.regular16FirstColor,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSaveButton() {
    return HabiButton(
      onPressed: controller.saveLocation,
      text: 'saveDisplayedLocation'.tr,
      textColor: Colors.white,
      backgroundColor: AppColors.ff01A796,
    );
  }
}
