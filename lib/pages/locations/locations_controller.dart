import 'dart:async';

import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_device.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/helpers/device_helper.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/retry_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/dev/hb_home.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/services/Iot_service.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_list_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/home_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class Room {
  final String id;
  final String name;
  final List<BaseDevice> devices;

  Room({
    required this.id,
    required this.name,
    required this.devices,
  });
}

class Location {
  final String id;
  final String name;
  final List<String> gatewayIds;
  List<Room> rooms;
  bool expanded;
  bool isLoading;
  bool hasError;

  Location({
    required this.id,
    required this.name,
    required this.gatewayIds,
    this.rooms = const [],
    this.expanded = false,
    this.isLoading = false,
    this.hasError = false,
  });
}

class _CustomException implements Exception {
  final String message;

  _CustomException(this.message);

  @override
  String toString() {
    return message;
  }
}

class LocationsController extends GetxController {
  static const String _logTag = '[LocationsController]';
  final demoModeService = DemoModeService.to;
  final deviceListService = DeviceListService.to;
  final globalService = GlobalService.to;
  final localStorageService = LocalStorageService.to;
  final deviceShadowService = DeviceShadowService.to;
  final dynamoDBService = DynamoDBService.to;
  final iotService = IotService.to;
  final homeService = HomeService.to;
  final devService = DevService.to;

  String? selectedLocationId;
  List<Location> locations = [];
  bool isLoading = false;
  bool hasError = false;
  Map<String, BaseDevice> _deviceMap = {};
  String? currentGatewayId;

  @override
  void onInit() {
    super.onInit();

    _init();
  }

  Future<void> _init() async {
    try {
      isLoading = true;
      hasError = false;
      _deviceMap = {};
      update();

      currentGatewayId = await localStorageService.getThingGroup();
      log.d("$_logTag _init() -> Current gateway ID: $currentGatewayId");

      final homes = await _fetchHomes();
      final newLocations = <Location>[];
      Location? currentLocation;

      log.d("$_logTag _init() -> Homes length: ${homes.length}");

      if (homes.isNotEmpty) {
        final userGatewayIds = await _fetchGateways();
        log.d("$_logTag _init() -> User gateway IDs: $userGatewayIds");

        for (final home in homes) {
          final homeId = home.homeId;
          final homeName = home.name;

          if (homeId != null && homeName != null) {
            final homeGatewayIds = home.deviceList
                ?.where((id) => userGatewayIds.contains(id))
                .toSet()
                .toList();
            final location = Location(
              id: homeId,
              name: homeName,
              gatewayIds: homeGatewayIds ?? [],
            );

            newLocations.add(location);
            if (currentLocation == null &&
                location.gatewayIds.contains(currentGatewayId)) {
              currentLocation = location;
            }
          }
        }
      }

      selectedLocationId = currentLocation?.id;
      locations = newLocations;
    } catch (e) {
      hasError = true;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> _loadLocation(Location location) async {
    try {
      log.d(
          "$_logTag _loadLocation() -> Loading location: ${location.id}, ${location.name}");
      location.isLoading = true;
      location.hasError = false;

      final rooms = await _fetchRooms(location.id);
      final newRooms = <Room>[];
      log.d("$_logTag _loadLocation() -> Fetched rooms count: ${rooms.length}");

      if (rooms.isNotEmpty) {
        final userDeviceIds = await _fetchUserDeviceIds(location.gatewayIds);

        for (final room in rooms) {
          final roomId = room.roomId;
          final roomName = room.name;

          if (roomId != null && roomName != null) {
            final roomDeviceIds = room.deviceList
                ?.where((device) => userDeviceIds.contains(device))
                .toSet()
                .toList();

            final List<BaseDevice> newDevices = [];
            if (roomDeviceIds != null && roomDeviceIds.isNotEmpty) {
              await Future.wait(
                roomDeviceIds.map((deviceId) async {
                  final device = await _createDeviceModel(deviceId);
                  newDevices.add(device);
                }),
              );
            }

            final newRoom = Room(
              id: roomId,
              name: roomName,
              devices: newDevices,
            );
            newRooms.add(newRoom);
          }
        }
      }

      location.rooms = newRooms;
      log.d(
          "$_logTag _loadLocation() -> Completed loading location: ${location.id} with ${newRooms.length} rooms");
    } catch (e) {
      log.e(
        "$_logTag _loadLocation() -> Failed to load location: ${location.id}",
        error: e,
      );
      location.hasError = true;
    } finally {
      location.isLoading = false;
      update();
    }
  }

  Future<List<HBHome>> _fetchHomes() async {
    return RetryHelper.execute<List<HBHome>>(
      operation: () async {
        log.d("$_logTag _fetchHomes() -> Fetching homes");
        final response = await devService
            .getUserHomes()
            .timeout(const Duration(seconds: 30));
        if (response.success != true) {
          log.e(
              "$_logTag _fetchHomes() -> Failed with error code: ${response.errorCode}");
          throw Exception('Failed to fetch homes: ${response.errorCode}');
        }
        log.d(
            "$_logTag _fetchHomes() -> Successfully fetched ${response.homes?.length ?? 0} homes");
        return response.homes ?? [];
      },
      operationName: 'Fetch homes',
      maxRetries: 2,
    );
  }

  Future<List<HBRoom>> _fetchRooms(String homeId) async {
    return RetryHelper.execute<List<HBRoom>>(
      operation: () async {
        log.d("$_logTag _fetchRooms() -> Fetching rooms for home: $homeId");
        final response = await devService
            .getHomeRooms(homeId)
            .timeout(const Duration(seconds: 30));
        if (response.success != true) {
          log.e(
              "$_logTag _fetchRooms() -> Failed with error code: ${response.errorCode}");
          throw Exception(
            'Failed to fetch rooms for home $homeId: ${response.errorCode}',
          );
        }
        log.d(
            "$_logTag _fetchRooms() -> Successfully fetched ${response.rooms?.length ?? 0} rooms for home: $homeId");
        return response.rooms ?? [];
      },
      operationName: 'Fetch rooms for home $homeId',
      maxRetries: 2,
    );
  }

  Future<HBRoom?> _fetchRoom({
    required String homeId,
    required String roomId,
  }) async {
    log.d("$_logTag _fetchRoom() -> Fetching room: $roomId");
    final rooms = await _fetchRooms(homeId);
    log.d("$_logTag _fetchRoom() -> Successfully fetched room: $roomId");
    return rooms.firstWhereOrNull((r) => r.roomId == roomId);
  }

  Future<List<String>> _fetchGateways() async {
    return RetryHelper.execute<List<String>>(
      operation: () async {
        log.d("$_logTag _fetchGateways() -> Fetching gateways");
        final userDevices = await dynamoDBService
            .fetchUserToDeviceList()
            .timeout(const Duration(seconds: 30));
        return userDevices?.getAllNames() ?? [];
      },
      operationName: "$_logTag Fetch User Gateway IDs ",
      maxRetries: 2,
    );
  }

  Future<List<String>> _fetchGatewayDevices(String gatewayId) async {
    final thingGroupName = Sait85rHelper.getThingGroupName(gatewayId);
    return await RetryHelper.execute<List<String>>(
      operation: () async {
        log.d(
            "$_logTag _fetchGatewayDevices() -> Fetching devices for gateway: $gatewayId");
        final devices = await iotService
            .listThingsInThingGroup(thingGroupName)
            .timeout(const Duration(seconds: 30));
        log.d(
            "$_logTag _fetchGatewayDevices() -> Successfully fetched ${devices?.length ?? 0} devices for gateway: $gatewayId");
        return devices ?? [];
      },
      operationName: "$_logTag Fetch Gateway Devices $gatewayId ",
      maxRetries: 2,
    );
  }

  Future<List<String>> _fetchUserDeviceIds(List<String> gatewayIds) async {
    final responses = await Future.wait(
      gatewayIds.map(_fetchGatewayDevices),
    );
    return responses.expand((ids) => ids).toList();
  }

  Future<void> _changeLocation(String thingGroup) async {
    try {
      log.d(
          "$_logTag _changeLocation() -> Changing to location with thing group: $thingGroup");
      DialogUtils.showLoadingDialog(message: 'saving'.tr);

      final String oldThingGroup = currentGatewayId ?? '';
      if (oldThingGroup.isNotEmpty) {
        await _shutdownMatterClient(oldThingGroup);
      }

      await localStorageService.setThingGroup(thingGroup);
      log.d(
          "$_logTag _changeLocation() -> Thing group set in local storage: $thingGroup");

      globalService
          .getEventStreamController()
          .add(AppEvent(name: AppEvents.changeLocation, data: thingGroup));
      log.d("$_logTag _changeLocation() -> Location change event dispatched");

      DialogUtils.hideDialog();
      Get.back();
    } catch (e) {
      log.e(
        "$_logTag _changeLocation() -> Failed to change location",
        error: e,
      );
      DialogUtils.hideDialog();
      DialogUtils.showErrorDialog(
        title: 'saveFailed'.tr,
        content: e.toString(),
      );
    }
  }

  Future<void> _setLeaveNetwork(BaseDevice device) async {
    final setLeaveNetworkPayload = device.getLeaveNetworkPayload();
    if (setLeaveNetworkPayload != null) {
      log.d(
          "$_logTag _setLeaveNetwork() -> Set leave network for device: ${device.thingName}");
      await RetryHelper.execute<void>(
        operation: () async {
          await deviceShadowService.updateDeviceProperties(
            thingName: device.thingName,
            property: setLeaveNetworkPayload,
            subId: device.baseKey,
          );
        },
        operationName: "$_logTag Set Leave Network ${device.thingName}",
        maxRetries: 2,
      );
    }
  }

  Future<bool> _checkThingList({
    required List<String> gatewayIds,
    required List<String> deletedDeviceIds,
  }) async {
    const int maxAttempts = 10;
    const Duration checkInterval = Duration(seconds: 6);

    for (int attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        log.d("$_logTag _checkThingList() -> Checking thing list");
        final userDeviceIds = await _fetchUserDeviceIds(gatewayIds);

        if (deletedDeviceIds.every((id) => !userDeviceIds.contains(id))) {
          log.d(
              "$_logTag _checkThingList() -> All deleted devices have been removed");
          return true;
        }
        log.d("$_logTag _checkThingList() -> Some deleted devices still exist");
      } catch (e) {
        log.e("$_logTag _checkThingList() -> Failed", error: e);
      }

      if (attempt < maxAttempts - 1) {
        await Future.delayed(checkInterval);
      }
    }

    log.d("$_logTag _checkThingList() -> Failed to remove all deleted devices");
    return false;
  }

  Future<void> _deleteRoom({
    required String homeId,
    required String roomId,
  }) async {
    return RetryHelper.execute<void>(
      operation: () async {
        try {
          log.i("$_logTag _deleteRoom() -> Deleting room: $roomId");
          await devService.deleteRoom(roomId);
          log.i("$_logTag _deleteRoom() -> Deleted room: $roomId");
        } catch (e) {
          log.e("$_logTag _deleteRoom() -> Failed to delete room", error: e);
          final room = await _fetchRoom(
            homeId: homeId,
            roomId: roomId,
          );
          if (room == null) {
            log.i("$_logTag _deleteRoom() -> Room has been deleted");
            return;
          }
          rethrow;
        }
      },
      operationName: 'Delete room $roomId',
      maxRetries: 2,
    );
  }

  Future<BaseDevice> _createDeviceModel(String deviceId) async {
    log.d(
        "$_logTag _createDeviceModel() -> Creating device model for: $deviceId");
    final shadow = await deviceShadowService
        .fetchDeviceShadow(deviceId, maxRetries: 2)
        .timeout(const Duration(seconds: 60));
    final device = DeviceHelper.createDeviceModel(
      thingName: deviceId,
      shadow: shadow,
    );
    _deviceMap[deviceId] = device;
    log.d(
        "$_logTag _createDeviceModel() -> Device model created for: $deviceId, type: ${device.runtimeType}");
    return device;
  }

  void toggleLocation(Location location) {
    log.d(
        "$_logTag toggleLocation() -> Toggling location: ${location.id}, ${location.name}, expanded: ${location.expanded}");
    location.expanded = !location.expanded;
    if (location.expanded && !location.isLoading) {
      _loadLocation(location);
    }
    update();
  }

  void selectLocation(Location location) {
    log.d(
        "$_logTag selectLocation() -> Selected location: ${location.id}, ${location.name}");
    selectedLocationId = location.id;
    update();
  }

  void tryAgain() {
    log.d("$_logTag tryAgain() -> Retrying initialization");
    _init();
  }

  Future<void> _handleDeleteRoom({
    required Location location,
    required Room room,
  }) async {
    globalService
        .getEventStreamController()
        .add(AppEvent(name: AppEvents.refreshDevicePage));

    try {
      log.d(
          "$_logTag _handleDeleteRoom() -> Handling delete room: ${room.id}, ${room.name} in location: ${location.id}");
      DialogUtils.showLoadingDialog(message: 'deleting'.tr);

      final hbRoom = await _fetchRoom(
        homeId: location.id,
        roomId: room.id,
      );
      final hbRoomDeviceIds = hbRoom?.deviceList ?? [];
      log.d(
          "$_logTag _handleDeleteRoom() -> Room device count: ${hbRoomDeviceIds.length}");

      if (hbRoomDeviceIds.isNotEmpty) {
        final filteredDevices = room.devices
            .where((device) => hbRoomDeviceIds.contains(device.thingName))
            .toList();
        log.d(
            "$_logTag _handleDeleteRoom() -> Filtered devices count: ${filteredDevices.length}");

        if (filteredDevices.isNotEmpty) {
          log.d(
              "$_logTag _handleDeleteRoom() -> Setting devices to leave network");
          await Future.wait(
            filteredDevices.map((device) async {
              await _setLeaveNetwork(device);
            }),
          );

          log.d(
              "$_logTag _handleDeleteRoom() -> Checking thing list for deleted devices");
          final isThingListCleared = await _checkThingList(
            gatewayIds: location.gatewayIds,
            deletedDeviceIds: filteredDevices.map((d) => d.thingName).toList(),
          );

          if (!isThingListCleared) {
            log.e("$_logTag _handleDeleteRoom() -> Thing list not cleared");
            throw _CustomException('deleteLocationDevicesFailed'.tr);
          }
        }
      }

      log.d("$_logTag _handleDeleteRoom() -> Deleting room: ${room.id}");
      await _deleteRoom(
        homeId: location.id,
        roomId: room.id,
      );

      locations
          .firstWhereOrNull((item) => item.id == location.id)
          ?.rooms
          .removeWhere((item) => item.id == room.id);

      log.d(
          "$_logTag _handleDeleteRoom() -> Room deleted successfully: ${room.id}");

      DialogUtils.hideDialog();
    } catch (e) {
      log.e("$_logTag _handleDeleteRoom() -> Failed to delete room", error: e);
      DialogUtils.hideDialog();
      DialogUtils.showErrorDialog(
        title: 'deleteFailed'.tr,
        content: e is _CustomException ? e.message : 'deleteLocationFailed'.tr,
        onPrimaryButtonPressed: tryAgain,
      );
    }
    update();
  }

  void onDeleteRoomPressed({
    required Location location,
    required Room room,
  }) async {
    if (demoModeService.isDemo && room.devices.isNotEmpty) {
      showSnackBar('cannotDeleteRoomInDemoMode'.tr);
      return;
    }

    log.d(
        "$_logTag onDeleteRoomPressed() -> Delete room button pressed for: ${room.id}, ${room.name} in location: ${location.id}");
    DialogUtils.showDialog(
      paragraphs: [
        'deleteLocationPrompt'.tr,
        'deleteLocationPrompt2'.tr,
      ],
      primaryButtonText: 'cancel'.tr.toUpperCase(),
      secondaryButtonText: 'delete'.tr.toUpperCase(),
      secondaryButtonColor: AppColors.ffFF4E4E,
      onSecondaryButtonPressed: () => _handleDeleteRoom(
        location: location,
        room: room,
      ),
    );
  }

  void saveLocation() {
    log.d(
        "$_logTag saveLocation() -> Saving location, selected ID: $selectedLocationId");
    if (selectedLocationId == null) {
      log.d("$_logTag saveLocation() -> No location selected, returning");
      Get.back();
      return;
    }

    final location = locations.firstWhereOrNull(
      (item) => item.id == selectedLocationId,
    );

    if (location == null || location.gatewayIds.isEmpty) {
      log.d(
          "$_logTag saveLocation() -> Location not found or has no gateways, returning");
      Get.back();
      return;
    }

    final thingGroup = location.gatewayIds.first;

    if (thingGroup.isEmpty) {
      log.d("$_logTag saveLocation() -> Thing group is empty, returning");
      Get.back();
      return;
    }

    if (currentGatewayId == thingGroup) {
      log.d(
          "$_logTag saveLocation() -> Current gateway is the same as selected, returning");
      Get.back();
      return;
    }

    log.d(
        "$_logTag saveLocation() -> Changing location to thing group: $thingGroup");
    _changeLocation(thingGroup);
  }

  Future<void> _shutdownMatterClient(String thingGroup) async {
    try {
      String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
      log.i('$_logTag shutdownMatterClient() -> mcThingName: $mcThingName');

      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId != null && fabricId.isNotEmpty) {
        CtFlutterMatterPlugin.getInstance()
            .shutdown(fabricId);
        log.i('$_logTag shutdownMatterClient() -> shutdown succeeded!');
      } else {
        log.e('$_logTag shutdownMatterClient() -> shutdown failed: fabricId = null');
      }
    } catch (e) {
      log.e('$_logTag shutdownMatterClient() -> shutdown failed: $e');
    }
  }
}
