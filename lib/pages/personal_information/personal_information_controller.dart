import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/control_modes.dart';
import 'package:habi_app/constants/user_attribute_keys.dart';
import 'package:habi_app/helpers/app_language_helper.dart';
import 'package:habi_app/helpers/app_theme_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_language.dart';
import 'package:habi_app/models/dev/hb_home_response.dart';
import 'package:habi_app/models/device_provision/device_provision_result.dart';
import 'package:habi_app/pages/change_password/change_password_bindings.dart';
import 'package:habi_app/pages/change_password/change_password_page.dart';
import 'package:habi_app/pages/login/login_bindings.dart';
import 'package:habi_app/pages/login/login_page.dart';
import 'package:habi_app/services/demo_mode_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_provision_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';

class PersonalInformationController extends BaseController {
  final demoModeService = DemoModeService.to;
  final userAttributesService = UserAttributesService.to;

  PersonalInformationController();

  final TextEditingController nicknameController = TextEditingController();
  final TextEditingController nameAndSurnameController = TextEditingController();

  var editNickname = false.obs;
  var editNameAndSurname = false.obs;

  var languageEntries = <DropdownMenuEntry<String>>[].obs;
  var themeEntries = <DropdownMenuEntry<String>>[].obs;

  var selectedCountry = ''.obs;
  var selectedLanguage = ''.obs;
  var selectedTheme = ''.obs;
  var selectedControlMode = 0.obs;
  var nickname = ''.obs;
  var nameAndSurname = ''.obs;

  @override
  void onInit() {
    super.onInit();
    nickname.value = userAttributesService.name ?? '';
    nameAndSurname.value = userAttributesService.familyName ?? '';
    selectedCountry.value = userAttributesService.country ?? '';
  }

  @override
  void onReady() {
    super.onReady();
    Future(() async {
      updateLoading(true);

      languageEntries.value = _buildDropdownMenuEntriesForLanguages();
      themeEntries.value = _buildDropdownMenuEntriesForThemes();

      LocalStorageService localStorageService = LocalStorageService.to;

      selectedControlMode.value = await localStorageService.getControlMode() ?? ControlModes.remote;

      String? thatLanguage = await localStorageService.getLanguage();
      selectedLanguage.value = thatLanguage ?? 'en';

      String? thatTheme = await localStorageService.getTheme();
      selectedTheme.value = thatTheme ?? 'light';

      await UserAttributesService.to.fetchAttributes();
      final attrs = UserAttributesService.to.attributes.value;
      if (attrs != null) {
        nicknameController.text = attrs.name;
        nickname.value = attrs.name;
        nameAndSurnameController.text = attrs.familyName;
        nameAndSurname.value = attrs.familyName;
        selectedCountry.value = attrs.country;
      }

      updateLoading(false);
    });
  }

  @override
  void onClose() {
    nicknameController.dispose();
    nameAndSurnameController.dispose();
    super.onClose();
  }

  void changeCountry(String country) async {
    if (selectedCountry.value == country) {
      return;
    }

    updateLoading(true);
    try {
      selectedCountry.value = country;
      await userAttributesService.updateCountry(country);
      log.i('changeCountry() -> successfully');
      showSuccessSnackBar('successfully changed country.');
    } catch (e, r) {
      log.e('changeCountry() -> failed to publish country to cloud: $e, $r');
      selectedCountry.value = userAttributesService.country ?? '';
      showErrorSnackBar('failed to change country.');
    } finally {
      updateLoading(false);
    }
  }

  void changeLanguage(String language) async {
    if (selectedLanguage.value == language) {
      return;
    }
    selectedLanguage.value = language;
    await LocalStorageService.to.setLanguage(language);
  }

  void changeTheme(String theme) async {
    if (selectedTheme.value == theme) {
      return;
    }

    selectedTheme.value = theme;

    if (theme == 'light') {
      Get.changeTheme(appLightTheme());
    } else {
      Get.changeTheme(appDarkTheme());
    }

    await LocalStorageService.to.setTheme(theme);
  }

  void changePassword() {
    if (demoModeService.isDemo) {
      showSnackBar('featureNotAvailableInDemoMode'.tr);
      return;
    }
    Get.to(() => ChangePasswordPage(), binding: ChangePasswordBindings());
  }

  List<DropdownMenuEntry<String>> _buildDropdownMenuEntriesForLanguages() {
    final List<AppLanguage> appLanguages = AppLanguageHelper.getLanguageList();
    final List<DropdownMenuEntry<String>> dropdownMenuEntries = [];
    for (final AppLanguage appLanguage in appLanguages) {
      dropdownMenuEntries.add(
          DropdownMenuEntry<String>(
              value: appLanguage.code,
              label: appLanguage.name
          )
      );
    }
    return dropdownMenuEntries;
  }

  List<DropdownMenuEntry<String>> _buildDropdownMenuEntriesForThemes() {
    return [
      const DropdownMenuEntry<String>(
          value: 'light',
          label: 'Light'
      ),
      const DropdownMenuEntry<String>(
          value: 'dark',
          label: 'Dark'
      )
    ];
  }

  Future<void> deleteUserProfile() async {
    updateLoading(true);

    if (demoModeService.isDemo) {
      await demoModeService.deleteDemoUserProfile();
    } else {
      try {
        DeviceProvisionResult result = await DeviceProvisionService.to
            .removeUserRecord()
            .timeout(const Duration(seconds: 30));

        if (!result.isSuccess()) {
          updateLoading(false);
          showErrorSnackBar('delete user profile failed.');
          return;
        }
      } catch (e) {
        updateLoading(false);
        showErrorSnackBar('delete user profile failed.');
        return;
      }

      try {
        HBHomeResponse response = await DevService.to.getUserHomes()
            .timeout(const Duration(seconds: 30));
        bool isSuccess = response.success ?? false;
        if (isSuccess) {
          var homes = response.homes;
          if (homes != null && homes.isNotEmpty) {
            for (var home in homes) {
              try {
                log.i('deleteUserProfile() -> deleteHome..., homeId: ${home.homeId}');
                await DevService.to.deleteHome(home.homeId!)
                    .timeout(const Duration(seconds: 30));
                log.i('deleteUserProfile() -> deleteHome succeeded');
              } catch (e) {
                log.e('deleteUserProfile() -> deleteHome failed: $e');
              }
            }
          }
        }
      } catch (e) {
        log.e('deleteUserProfile() -> deleteHome failed: $e');
      }
    }

    await clearLocalData();

    Get.offAll(() => LoginPage(),
        routeName: LoginPage.routeName, binding: LoginBindings());

    updateLoading(false);

    showSuccessSnackBar('delete user profile success.');
  }

  Future<void> clearLocalData() async {
    LocalStorageService localStorageService = LocalStorageService.to;
    try {
      await localStorageService.eraseAllData();
    } catch (e) {
      log.e('deleteUserProfile() -> clear all data failed: $e');
    }
  }

  Future<void> changeName() async {
    String thatNickName = nicknameController.text;
    String thatNameAndSurname = nameAndSurnameController.text;

    if (thatNickName.isEmpty || thatNameAndSurname.isEmpty) {
      log.e('name cannot be empty');
      return;
    }

    if (thatNickName == nickname.value && thatNameAndSurname == nameAndSurname.value) {
      log.e('name cannot be same as before');
      return;
    }

    updateLoading(true);

    try {
      await UserAttributesService.to.updateAttributes({
        UserAttributeKeys.name: thatNickName,
        UserAttributeKeys.familyName: thatNameAndSurname,
      });

      nickname.value = thatNickName;
      nameAndSurname.value = thatNameAndSurname;

      await const Duration(seconds: 1).delay();

      log.i('changeName() -> successfully');
      showSuccessSnackBar('successfully changed name.');
      updateLoading(false);
    } catch (e, r) {
      log.e('changeName() -> failed to publish name to cloud: $e, $r');
      showErrorSnackBar('failed to change name.');
      updateLoading(false);
    }
  }

}
