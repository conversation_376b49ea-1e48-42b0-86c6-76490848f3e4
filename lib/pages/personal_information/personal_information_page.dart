import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/control_modes.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/personal_information/personal_information_controller.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/widgets/country_dropdown_menu.dart';
import 'package:habi_app/widgets/delete_widget.dart';

class PersonalInformationPage extends BasePage<PersonalInformationController> {

  const PersonalInformationPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            'personalInformations'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppImagePaths.backwardArrow,
              width: 26,
              height: 26,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 36),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
          
                SizedBox(height: 33.h),
          
                Obx(() {
                  return _buildPersonalItem(
                      context: context,
                      title: 'changeNickname'.tr,
                      subTitle: controller.nickname.value,
                      showEditTextField: controller.editNickname.value,
                      controller: controller.nicknameController,
                      onTap: () {
                        controller.editNickname.value = !controller.editNickname.value;
                      },
                      onFieldSubmitted: (value) {
                        log.i('onFieldSubmitted() -> value: $value');
                        controller.editNickname.value = false;
                        controller.editNameAndSurname.value = false;
                        controller.changeName();
                      }
                  );
                }),
          
                SizedBox(height: 33.h),
          
                Obx(() {
                  return _buildPersonalItem(
                      context: context,
                      title: 'nameAndSurname'.tr,
                      subTitle: controller.nameAndSurname.value,
                      showEditTextField: controller.editNameAndSurname.value,
                      controller: controller.nameAndSurnameController,
                      onTap: () {
                        controller.editNameAndSurname.value = !controller.editNameAndSurname.value;
                      },
                      onFieldSubmitted: (value) {
                        log.i('onFieldSubmitted() -> value: $value');
                        controller.editNickname.value = false;
                        controller.editNameAndSurname.value = false;
                        controller.changeName();
                      }
                  );
                }),

                SizedBox(height: 33.h),
          
                Opacity(
                  opacity: 0.5,
                  child: Text(
                      'country'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      )
                  ),
                ),
          
                SizedBox(height: 14.h),

                Obx(() {
                  return CountryDropdownMenu(
                    onChanged: (String countryCode) {
                      controller.changeCountry(countryCode);
                    },
                    selectedCountryCode: controller.selectedCountry.value,
                    width: Get.width - 72,
                    menuHeight: 350.h,
                  );
                }),

                SizedBox(height: 33.h),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                    'language'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),

                SizedBox(height: 14.h,),

                Obx(() {
                  return DropdownMenu(
                    width: Get.width - 72,
                    menuHeight: 350.h,
                    dropdownMenuEntries: controller.languageEntries,
                    initialSelection: controller.selectedLanguage.value,
                    onSelected: _onLanguageSelected,
                  );
                }),

                SizedBox(height: 33.h),

                Opacity(
                  opacity: 0.5,
                  child: Text(
                      'Theme'.tr,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.medium,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      )
                  ),
                ),

                SizedBox(height: 14.h),

                Obx(() {
                  return DropdownMenu(
                    width: Get.width - 72,
                    menuHeight: 350,
                    dropdownMenuEntries: controller.themeEntries,
                    initialSelection: controller.selectedTheme.value,
                    onSelected: _onThemeSelected,
                  );
                }),

                Visibility(
                  visible: false,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(height: 33.h),

                      Opacity(
                        opacity: 0.5,
                        child: Text(
                            'controlMode'.tr,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: AppFontWeights.medium,
                              color: Theme.of(context)
                                  .extension<AppThemeExtension>()!.firstColor,
                            )
                        ),
                      ),

                      Obx(() {
                        return Row(
                          children: [
                            Radio<int>(
                              value: ControlModes.remote,
                              groupValue: controller.selectedControlMode.value,
                              onChanged: (int? value) async {
                                controller.selectedControlMode.value = value!;
                                await LocalStorageService.to.setControlMode(value);
                              },
                            ),
                            Text(
                                'Remote'.tr,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: AppFontWeights.medium,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!.firstColor,
                                )
                            ),
                            const SizedBox(width: 50,),
                            Radio<int>(
                              value: ControlModes.local,
                              groupValue: controller.selectedControlMode.value,
                              onChanged: (int? value) async {
                                controller.selectedControlMode.value = value!;
                                await LocalStorageService.to.setControlMode(value);
                              },
                            ),
                            Text(
                                'Local'.tr,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: AppFontWeights.medium,
                                  color: Theme.of(context)
                                      .extension<AppThemeExtension>()!.firstColor,
                                )
                            ),
                          ],
                        );
                      })
                    ],
                  ),
                ),
          
                SizedBox(height: 34.h),
          
                InkWell(
                  onTap: _onChangePassword,
                  child: Text(
                      'changePassword'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff01A796,
                      )
                  ),
                ),
          
                SizedBox(height: 24.h),
          
                InkWell(
                  onTap: _onDeleteUserProfile,
                  child: Text(
                      'deleteUserProfile'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff01A796,
                      )
                  ),
                ),

                /*
                InkWell(
                  onTap: _onFaceID,
                  child: Text(
                      'faceID'.tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: AppFontWeights.regular,
                        color: AppColors.ff01A796,
                      )
                  ),
                ),
                 */

                SizedBox(height: 270.h),
          
              ],
            ),
          ),
        )
    );
  }

  Widget _buildPersonalItem({
    required BuildContext context,
    required String title,
    required String subTitle,
    required bool showEditTextField,
    required TextEditingController controller,
    required GestureTapCallback onTap,
    required ValueChanged<String> onFieldSubmitted,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Opacity(
          opacity: 0.5,
          child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )
          ),
        ),
        const SizedBox(height: 8,),
        if (!showEditTextField)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  subTitle,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  )
              ),
              InkWell(
                onTap: onTap,
                child: SvgPicture.asset(
                  AppImagePaths.edit,
                  width: 22,
                  height: 22,
                ),
              )
            ],
          )
        else
          ConstrainedBox(
            constraints: const BoxConstraints(
                maxHeight: 148,
                minHeight: 48
            ),
            child: TextFormField(
              onFieldSubmitted: onFieldSubmitted,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
              controller: controller,
            ),
          )
      ],
    );
  }

  void _onLanguageSelected(value) {
    controller.changeLanguage(value);
  }

  void _onThemeSelected(value) {
    controller.changeTheme(value);
  }

  void _onChangePassword() {
    controller.changePassword();
  }

  void _onDeleteUserProfile() {
    showDialog(
        context: Get.context!,
        builder: (context) {
          return DeleteWidget(
            title: 'areYouSureYouWantToDeleteYourProfile'.tr,
            onDelete: () async {
              controller.deleteUserProfile();
            },
          );
        }
    );
  }

  // void _onFaceID() {
  // }

}
