import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/boarding/boarding_controller.dart';
import 'package:habi_app/pages/language_select/language_select_bindings.dart';
import 'package:habi_app/pages/language_select/language_select_page.dart';
import 'package:habi_app/pages/login/login_bindings.dart';
import 'package:habi_app/pages/login/login_page.dart';
import 'package:habi_app/widgets/habi_logo.dart';

class BoardingPage extends GetView<BoardingController> {

  const BoardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;
    return Scaffold(
      appBar: const PreferredSize(preferredSize: Size.fromHeight(0), child: SizedBox()),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(top: topPadding, left: 36, right: 36),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            SizedBox(height: 34.h,),

            HabiLogo(
              height: 51.h,
              width: 78.w,
            ),

            SizedBox(height: 70.h,),

            Image.asset(
              AppImagePaths.boarding,
              height: 272.h,
              width: 254.w,
            ),

            SizedBox(height: 40.h,),

            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'mainTitle'.tr,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 40,
                  fontWeight: AppFontWeights.regular,
                  color: AppColors.ff01A796,
                ),
              ),
            ),

            SizedBox(height: 18.h,),

            Text(
              'mainBody'.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 17,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),

            SizedBox(height: 34.h,),

            SizedBox(
              width: double.infinity,
              height: 48,
              child: FilledButton(
                onPressed: _oniAlreadyHaveAnAccount,
                child: Text(
                  'iAlreadyHaveAnAccount'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.bold,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.secondColor,
                  ),
                ),
              ),
            ),

            SizedBox(height: 28.h,),

            SizedBox(
              width: double.infinity,
              height: 48,
              child: OutlinedButton(
                onPressed: _onCreateAnAccount,
                child: Text(
                  'createAnAccount'.tr.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.ff01A796,
                  ),
                ),
              ),
            ),

            SizedBox(height: 85.h,),


          ],
        ),
      ),
    );
  }


  void _onCreateAnAccount() {
    Get.to(() => const LanguageSelectPage(), binding: LanguageSelectBindings());
  }

  void _oniAlreadyHaveAnAccount() {
    Get.to(
        () => LoginPage(showBackIcon: true,),
        binding: LoginBindings(),
        routeName: LoginPage.routeName);
  }

}

