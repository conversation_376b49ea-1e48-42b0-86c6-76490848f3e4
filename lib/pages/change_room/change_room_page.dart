import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/widgets/expanded_scroll_view.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'change_room_controller.dart';

class ChangeRoomPage extends BasePage<ChangeRoomController> {
  const ChangeRoomPage({super.key});

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: HabiAppBar(
        titleText: 'changeRoom'.tr,
      ),
      body: ExpandedScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 36),
        topContent: Column(
          children: [
            SizedBox(height: 33.h),
            Obx(() {
              if (controller.roomOptions.isEmpty) {
                return const SizedBox();
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Opacity(
                    opacity: 0.5,
                    child: Text(
                      'room'.tr,
                      style: TextStyles.medium16FirstColor,
                    ),
                  ),
                  const SizedBox(height: 20),
                  DropdownMenu<String>(
                    width: Get.width - 72,
                    menuHeight: 350.h,
                    enableSearch: false,
                    dropdownMenuEntries: controller.roomOptions.map((option) {
                      return DropdownMenuEntry(
                        value: option.value,
                        label: option.label,
                      );
                    }).toList(),
                    initialSelection: controller.selectedRoomId.value,
                    onSelected: controller.onRoomSelected,
                  ),
                  const SizedBox(height: 20),
                ],
              );
            }),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: OutlinedButton.icon(
                onPressed: controller.onAddNewRoomTap,
                icon: const Icon(
                  Icons.add,
                  color: AppColors.ff01A796,
                ),
                label: Text(
                  'addNewRoom'.tr,
                  style: TextStyles.regular14.copyWith(
                    color: AppColors.ff01A796,
                  ),
                ),
              ),
            ),
            SizedBox(height: 33.h),
          ],
        ),
        bottomContent: Column(
          children: [
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: controller.onSaveTap,
                child: Text(
                  'save'.tr.toUpperCase(),
                  style: TextStyles.bold14White,
                ),
              ),
            ),
            SizedBox(height: 113.h),
          ],
        ),
      ),
    );
  }
}
