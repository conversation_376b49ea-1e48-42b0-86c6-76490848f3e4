import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_event.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/models/option_item.dart';
import 'package:habi_app/pages/add_room/add_room_bindings.dart';
import 'package:habi_app/pages/add_room/add_room_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/home_service.dart';
import 'package:habi_app/utility/dialog_utils.dart';

class ChangeRoomController extends BaseController {
  final deviceShadowService = DeviceShadowService.to;
  final homeService = HomeService.to;
  final devService = DevService.to;

  static const String _tag = '[ChangeRoomController]';

  final String homeId;
  final String roomId;
  final String thingName;
  final String gwThingName;

  final roomOptions = <OptionItem<String>>[].obs;
  final selectedRoomId = RxnString();
  Set<String> currentRoomIds = {}; // 可能由于某些错误导致多个 Room 同时拥有同一台设备
  List<HBRoom> currentRooms = [];

  ChangeRoomController({
    required this.homeId,
    required this.roomId,
    required this.thingName,
    required this.gwThingName,
  });

  @override
  void onInit() {
    super.onInit();

    Future(loadData);
  }

  Future<void> loadData() async {
    updateLoading(true);

    try {
      final response = await devService.getHomeRooms(homeId);

      if (response.success != true) {
        throw Exception(
          'Failed to fetch rooms for home $homeId (${response.errorCode})',
        );
      }

      final options = <OptionItem<String>>[];
      String? currentRoomId;
      currentRoomIds = {};
      currentRooms = response.rooms ?? [];

      for (final room in currentRooms) {
        final name = room.name;
        final id = room.roomId;
        final deviceList = room.deviceList ?? [];

        if (name != null && id != null && id.isNotEmpty) {
          if (deviceList.contains(thingName)) {
            currentRoomIds.add(id);
            if (id == roomId) {
              currentRoomId = id;
            }
          }
          options.add(
            OptionItem(
              label: name,
              value: id,
            ),
          );
        }
      }

      selectedRoomId.value = currentRoomId ?? currentRoomIds.firstOrNull;
      roomOptions.value = options;
    } catch (e) {
      log.e('$_tag -> loadData failed', error: e);
      showLoadDataErrorDialog();
    }

    updateLoading(false);
  }

  void showLoadDataErrorDialog() async {
    final result = await DialogUtils.showConfirmDialog(
      content: 'failedToLoadData'.tr,
      primaryButtonText: 'tryAgain'.tr,
      secondaryButtonText: 'cancel'.tr,
    );

    if (result) {
      loadData();
    } else {
      Get.back();
    }
  }

  void onRoomSelected(String? value) {
    if (value == null || value.isEmpty) {
      return;
    }
    selectedRoomId.value = value;
  }

  void onAddNewRoomTap() async {
    if (homeId.isEmpty) {
      DialogUtils.showErrorDialog(
        content: 'No home found',
      );
      return;
    }

    final result = await Get.to(
      () => AddRoomPage(),
      binding: AddRoomBindings(homeId: homeId, rooms: currentRooms),
    );
    if (result != null) {
      await loadData();
    }
  }

  Future<void> onSaveTap() async {
    if (selectedRoomId.value == null) {
      DialogUtils.showErrorDialog(
        content: 'Please choose which Room you want Device to join.',
      );
      return;
    }

    updateLoadingMessage('saving'.tr);

    try {
      bool isChanged = false;
      String newRoomId = selectedRoomId.value!;

      if (!currentRoomIds.contains(newRoomId)) {
        try {
          await DevService.to
              .addDeviceToRoom(newRoomId, thingName)
              .timeout(const Duration(seconds: 60));
          isChanged = true;
        } catch (e) {
          log.e('$_tag -> addDeviceToRoom failed', error: e);
          DialogUtils.showErrorDialog(
            title: 'saveFailed'.tr,
            content: 'unableToAddDeviceToRoom'.tr,
            onPrimaryButtonPressed: loadData,
          );
          rethrow;
        }
      }

      final oldRoomIds = currentRoomIds.where((id) => id != newRoomId);

      if (oldRoomIds.isNotEmpty) {
        try {
          await Future.wait(oldRoomIds.map((id) {
            return DevService.to.removeDeviceFromRoom(id, thingName);
          })).timeout(const Duration(seconds: 60));
          isChanged = true;
        } catch (e) {
          log.e('$_tag -> removeDeviceFromRoom failed', error: e);
          DialogUtils.showErrorDialog(
            title: 'saveFailed'.tr,
            content: 'unableToRemoveDeviceFromRoom'.tr,
            onPrimaryButtonPressed: loadData,
          );
          rethrow;
        }
      }

      if (isChanged) {
        GlobalService.to
            .getEventStreamController()
            .add(AppEvent(name: AppEvents.refreshDevicePage));

        Get.until((route) => route.settings.name == Routes.homeManagement);
      }
    } catch (e, r) {
      log.e('$_tag -> save failed', error: e, stackTrace: r);
    }

    updateLoading(false);
  }
}
