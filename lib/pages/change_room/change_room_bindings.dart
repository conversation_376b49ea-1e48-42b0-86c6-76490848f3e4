import 'package:get/instance_manager.dart';
import 'change_room_controller.dart';

class ChangeRoomBindings extends Bindings {
  final String homeId;
  final String roomId;
  final String thingName;
  final String gwThingName;

  ChangeRoomBindings({
    required this.homeId,
    required this.roomId,
    required this.thingName,
    required this.gwThingName,
  });

  @override
  void dependencies() {
    Get.lazyPut(
      () => ChangeRoomController(
        homeId: homeId,
        roomId: roomId,
        gwThingName: gwThingName,
        thingName: thingName,
      ),
    );
  }
}
