import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/pairing_code/add_receiver_pairing_code_controller.dart';

class AddReceiverPairingCodePage extends StatefulWidget implements UIDelegateBridge {
  final UIDelegateManager delegateManager;

  const AddReceiverPairingCodePage({super.key, required this.delegateManager});

  @override
  State<AddReceiverPairingCodePage> createState() => _AddReceiverPairingCodePageState();

  @override
  UIDelegateManager getUIDelegateManager() => delegateManager;

}

class _AddReceiverPairingCodePageState extends State<AddReceiverPairingCodePage>
    with AutomaticKeepAliveClientMixin implements UIDelegateLifeCycle {

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late AddReceiverPairingCodeController pairingCodeController;

  @override
  void initState() {
    super.initState();
    pairingCodeController = Get.find<AddReceiverPairingCodeController>();
    widget.delegateManager.setUIDelegateLifecycle(this);
  }

  @override
  void dispose() {
    widget.delegateManager.setUIDelegateLifecycle(null);
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onHide() {
    log.i('UIDelegate{} -> pairingCodePage onHide');
  }

  @override
  void onShow() {
    log.i('UIDelegate{} -> pairingCodePage onShow');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 20,),

        Text(
          textAlign: TextAlign.center,
          'pairYourReceiver'.tr,
          style: TextStyle(
            fontSize: 22,
            fontWeight: AppFontWeights.medium,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),

        const SizedBox(height: 37,),

        _buildForm(context),

        const SizedBox(height: 37,),

        Expanded(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: FilledButton(
                onPressed: _onContinue,
                child: Text(
                  'continue'.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.medium,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(height: 37,),

      ],
    );
  }

  Widget _buildForm(BuildContext context) {
    return Form(
        key: _formKey,
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: Opacity(
                opacity: 0.5,
                child: Text(
                  'macAddress'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.medium,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 14,),

            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                textAlign: TextAlign.center,
                textAlignVertical: TextAlignVertical.center,
                controller: pairingCodeController.pairingCodeController,
                keyboardType: TextInputType.emailAddress,
                validator: _validatePairingCode,
              ),
            ),

          ],
        )
    );
  }

  String? _validatePairingCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }
    final regex = RegExp(r'^[a-zA-Z0-9]{12}$');
    if (!regex.hasMatch(value)) {
      return 'incorrectMacAddress'.tr;
    }
    return null;
  }


  void _onContinue() {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    KeyboardHelper.dismissKeyboard(Get.context!);
    pairingCodeController.requestPermission();
  }


}