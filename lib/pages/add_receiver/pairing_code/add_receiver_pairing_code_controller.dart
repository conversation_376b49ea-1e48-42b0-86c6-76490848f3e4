import 'dart:async';

import 'package:ct_flutter_ble_plugin/ct_ble_plugin.dart';
import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_controller.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_bindings.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_page.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:permission_handler/permission_handler.dart';


class AddReceiverPairingCodeController extends GetxController {

  final TextEditingController pairingCodeController = TextEditingController();
  late AddReceiverController _addReceiverController;
  StreamSubscription? _listenScanControllerSubscription;
  StreamSubscription? _listenConnectionStateChangeSubscription;
  CtController? _currentController;
  bool _isDiscoverServices = false;
  int _reconnectCount = 3;
  Timer? _startScanControllerTimer;

  @override
  void onInit() {
    super.onInit();
    _addReceiverController = Get.find<AddReceiverController>();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    pairingCodeController.dispose();

    Future(() async {
      bool isConnected = _currentController?.isConnected ?? false;
      log.i('onClose() -> isConnected:$isConnected');
      if (isConnected) {
        await _currentController?.disconnect();
        log.i('onClose() -> 断开蓝牙连接...');
        _currentController = null;
      }
    });

    _startScanControllerTimer?.cancel();
    _listenScanControllerSubscription?.cancel();
    _listenConnectionStateChangeSubscription?.cancel();
    bool isScan = CtBlePlugin.getInstance().isScanningNow();
    log.i('isScan=$isScan');
    if (isScan) {
      CtBlePlugin.getInstance()
          .stopScanController()
          .then((data) {
        log.i('stopScanController() -> success!');
      }).catchError((error) {
        log.e('stopScanController() -> error: $error');
      });
    }
    super.onClose();
  }

  Future<void> findReceiverViaBLE() async {
    BluetoothAdapterState adapterState;

    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('findReceiverViaBLE() -> 获取蓝牙状态=$adapterState');
    } catch (e, r) {
      log.e('findReceiverViaBLE() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      showSnackBar('pleaseTurnOnBluetooth'.tr);;
      return;
    }

    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      showSnackBar('pleaseTurnOnLocation'.tr);
      return;
    }

    if (_addReceiverController.isLoading) {
      return;
    }

    _addReceiverController.updateLoadingMessage('connecting...');

    bool isConnected = _currentController?.isConnected ?? false;
    log.i('findReceiverViaBLE() -> isConnected:$isConnected');
    if (isConnected) {
      log.i('findReceiverViaBLE() -> 已经有一个连接了');
      await Future.delayed(const Duration(milliseconds: 1000));
      await navigateToPage();
      _addReceiverController.updateLoading(false);
      return;
    }

    try {
      _startScanControllerTimer?.cancel();
      _listenScanControllerSubscription?.cancel();
      _listenConnectionStateChangeSubscription?.cancel();

      _currentController = null;
      _isDiscoverServices = false;
      _reconnectCount = 3;

      String scanGroupName = Sait85rHelper.getScanGroupNameByMacAddress(pairingCodeController.text);
      String receiverName = scanGroupName;
      log.i('findReceiverViaBLE() -> receiverName: $receiverName');

      _listenScanControllerSubscription = CtBlePlugin.getInstance()
          .listenScanController((results) async {
        if (_currentController != null) {
          log.i('findReceiverViaBLE() -> already found receiver, ignore.');
          return;
        }

        CtController? ctController;
        try {
          ctController = results.firstWhere((item) => item.name == receiverName);
        } catch (e) {
          log.e('findReceiverViaBLE() -> no receiver found: $e');
          ctController = null;
        }

        if (ctController == null) {
          return;
        }

        log.i('findReceiverViaBLE() -> receiver found!');
        _currentController = ctController;

        try {
          await CtBlePlugin.getInstance().stopScanController();
        } catch (e, r) {
          log.e('findReceiverViaBLE() -> stop scan failed: $e, $r');
        }

        _listenConnectionStateChangeSubscription =
            _currentController!.listenConnectionStateChange((state) async {
              if (state == BluetoothConnectionState.connected) {
                try {
                  _startScanControllerTimer?.cancel();
                  log.i('findReceiverViaBLE() -> 监听到controller已经连接');
                  log.i('findReceiverViaBLE() -> 开始发现服务...');
                  await ctController!.discoverServices();
                  _isDiscoverServices = true;
                  log.i('findReceiverViaBLE() -> 发现服务成功');
                  await Future.delayed(const Duration(milliseconds: 1000));
                  await navigateToPage();
                  _addReceiverController.updateLoading(false);
                } catch (e) {
                  log.e('findReceiverViaBLE() -> 发现服务失败');
                }
              } else if (state == BluetoothConnectionState.disconnected) {
                try {
                  log.i('findReceiverViaBLE() -> 监听到controller断开连接');
                  if (!_isDiscoverServices) {
                    log.i('findReceiverViaBLE() -> 检测到还没有连接成功蓝牙ble就已经断开...');
                    await reconnectController();
                  }
                } catch (e) {
                  log.e('findReceiverViaBLE() -> 执行重连失败!!!');
                }
              }
            });

        try {
          await _currentController!.connect();
        } catch (e, r) {
          log.e('findReceiverViaBLE() -> connect failed: $e, $r');
        }

      });

      _startScanControllerTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        timer.cancel();
        CtBlePlugin.getInstance()
            .stopScanController()
            .then((data) {
          log.i('stopScanController() -> success!');
        }).catchError((error) {
          log.e('stopScanController() -> error: $error');
        });
        _addReceiverController.updateLoading(false);
        showErrorDialog('Failed to add receiver', 'Receiver search timed out, please activate pairing mode and move closer to the device.');
      });

      await CtBlePlugin.getInstance().startScanController();
    } catch (e, r) {
      log.e('findReceiverViaBLE() -> failed to find receiver: $e, $r');
      showErrorDialog('Failed to add receiver', 'Receiver search failed.');
    }
  }

  Future<void> navigateToPage() async {
    Get.to(() => SelectNetworkPage(),
        binding: SelectNetworkBindings(
            matterController: _currentController!,
            isOnlyChangeWiFi: false
        ));
  }

  Future<void> reconnectController() async {
    if (_reconnectCount > 0) {
      _reconnectCount--;
      log.i('尝试重新连接蓝牙ble, 剩余重连次数：${_reconnectCount}');
      log.i('启动重连蓝牙ble任务，3.5s后执行');
      await Future.delayed(const Duration(milliseconds: 3500));
      await _currentController!.connect();
    } else {
      log.e('3次重连全部失败!!!');
      _addReceiverController.updateLoading(false);
      showErrorSnackBar('Bluetooth connection failure.');
    }
  }

  void showErrorDialog(String title, String content) {
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

  //申请权限
  Future<void> requestPermission() async {
    if (PlatformUtils.isAndroid) {
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.location
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetoothScan] == PermissionStatus.granted
          && result[Permission.bluetoothConnect] == PermissionStatus.granted
          && result[Permission.location] == PermissionStatus.granted
      ) {
        findReceiverViaBLE();
      } else {
        log.i('_requestPermission() -> 权限不足');
        openAppSettings();
      }
    } else if (PlatformUtils.isIOS){
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetooth,
        Permission.locationWhenInUse,
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetooth] == PermissionStatus.granted
          && result[Permission.locationWhenInUse] == PermissionStatus.granted
      ) {
        findReceiverViaBLE();
      } else {
        log.i('_requestPermission() -> 权限不足');
        openAppSettings();
      }
    } else {
      showSnackBar('Platform not supported');
    }

  }


}
