import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_events.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/qr_code/add_receiver_qr_code_controller.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class AddReceiverQrCodePage extends StatefulWidget implements UIDelegateBridge {
  final UIDelegateManager delegateManager;

  const AddReceiverQrCodePage({super.key, required this.delegateManager});

  @override
  State<AddReceiverQrCodePage> createState() {
    return _AddReceiverQrCodePageState();
  }

  @override
  UIDelegateManager getUIDelegateManager() => delegateManager;
}

class _AddReceiverQrCodePageState extends State<AddReceiverQrCodePage>
    with AutomaticKeepAliveClientMixin implements UIDelegateLifeCycle {

  final GlobalKey _scanQRCodeKey = GlobalKey();
  late AddReceiverQrCodeController qrCodeController;
  QRViewController? qrViewController;
  StreamSubscription? subscription;
  bool isShowSelectDeviceTypeDialog = false;
  StreamSubscription? eventSubscription;

  @override
  void initState() {
    super.initState();
    qrCodeController = Get.find<AddReceiverQrCodeController>();
    widget.delegateManager.setUIDelegateLifecycle(this);

    GlobalService globalService = GlobalService.to;
    eventSubscription = globalService.getEventStream().listen((event) {
      log.i('AddDeviceQrCodePage{} -> event=$event');
      if (event.name == AppEvents.resumeCamera) {
        isShowSelectDeviceTypeDialog = false;
        qrViewController?.resumeCamera();
      }
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    qrViewController?.dispose();
    widget.delegateManager.setUIDelegateLifecycle(null);
    eventSubscription?.cancel();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onHide() {
    log.i('UIDelegate{} -> QrCodePage onHide');
    qrViewController?.pauseCamera();
  }

  @override
  void onShow() {
    log.i('UIDelegate{} -> QrCodePage onShow');
    isShowSelectDeviceTypeDialog = false;
    qrViewController?.resumeCamera();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      width: 342.w,
      height: 534.h,
      padding: const EdgeInsets.only(top: 60, bottom: 60),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(50))
      ),
      child: Stack(
        children: [
          buildPrepareWidget(),
          buildScanQRCodeWidget(),
        ],
      ),
    );
  }

  Widget buildPrepareWidget() {
    return Obx(() {
      if (qrCodeController.cameraInit.value) {
        return Container();
      }
      return Container(
        width: 342.w,
        height: 534.h,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
            color: AppColors.ffE7F9F7,
            borderRadius: BorderRadius.all(Radius.circular(50))
        ),
        child: SvgPicture.asset(
          AppImagePaths.camera,
          width: 131.22.w,
          height: 106.18.h,
        ),
      );
    });
  }

  Widget buildScanQRCodeWidget() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(50.0),
      child: QRView(
          key: _scanQRCodeKey,
          // overlay: QrScannerOverlayShape(
          //     borderColor: widget.borderColor,
          //     borderRadius: 50,
          //     borderLength: 1,
          //     borderWidth: 1,
          //     cutOutWidth: widget.width ?? 260,
          //     cutOutHeight: widget.height ?? 260
          // ),
          onQRViewCreated: _onQRViewCreated
      ),
    );
  }

  void _onQRViewCreated(controller) {
    qrViewController = controller;
    qrCodeController.cameraInit.value = true;
    subscription = qrViewController?.scannedDataStream.listen((data){
      String qrcode = data.code ?? '';
      log.i('qrcode=$qrcode');
      if (qrcode.isEmpty) {
        return;
      }
      if (!isShowSelectDeviceTypeDialog) {
        isShowSelectDeviceTypeDialog = true;
        qrViewController?.pauseCamera();
        qrCodeController.requestPermission(qrcode);
      }
    });
  }

}