import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_controller.dart';
import 'package:habi_app/pages/add_receiver/network_scan/add_receiver_network_scan_controller.dart';
import 'package:habi_app/pages/add_receiver/pairing_code/add_receiver_pairing_code_controller.dart';
import 'package:habi_app/pages/add_receiver/qr_code/add_receiver_qr_code_controller.dart';

class AddReceiverBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AddReceiverController());
    Get.lazyPut(() => AddReceiverNetworkScanController());
    Get.lazyPut(() => AddReceiverPairingCodeController());
    Get.lazyPut(() => AddReceiverQrCodeController());
  }
}
