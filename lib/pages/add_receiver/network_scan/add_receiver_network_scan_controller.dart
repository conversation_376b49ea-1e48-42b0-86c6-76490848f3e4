import 'dart:async';
import 'package:ct_flutter_ble_plugin/ct_ble_plugin.dart';
import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_controller.dart';
import 'package:habi_app/pages/add_receiver/wifi_ssid_password/wifi_ssid_password_bindings.dart';
import 'package:habi_app/pages/add_receiver/wifi_ssid_password/wifi_ssid_password_page.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/widgets/scan_device_ripple_widget.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_bindings.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_page.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class AddReceiverNetworkScanController extends GetxController {

  final GlobalKey<ScanDeviceRippleWidgetState> rippleWidgetKey = GlobalKey<ScanDeviceRippleWidgetState>();
  late AddReceiverController _addReceiverController;

  StreamSubscription? _adapterStateSubscription;
  StreamSubscription? _isScanSubscription;
  StreamSubscription? _listenScanControllerSubscription;
  StreamSubscription? _listenConnectionStateChangeSubscription;

  var matterControllerList = <CtController>[].obs;
  var showTryAgain = false.obs;
  bool isFirstTime = true;

  CtController? _currentController;
  late NetworkInfo networkInfo;
  bool _isDiscoverServices = false;
  int _reconnectCount = 3;

  @override
  void onInit() {
    super.onInit();
    networkInfo = NetworkInfo();

    _addReceiverController = Get.find<AddReceiverController>();

    //监听蓝牙状态
    _adapterStateSubscription = FlutterBluePlus.adapterState.listen(
            (BluetoothAdapterState state) {
      log.i('onInit() -> Bluetooth adapter state=$state');
    });

    //监听是否在扫描
    _isScanSubscription = FlutterBluePlus.isScanning.listen((isScan) {
      log.i('onInit() -> isScan=$isScan');
      if (isFirstTime) {
        isFirstTime = false;
      } else {
        if (!isScan) {
          //扫描结束停止波纹动画
          rippleWidgetKey.currentState?.stopAnimation();
          showTryAgain.value = true;
        }
      }
    });

    _listenScanController();

  }

  @override
  void onReady() {
    super.onReady();
    Future.delayed(const Duration(milliseconds: 500), startScanController);
  }

  @override
  void onClose() {
    stopScanController();
    disconnectController();
    _adapterStateSubscription?.cancel();
    _isScanSubscription?.cancel();
    _listenScanControllerSubscription?.cancel();
    _listenConnectionStateChangeSubscription?.cancel();
    super.onClose();
  }

  Future<void> startScanController() async {
    if (!CtBlePlugin.getInstance().isScanningNow()) {
      try {
        log.i('startScanController() -> 开始扫描...');
        await CtBlePlugin.getInstance().startScanController();
      } catch(e) {
        log.e('startScanController() -> 开始扫描失败');
      }
    }
  }

  Future<void> reStartScanController() async {
    showTryAgain.value = false;
    matterControllerList.value = [];
    rippleWidgetKey.currentState?.startAnimation();
    stopScanController();
    _listenScanController();
    Future.delayed(const Duration(milliseconds: 500), () {
      startScanController();
    });
  }

  Future<void> stopScanController() async {
    if (CtBlePlugin.getInstance().isScanningNow()) {
      try {
        log.i('stopScanController() -> 退出扫描...');
        await CtBlePlugin.getInstance().stopScanController();
      } catch (e){
        log.e('stopScanController() -> 退出扫描失败');
      }
    }
  }

  //申请权限
  Future<void> requestPermission(CtController controller) async {
    if (PlatformUtils.isAndroid) {
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.location
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetoothScan] == PermissionStatus.granted
          && result[Permission.bluetoothConnect] == PermissionStatus.granted
          && result[Permission.location] == PermissionStatus.granted
      ) {
        connectController(controller);
      } else {
        log.i('_requestPermission() -> 权限不足');
        openAppSettings();
      }
    } else if (PlatformUtils.isIOS){
      Map<Permission, PermissionStatus> result = await [
        Permission.camera,
        Permission.bluetooth,
        Permission.locationWhenInUse,
      ].request();

      log.i('_requestPermission() -> 申请权限结果=$result');

      if (result[Permission.camera] == PermissionStatus.granted
          && result[Permission.bluetooth] == PermissionStatus.granted
          && result[Permission.locationWhenInUse] == PermissionStatus.granted
      ) {
        connectController(controller);
      } else {
        log.i('_requestPermission() -> 权限不足');
        openAppSettings();
      }
    } else {
      showSnackBar('Platform not supported');
    }

  }

  Future<void> connectController(CtController controller) async {
    BluetoothAdapterState adapterState;

    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('connectController() -> 获取蓝牙状态=$adapterState');
    } catch (e, r) {
      log.e('connectController() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      showSnackBar('pleaseTurnOnBluetooth'.tr);
      return;
    }

    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      showSnackBar('pleaseTurnOnLocation'.tr);
      return;
    }

    if (_addReceiverController.isLoading) {
      log.i('connectController() -> 正在连接中');
      return;
    }

    _addReceiverController.updateLoadingMessage('connecting...');

    try {
      //停止扫描
      await stopScanController();

      //暂停波纹动画
      rippleWidgetKey.currentState?.stopAnimation();

      if (_currentController != null) {
        if (_currentController!.remoteId == controller.remoteId) {
          log.i('connectController() -> 检测到是同一个controller，需要判断是否有连接：${_currentController!.name}');
          if (_currentController!.isConnected) {
            log.i('connectController() -> 这个controller已经有连接了，直接跳转到选择wifi页面...');
            await Future.delayed(const Duration(milliseconds: 1000));
            await navigateToPage(controller);
            _addReceiverController.updateLoading(false);
            return;
          }
        } else {
          log.i('connectController() -> 不是同一个controller，需要判断是否有连接，有的话要断开连接：${_currentController!.name}');
          if (_currentController!.isConnected) {
            _isDiscoverServices = false;
            _listenConnectionStateChangeSubscription?.cancel();
            log.i('connectController() -> 正在断开连接...');
            await _currentController!.disconnect();
            log.i('connectController() -> 断开连接成功，为保证稳定性，延迟1000ms再去连接...');
            await Future.delayed(const Duration(milliseconds: 1000));
          }
        }
      }

      _currentController = controller;
      _listenConnectionStateChangeSubscription =
          _currentController!.listenConnectionStateChange((state) async {
        if (state == BluetoothConnectionState.connected) {
          try {
            log.i('connectController() -> 监听到controller已经连接');
            log.i('connectController() -> 开始发现服务...');
            await _currentController!.discoverServices();
            _isDiscoverServices = true;
            log.i('connectController() -> 发现服务成功');
            await Future.delayed(const Duration(milliseconds: 1000));
            await navigateToPage(controller);
            _addReceiverController.updateLoading(false);
          } catch (e) {
            log.e('connectController() -> 发现服务失败');
          }
        } else if (state == BluetoothConnectionState.disconnected) {
          try {
            log.i('connectController() -> 监听到controller断开连接');
            if (!_isDiscoverServices) {
              log.i('connectController() -> 检测到还没有连接成功蓝牙ble就已经断开...');
              await reconnectController();
            }
          } catch (e) {
            log.e('connectController() -> 执行重连失败!!!');
          }
        }
      });

      log.i('connectController() -> 正在连接的controller是：${controller.name}');
      await _currentController!.connect();
    } catch (e, r) {
      log.e('connectController() -> 连接失败: $e, $r');
      _addReceiverController.updateLoading(false);
      showErrorSnackBar('Bluetooth connection failure.');
    }
  }

  Future<void> disconnectController() async {
    if (_currentController != null && _currentController!.isConnected) {
      try {
        log.i('disconnectController() -> 正在断开连接...');
        await _currentController!.disconnect();
        log.i('disconnectController() -> 断开连接成功');
      } catch(e) {
        log.e('disconnectController() -> 断开连接失败');
      }
    }
  }

  Future<void> navigateToPage(CtController controller) async {
    if (GlobalService.to.config.factoryParingMode == false) {
      Get.to(() => SelectNetworkPage(),
          binding: SelectNetworkBindings(
              matterController: controller,
              isOnlyChangeWiFi: false
          ));
    } else {
      String? ssid;
      try {
        ssid = await networkInfo.getWifiName();
        log.i('ssid=$ssid');
      } catch (e, r) {
        log.e('获取wifi名称失败，e=$e, r=$r');
      }

      if (ssid != null && ssid.isNotEmpty) {
        String newSsid;
        if (PlatformUtils.isAndroid) {
          // String newSsid = ssid.replaceAll('"', '');
          newSsid = ssid.substring(1, ssid.length - 1);
        } else {
          newSsid = ssid;
        }

        log.i('newSsid=$newSsid');
        Get.to(() => WifiSsidPasswordPage(),
            binding: WifiSsidPasswordBindings(
                matterController: controller,
                ssid: newSsid,
                isOnlyChangeWiFi: false
            ));
      } else {
        Get.to(() => WifiSsidPasswordPage(),
            binding: WifiSsidPasswordBindings(
                matterController: controller,
                ssid: '',
                isOnlyChangeWiFi: false
            )
        );
      }
    }
  }

  Future<void> reconnectController() async {
    if (_reconnectCount > 0) {
      _reconnectCount--;
      log.i('尝试重新连接蓝牙ble, 剩余重连次数：${_reconnectCount}');
      log.i('启动重连蓝牙ble任务，3.5s后执行');
      await Future.delayed(const Duration(milliseconds: 3500));
      await _currentController!.connect();
    } else {
      log.e('3次重连全部失败!!!');
      _addReceiverController.updateLoading(false);
      showErrorSnackBar('Bluetooth connection failure.');
    }
  }

  void _listenScanController() {
    //监听扫描Matter控制器
    _listenScanControllerSubscription = CtBlePlugin.getInstance()
        .listenScanController((results) {
      matterControllerList.value = [...results];
    });
  }

}
