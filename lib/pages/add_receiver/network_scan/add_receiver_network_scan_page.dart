import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/widgets/scan_device_ripple_widget.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/network_scan/add_receiver_network_scan_controller.dart';

class AddReceiverNetworkScanPage extends StatefulWidget implements UIDelegateBridge{
  final UIDelegateManager delegateManager;

  const AddReceiverNetworkScanPage({super.key, required this.delegateManager});

  @override
  State<AddReceiverNetworkScanPage> createState() => _AddReceiverNetworkScanPageState();

  @override
  UIDelegateManager getUIDelegateManager() => delegateManager;
}

class _AddReceiverNetworkScanPageState extends State<AddReceiverNetworkScanPage>
    with AutomaticKeepAliveClientMixin implements UIDelegateLifeCycle{

  late AddReceiverNetworkScanController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<AddReceiverNetworkScanController>();
    widget.delegateManager.setUIDelegateLifecycle(this);
  }

  @override
  void dispose() {
    widget.delegateManager.setUIDelegateLifecycle(null);
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onHide() {
    log.i('UIDelegate{} -> AddReceiverNetworkScanPage onHide');
    controller.stopScanController();
  }

  @override
  void onShow() {
    log.i('UIDelegate{} -> AddReceiverNetworkScanPage onShow');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(height: 20.h,),

          Text(
            'lookingForDevices'.tr,
            style: TextStyle(
              fontSize: 22,
              fontWeight: AppFontWeights.medium,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
          ),

          SizedBox(height: 28.h,),

          Center(
              child: ScanDeviceRippleWidget(
                key: controller.rippleWidgetKey,
                width: 342.w,
                height: 342.h,
              )
          ),

          SizedBox(height: 39.h,),

          Opacity(
            opacity: 0.5,
            child: Text(
              'discoveredDevices'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            ),
          ),

          SizedBox(height: 21.h,),

          SizedBox(
            height: 91,
            child: Obx(() {
              return ListView.separated(
                itemBuilder: _buildSanDeviceItem,
                scrollDirection: Axis.horizontal,
                itemCount: controller.matterControllerList.length,
                separatorBuilder: (BuildContext context, int index) {
                  return const SizedBox(width: 16,);
                },
              );
            }),
          ),

          SizedBox(height: 21.h,),

          Obx(() {
            return Visibility(
              visible: controller.showTryAgain.value,
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: OutlinedButton(
                  onPressed: _onTryAgain,
                  child: Text(
                    'tryAgain'.tr.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.bold,
                      color: AppColors.ff01A796,
                    ),
                  ),
                ),
              ),
            );
          }),

          SizedBox(height: 14.h,),

        ],
      ),
    );
  }


  Widget _buildSanDeviceItem(BuildContext context, int index) {
    final matterController = controller.matterControllerList[index];
    return InkWell(
      onTap: () => _onScanDeviceItemTap(context, index),
      child: SizedBox(
        width: 109,
        height: 91,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 71,
              height: 61,
              child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Positioned(
                      left: 4,
                      child: SvgPicture.asset(
                        AppImagePaths.receiver,
                        width: 56,
                        height: 56,
                      ),
                    ),
                    Positioned(
                      right: 4,
                      bottom: 0,
                      child: SvgPicture.asset(
                        AppImagePaths.addDevice,
                        width: 30,
                        height: 30,
                      ),
                    ),
                  ]
              ),
            ),

            const SizedBox(height: 7),

            SizedBox(
              height: 23,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.center,
                child: Text(
                  matterController.name,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }

  void _onScanDeviceItemTap(BuildContext context, int index) async {
    final matterController = controller.matterControllerList[index];
    await controller.requestPermission(matterController);
  }

  void _onTryAgain() {
    controller.reStartScanController();
  }

}
