import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_stateful_widget.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/delegate/tab_bar_view_ui_delegate.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/network_scan/add_receiver_network_scan_page.dart';
import 'package:habi_app/pages/add_receiver/pairing_code/add_receiver_pairing_code_page.dart';
import 'package:habi_app/pages/add_receiver/qr_code/add_receiver_qr_code_page.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_controller.dart';

class AddReceiverPage extends BaseStatefulWidget {
  const AddReceiverPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _AddReceiverPageState();
  }
}

class _AddReceiverPageState extends BaseStatefulState<AddReceiverPage>
    with SingleTickerProviderStateMixin {

  late AddReceiverController _addReceiverController;
  late TabController _tabController;
  late List<Widget> _tabs;
  late List<Widget> _tabPages;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _addReceiverController = Get.find<AddReceiverController>();
    _addReceiverController.setUpdateLoadingCallback((value) {
      updateLoading(value);
    });
    _addReceiverController.setUpdateLoadingMessageCallback((value) {
      updateLoadingMessage(value);
    });
    _addReceiverController.setUpdateTransparentLoadingCallback((value) {
      updateTransparentLoading(value);
    });
    _addReceiverController.setIsLoadingCallback(() {
      return isLoading;
    });
    _addReceiverController.setIsTransparentLoadingCallback(() {
      return isTransparentLoading;
    });

    _tabs = _getTabs();
    _tabPages = _getTabPages();
    _tabController =  TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    log.i('当前选中的Tab索引：${_tabController.index}');
    if (_currentIndex == _tabController.index) {
      return;
    }

    UIDelegateBridge lastBridge = _tabPages[_currentIndex] as UIDelegateBridge;
    lastBridge
        .getUIDelegateManager()
        .delegateLifeCycle
        ?.onHide();

    UIDelegateBridge nextBridge = _tabPages[_tabController.index] as UIDelegateBridge;
    nextBridge
        .getUIDelegateManager()
        .delegateLifeCycle
        ?.onShow();

    _currentIndex = _tabController.index;
  }

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'addReceiver'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 30.0, right: 30.0),
        child: Column(
          children: [
            const SizedBox(height: 20,),

            SizedBox(
              width: double.infinity,
              height: 48,
              child: TabBar(
                controller: _tabController,
                tabs: _tabs,
                padding: EdgeInsets.zero,
                labelPadding: EdgeInsets.zero,
                indicatorColor: AppColors.ff01A796,
                indicatorWeight: 2,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: AppColors.ff707070,
                dividerHeight: 2,
                labelStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()
                        !.firstColor
                ),
                unselectedLabelStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!
                        .firstColor
                        .withOpacity(0.5)
                ),

              ),
            ),

            Expanded(
                child: TabBarView(
                    controller: _tabController,
                    children: _tabPages
                )
            )
          ],
        ),
      ),
    );
  }

  List<Widget> _getTabs() {
    final List<Tab> tabs = [];

    final qrCodeText = 'qrCode'.tr;
    final networkScanText = 'networkScan'.tr;
    final pairingCodeText = 'macAddress'.tr;

    tabs.add(Tab(text: qrCodeText));
    tabs.add(Tab(text: networkScanText));
    tabs.add(Tab(text: pairingCodeText));

    return tabs;
  }

  List<Widget> _getTabPages() {
    final List<Widget> pages = [];

    pages.add(AddReceiverQrCodePage(delegateManager: UIDelegateManager()));
    pages.add(AddReceiverNetworkScanPage(delegateManager: UIDelegateManager()));
    pages.add(AddReceiverPairingCodePage(delegateManager: UIDelegateManager()));

    return pages;
  }


}



