import 'package:ct_flutter_ble_plugin/ct_wifi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_controller.dart';
import 'package:habi_app/pages/add_receiver/wifi_ssid_password/wifi_ssid_password_bindings.dart';
import 'package:habi_app/pages/add_receiver/wifi_ssid_password/wifi_ssid_password_page.dart';

class SelectNetworkPage extends BasePage<SelectNetworkController> {

  const SelectNetworkPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          controller.isOnlyChangeWiFi ? 'changeReceiverWiFi'.tr : 'addReceiver'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 36.0, right: 36.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const SizedBox(height: 37,),

            Opacity(
              opacity: 0.5,
              child: Text(
                'selectNetwork'.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
            ),

            const SizedBox(height: 16,),

            Expanded(
              child: Obx(() {
                return ListView.separated(
                  itemBuilder: _buildNetworkItem,
                  scrollDirection: Axis.vertical,
                  itemCount: controller.wifiList.length,
                  separatorBuilder: (BuildContext context, int index) {
                    return const SizedBox(height: 16,);
                  },
                );
              }),
            ),

            const SizedBox(height: 7,),

            SizedBox(
              width: double.infinity,
              height: 48,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OutlinedButton.icon(
                    onPressed: _onCancel,
                    label: Text(
                      'cancel'.tr,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.bold,
                        color: AppColors.ff01A796,
                      ),
                    ),
                  ),

                  const SizedBox(height: 28,),

                  FilledButton(
                      onPressed: _onRescan,
                      child: Text(
                        'rescan'.tr,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.bold,
                          color: Colors.white,
                        ),
                      )
                  ),


                ],
              ),
            ),

            const SizedBox(height: 43,),

          ],
        ),
      ),
    );
  }

  Widget _buildNetworkItem(BuildContext context, int index) {
    final wifi = controller.wifiList[index];
    return InkWell(
      onTap: () => _onNetworkItemTap(wifi),
      child: SizedBox(
        width: double.infinity,
        height: 50.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 50,
              height: 55.h,
              child: Align(
                alignment: Alignment.centerLeft,
                child: SvgPicture.asset(
                  AppImagePaths.receiver,
                  width: 40,
                  height: 40,
                ),
              ),
            ),

            Expanded(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    wifi.ssid!,
                    textAlign: TextAlign.start,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 26,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                )
            ),

            SizedBox(
              width: 50,
              height: 55.h,
              child: Align(
                alignment: Alignment.centerRight,
                child: Opacity(
                  opacity: 0.5,
                  child: Text(
                    (wifi.auth ?? false) ? 'WPA' : 'OPEN',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }

  void _onRescan() async {
    await controller.rescanWiFiList();
  }

  void _onCancel() {
    Get.back();
  }

  void _onNetworkItemTap(CtWifi wifi) {
    log.i('_onNetworkItemTap() -> WiFi: $wifi');
    Get.to(() => WifiSsidPasswordPage(), binding: WifiSsidPasswordBindings(
        matterController: controller.matterController,
        ssid: wifi.ssid!,
        isOnlyChangeWiFi: controller.isOnlyChangeWiFi
    ));
  }

}
