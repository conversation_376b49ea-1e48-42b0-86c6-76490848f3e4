import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/add_receiver/select_network/select_network_controller.dart';

class SelectNetworkBindings extends Bindings {
  final CtController matterController;
  final bool isOnlyChangeWiFi;

  SelectNetworkBindings({
    required this.matterController,
    required this.isOnlyChangeWiFi,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => SelectNetworkController(
      matterController: matterController,
      isOnlyChangeWiFi: isOnlyChangeWiFi,
    ));
  }
}
