import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:ct_flutter_ble_plugin/ct_wifi.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';

class SelectNetworkController extends BaseController {

  final CtController matterController;
  final bool isOnlyChangeWiFi;
  var wifiList = <CtWifi>[].obs;
  bool isRescan = false;

  SelectNetworkController({
    required this.matterController,
    required this.isOnlyChangeWiFi,
  });

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    Future(() async {
      await getWiFiList();
    });
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> getWiFiList() async {
    updateLoading(true);
    try {
      log.i('getWiFiList() -> 开始获取WiFi列表...');
      wifiList.value = await matterController.getWiFiList()
          .timeout(const Duration(seconds: 30));
      log.i('getWiFiList() -> 获取WiFi列表成功');
      updateLoading(false);
    } catch(e, s) {
      log.e('getWiFiList() -> 获取WiFi列表失败：$e, $s');
      updateLoading(false);
    }
  }

  Future<void> rescanWiFiList() async {
    BluetoothAdapterState adapterState;

    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('requestPermission() -> 获取蓝牙状态=$adapterState');
    } catch (e, r) {
      log.e('sendWifiPassword() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      log.i('rescanWiFiList() -> 蓝牙未打开');
      showSnackBar('pleaseTurnOnBluetooth'.tr);
      return;
    }

    if(isRescan) {
      log.i('rescanWiFiList() -> 已经在重新扫描了...');
      return;
    }

    isRescan = true;
    updateLoading(true);

    try {
      if (!matterController.isConnected) {
        log.i('rescanWiFiList() -> 检测到controller没有连接，正在重新连接...');
        await matterController.connect();
      }

      log.i('rescanWiFiList() -> 重新获取WiFi列表...');
      wifiList.value = await matterController.getWiFiList()
          .timeout(const Duration(seconds: 30));
      log.i('rescanWiFiList() -> 重新获取WiFi列表成功');
      isRescan = false;
      updateLoading(false);
    } catch(e, r) {
      log.e('rescanWiFiList() -> 重新获取WiFi列表失败: $e, $r');
      isRescan = false;
      updateLoading(false);
    }
  }

}
