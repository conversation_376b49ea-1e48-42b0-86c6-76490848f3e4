import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:get/instance_manager.dart';
import 'package:habi_app/pages/add_receiver/wifi_ssid_password/wifi_ssid_password_controller.dart';

class WifiSsidPasswordBindings extends Bindings {

  final CtController matterController;
  final String ssid;
  final bool isOnlyChangeWiFi;

  WifiSsidPasswordBindings({
    required this.matterController,
    required this.ssid,
    required this.isOnlyChangeWiFi,
  });

  @override
  void dependencies() {
    Get.lazyPut(() => WifiSsidPasswordController(
      matterController: matterController,
      ssid: ssid,
      isOnlyChangeWiFi: isOnlyChangeWiFi
    ));
  }
}
