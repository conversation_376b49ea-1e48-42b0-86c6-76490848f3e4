import 'dart:async';
import 'package:ct_flutter_ble_plugin/ct_controller.dart';
import 'package:ct_flutter_ble_plugin/ct_utils.dart';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_controller.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/models/device_provision/device_provision_result.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/models/dynamo_db/controller_fabric_property.dart';
import 'package:habi_app/pages/device_type/receiver/add_receiver_settings/add_receiver_settings_bindings.dart';
import 'package:habi_app/pages/device_type/receiver/add_receiver_settings/add_receiver_settings_page.dart';
import 'package:habi_app/pages/location_settings/location_settings_page.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/device_provision_service.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/utility/sentry_utils.dart';
import 'package:habi_app/widgets/view_error_log.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class WifiSsidPasswordController extends BaseController {
  final deviceShadowService = DeviceShadowService.to;
  final localStorageService = LocalStorageService.to;

  final CtController matterController;
  final String ssid;
  final bool isOnlyChangeWiFi;

  WifiSsidPasswordController({
    required this.matterController,
    required this.ssid,
    required this.isOnlyChangeWiFi,
  });

  final TextEditingController ssidController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  bool isSendWifiPassword = false;
  var obscureText = true.obs;
  bool _interrupt = false;

  static const String sendWifiPasswordKey = 'sendWifiPassword';
  static const String isWiFiConnectedKey = 'isWiFiConnected';
  static const String getThingNameKey = 'getThingName';
  static const String registerDeviceOwnerKey = 'registerDeviceOwner';
  static const String checkFabId = 'checkFabId';
  static const String getRootCertArn = 'getRootCertArn';
  static const String getUserNoc = 'getUserNoc';
  static const String createMatterClient = 'createMatterClient';
  static const String sendCSRRequestKey = 'sendCSRRequest';
  static const String checkNodeId = 'checkNodeId';
  static const String checkThdDataset = 'checkThdDataset';
  static const String setThdDataset = 'setThdDataset';
  static const String addComplete = 'addComplete';

  Map<String, bool> stepMap = {
    sendWifiPasswordKey: false,
    isWiFiConnectedKey: false,
    getThingNameKey: false,
    registerDeviceOwnerKey: false,
    checkFabId: false,
    getRootCertArn: false,
    getUserNoc: false,
    createMatterClient: false,
    sendCSRRequestKey: false,
    checkNodeId: false,
    checkThdDataset: false,
    setThdDataset: true,
    addComplete: false,
  };

  late String thingName;
  late String mcThingName;
  late String mcNodeId;
  late String rootCertArn;
  late Map<Object?, Object?> rootCaMap;
  late Map<String, dynamic> fabricMap;

  @override
  void onInit() {
    super.onInit();
    fabricMap = {};
  }

  @override
  void onReady() {
    super.onReady();
    if (GlobalService.to.config.factoryParingMode == true) {
      Future(() async {
        ssidController.text = ssid;
        String wifiPassword = await localStorageService.getFactoryPairingWiFiPassword() ?? '';
        passwordController.text = wifiPassword;
      });
      obscureText.value = false;
    }
  }

  @override
  void onClose() {
    super.onClose();
    _interrupt = true;
    ssidController.dispose();
    passwordController.dispose();
  }

  Future<void> sendWifiPassword() async {
    ISentrySpan? transaction;
    try {
      transaction = SentryUtils.startTransaction(
        isOnlyChangeWiFi ? 'changeReceiverWiFi()' : 'addReceiver()',
        'task',
        bindToScope: true,
      );
      await addReceiverV2(transaction)
          .timeout(const Duration(minutes: 5));
      transaction?.status = const SpanStatus.ok();
    } catch (e, s) {
      log.e('addReceiver() -> error=$e, $s');

      transaction?.throwable = e;
      transaction?.status = const SpanStatus.unknownError();

      SentryUtils.captureMessage('addReceiver() -> failed, errorMessage: $e', level: SentryLevel.error);

      isSendWifiPassword = false;
      updateLoading(false);

      if (e is TimeoutException) {
        showErrorDialog('Timeout when adding receiver.', -1);
      } else {
        showErrorDialog('Unknown cause', -1);
      }

      _interrupt = true;

    } finally {
      await transaction?.finish();
    }
  }

  Future<void> addReceiverV2(ISentrySpan? transaction) async {
    BluetoothAdapterState adapterState;

    try {
      adapterState = await FlutterBluePlus.adapterState.where(
              (val) => val == BluetoothAdapterState.on)
          .first.timeout(const Duration(seconds: 5));
      log.i('addReceiver() -> 获取蓝牙状态成功：$adapterState');
    } catch (e, r) {
      log.e('addReceiver() -> 获取蓝牙状态失败，e=$e, r=$r');
      adapterState = FlutterBluePlus.adapterStateNow;
    }

    if (adapterState != BluetoothAdapterState.on) {
      log.i('addReceiver() -> 蓝牙未打开');
      showSnackBar('pleaseTurnOnBluetooth'.tr);
      return;
    }

    if (stepMap[addComplete] == true) {
      log.i('addReceiver() -> 添加流程已经执行完毕...');
      Get.to(() => AddReceiverSettingsPage(),
          binding: AddReceiverSettingsBindings(
              thingName: thingName,
          ));
      return;
    }

    if (isSendWifiPassword) {
      log.i('addReceiver() -> 已经在开始发送ssid和密码...');
      return;
    }

    isSendWifiPassword = true;
    KeyboardHelper.dismissKeyboard(Get.context!);
    updateLoadingMessage('send ssid and password...');

    if (stepMap[sendWifiPasswordKey] == false || stepMap[isWiFiConnectedKey] == false) {
      ISentrySpan? span;
      final startTime = DateTime.now();

      try {
        if (!matterController.isConnected) {
          log.i('addReceiver() -> 检测到controller没有连接，正在重新连接...');
          await matterController.connect();
          log.i('addReceiver() -> 正在重新连接成功');
        }
        log.i('addReceiver() -> 开始发送ssid和password: $ssid-${passwordController.text}');
        span = SentryUtils.startChild(transaction, 'sendSSIDAndPwd()');
        await matterController.sendSSIDAndPwd(ssid, passwordController.text);
        log.i('addReceiver() -> 发送ssid和password成功！');
        span?.status = const SpanStatus.ok();
        stepMap[sendWifiPasswordKey] = true;
      } catch (e, r) {
        log.e('addReceiver() -> 发送ssid和密码失败: $e, $r');
        SentryUtils.captureMessage('addReceiver() -> sendSSIDAndPwd() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();
        isSendWifiPassword = false;
        updateLoading(false);
        showErrorDialog('Failed to send ssid and password.', 1010);
      } finally {
        await span?.finish();
      }

      final endTime = DateTime.now();
      final executionTime = endTime.difference(startTime);
      log.i('addReceiver() -> The total time taken to send SSID and password is：${executionTime.inMilliseconds} ms');
    }

    if (stepMap[isWiFiConnectedKey] == false) {
      updateLoadingMessage('check wifi connection...');
      log.i('addReceiver() -> 开始检测WiFi是否连接成功');
      bool isWiFiConnected = false;
      ISentrySpan? span;
      final startTime = DateTime.now();

      try {
        span = SentryUtils.startChild(transaction, 'checkWiFiConnection()');

        if (isOnlyChangeWiFi) {
          log.d('checkWiFiConnection() -> 检测到是修改Receiver WiFi，需要延迟5s');
          await Future.delayed(const Duration(milliseconds: 5000));
        } else {
          await Future.delayed(const Duration(milliseconds: 1000));
        }

        for (int i = 0; i < 60; i++) {
          if (_interrupt) {
            log.i('checkWiFiConnection() -> 终止...');
            break;
          }
          try {
            log.i('addReceiver() -> 正在获取WiFi连接状态，第${i + 1}次');
            isWiFiConnected = await matterController.isWiFiConnected();
          } catch (e, r) {
            log.e('addReceiver() -> 获取WiFi连接状态失败: $e, $r');
          }

          log.i('addReceiver() -> WiFi连接状态是: $isWiFiConnected');

          if (isWiFiConnected) {
            log.i('addReceiver() -> 检测到WiFi连接成功！');
            stepMap[isWiFiConnectedKey] = true;
            span?.status = const SpanStatus.ok();
            final endTime = DateTime.now();
            final executionTime = endTime.difference(startTime);
            log.i('addReceiver() -> 检测到WiFi连接成功！');
            log.i('addReceiver() -> The total time taken to check the WiFi connection is：${executionTime.inMilliseconds} ms');
            await localStorageService.setFactoryPairingWiFiPassword(passwordController.text);
            break;
          }

          await Future.delayed(const Duration(milliseconds: 1000));
        }

        if (!isWiFiConnected) {
          log.e('addReceiver() -> 检测到WiFi连接失败！');
          span?.throwable = Exception('unableToConnectThisWiFi'.tr);
          span?.status = const SpanStatus.unknownError();
          final endTime = DateTime.now();
          final executionTime = endTime.difference(startTime);
          log.i('addReceiver() -> The total time taken to check the WiFi connection is：${executionTime.inMilliseconds} ms');
          isSendWifiPassword = false;
          updateLoading(false);
          showErrorDialog('unableToConnectThisWiFi'.tr, 1010);
          return;
        }
      } catch (e) {
        SentryUtils.captureMessage('addReceiver() -> checkWiFiConnection() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();
      } finally {
        await span?.finish();
      }
    }

    //这里暂时不检测
    /*log.i('addReceiver() -> 开始检测Cloud是否连接成功');
    bool isCloudConnected = false;
    for (int i = 0; i < 30; i++) {
      try {
        log.i('addReceiver() -> 正在获取Cloud连接状态$i');
        isCloudConnected = await matterController.isCloudConnected();
      } catch (e, r) {
        log.e('addReceiver() -> 获取Cloud连接状态失败: $e, $r');
      }
      log.i('addReceiver() -> Cloud连接状态=$isCloudConnected');
      if (isCloudConnected) {
        log.i('addReceiver() -> Cloud连接成功!');
        break;
      }
      await Future.delayed(const Duration(milliseconds: 1000));
    }

    if (!isCloudConnected) {
      log.e('addReceiver() -> Cloud连接失败');
      updateLoading(false);
      showErrorDialog('Unable to connect to the cloud.', 1010);
      return;
    }*/

    if (stepMap[getThingNameKey] == false) {
      updateLoadingMessage('Start generating thing name...');
      log.i('addReceiver() -> 开始生成thing name...');
      ISentrySpan? span;
      final startTime = DateTime.now();
      try {
        span = SentryUtils.startChild(transaction, 'generatingThingName()');

        String euid = await matterController.getWifiEUid();
        log.i('addReceiver() -> euid=$euid');

        String macAddress = CtUtils.getMacAddress(euid);
        log.i('addReceiver() -> macAddress=$macAddress');

        thingName = Sait85rHelper.getMRGThingName(macAddress);
        log.i('addReceiver() -> thingName=$thingName');

        mcThingName = Sait85rHelper.getMBRCThingName(thingName, macAddress: macAddress);
        log.i('addReceiver() -> mcThingName=$mcThingName');

        stepMap[getThingNameKey] = true;
        span?.status = const SpanStatus.ok();
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> The total time taken to retrieve euid is：${executionTime.inMilliseconds} ms');

        log.i('addReceiver() -> 保存WiFi账号和密码...');
        Map<String, dynamic> allWifiMap = await localStorageService
            .getWiFiMap() ?? <String, dynamic>{};
        allWifiMap[thingName] = <String, dynamic>{
          'ssid': ssid,
          'password': passwordController.text,
        };
        await localStorageService.setWiFiMap(allWifiMap);
      } catch (e, r) {
        log.e('addReceiver() -> 生成thing name失败: $e, $r');
        SentryUtils.captureMessage('addReceiver() -> generatingThingName() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> The total time taken to retrieve euid is：${executionTime.inMilliseconds} ms');
        isSendWifiPassword = false;
        updateLoading(false);
        showErrorDialog('Failed to generate thing name.', 1010);
        return;
      } finally {
        await span?.finish();
      }
    }

    if (isOnlyChangeWiFi) {
      log.i('addReceiver() -> 检测到当前只是修改WiFi');

      try {
        log.i('addReceiver() -> 修改WiFi流程完毕，即将断开连接');
        await matterController.disconnect();
        log.i('addReceiver() -> 断开连接完成');
      } catch (e, r) {
        log.e('addReceiver() -> 销毁插件和断开连接失败: $e, $r');
      }

      isSendWifiPassword = false;
      stepMap[addComplete] = true;
      updateLoading(false);

      Get.until((route) {
        return route.settings.name == LocationSettingsPage.routeName;
      });

      showSuccessSnackBar('Successfully!');
      return;
    }

    /*bool hasUserToDevice = false;
    try {
      log.i('addReceiver() ->  开始检测$thingName是否已经添加在用户下');
      UserToDeviceList? list = await DynamoDBService.to.fetchUserToDeviceList();
      hasUserToDevice = list?.ownNames?.contains(thingName) ?? false;
      log.i('addReceiver() ->  list=$list');
      log.i('addReceiver() ->  hasUserToDevice=$hasUserToDevice');
    } catch(e) {
      log.e('addReceiver() ->  error = $e');
    }

    if (hasUserToDevice) {
      log.i('addReceiver() ->  检测到$thingName已经添加在用户下');
      stepMap[createUserRecordKey] = true;
      stepMap[registerDeviceOwnerKey] = true;
    } else {
      log.i('addReceiver() ->  $thingName没有添加在用户下，需要执行createUserRecord、registerDeviceOwner');
    }*/

    if (stepMap[registerDeviceOwnerKey] == false) {
      updateLoadingMessage('device provision...');
      ISentrySpan? span;

      try {
        bool registerDeviceOwner = false;
        String? registerDeviceOwnerBodyMassage;
        String? registerDeviceOwnerErrorMassage;
        final startTime = DateTime.now();
        span = SentryUtils.startChild(transaction, 'deviceProvision()');
        for (int i = 0; i < 12; i++) {
          if (_interrupt) {
            log.i('deviceProvision() -> 终止...');
            break;
          }
          if (i != 0) {
            await Future.delayed(const Duration(seconds: 3));
          }
          try {
            log.i('addReceiver() -> 开始注册设备所有者，第${i+1}次');
            //{statusCode: 408, body: "Waiting Device Online"}
            //{statusCode: 400, body: "Device has access already SAIT85R-xxxxxx"}
            //{statusCode: 400, body: "Device belongs to others"}
            //{statusCode: 400, body: "Invalid Bluetooth or mDNS ID"}
            DeviceProvisionResult registerDeviceOwnerResult = await DeviceProvisionService.to
                .registerDeviceOwner(bluetoothID: '$thingName-00000')
                .timeout(const Duration(seconds: 60));
            if (registerDeviceOwnerResult.isSuccess()) {
              registerDeviceOwner = true;
            } else {
              registerDeviceOwnerBodyMassage = registerDeviceOwnerResult.body;
              if (registerDeviceOwnerResult.body.contains('Device has access already')) {
                registerDeviceOwner = true;
              } else if (registerDeviceOwnerResult.body.contains('Waiting Device Online')) {
                log.i('addReceiver() -> 等待设备online...');
              } else {
                isSendWifiPassword = false;
                updateLoading(false);
                showErrorDialog(registerDeviceOwnerResult.body, 1020);
                return;
              }
            }
          } catch (e, r) {
            log.e('addReceiver() -> 注册设备所有者，第${i+1}次失败: $e, $r');
            registerDeviceOwnerErrorMassage = e.toString();
          }
          if (registerDeviceOwner) {
            log.i('addReceiver() -> 注册设备所有者成功！');
            span?.status = const SpanStatus.ok();
            final endTime = DateTime.now();
            final executionTime = endTime.difference(startTime);
            log.i('addReceiver() -> The total time taken to register the device owner is：${executionTime.inMilliseconds} ms');
            stepMap[registerDeviceOwnerKey] = true;
            break;
          }
        }

        if(!registerDeviceOwner) {
          log.e('addReceiver() -> 12次注册设备所有者全部失败！');

          final endTime = DateTime.now();
          final executionTime = endTime.difference(startTime);
          log.i('addReceiver() -> The total time taken to register the device owner is：${executionTime.inMilliseconds} ms');

          isSendWifiPassword = false;

          if (registerDeviceOwnerErrorMassage != null) {
            SentryUtils.captureMessage('addReceiver() -> deviceProvision() -> failed, errorMessage: $registerDeviceOwnerErrorMassage', level: SentryLevel.error);
            span?.throwable = Exception(registerDeviceOwnerErrorMassage);
            span?.status = const SpanStatus.unknownError();
            showDetailsErrorDialog('Failed to register device owner.',
                1020, registerDeviceOwnerErrorMassage);
          } else {
            if (registerDeviceOwnerBodyMassage != null) {
              SentryUtils.captureMessage('addReceiver() -> deviceProvision() -> failed, errorMessage: $registerDeviceOwnerBodyMassage', level: SentryLevel.error);
              span?.throwable = Exception(registerDeviceOwnerBodyMassage);
              span?.status = const SpanStatus.unknownError();
              showDetailsErrorDialog('Failed to register device owner.',
                  1020, registerDeviceOwnerBodyMassage);
            } else {
              String errorMessage = 'Failed to register device owner.';
              SentryUtils.captureMessage('addReceiver() -> deviceProvision() -> failed, errorMessage: $errorMessage', level: SentryLevel.error);
              span?.throwable = Exception(errorMessage);
              span?.status = const SpanStatus.unknownError();
              showErrorDialog(errorMessage, 1020);
            }
          }

          updateLoading(false);
          return;
        }

      } catch (e) {
        SentryUtils.captureMessage('addReceiver() -> deviceProvision() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();
      } finally {
        span?.finish();
      }
    } else {
      log.i('addReceiver() -> 已经执行过注册设备所有者');
    }

    DateTime now = DateTime.now();
    DateTime oneMinuteLater = now.subtract(const Duration(minutes: 1));
    int createFabricTimestamp = oneMinuteLater.millisecondsSinceEpoch;
    log.i('addReceiver() -> 创建Fabric时间戳: $createFabricTimestamp');

    if (stepMap[checkFabId] == false) {
      updateLoadingMessage('check fabric id...');
      log.i('addReceiver() -> 开始检测fabricId是否出现在shadow...');
      bool fabIdReady = false;
      String? fabIdErrorMassage;
      final startTime = DateTime.now();
      ISentrySpan? span;

      try {
        span = SentryUtils.startChild(transaction, 'checkFabricId()');
        for (int i = 0; i < 60; i++) {
          if (_interrupt) {
            log.i('checkFabricId() -> 终止...');
            break;
          }
          String? fabId, rootCa, ipk;
          try {
            log.i('addReceiver() -> 正在获取mcShadow...');
            final mcShadow = await deviceShadowService.fetchDeviceShadow(mcThingName)
                .timeout(const Duration(seconds: 60));
            log.i('addReceiver() -> 获取mcShadow成功!');
            Sait85rMC sait85rMC = Sait85rMC.fromJson(mcShadow, mcThingName);
            fabId = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID;
            rootCa = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabRcAc;
            ipk = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabIPK;
            log.i('addReceiver() -> fabId=$fabId');
            log.i('addReceiver() -> rootCa=$rootCa');
            log.i('addReceiver() -> ipk=$ipk');
          } catch (e, r) {
            log.e('addReceiver() -> 获取mcShadow失败: $e, $r');
            fabIdErrorMassage = e.toString();
          }

          if (fabId != null && fabId.isNotEmpty &&
              rootCa != null && rootCa.isNotEmpty &&
              ipk != null && ipk.isNotEmpty
          ) {
            fabIdReady = true;
            fabricMap['fabricId'] = fabId;
            fabricMap['rootCa'] = rootCa;
            fabricMap['ipk'] = ipk;
            stepMap[checkFabId] = true;
            span?.status = const SpanStatus.ok();
            final endTime = DateTime.now();
            final executionTime = endTime.difference(startTime);
            log.i('addReceiver() -> 检测到fabricId已经出现在shadow!');
            log.i('addReceiver() -> The total time taken to detect fabricId is：${executionTime.inMilliseconds} ms');
            break;
          }

          await Future.delayed(const Duration(milliseconds: 1000));
        }

        if (!fabIdReady) {
          isSendWifiPassword = false;

          if (fabIdErrorMassage != null) {
            SentryUtils.captureMessage('addReceiver() -> checkFabricId() -> failed, errorMessage: $fabIdErrorMassage', level: SentryLevel.error);
            span?.throwable = Exception(fabIdErrorMassage);
            span?.status = const SpanStatus.unknownError();
            showDetailsErrorDialog('Failed to get fabric id.', 1050, fabIdErrorMassage);
          } else {
            String errorMessage = 'Failed to get fabric id.';
            SentryUtils.captureMessage('addReceiver() -> checkFabricId() -> failed, errorMessage: $errorMessage', level: SentryLevel.error);
            span?.throwable = Exception(errorMessage);
            span?.status = const SpanStatus.unknownError();
            showErrorDialog(errorMessage, 1050);
          }

          final endTime = DateTime.now();
          final executionTime = endTime.difference(startTime);
          log.e('addReceiver() -> 检测到fabricId不存在于shadow!');
          log.i('addReceiver() -> The total time taken to detect fabricId is：${executionTime.inMilliseconds} ms');

          updateLoading(false);
          return;
        }
      } catch (e) {
        SentryUtils.captureMessage('addReceiver() -> checkFabricId() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();
      } finally {
        span?.finish();
      }

    } else {
      log.i('addReceiver() -> 已经检查过fabricId 跳过此步骤...');
    }


    bool hasMatterClientFlag = false;
    try {
      String fabricId = fabricMap['fabricId'] as String;

      hasMatterClientFlag = await CtFlutterMatterPlugin.getInstance()
          .hasMatterClient(fabricId);
      if (hasMatterClientFlag) {
        log.i('addReceiver() -> 已经创建过MatterClient, 跳过此步骤...');
        stepMap[getRootCertArn] = true;
        stepMap[getUserNoc] = true;
        stepMap[createMatterClient] = true;
      }
    } catch (e, r) {
      log.e('addReceiver() -> hasMatterClient失败: $e, $r');
    }

    if(stepMap[getRootCertArn] == false) {
      updateLoadingMessage('fetch root cert arn...');
      log.i('addReceiver() -> 开始获取root cert arn...');
      final startTime = DateTime.now();
      ISentrySpan? span;

      try {
        span = SentryUtils.startChild(transaction, 'getRootCertArn()');
        String deviceId = mcThingName;
        ControllerFabricProperty? property = await DynamoDBService
            .to.fetchControllerFabricProperty(deviceId);
        log.e('addReceiver() -> property: $property');

        if (property == null || property.rootCertArn == null || property.rootCertArn!.isEmpty) {
          log.e('addReceiver() -> property为空');
          String errorMessage = 'Failed to get root cert arn: property is empty';
          SentryUtils.captureMessage('addReceiver() -> getRootCertArn() -> failed, errorMessage: $errorMessage', level: SentryLevel.error);
          span?.throwable = Exception(errorMessage);
          span?.status = const SpanStatus.unknownError();
          isSendWifiPassword = false;
          showErrorDialog('Failed to get root cert arn.', 1060);
          updateLoading(false);

          final endTime = DateTime.now();
          final executionTime = endTime.difference(startTime);
          log.i('addReceiver() -> 获取root cert arn失败!');
          log.i('addReceiver() -> Total time taken to retrieve the root cert ARN is：${executionTime.inMilliseconds} ms');
          return;
        }

        stepMap[getRootCertArn] = true;
        rootCertArn = property.rootCertArn!;
        span?.status = const SpanStatus.ok();
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> Total time taken to retrieve the root cert ARN is：${executionTime.inMilliseconds} ms');
        log.i('addReceiver() -> root cert arn=$rootCertArn');
        log.i('addReceiver() -> 获取root cert arn成功!');
      } catch (e, r) {
        log.e('addReceiver() -> 获取root cert arn失败: $e, $r');
        SentryUtils.captureMessage('addReceiver() -> getRootCertArn() -> failed, errorMessage: $e, $r', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();

        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> Total time taken to retrieve the root cert ARN is：${executionTime.inMilliseconds} ms');

        isSendWifiPassword = false;
        String rootCertArnErrorMassage = e.toString();
        showDetailsErrorDialog('Failed to get root cert arn.', 1060, rootCertArnErrorMassage);
        updateLoading(false);
        return;
      } finally {
        await span?.finish();
      }
    } else {
      log.i('addReceiver() -> 已经获取过root cert arn, 跳过此步骤...');
    }

    if(stepMap[getUserNoc] == false) {
      updateLoadingMessage('fetch user noc...');
      log.i('addReceiver() -> 开始获取user noc...');
      final startTimeUserNoc = DateTime.now();
      ISentrySpan? span;

      try {
        span = SentryUtils.startChild(transaction, 'getUserNoc()');
        String userNoc = await CtFlutterMatterPlugin.getInstance()
            .getUserNoc(fabricId: fabricMap['fabricId'] as String, rootCAArn: rootCertArn);

        final endTimeUserNoc = DateTime.now();
        final executionTimeUserNoc = endTimeUserNoc.difference(startTimeUserNoc);
        log.i('addReceiver() -> user noc=$userNoc');
        log.i('addReceiver() -> 获取user noc成功!');
        log.i('addReceiver() -> Total time taken to retrieve the user NOC is：${executionTimeUserNoc.inMilliseconds} ms');

        stepMap[getUserNoc] = true;
        fabricMap['userNoc'] = userNoc;
        fabricMap['rootCertArn'] = rootCertArn;
        span?.status = const SpanStatus.ok();

        try {
          log.i('addReceiver() -> 开始保存fabric到本地: $fabricMap');
          Map<String, dynamic> allFabricMap = await localStorageService
              .getFabricMap() ?? <String, dynamic>{};
          allFabricMap[mcThingName] = fabricMap;
          await localStorageService.setFabricMap(allFabricMap);
          log.i('addReceiver() -> 保存fabric到本地成功!');
        } catch (e, r) {
          log.e('addReceiver() -> 保存fabric到本地失败: $e, $r');
        }
      } catch (e, r) {
        log.e('addReceiver() -> 获取user noc失败: $e, $r');
        SentryUtils.captureMessage('addReceiver() -> getUserNoc() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();

        final endTimeUserNoc = DateTime.now();
        final executionTimeUserNoc = endTimeUserNoc.difference(startTimeUserNoc);
        log.i('addReceiver() -> Total time taken to retrieve the user NOC is：${executionTimeUserNoc.inMilliseconds} ms');

        isSendWifiPassword = false;
        String userNocErrorMassage = e.toString();
        showDetailsErrorDialog('Failed to get user noc.', 1060, userNocErrorMassage);
        updateLoading(false);
        return;
      } finally {
        await span?.finish();
      }
    } else {
      log.i('addReceiver() -> 已经获取过user noc, 跳过此步骤...');
    }

    if(stepMap[createMatterClient] == false) {
      updateLoadingMessage('create matter client...');
      final startTime = DateTime.now();
      try {
        var rootCa = fabricMap['rootCa'] as String;
        var userNoc = fabricMap['userNoc'] as String;
        var ipk = fabricMap['ipk'] as String;
        var fabricId = fabricMap['fabricId'] as String;

        await CtFlutterMatterPlugin.getInstance()
            .createMatterClient(
            rootCa: rootCa,
            userNoc: userNoc,
            ipk: ipk,
            fabricId: fabricId,
            rootCAArn: rootCertArn
        );
        log.i('addReceiver() -> 创建MatterClient成功！');
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> The total time taken to create the matter client is：${executionTime.inMilliseconds} ms');
        stepMap[createMatterClient] = true;
      } catch (e, r) {
        log.e('addReceiver() -> 创建MatterClient失败: $e, $r');
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> The total time taken to create the matter client is：${executionTime.inMilliseconds} ms');
      }
    } else {
      log.i('addReceiver() -> 已经创建过MatterClient, 跳过此步骤...');
    }

    if (stepMap[checkNodeId] == false) {
      final startTime = DateTime.now();
      updateLoadingMessage('check node id...');
      log.i('addReceiver() -> 开始检测nodeId是否存在于shadow...');
      bool nodeIDReady = false;
      String? nodeIdErrorMessage;
      ISentrySpan? span;

      try {
        span = SentryUtils.startChild(transaction, 'checkNodeId()');
        for (int i = 0; i < 60; i++) {
          if (_interrupt) {
            log.i('checkNodeId() -> 终止...');
            break;
          }
          String? nodeID;
          try {
            log.i('addReceiver() -> 正在获取mcShadow, mcThingName=$mcThingName');
            final mcShadow = await deviceShadowService.fetchDeviceShadow(mcThingName)
                .timeout(const Duration(seconds: 60));
            log.i('addReceiver() -> 获取mcShadow成功!');

            Sait85rMC sait85rMC = Sait85rMC.fromJson(mcShadow, mcThingName);
            nodeID = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.nodeID;

            log.i('addReceiver() -> nodeId=$nodeID');
          } catch (e, r) {
            log.e('addReceiver() -> 获取mcShadow失败: $e, $r');
            nodeIdErrorMessage = e.toString();
          }

          if (nodeID != null && nodeID.isNotEmpty) {
            nodeIDReady = true;
            mcNodeId = nodeID;
            stepMap[checkNodeId] = true;
            span?.status = const SpanStatus.ok();
            log.i('addReceiver() -> 检测到nodeId已经存在于shadow！');

            final endTime = DateTime.now();
            final executionTime = endTime.difference(startTime);
            log.i('addReceiver() -> Total time taken to detect the nodeId is：${executionTime.inMilliseconds} ms');

            break;
          }

          await Future.delayed(const Duration(milliseconds: 1000));
        }

        if (!nodeIDReady) {
          isSendWifiPassword = false;
          log.e('addReceiver() -> 检测到nodeId不存在于shadow！');

          if (nodeIdErrorMessage != null) {
            SentryUtils.captureMessage('addReceiver() -> checkNodeId() -> failed, errorMessage: $nodeIdErrorMessage', level: SentryLevel.error);
            showDetailsErrorDialog('Failed to get node id.', 1090, nodeIdErrorMessage);
          } else {
            String errorMessage = 'Failed to get node id';
            SentryUtils.captureMessage('addReceiver() -> checkNodeId() -> failed, errorMessage: $errorMessage', level: SentryLevel.error);
            showErrorDialog(errorMessage, 1090);
          }

          span?.throwable = Exception(nodeIdErrorMessage ?? 'Failed to get node id');
          span?.status = const SpanStatus.unknownError();

          final endTime = DateTime.now();
          final executionTime = endTime.difference(startTime);
          log.i('addReceiver() -> Total time taken to detect the nodeId is：${executionTime.inMilliseconds} ms');

          updateLoading(false);
          return;
        }
      } catch (e) {
        SentryUtils.captureMessage('addReceiver() -> checkNodeId() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();

        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.e('addReceiver() -> 检测到nodeId不存在于shadow！');
        log.i('addReceiver() -> Total time taken to detect the nodeId is：${executionTime.inMilliseconds} ms');
      } finally {
        await span?.finish();
      }

    } else {
      log.i('addReceiver() -> 已经检查过nodeId, 跳过此步骤...');
    }

    if (stepMap[checkThdDataset] == false) {
      final startTime = DateTime.now();
      updateLoadingMessage('check thread dataset...');
      log.i('addReceiver() -> 开始检测dataset是否存在于shadow...');
      bool thdDatasetReady = false;
      String? thdDatasetErrorMessage;
      ISentrySpan? span;

      try {
        span = SentryUtils.startChild(transaction, 'checkThdDataset()');
        for (int i = 0; i < 60; i++) {
          if (_interrupt) {
            log.i('checkThdDataset() -> 终止...');
            break;
          }

          String? thdDataset;
          String? thdBaId;
          String? thdExtendedAddress;

          try {
            log.i('addReceiver() -> 正在获取dataset...');
            final mcShadow = await deviceShadowService.fetchDeviceShadow(mcThingName)
                .timeout(const Duration(seconds: 60));
            log.i('addReceiver() -> 获取dataset成功!');

            Sait85rMC sait85rMC = Sait85rMC.fromJson(mcShadow, mcThingName);
            thdDataset = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.thdDataset;
            thdBaId = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.thdBaId;
            thdExtendedAddress = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.thdExtendedAddress;

            log.i('addReceiver() -> dataset=$thdDataset');
            log.i('addReceiver() -> baId=$thdBaId');
            log.i('addReceiver() -> extendedAddress=$thdExtendedAddress');
          } catch (e, r) {
            log.e('addReceiver() -> 获取dataset失败: $e, $r');
            thdDatasetErrorMessage = e.toString();
          }

          if (thdDataset != null && thdDataset.isNotEmpty) {
            final endTime = DateTime.now();
            final executionTime = endTime.difference(startTime);
            log.i('addReceiver() -> 检测到dataset已经存在于shadow！');
            log.i('addReceiver() -> Total time taken to detect the dataset is：${executionTime.inMilliseconds} ms');

            thdDatasetReady = true;
            stepMap[checkThdDataset] = true;
            span?.status = const SpanStatus.ok();

            if (PlatformUtils.isIOS) {
              final startTimeSaveTHC = DateTime.now();

              if (thdExtendedAddress != null && thdExtendedAddress.isNotEmpty) {
                try {
                  await CtFlutterMatterPlugin.getInstance()
                      .saveThreadOperationalCredentials(thdExtendedAddress, thdDataset)
                      .timeout(const Duration(seconds: 15));
                  log.i('addReceiver() -> 保存ThreadOperationalCredentials成功!');
                } catch (e, r) {
                  log.e('addReceiver() -> 保存ThreadOperationalCredentials失败: $e, $r');
                }
              }

              final endTimeSaveTHC = DateTime.now();
              final executionTimeSaveTHC = endTimeSaveTHC.difference(startTimeSaveTHC);
              log.i('addReceiver() -> The total time taken to save ThreadOperationalCredentials is：${executionTimeSaveTHC.inMilliseconds} ms');

            } else if (PlatformUtils.isAndroid) {
              /*if (thdBaId != null && thdBaId.isNotEmpty) {
                try {
                  await CtFlutterMatterPlugin.getInstance()
                      .saveThreadOperationalCredentials(thdBaId, thdDataset)
                      .timeout(const Duration(seconds: 15));
                  log.i('addReceiver() -> 保存ThreadOperationalCredentials成功!');
                } catch (e, r) {
                  log.e('addReceiver() -> 保存ThreadOperationalCredentials失败: $e, $r');
                }
              }*/
            }
            break;
          }

          await Future.delayed(const Duration(milliseconds: 1000));
        }

        if (!thdDatasetReady) {
          isSendWifiPassword = false;
          log.e('addReceiver() -> 检测到dataset不存在于shadow！');

          if (thdDatasetErrorMessage != null) {
            SentryUtils.captureMessage('addReceiver() -> checkThdDataset() -> failed, errorMessage: $thdDatasetErrorMessage', level: SentryLevel.error);
            span?.throwable = Exception(thdDatasetErrorMessage);
            span?.status = const SpanStatus.unknownError();
            showDetailsErrorDialog('Failed to get dataset.', 1090, thdDatasetErrorMessage);
          } else {
            String errorMessage = 'Failed to get dataset.';
            SentryUtils.captureMessage('addReceiver() -> checkThdDataset() -> failed, errorMessage: $errorMessage', level: SentryLevel.error);
            span?.throwable = Exception(errorMessage);
            span?.status = const SpanStatus.unknownError();
            showErrorDialog(errorMessage, 1090);
          }

          final endTime = DateTime.now();
          final executionTime = endTime.difference(startTime);
          log.i('addReceiver() -> Total time taken to detect the dataset is：${executionTime.inMilliseconds} ms');

          updateLoading(false);
          return;
        }
      } catch (e) {
        SentryUtils.captureMessage('addReceiver() -> checkThdDataset() -> failed, errorMessage: $e', level: SentryLevel.error);
        span?.throwable = e;
        span?.status = const SpanStatus.unknownError();
        final endTime = DateTime.now();
        final executionTime = endTime.difference(startTime);
        log.i('addReceiver() -> Total time taken to detect the dataset is：${executionTime.inMilliseconds} ms');
      } finally {
        await span?.finish();
      }

    } else {
      log.i('addReceiver() -> 已经检查过dataset, 跳过此步骤...');
    }

    if (stepMap[setThdDataset] == false) {
      updateLoadingMessage('set thread dataset...');
      log.i('addReceiver() -> 开始设置dataset到controller');
      try {
        String dataset = await _fetchPreferredThreadDataset();
        if (dataset.isEmpty) {
          log.d('addReceiver() -> 没有找到首选的Thread Dataset');
          return;
        }
        await deviceShadowService.updateDeviceProperties(
            thingName: mcThingName,
            property: {
              'ep0:sMCtlr:sThdDataset': dataset
            },
            subId: Sait85rMC.subId
        );
        stepMap[setThdDataset] = true;
        log.i('addReceiver() -> 设置Dataset成功');
      } catch (e, r) {
        log.e('addReceiver() -> 设置Dataset失败: $e, $r');
      }
    } else {
      log.i('addReceiver() -> 已经设置过dataset, 跳过此步骤...');
    }

    try {
      log.i('addReceiver() -> 添加Controller流程完毕，即将断开连接');
      await matterController.disconnect();
      log.i('addReceiver() -> 断开连接完成');
    } catch (e, r) {
      log.e('addReceiver() -> 销毁插件和断开连接失败: $e, $r');
    }

    isSendWifiPassword = false;
    stepMap[addComplete] = true;
    updateLoading(false);

    Get.to(() => AddReceiverSettingsPage(),
          binding: AddReceiverSettingsBindings(
              thingName: thingName,
          ));

    showSuccessSnackBar('Successfully added receiver');
  }

  Future<String> _fetchPreferredThreadDataset() async {
    return '';
  }

  void showErrorDialog(String content, int errorCode) {
    if (_interrupt) {
      log.i('showErrorDialog() -> 添加Receiver操作已被中断，不显示错误弹窗...');
      return;
    }
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                isOnlyChangeWiFi ? 'Failed to change receiver Wi-Fi' : 'Failed to add receiver',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Text(
                '$content\nError code: $errorCode',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

  void showDetailsErrorDialog(String content, int errorCode, String errorLog) {
    if (_interrupt) {
      log.i('showErrorDialog() -> 添加Receiver操作已被中断，不显示错误弹窗...');
      return;
    }
    showDialog(
        context: Get.context!,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(
                isOnlyChangeWiFi ? 'Failed to change receiver Wi-Fi' : 'Failed to add receiver',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                )
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                    '$content\nError code: $errorCode',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    )
                ),

                const SizedBox(height: 20),

                ViewErrorLog(errorLog: errorLog),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back();
                },
                child: Text(
                  'confirm'.tr,
                  style: const TextStyle(
                      color: AppColors.ff01A796
                  ),
                ),
              ),
            ],
          );
        }
    );
  }

}
