import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/base/base_page.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/pages/add_receiver/wifi_ssid_password/wifi_ssid_password_controller.dart';
import 'package:habi_app/services/global_service.dart';

class WifiSsidPasswordPage extends BasePage<WifiSsidPasswordController> {

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  WifiSsidPasswordPage({
    super.key,
  });

  @override
  Widget buildBody(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          controller.isOnlyChangeWiFi ? 'changeReceiverWiFi'.tr : 'addReceiver'.tr,
          style: TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: PopScope(
        canPop: false,
        onPopInvoked: (didPop) {
          bool isLoading = controller.isLoading();
          log.i('onPopInvoked()-> didPop: $didPop, isLoading: $isLoading');
          if (!didPop) {
            if (isLoading) {
              log.d('正在添加Receiver，不能强制退出...');
            } else {
              Get.back();
            }
          }
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 36.0, right: 36.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 36,),

              Expanded(
                  child: Obx(() {
                    return _buildForm(context);
                  })
              ),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: FilledButton(
                  onPressed: _onNext,
                  child: Text(
                    'next'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.secondColor,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 57,),

            ],
          ),
        ),
      ),
    );
  }

  Widget _buildForm(BuildContext context) {
    bool factoryParingMode = GlobalService.to.config.factoryParingMode;
    return Form(
        key: _formKey,
        child: Column(
          children: [
            factoryParingMode ? ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                controller: controller.ssidController,
                validator: _validateSsid,
              ),
            ) : SizedBox(
                width: double.infinity,
                child: Opacity(
                    opacity: 0.5,
                    child: Text(
                      controller.ssid,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.regular,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!.firstColor,
                      ),
                    )
                )
            ),

            const SizedBox(height: 14,),
            ConstrainedBox(
              constraints: const BoxConstraints(
                  maxHeight: 148,
                  minHeight: 48
              ),
              child: TextFormField(
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
                decoration: InputDecoration(
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.visibility),
                    onPressed: () {
                      controller.obscureText.value = !controller.obscureText.value;
                    },
                  ),
                ),
                controller: controller.passwordController,
                obscureText: controller.obscureText.value,
                validator: _validatePassword,
              ),
            ),

          ],
        )
    );
  }

  String? _validateSsid(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 8) {
      return 'passwordTooShort'.tr;
    }

    return null;
  }


  void _onNext() async {
    bool validate = _formKey.currentState?.validate() ?? false;
    if (!validate) {
      return;
    }
    await controller.sendWifiPassword();
  }



}
