import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:habi_app/helpers/keyboard_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/dev_service.dart';

class AddHomeController extends GetxController {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  var isLoading = false.obs;

  @override
  void onClose() {
    nameController.dispose();
    addressController.dispose();
    super.onClose();
  }

  Future<void> addHome() async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;
    KeyboardHelper.dismissKeyboard(Get.context!);

    String homeName = nameController.text;
    String homeAddress = addressController.text;

    try {
      var addHomeResult = await DevService.to.addHome(homeName)
          .timeout(const Duration(seconds: 60));
      bool isAddSuccess = addHomeResult.success ?? false;
      if (isAddSuccess) {
        log.i('addHome() -> succeeded');
        try {
          await DevService.to.addPropertyToHome(addHomeResult.homeId!,
              'address', homeAddress)
              .timeout(const Duration(seconds: 60));
        } catch (e) {
          log.e('addPropertyToHome() -> failed: $e');
        }
        Get.back(result: true);
        showSuccessSnackBar('Location added successfully.');
      } else {
        log.e('addHome() -> failed');
        showErrorSnackBar('Failed to add location.');
      }
    } catch (e) {
      log.e('addHome() -> failed: $e');
      showErrorSnackBar('Failed to add location.');
    } finally {
      isLoading.value = false;
    }

  }

}
