import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/helpers/app_language_helper.dart';
import 'package:habi_app/models/app_language.dart';

class LanguageSelectController extends GetxController {
  late List<AppLanguage> appLanguages;
  late List<DropdownMenuEntry<String>> languageEntries;

  final selectedCountry = 'gb'.obs;
  String selectedLanguage = 'en';

  @override
  void onInit() {
    super.onInit();
    appLanguages = AppLanguageHelper.getLanguageList();
    languageEntries = _buildDropdownMenuEntriesForLanguages();
  }

  List<DropdownMenuEntry<String>> _buildDropdownMenuEntriesForLanguages() {
    final List<DropdownMenuEntry<String>> dropdownMenuEntries = [];
    for (final AppLanguage appLanguage in appLanguages) {
      dropdownMenuEntries.add(
        DropdownMenuEntry<String>(
          value: appLanguage.code,
          label: appLanguage.name,
        ),
      );
    }
    return dropdownMenuEntries;
  }
}
