import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/pages/create_account/create_account_bindings.dart';
import 'package:habi_app/pages/create_account/create_account_page.dart';
import 'package:habi_app/pages/language_select/language_select_controller.dart';
import 'package:habi_app/widgets/country_dropdown_menu.dart';

class LanguageSelectPage extends GetView<LanguageSelectController> {

  const LanguageSelectPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'createAnAccount'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.firstColor,
            ),
        ),
        leading: IconButton(
          icon: SvgPicture.asset(
            AppImagePaths.backwardArrow,
            width: 26,
            height: 26,
          ),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 36),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 33.h,),

              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  'createAccountTitle'.tr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 40,
                    fontWeight: AppFontWeights.bold,
                    color: AppColors.ff01A796,
                  ),
                ),
              ),
        
              SizedBox(height: 18.h,),

              Text(
                'createAccountBody'.tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!
                      .firstColor,
                ),
              ),
        
              SizedBox(height: 37.h,),
        
              SizedBox(
                width: double.infinity,
                child: Opacity(
                  opacity: 0.5,
                  child: Text(
                    'country'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ),
        
              SizedBox(height: 14.h,),

              Obx(() {
                return CountryDropdownMenu(
                  onChanged: (String countryCode) {
                    controller.selectedCountry.value = countryCode;
                  },
                  selectedCountryCode: controller.selectedCountry.value,
                  width: Get.width - 72,
                  menuHeight: 350.h,
                );
              }),

              SizedBox(height: 14.h,),
        
              SizedBox(
                width: double.infinity,
                child: Opacity(
                  opacity: 0.5,
                  child: Text(
                    'language'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ),
        
              SizedBox(height: 14.h,),
        
              DropdownMenu(
                  width: Get.width - 72,
                  menuHeight: 350.h,
                  dropdownMenuEntries: controller.languageEntries,
                  initialSelection: controller.selectedLanguage,
                  onSelected: _onLanguageSelected,
              ),

              SizedBox(height: 180.h,),

              SizedBox(
                width: double.infinity,
                height: 48,
                child: FilledButton(
                  onPressed: _onContinue,
                  child: Text(
                    'continue'.tr,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.secondColor,
                    ),
                  ),
                ),
              ),
        
              SizedBox(height: 113.h,),
        
            ],
          ),
        ),
      ),
    );
  }

  void _onContinue() {
    Get.to(
        () => CreateAccountPage(
          country: controller.selectedCountry.value,
          language: controller.selectedLanguage,
        ),
        binding: CreateAccountBindings()
    );
    log("selectedCountry=${controller.selectedCountry.value}");
    log("selectedLanguage=${controller.selectedLanguage}");
  }

  void _onLanguageSelected(value) {
    controller.selectedLanguage = value;
  }


}
