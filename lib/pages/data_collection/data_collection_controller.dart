import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/user_attributes.dart';
import 'package:habi_app/pages/login/login_bindings.dart';
import 'package:habi_app/pages/login/login_page.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/user_attributes_service.dart';

class DataCollectionController extends GetxController {
  final newStatus = DataCollectionStatus.off.obs;
  final isLoading = false.obs;
  final isLogouting = false.obs;

  DataCollectionStatus? currentStatus;
  Worker? _attributesWorker;

  @override
  void onInit() {
    super.onInit();

    _attributesWorker = ever(
      UserAttributesService.to.attributes,
      _onUserAttributesChange,
    );

    _onUserAttributesChange(UserAttributesService.to.attributes.value);
  }

  @override
  void onClose() {
    _attributesWorker?.dispose();

    super.onClose();
  }

  void _onUserAttributesChange(UserAttributes? attributes) {
    final status = DataCollectionStatus.fromValue(
      attributes?.dataCollection,
    );

    currentStatus = status;
    newStatus.value = status;
  }

  void toggleDataCollection(DataCollectionStatus value) {
    newStatus.value = value;
  }

  Future<void> save() async {
    if (isLoading.value) {
      return;
    }

    isLoading.value = true;

    try {
      await UserAttributesService.to.updateDataCollection(
        newStatus.value.value,
      );

      if (currentStatus == DataCollectionStatus.on && Get.arguments != null) {
        _navigateToHomePage();
        return;
      }

      showSuccessSnackBar('Success!');
    } catch (e) {
      showErrorSnackBar('Failed!');
    }

    isLoading.value = false;
  }

  void onBackPressed() {
    if (currentStatus != DataCollectionStatus.on) {
      _logout();
      return;
    }

    if (Get.arguments != null) {
      _navigateToHomePage();
      return;
    }

    Get.back();
  }

  Future<void> _logout() async {
    if (isLogouting.value) {
      return;
    }

    isLogouting.value = true;

    try {
      final result = await AuthService.to.signOut();

      if (result is! CognitoCompleteSignOut) {
        throw Exception('Error signing user out: $result');
      }

      _navigateToLoginPage();

      showSuccessSnackBar('signOut Success.');
    } catch (e, r) {
      log.e("signOut() -> e=$e, r=$r");
      showErrorSnackBar('signOut Failed.');
    }

    isLogouting.value = false;
  }

  void _navigateToHomePage() {
    Get.offAllNamed(Routes.homeManagement);
  }

  void _navigateToLoginPage() {
    Get.offAll(
      () => LoginPage(),
      routeName: LoginPage.routeName,
      binding: LoginBindings(),
    );
  }
}
