import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/pages/data_collection/data_collection_controller.dart';
import 'package:habi_app/widgets/habi_app_bar.dart';
import 'package:habi_app/widgets/highlight_text.dart';
import 'package:habi_app/widgets/loading_widget.dart';

class DataCollectionPage extends GetView<DataCollectionController> {
  const DataCollectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildBody(),
        _buildLogouting(),
      ],
    );
  }

  Widget _buildBody() {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) return;
        controller.onBackPressed();
      },
      child: Scaffold(
        appBar: HabiAppBar(
          titleText: 'dataCollection'.tr,
          onBackPressed: controller.onBackPressed,
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 16.h),
            child: Obx(() {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 16.h),
                  _buildDataCollectionOption(
                    title: 'dataCollectionOn'.tr,
                    description: 'dataCollectionOnDescription'.tr,
                    value: DataCollectionStatus.on,
                  ),
                  SizedBox(height: 16.h),
                  _buildDataCollectionOption(
                    title: 'dataCollectionOff'.tr,
                    description: 'dataCollectionOffDescription'.tr,
                    value: DataCollectionStatus.off,
                  ),
                  SizedBox(height: 16.h),
                  Padding(
                    padding: EdgeInsets.only(left: 36.w),
                    child: Text(
                      'dataCollectionNote'.tr,
                      style: TextStyles.regular14FirstColor,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  SizedBox(
                    width: double.infinity,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildSaveButton(),
                      ],
                    ),
                  ),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildLogouting() {
    return Obx(() {
      if (controller.isLogouting.value) {
        return LoadingContent(message: 'logouting'.tr);
      }
      return const SizedBox();
    });
  }

  Widget _buildDataCollectionOption({
    required String title,
    required String description,
    required DataCollectionStatus value,
  }) {
    return GestureDetector(
      onTap: () => controller.toggleDataCollection(value),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildRadioIcon(
                value == controller.newStatus.value,
              ),
              SizedBox(width: 12.w),
              HighlightText(
                text: title,
                defaultStyle: TextStyles.regular16Green,
                highlightStyle: TextStyles.bold16Green,
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Padding(
            padding: EdgeInsets.only(left: 36.w),
            child: Text(
              description,
              style: TextStyles.regular14FirstColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return FilledButton(
      onPressed: controller.isLoading.value ||
              controller.currentStatus == controller.newStatus.value
          ? null
          : controller.save,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 12.h),
            child: Text(
              'uppercaseSave'.tr,
              style: TextStyles.medium14SecondColor,
            ),
          ),
          if (controller.isLoading.value)
            SizedBox(
              width: 24.w,
              height: 24.w,
              child: const CircularProgressIndicator(
                color: Colors.white,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRadioIcon(bool isSelected) {
    return Container(
      width: 24.w,
      height: 24.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.ff01A796,
          width: 2.w,
        ),
      ),
      child: isSelected
          ? Center(
              child: Container(
                width: 12.w,
                height: 12.w,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.ff01A796,
                ),
              ),
            )
          : null,
    );
  }
}
