import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:amplify_push_notifications_pinpoint/amplify_push_notifications_pinpoint.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart' as kFoundation;
import 'package:graphs/graphs.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_settings.dart';
import 'package:habi_app/constants/matter_device_name.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/extensions/it850_extension.dart';
import 'package:habi_app/helpers/app_map_helper.dart';
import 'package:habi_app/helpers/device_ota_helper.dart';
import 'package:habi_app/helpers/fabric_helper.dart';
import 'package:habi_app/helpers/hex_helper.dart';
import 'package:habi_app/helpers/sait85r_helper.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/models/app_config.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/dev/hb_device_versions_response.dart';
import 'package:habi_app/models/dev/hb_get_home_by_device_id_response.dart';
import 'package:habi_app/models/dev/hb_get_home_by_id_response.dart';
import 'package:habi_app/models/dev/hb_update_device_response.dart';
import 'package:habi_app/models/dev/hb_version.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/device_provision/device_provision_result.dart';
import 'package:habi_app/models/dynamo_db/controller_fabric_property.dart';
import 'package:habi_app/models/dynamo_db/user_to_device_list.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/services/Iot_service.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/database_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_provision_service.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/services/thing_shadow_service.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:habi_app/services/user_attributes_service.dart';
import 'package:habi_app/utility/platform_utils.dart';
import 'package:habi_app/utility/sentry_utils.dart';
import 'package:habi_app/widgets/view_error_log.dart';
import 'package:hex/hex.dart';
import 'package:intl/intl.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:amplify_auth_cognito_dart/amplify_auth_cognito_dart.dart';
import 'package:amplify_push_notifications/amplify_push_notifications.dart';
import 'package:share_plus/share_plus.dart';
import 'package:sqflite/sqflite.dart';

class TestController extends GetxController {

  bool onOffValue = false;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> helperTest() async {
    // String thingName = 'SAIT85R-001E5E0B7DA8-FFF1_8000-001E5E090C0B8095';
    // var result = Sait85rHelper.parseThingName(thingName);
    // log.i('result=$result');

    // String thingName = 'SAIT85R-001E5E0B7DA8-FFF1_8000-001E5E090C0B8095+DEDEDEDE17132974+Light';
    // var result = Sait85rHelper.parseThingName(thingName);
    // log.i('result=$result');

    // String gwThingName = 'SAIT85R-001E5E0B7DA8';
    // var result = Sait85rHelper.parseGWThingName(gwThingName);
    // log.i('result=$result');

    // String gwThingName = 'SAIT85R-001E5E0B7DA8';
    // var result = Sait85rHelper.getScanGroupName(gwThingName);
    // log.i('result=$result');

    // var euID = '001E5E09FD70';
    // var asciiString = ascii.decode(HEX.decode(euID));
    // log.i('asciiString=$asciiString');

    // int number = 4324324;
    // String formattedNumber = number.toRadixString(16).padLeft(4, '0');
    // print(formattedNumber); // 输出: 0001

  }

  Future<void> sentryTest() async {
    // try {
    //   throw Exception('test error');
    // } catch (e) {
    //   debugPrint('捕获错误...');
    //   SentryUtils.captureMessage('test sentry message', level: SentryLevel.error);
    // }

    try {
      List<int> array = [1,2,3];
      debugPrint('item: ${array[6]}');

    } catch (e) {
      debugPrint('捕获错误...');
      SentryUtils.captureMessage('test error',
          level: SentryLevel.error,
          template: 'template content',
          hint: Hint.withMap({
          'testContent': '0',
          'testContent1': '1',
          })
      );
    }

  }

  Future<void> otaTest() async {
    // final devService = DevService.to;
    //
    // try {
    //   debugPrint('getDeviceVersions() -> start');
    //   HbDeviceVersionsResponse response = await devService.getDeviceVersions();
    //   if (response.success ?? false) {
    //     List<HbVersion> versions = response.versions ?? [];
    //     debugPrint('getDeviceVersions() -> versions: $versions');
    //   } else {
    //     debugPrint('getDeviceVersions() -> error: ${response.errorCode}');
    //   }
    // } catch (e) {
    //   debugPrint('getDeviceVersions() -> error: $e');
    // }
    //
    // try {
    //   debugPrint('updateDevice() -> start');
    //   List<String> deviceIdList = ['SAIT85R-001E5E0B7F58'];
    //
    //   HBUpdateDeviceResponse response = await devService.updateDevice(deviceIdList);
    //   if (response.success ?? false) {
    //     debugPrint('updateDevice() -> success: ${response.otaStatus}');
    //   } else {
    //     debugPrint('updateDevice() -> error: ${response.errorCode}');
    //   }
    // } catch (e) {
    //   debugPrint('updateDevice() -> error: $e');
    // }

    String currentVersion = '0101802024092620250122';
    String serverVersion = '0101902024092620250122';

    int result = currentVersion.compareTo(serverVersion);
    if (result < 0) {
      log.i('小于');
    } else if (result > 0) {
      log.i('大于');
    } else {
      log.i('等于');
    }

    // DeviceOTAHelper deviceOTAHelper = DeviceOTAHelper();
    // bool result = await deviceOTAHelper.updateDevice('SAIT85R-001E5E0B7F58');
    // log.i('ota result=$result');

    // DeviceOTAHelper deviceOTAHelper = DeviceOTAHelper();
    // String deviceId = 'SAIT85R-001E5E0B7F58';
    // HbVersion currentVersion = HbVersion(
    //   model: 'SAIT85R',
    //   version: '0101902024092620250122',
    // );
    // bool result = await deviceOTAHelper.checkAndUpdate(deviceId, currentVersion);
    // log.i('otaTest() -> result: $result');
  }

  Future<void> mqttConnect() async {
    // String thingName = 'SAIT85R-001E5E0B7DA4';
    //
    // List<String> allThingNames = [
    //   thingName,
    //   '$thingName-MCTLR-0000001E5E0B7DA4'
    // ];
    //
    // try {
    //   GlobalService service = GlobalService.to;
    //   // service.setMqttClient(thingName, client);
    // } catch (e) {
    //   log.e('fetchData() -> mqtt init error: $e');
    // }
  }

  Future<void> mqttDisconnect() async {
    // try {
    //   log.d('fetchData() -> mqtt disconnect...');
    //   GlobalService service = GlobalService.to;
    //   service.disconnectMqttClient('');

    //   log.i('fetchData() -> mqtt disconnect success!');
    // } catch (e) {
    //   log.e('fetchData() -> mqtt disconnect error: $e');
    // }
  }

  Future<void> subscribeThing() async {
    // final GlobalService mqttService = GlobalService.to;
    // String thingName = 'SAIT85R-001E5E0B7F68';
    // String newThingName = 'SAIT85R-001E5E0B7F68-FFF1_8000-001E5E090C0B8091';
    // try {
    //   HBMqttClient? mqttClient = mqttService.getMqttClient(thingName);
    //   if (mqttClient != null) {
    //     mqttClient.subscribe(newThingName);
    //     log.i('subscribeNewThing() -> subscribe success');
    //   }
    // } catch (e) {
    //   log.e('subscribeNewThing -> subscribe failed: $e');
    // }
  }

  Future<void> currentDatetimeTest() async {
    int? microsecond = DateTime.now().microsecond;
    int? microsecondsSinceEpoch = DateTime.now().microsecondsSinceEpoch;
    print('microsecond: $microsecond');
    print('microsecondsSinceEpoch: $microsecond');

    int? millisecond = DateTime.now().millisecond;
    int? millisecondsSinceEpoch = DateTime.now().millisecondsSinceEpoch;
    print('millisecond: $millisecond');
    print('millisecondsSinceEpoch: $millisecondsSinceEpoch');

    DateTime timestamp1 = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
    DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    String formattedDate1 = dateFormat.format(timestamp1);
    print('Formatted Date1: $formattedDate1');
  }

  Future<void> datetimeTest() async {
    try {
      // var t1 = 1727168404 * 1000;
      // var t2 = 1727236357 * 1000;
      //
      // DateTime timestamp1 = DateTime.fromMillisecondsSinceEpoch(t1);
      // DateTime timestamp2 = DateTime.fromMillisecondsSinceEpoch(t2);
      //
      // print('T1: $t1');
      // print('T2: $t2');
      //
      // // 创建一个DateFormat对象
      // DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
      //
      // // 格式化DateTime对象
      // String formattedDate1 = dateFormat.format(timestamp1);
      // String formattedDate2 = dateFormat.format(timestamp2);
      //
      // print('Formatted Date1: $formattedDate1');
      // print('Formatted Date2: $formattedDate2');
      //
      // // 使用isAfter方法判断timestamp1是否在timestamp2之后
      // bool isAfter = timestamp1.isAfter(timestamp2);
      // print('timestamp1 is after timestamp2: $isAfter');
      //
      // // 使用isBefore方法判断timestamp1是否在timestamp2之前
      // bool isBefore = timestamp1.isBefore(timestamp2);
      // print('timestamp1 is before timestamp2: $isBefore');
      //
      // // 使用compareTo方法比较两个时间戳
      // int comparison = timestamp1.compareTo(timestamp2);
      // if (comparison > 0) {
      //   print('timestamp1 is after timestamp2');
      // } else if (comparison < 0) {
      //   print('timestamp1 is before timestamp2');
      // } else {
      //   print('timestamp1 is at the same moment as timestamp2');
      // }

      // 获取当前的UTC时间
      DateTime utcTime = DateTime.now().toUtc();
      // 格式化UTC时间为字符串
      String formattedUtcTime = utcTime.toIso8601String();
      log.i('formattedUtcTime: $formattedUtcTime');
      // 使用DateFormat格式化UTC时间，去掉尾部的毫秒和纳秒部分
      String formattedUtcTime2 = DateFormat("yyyy-MM-ddTHH:mm:ss'Z'").format(utcTime);
      log.i('formattedUtcTime2: $formattedUtcTime2');

    } catch(e) {
      log.e('error = $e');
    }
  }

  Future<void> createUserRecord() async {
    try {
      await DeviceProvisionService.to.createUserRecord();
    } catch (e) {
      log.e('createUserRecord failed: $e');
    }
  }

  Future<void> addControllerToUser() async {
    try {
      String macAddress = '001E5E0B7F58';
      String thingName = 'SAIT85R-$macAddress';
      String bluetoothID = '$thingName-00000';
      log.i('thingName=$thingName');
      log.i('bluetoothID=$bluetoothID');

      DeviceProvisionResult register = await DeviceProvisionService.to.registerDeviceOwner(
          bluetoothID: bluetoothID);
      log.i('addControllerToUser succeeded: $register');
    } catch(e, r) {
      log.e('addControllerToUser failed: $e, $r');
    }
  }

  Future<void> recoverControllerToUser() async {
    try {
      String macAddress = '001E5E0B7F58';
      String thingName = 'SAIT85R-$macAddress';
      String bluetoothID = '$thingName-00000';
      log.i('thingName=$thingName');
      log.i('bluetoothID=$bluetoothID');

      DeviceProvisionResult register = await DeviceProvisionService.to.registerDeviceOwner(
          bluetoothID: bluetoothID);
      if (!register.isSuccess()) {
        log.i('registerDeviceOwner failed: $register');
        return;
      }
      log.i('registerDeviceOwner succeeded: $register');


      String deviceId = thingName;
      HbGetHomeByDeviceIdResponse getHomeByDeviceIdResponse = await DevService.to.getHomeByDeviceID(deviceId);
      if (!(getHomeByDeviceIdResponse.success ?? false)) {
        log.i('getHomeByDeviceID failed: ${getHomeByDeviceIdResponse.toJson()}');
        return;
      }
      log.i('getHomeByDeviceID succeeded: ${getHomeByDeviceIdResponse.toJson()}');
      String homeId = getHomeByDeviceIdResponse.home!.homeId!;
      log.i('homeId: $homeId');

      try {
        final session = await AuthService.to.fetchCognitoAuthSession();
        final userId = session.identityIdResult.value;
        log.i('userId: $userId');
        List<String> userIdList = [userId];
        bool isOwner = true;
        await DevService.to.addUserToHome(homeId, userIdList, isOwner);
        log.i('addUserToHome succeeded');
      } catch (e, r) {
        log.e('addUserToHome failed: $e, $r');
        return;
      }

      HbGetHomeByIdResponse getHomeByIdResponse = await DevService.to.getHomeByID(homeId);
      if (!(getHomeByIdResponse.success ?? false)) {
        log.i('getHomeByID failed: ${getHomeByIdResponse.toJson()}');
        return;
      }
      log.i('getHomeByID succeeded: ${getHomeByIdResponse.toJson()}');

    } catch(e, r) {
      log.e('recoverControllerToUser failed: $e, $r');
    }
  }

  Future<void> removeControllerFromUser() async {
    try {
      String deviceId = 'SAIT85R-001E5E0B7F58';
      DeviceProvisionResult result = await DeviceProvisionService.to
          .removeDeviceRegistration(deviceId: deviceId);
      if (result.isSuccess()) {
      } else {
        log.e('removeControllerFromUser failed.');
      }
    } catch(e) {
      log.e('removeControllerFromUser failed: $e');
    }
  }

  Future<void> removeUserRecord() async {
    try {
      await DeviceProvisionService.to.removeUserRecord();
    } catch (e) {
      log.e('removeUserRecord failed: $e');
    }
  }

  Future<void> shareDeviceByOwner() async {
    try {
      DeviceProvisionResult result = await DeviceProvisionService.to.shareDeviceByOwner(
          deviceId: 'SAIT85R-001E5E0B7DA4',
          username: '<EMAIL>'
      ).timeout(const Duration(seconds: 30));
      log.i('shareDeviceByOwner() -> result=$result');
    } catch(e) {
      log.e('shareDeviceByOwner() -> error = $e');
    }
  }

  Future<void> removeShareDeviceByOwner() async {
    try {
      DeviceProvisionResult result = await DeviceProvisionService.to.removeShareDeviceByOwner(
          deviceId: 'SAIT85R-001E5E0B7DA4',
          username: '<EMAIL>'
      ).timeout(const Duration(seconds: 30));
      log.i('removeShareDeviceByOwner() -> result=$result');
    } catch(e) {
      log.e('removeShareDeviceByOwner() -> error = $e');
    }
  }

  Future<void> fetchUserToDeviceList() async {
    try {
      UserToDeviceList? list = await DynamoDBService.to.fetchUserToDeviceList();
      log.i('fetchUserToDeviceList() -> list=$list');
    } catch(e) {
      log.e('fetchUserToDeviceList() -> error = $e');
    }
  }

  Future<void> listThingGroups() async {
    try {
      var list = await IotService.to.listThingGroups();
      if  (list == null || list.isEmpty) {
        log.i('listThingGroups() -> list is empty');
        return;
      }
      for (var group in list) {
        log.i('listThingGroups() -> groupName=${group.groupName}');
      }
    } catch(e) {
      log.e('listThingGroups() -> error = $e');
    }
  }

  Future<void> listThingsInThingGroup() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String thingGroupName = Sait85rHelper.getThingGroupName(thingGroup);
    log.i('thingGroupName: $thingGroupName');

    try {
      var list = await IotService.to.listThingsInThingGroup(thingGroupName);
      log.i('listThingsInThingGroup() -> list=$list');
    } catch(e) {
      log.e('listThingsInThingGroup() -> error = $e');
    }
  }

  Future<void> getThingShadow() async {
    try {
      // String macAddress = '001E5E08F31C';
      // String macAddress = '001E5E08F304';
      // String macAddress = '001E5E0B7DA4';
      // String thingName = Sait85rHelper.getMRGThingName(macAddress);
      // String mcThingName = Sait85rHelper.getMBRCThingName(thingName, macAddress: macAddress);

      // final shadow = await ThingShadowService.to.getShadow(thingName);
      // log.i('getShadow() -> shadow=\n$shadow');
      // Sait85rGW sait85rGW =  Sait85rGW.fromJson(shadow, thingName);

      // final mcShadow = await ThingShadowService.to.getShadow(mcThingName);
      // log.i('getShadow() -> mcShadow=\n$mcShadow');
      // Sait85rMC sait85rMC = Sait85rMC.fromJson(mcShadow, mcThingName);


      try {
        String thingName = 'SAIT85R-001E5E0B7F58-MCTLR-0000001E5E0B7F58';
        final shadow = await ThingShadowService.to.getShadow(thingName);
        log.i('getThingShadow() -> shadow: \n $shadow');
      } catch (e, s) {
        log.e('getThingShadow() -> e=$e, s=$s');
      }


      // final shadow = await LocalStorageService.to.getThingShadow(thingName) ?? {};
      // log.i('getShadow() -> shadow=\n$shadow');
      // IT850 it850 = IT850.fromJson(shadow, thingName);
      // log.i('getShadow() -> before toJson=\n${it850.toJson()}');
      //
      // it850.mapJson({
      //   "state": {
      //     "reported": {
      //       "connected": "true",
      //       "version": 1,
      //       "cloud_conn": 1,
      //       "node_conn": 1,
      //       "11": {
      //         "model": "FFF1_8000",
      //         "properties": {
      //           "ep1:sTherS:HeatingSp": 2900,
      //           "ep1:sTherS:HeatingSp_a": 2900
      //         }
      //       },
      //       "createdAt": "2024-11-23T01:42:50.055Z"
      //     }
      //   },
      //   "metadata": {
      //     "reported": {
      //       "connected": {"timestamp": 1732326170},
      //       "version": {"timestamp": 1732326170},
      //       "cloud_conn": {"timestamp": 1732326170},
      //       "node_conn": {"timestamp": 1732326170},
      //       "11": {
      //         "model": {"timestamp": 1732326170},
      //         "properties": {
      //           "ep1:sTherS:HeatingSp": {"timestamp": 1732326170},
      //           "ep1:sTherS:HeatingSp_a": {"timestamp": 1732326170}
      //         }
      //       },
      //       "createdAt": {"timestamp": 1732326170}
      //     }
      //   },
      //   "version": 476,
      //   "timestamp": 1732326170
      // });

      // it850.mapJson({
      //   "state": {
      //     "reported": {
      //       "connected": "true",
      //       "version": 1,
      //       "cloud_conn": 1,
      //       "node_conn": 1,
      //       "11": {
      //         "model": "FFF1_8000",
      //         "properties": {
      //           "ep1:sTherS:RunningState": 8,
      //           // "ep1:sTherS:SystemMode": 4,
      //           // "ep1:sTherS:SystemMode_a": 4
      //         }
      //       },
      //       "createdAt": "2024-11-21T01:26:06.743Z"
      //     }
      //   },
      //   "metadata": {
      //     "reported": {
      //       "connected": {"timestamp": 1732152367},
      //       "version": {"timestamp": 1732152367},
      //       "cloud_conn": {"timestamp": 1732152367},
      //       "node_conn": {"timestamp": 1732152367},
      //       "11": {
      //         "model": {"timestamp": 1732152367},
      //         "properties": {
      //           "ep1:sTherS:RunningState": {"timestamp": 1732152367}
      //         }
      //       },
      //       "createdAt": {"timestamp": 1732152367}
      //     }
      //   },
      //   "version": 91,
      //   "timestamp": 1732152367
      // });

      // log.i('getShadow() -> after toJson=\n${it850.toJson()}');

    } catch (e) {
      log.e('getShadow() -> failed: $e');
    }
  }


  Future<void> fetchControllerFabricProperty() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    try {

      String deviceId = mcThingName;
      ControllerFabricProperty? controllerFabricProperty = await DynamoDBService
          .to.fetchControllerFabricProperty(deviceId);
      log.e('fetchControllerFabricProperty() -> cfp = $controllerFabricProperty');
    } catch(e) {
      log.e('fetchControllerFabricProperty() -> error = $e');
    }
  }

  Future<void> updateUserAttributes() async {
    try {
      // var results = await Amplify.Auth.updateUserAttribute(
      //     userAttributeKey: AuthUserAttributeKey.name,
      //     value: 'nealon'
      // );
      // log.i('userAttributesTest() -> results: $results');

      List<AuthUserAttribute> attributesList = [
        const AuthUserAttribute(
          userAttributeKey: CognitoUserAttributeKey.name,
          value: 'nealon',
        ),
        const AuthUserAttribute(
          userAttributeKey: CognitoUserAttributeKey.familyName,
          value: 'cao',
        ),
      ];
      var results = await Amplify.Auth.updateUserAttributes(attributes: attributesList);
      log.i('updateUserAttributes() -> results: $results');
    } catch(e) {
      log.e('updateUserAttributes failed: $e');
    }
  }

  Future<void> fetchUserAttributes() async {
    try {
      var results = await Amplify.Auth.fetchUserAttributes();
      log.i('fetchUserAttributes() -> results: $results');
    } catch(e) {
      log.e('fetchUserAttributes failed: $e');
    }
  }

  Future<void> publishProperty() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    try {
      await ThingShadowService.to.publishProperty(
          thingName: thingGroup,
          property: {
            'ep0:sGateway:SetDeviceName': 'Nealon Matter Controller'
          },
          subId: '000000000001'
      ).timeout(const Duration(seconds: 30));
      log.i('publishProperty() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('publishProperty() -> failed to publish data to cloud: $e, $r');
    }
  }

  Future<void> publishOTAFirmwareURL() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    // try {
    //   await ThingShadowService.to.publishProperty(
    //       thingName: thingGroup,
    //       property: {
    //         'ep0:sOTA:SetOTAFirmwareURL_d': 'http://ec2-18-194-20-66.eu-central-1.compute.amazonaws.com/debug/OTA_Test/SAIT85R/SAIT85R_0101602024092620241104.bin'
    //       },
    //       subId: '000000000001'
    //   ).timeout(const Duration(seconds: 30));
    //   log.i('publishOTAFirmwareURL() -> successfully publish data to cloud!');
    // } catch (e, r) {
    //   log.e('publishOTAFirmwareURL() -> failed to publish data to cloud: $e, $r');
    // }
  }

  Future<void> publishNewNodeID() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      await ThingShadowService.to.publishProperty(
          thingName: mcThingName,
          property: {
            'ep0:sMCtlr:sNewNodeID': nodeId
          },
          subId: '11'
      ).timeout(const Duration(seconds: 30));
      log.i('publishNewNodeID() -> successfully publish data to cloud!');
    } catch (e, r) {
      log.e('publishNewNodeID() -> failed to publish data to cloud: $e, $r');
    }
  }

  Future<void> cognitoTest() async {
    try {
      // final session = await AuthService.to.fetchCognitoAuthSession();
      // final user = await AuthService.to.getCurrentUser();

      // log.i('awsCognitoTest() -> session: $session');
      // log.i('awsCognitoTest() -> user: $user');

      // log.i('awsCognitoTest() -> credentialsResult: ${session.credentialsResult.value.toJson()}');
      // log.i('awsCognitoTest() -> userPoolTokensResult: ${session.userPoolTokensResult.value.toJson()}');
      // log.i('awsCognitoTest() -> signInDetails: ${user.signInDetails.toJson()}');
      // log.i('awsCognitoTest() -> props: ${session.userPoolTokensResult.value.accessToken.toString()}');
      // log.i('awsCognitoTest() -> claims: ${session.userPoolTokensResult.value.accessToken.claims.toJson()}');
    } catch(e) {
      log.e('failed: $e');
    }
  }

  Future<void> amplifyTest() async {
    try {
      final AmplifyAuthProviderRepository authProviderRepo = AmplifyAuthProviderRepository();

      /// The Auth category.
      final AuthCategory Auth = AuthCategory();

      /// The Analytics category.
      final AnalyticsCategory Analytics = AnalyticsCategory();

      /// The Storage category.
      final StorageCategory Storage = StorageCategory();

      /// The DataStore category.
      final DataStoreCategory DataStore = DataStoreCategory();

      /// The API category.
      final APICategory API = APICategory();

      /// The Amplify event hub.
      final AmplifyHub Hub = AmplifyHub();

      /// The Notifications category.
      final NotificationsCategory Notifications = NotificationsCategory();

      final categories = <Category, AmplifyCategory>{
        Category.analytics: Analytics,
        Category.api: API,
        Category.auth: Auth,
        Category.dataStore: DataStore,
        Category.pushNotifications: Notifications.Push,
        Category.storage: Storage,
      };

      final appConfig = await AppConfig.forEnvironment('prod');

      final json = jsonDecode(appConfig.amplify) as Map;
      AmplifyConfig amplifyConfig = AmplifyConfig.fromJson(json.cast());

      final sortedCategories = topologicalSort(
        categories.keys,
            (category) => categories[category]!.categoryDependencies,
      ).reversed;

      log.i('sortedCategories: $sortedCategories');

      for (final category in sortedCategories) {
        log.i('category: $category');
        final plugins = categories[category]!.plugins;
        log.i('plugins: $plugins');
        await Future.wait(
          eagerError: true,
          plugins.map((plugin) => plugin.configure(
              config: amplifyConfig,
              authProviderRepo: authProviderRepo,
            ),
          ),
        );
      }

      log.i('success!');

    } catch(e, s) {
      log.e('failed: $e, $s');
    }
  }

  Future<void> reAmplifyTest() async {
    try {
      log.i('Amplify.isConfigured: ${Amplify.isConfigured}');

      // log.i('Amplify.Notifications.Push.plugins: ${Amplify.Notifications.Push.plugins}');
      // AmplifyPushNotificationsPinpoint? amplifyPushNotificationsPinpoint = Amplify.Notifications.Push.plugins.first as AmplifyPushNotificationsPinpoint;
      // AmplifyPushNotifications amplifyPushNotifications = amplifyPushNotificationsPinpoint as AmplifyPushNotifications;
      // log.i('amplifyPushNotificationsPinpoint: $amplifyPushNotificationsPinpoint');
      // log.i('amplifyPushNotifications: $amplifyPushNotifications');
      // await amplifyPushNotifications.reset();
      // await amplifyPushNotifications.configure(
      //     config: amplifyConfig,
      //     authProviderRepo: authProviderRepo
      // );


      // log.i('Amplify.Auth.plugins: ${Amplify.Auth.plugins}');
      // AmplifyAuthCognito? amplifyAuthCognito = Amplify.Auth.plugins.first as AmplifyAuthCognito;
      // AmplifyAuthCognitoDart amplifyAuthCognitoDart = amplifyAuthCognito as AmplifyAuthCognitoDart;
      // log.i('amplifyAuthCognito: $amplifyAuthCognito');
      // log.i('amplifyAuthCognitoDart: $amplifyAuthCognitoDart');
      // amplifyAuthCognitoDart.close();

      // final appConfig = await AppConfig.forEnvironment('prod');
      // try {
      //   await Amplify.addPlugins([
      //     AmplifyAuthCognito(),
      //     AmplifyAPI(),
      //     if (!kFoundation.kIsWeb) AmplifyPushNotificationsPinpoint(),
      //   ]);
      //   await Amplify.configure(appConfig.amplify);
      // } on Exception catch (e) {
      //   debugPrint('error: $e');
      // }

      log.i('success!');
    } catch(e) {
      log.e('failed: $e');
    }
  }


  Future<void> exportDatabase() async {
    log.i('exportDatabase() -> 开始测试...');
    try {
      final dbPath = '${await getDatabasesPath()}/habi.db';
      debugPrint("exportDatabase() -> dbPath: $dbPath");
      List<XFile> xFiles = [XFile(dbPath)];
      ShareResult result;
      if (PlatformUtils.isIOS) {
        final deviceInfo = await DeviceInfoPlugin().deviceInfo;
        final deviceInfoList = deviceInfo.data.entries;
        var isIpad = deviceInfoList.any((entry) =>
        (entry.key == 'model' && entry.value.toString().contains('iPad')) ||
            (entry.key == 'name' && entry.value.toString().contains('iPad')) ||
            (entry.key == 'systemName' && entry.value.toString().contains('iPadOS'))
        );
        final mqSize = MediaQuery.of(Get.context!).size;
        if (isIpad) {
          debugPrint("exportDatabase() -> is ipad");
          result = await Share.shareXFiles(
            xFiles,
            subject: 'Export Database',
            // text: 'Attached application logs',
            sharePositionOrigin: Rect.fromLTWH(
                0,
                0,
                mqSize.width,
                mqSize.height / 4
            ),
          );
        } else {
          debugPrint("exportDatabase() -> is iPhone");
          result = await Share.shareXFiles(
            xFiles,
            subject: 'Export Database',
            // text: 'Attached application logs',
          );
        }
      } else {
        debugPrint("exportDatabase() -> is android");
        result = await Share.shareXFiles(
          xFiles,
          subject: 'Export Database',
          // text: 'Attached application logs',
        );
      }

      debugPrint('exportDatabase() -> Result: ${result.status}');

      if (result.status == ShareResultStatus.success) {
        showSuccessSnackBar('successfully!');
      }
    } catch (e, s) {
      log.e('exportDatabase() -> error: $e, $s');
    }
  }

  Future<void> readAllLocalData() async {
    log.i('readAllLocalData() -> 开始测试...');
    LocalStorageService.to.printAllData();
  }

  Future<void> writeLocalData() async {
    log.i('writeLocalData() -> 开始测试...');

    // try {
    //   LocalStorageService service = LocalStorageService.to;
    //   await LocalStorageService.to.eraseThingShadowData();
    //   await service.setHomeList('');
    //   await service.setFabricMap(<String, dynamic>{});
    //   await service.setWiFiMap(<String, dynamic>{});
    //   log.i('writeLocalData() -> end');
    // } catch (e, r) {
    //   log.e('writeLocalData() -> 失败: $e, $r');
    // }

    try {
      LocalStorageService service = LocalStorageService.to;
      String thingName = 'SAIT85R-001E5E0B7DA4';

      Map<String, dynamic> allWifiMap = await service
          .getWiFiMap() ?? <String, dynamic>{};
      allWifiMap[thingName] = <String, dynamic>{
        'ssid': 'TP-LINK_35FC',
        'password': 'asdf123456@',
      };
      await service.setWiFiMap(allWifiMap);

      log.i('writeLocalData() -> end');
    } catch (e, r) {
      log.e('writeLocalData() -> 失败: $e, $r');
    }

  }

  Future<void> eraseAllData() async {
    // await LocalStorageService.to.eraseAllData();
    // await LocalStorageService.to.clearThingShadow('SAIT85R-001E5E0B7F68');
    // await LocalStorageService.to.setThingGroup('');
    log.i('eraseAllData() -> done.');
  }

  Future<void> getControllerNodeId() async {
    try {
      String? thingGroup = await LocalStorageService.to.getThingGroup();
      if (thingGroup == null || thingGroup.isEmpty) {
        return;
      }

      String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
      log.e('getControllerNodeId() -> mcThingName: $mcThingName');

      String? fabricId = await FabricHelper.getFabricId(mcThingName);
      if (fabricId == null || fabricId.isEmpty) {
        return;
      }

      log.e('getControllerNodeId() -> fabricId: $fabricId');
      dynamic nodeId = await CtFlutterMatterPlugin.getInstance()
          .getControllerNodeId(fabricId);

      BigInt bigInt = BigInt.from(nodeId);
      if (bigInt.isNegative) {
        bigInt = bigInt + (BigInt.one << 64);
      }

      String convertNodeId = bigInt.toRadixString(16).toUpperCase();
      log.i('getControllerNodeId() -> convertNodeId=$convertNodeId');

      showDialog(
          context: Get.context!,
          builder: (context) {
            return AlertDialog(
              title: const Text('Controller Node ID'),
              content: Text(convertNodeId),
              actions: [
                TextButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          }
      );

    } catch (e, r) {
      log.e('getControllerNodeId() -> 失败: $e, $r');
    }
  }

  Future<void> getRootCa() async {
    try {
      String macAddress = '001E5E0B7DA4';
      String thingName = 'SAIT85R-$macAddress';
      String mcThingName = '$thingName-MCTLR-0000$macAddress';

      Map<Object?, Object?> rootCaMap = await CtFlutterMatterPlugin.getInstance()
          .getRootCa(thingName: mcThingName);
      log.i('getRootCa() -> rootCaMap: $rootCaMap');

    } catch (e, r) {
      log.e('getRootCa() -> 失败: $e, $r');
    }
  }

  Future<void> getUserNoc() async {
    // log.i('getUserNoc() -> 开始测试...');
    // try {
    //   var noc = await CtFlutterMatterPlugin.getInstance()
    //       .getUserNoc(
    //       fabricId: 'FAB792600378299D',
    //       rootCAArn: 'arn:aws:s3:::matter-controller-ca/FAB792600378299D/Certificate/root_ca_FAB792600378299D.pem'
    //   );
    //   log.i('getUserNoc() -> noc: $noc');
    // } catch (e, r) {
    //   log.e('getUserNoc() -> 失败: $e, $r');
    // }
  }

  Future<void> createMatterClient() async {
    try {
      var rootCa = '';
      var rootCaCertArn = '';
      var userNoc = '';
      var ipk = '';
      var fabricId = '';

      await CtFlutterMatterPlugin.getInstance()
          .createMatterClient(
          rootCa: rootCa,
          rootCAArn: rootCaCertArn,
          userNoc: userNoc,
          ipk: ipk,
          fabricId: fabricId
      );
      log.i('createMatterClient() -> success');
    } catch (e, r) {
      log.e('createMatterClient() -> 失败: $e, $r');
    }
  }

  Future<void> shutdownMatterClient() async {
    try {
      CtFlutterMatterPlugin.getInstance().shutdown('FAB351158600258F');
      log.i('shutdownMatterClient() -> success');
    } catch (e) {
      log.e('shutdownMatterClient() -> failed: $e');
    }
  }

  Future<void> addMatterOverWiFiDevice() async {
    log.i('addMatterOverWiFiDevice() -> 开始测试...');
    try {
      String fabricId = 'FAB351158600258F';
      // String code = 'MT:4AQU6YFN16J3OT28120';
      String code = 'MT:Y.K9042C001I2319L10';
      String ssid = 'App-Matter-Test';
      String pwd = 'asdf123456@';

      if (ssid.isEmpty || pwd.isEmpty) {
        log.e('addMatterOverWiFiDevice() -> 添加设备失败: ssid和pwd不能为空');
        return;
      }

      log.i('addMatterOverWiFiDevice() -> ssid=$ssid, pwd=$pwd');
      var result = await CtFlutterMatterPlugin.getInstance()
          .addWiFiDevice(fabricId, code, ssid, pwd);
      log.i('addMatterOverWiFiDevice() -> result=$result');

    } catch (e, r) {
      log.e('addMatterOverWiFiDevice() -> 添加设备失败: $e, $r');
    }
  }

  Future<void> addThreadDeviceWithTBRDataset() async {
    try {
      String fabricId = 'FAB351158600258F';
      await CtFlutterMatterPlugin.getInstance()
          .addThreadDeviceWithTBRDataset(fabricId, "121", "", "0e080000000000000000000300000b35060004001fffe00208dead00beef00cafe0708fddead00beef00000510a4bd2148406166aeafc11589b8e1fa8d030a4f70656e54687265616401021a4a041061dec244ffe2dcd84d1c8d72d878c7610c0402a0f7f8");
      log.i('addThreadDeviceWithTBRDataset() -> 控制成功!');
    } catch (e, r) {
      log.e('addThreadDeviceWithTBRDataset() -> 控制失败: $e, $r');
    }
  }

  Future<void> onOffControl() async {
    log.i('onOffControl() -> 开始测试...');

    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {

      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .writeAttributeWithWildcard(fabricId, nodeId, 1, "onOff", "toggle", 2);

      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .lightOnOff(fabricId, nodeId, onOffValue ? 1 : 0);
      //
      // onOffValue = !onOffValue;

      var result = await CtFlutterMatterPlugin.getInstance()
          .lightToggle(fabricId, nodeId);

      log.i('onOffControl() -> 控制结果: $result');
    } catch (e, r) {
      log.e('onOffControl() -> 控制失败: $e, $r');
    }
  }

  Future<void> readPairingWindowStatus() async {
    log.i('readPairingWindowStatus() -> 开始测试...');
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readPairingWindowStatus(fabricId, nodeId);
      log.i('readPairingWindowStatus() -> 成功: $result');
    } catch (e, r) {
      log.e('readPairingWindowStatus() -> 失败: $e, $r');
    }
  }

  Future<void> readValue() async {
    log.i('readValue() -> 开始测试...');
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    //Serial_Number
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId, 0, 40, 15);
      log.i('readValue() -> serial number: $result');
    } catch (e, r) {
      log.e('readValue() -> 失败: $e, $r');
    }

    //PRODUCT_NAME
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId, 0, 40, 3);
      log.i('readValue() -> product name: $result');
    } catch (e, r) {
      log.e('readValue() -> 失败: $e, $r');
    }

    //VENDOR_ID
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId, 0, 40, 2);
      log.i('readValue() -> vendor id: $result');
    } catch (e, r) {
      log.e('readValue() -> 失败: $e, $r');
    }

    // System Mode
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAttribute(fabricId, nodeId, 1, 513, 28);
      log.i('readValue() -> system mode: $result');
    } catch (e, r) {
      log.e('readValue() -> 失败: $e, $r');
    }

    // Light OnOff
    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .readAttribute(fabricId, nodeId, 1, 6, 0);
    //   log.i('readValue() -> 成功: $result');
    // } catch (e, r) {
    //   log.e('readValue() -> 失败: $e, $r');
    // }

    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .readAllAttribute(fabricId, nodeId, endpointId: 0, clusterId: 70);
    //   log.i('readValue() -> 成功: $result');
    // } catch (e, r) {
    //   log.e('readValue() -> 失败: $e, $r');
    // }

    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .readThreadNetworkName(fabricId, nodeId);
    //   log.i('readValue() -> 成功: $result');
    // } catch (e, r) {
    //   log.e('readValue() -> 失败: $e, $r');
    // }
  }

  Future<void> writeValue() async {
    log.i('writeValue() -> 开始测试...');

    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {

      // open commission window
      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .writeAttribute(fabricId, nodeId, 0, 48, 0, 'SignedInteger', '180', 60000);

      // Light OnOff
      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .writeAttribute(fabricId, nodeId, 1, 6, 0, "UnsignedInteger", '0', 60000);

      // Thermostat system mode
      String off = '0';
      String heat = '4';
      String cool = '3';

      var result = await CtFlutterMatterPlugin.getInstance()
          .writeAttribute(fabricId, nodeId, 1, 513, 28, "UnsignedInteger", heat, 60000);

      // 通配符写入测试
      // String fabricId = 'FAB351158600258F';
      // String nodeId = 'DEDEDEDE88020652';
      // var result = await CtFlutterMatterPlugin.getInstance()
      //     .writeAttributeWithWildcard(nodeId, 1, "thermostat", "writeSystemModeAttribute", systemType);

      // OTA测试
      // String fabricId = 'FAB351158600258F';
      // String controllerNodeId = 'DEDEDEDE65877925';
      // showSnackBar('writeOTAValue() -> 准备写入：$controllerNodeId');
      //
      // int endpointId = 0;
      // int clusterId = 42;
      // int attributeId = 0;
      // String type = 'json';
      // String value = "{\"0:ARR-OBJ\":[{\"1:U64\":${controllerNodeId}, \"2:U16\":0}]}";
      //
      // await CtFlutterMatterPlugin.getInstance().writeAttribute(
      //     fabricId,
      //     controllerNodeId,
      //     endpointId,
      //     clusterId,
      //     attributeId,
      //     type,
      //     value,
      //     0
      // );

      log.i('writeValue() -> 结果: $result');
    } catch (e, r) {
      log.e('writeValue() -> 失败: $e, $r');
    }
  }

  Future<void> subscribeAttributes() async {
    log.i('subscribeAttributes() -> 开始测试...');

    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .subscribeAttributes(fabricId, nodeId);
      log.i('subscribeAttributes() -> 订阅结果: $result');
    } catch (e, r) {
      log.e('subscribeAttributes() -> 失败: $e, $r');
    }
  }

  Future<void> subscribeAttributesAndEvents() async {
    log.i('subscribeAttributesAndEvents() -> 开始测试...');
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .subscribeAttributesAndEvents(fabricId, nodeId);
      log.i('subscribeAttributesAndEvents() -> 订阅结果: $result');
    } catch (e, r) {
      log.e('subscribeAttributesAndEvents() -> 失败: $e, $r');
    }
  }

  Future<void> unsubscribes() async {
    log.i('subscribeAttributesAndEvents() -> 开始测试...');
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .unsubscribes(fabricId, nodeId);
      log.i('unsubscribes() -> 解除订阅结果: $result');
    } catch (e, r) {
      log.e('unsubscribes() -> 失败: $e, $r');
    }
  }

  Future<void> writeCustomValue(Map<String, String> map) async {
    log.i('writeCustomValue() -> 开始测试: $map');

    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      String endpointId = map['endpointId']!;
      String clusterId = map['clusterId']!;
      String attributeId = map['attributeId']!;
      String type = map['type']!;
      String value = map['value']!;

      await CtFlutterMatterPlugin.getInstance()
          .writeAttribute(fabricId, nodeId,
          int.parse(endpointId),
          int.parse(clusterId),
          int.parse(attributeId), type, value, 0);
      showSuccessSnackBar('writeCustomValue() -> 写入成功!');
    } catch (e, r) {
      showErrorSnackBar('writeCustomValue() -> 写入失败: $e, $r');
    }
  }

  Future<void> readFabricsAttribute() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readFabricsAttribute(fabricId, nodeId);
      log.i('result=$result');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> getNetworkInterfaces() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      List<Object?> result = await CtFlutterMatterPlugin.getInstance()
          .getNetworkInterfaces(fabricId, nodeId);
      log.i('result=$result');

      Map<Object?, Object?> map = result.first as Map<Object?, Object?>;
      List<int> bytes = map["hardwareAddress"] as List<int>;
      log.i("address字节列表:$bytes");

      //001E5E06B7CC WiFi设备的mac-address
      //0000001E5E06B7CC 补完零后的WiFi设备的mac-address
      //4831B7090CC35ACE Thread设备的mac-address
      //001E5E090C0DA63C
      String hexString = bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0'))
          .join()
          .padLeft(16, '0')
          .toUpperCase();

      log.i("address十六进制:$hexString");

      String newThingName = Sait85rHelper.formatThingName(
          'SAIT85R-001E5E0B7DA8', 4891, 6683, hexString.toUpperCase());

      log.i('newThingName:$newThingName');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> readAccessControlList() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }
    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readAccessControlList(fabricId, nodeId);
      log.i('result=$result');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> getDeviceBasicInfo() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var deviceInfo = await CtFlutterMatterPlugin.getInstance()
          .getDeviceBasicInfo(fabricId, nodeId, 0);
      log.i('ep0 deviceInfo=$deviceInfo');

      var deviceInfo2 = await CtFlutterMatterPlugin.getInstance()
          .getDeviceBasicInfo(fabricId, nodeId, 1);
      log.i('ep1 deviceInfo=$deviceInfo2');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> writeCatValue() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      String catIdOperate = 'AB120003';
      var result = await CtFlutterMatterPlugin.getInstance()
          .writeCatValue(fabricId, nodeId, catIdOperate);
      log.i('writeCatValue() -> result: $result');
    } catch (e, r) {
      log.e('writeCatValue() -> failed: $e, $r');
    }
  }

  Future<void> removeFabric() async {
    String fabricId = 'FAB417901363657F';
    String nodeId = 'DEDEDEDE86335313';
    try {
      await CtFlutterMatterPlugin.getInstance()
          .removeFabric(fabricId, nodeId)
          .timeout(const Duration(seconds: 15));
      log.e('removeFabric succeeded');
    } catch (e) {
      log.e('removeFabric failed: $e');
    }
  }

  Future<void> cancelCommissioning() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.e('cancelCommissioning() -> mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    int nodeId = 0;
    try {
      await CtFlutterMatterPlugin.getInstance()
          .cancelCommissioning(fabricId, nodeId)
          .timeout(const Duration(seconds: 15));
      log.i('cancelCommissioning succeeded');
    } catch (e) {
      log.e('cancelCommissioning failed: $e');
    }
  }

  Future<void> unpairDevice() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      await CtFlutterMatterPlugin.getInstance()
          .unpairDevice(fabricId, nodeId)
          .timeout(const Duration(seconds: 15));
      log.i('unpairDevice succeeded');
    } catch (e) {
      log.e('unpairDevice failed: $e');
    }
  }

  Future<void> unregisterICDClient() async {
    String fabricId = 'FAB467322344207F';
    String nodeId = 'DEDEDEDE66505423';

    // var decimal = BigInt.from(16059518366669373554);
    // String unregisterNodeId = decimal.toRadixString(16).toUpperCase();
    // log.i('unregisterNodeId: ${unregisterNodeId}');

    // int decimal = -2387225707040178062;
    // BigInt bigInt = BigInt.from(decimal);
    // if (bigInt.isNegative) {
    //   bigInt = bigInt + (BigInt.one << 64);
    // }

    // String unregisterNodeId = bigInt.toRadixString(16).toUpperCase();
    // log.i('unregisterNodeId=${unregisterNodeId}');

    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .unregisterICDClient(fabricId, nodeId, unregisterNodeId);
    //   log.i('unregisterICDClient succeeded: $result');
    // } catch (e) {
    //   log.e('unregisterICDClient failed: $e');
    // }
  }

  Future<void> readRegisteredClients() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    try {
      var result = await CtFlutterMatterPlugin.getInstance()
          .readRegisteredClients(fabricId, nodeId);
      log.i('readRegisteredClients succeeded: $result');

    } catch (e) {
      log.e('readRegisteredClients failed: $e');
    }
  }

  Future<void> threadOperationalCredentialsTest() async {
    // try {
    //   String fabricId = 'FAB351158600258F';
    //   String borderRouterId = 'DEDEDEDE16145003';
    //   var dataset = await CtFlutterMatterPlugin.getInstance()
    //       .readThreadBorderRouterDataset(fabricId, borderRouterId);
    //   log.i('读取成功: $dataset');
    // } catch (e, r) {
    //   log.e('读取失败: $e, $r');
    // }


    // try {
    //   String fabricId = 'FAB351158600258F';
    //   String borderRouterId = 'DEDEDEDE16145003';
    //   var dataset = await CtFlutterMatterPlugin.getInstance()
    //       .readAttribute(fabricId, borderRouterId, 0, 0x131BFC02, 0x0);
    //   log.i('读取成功，dataset=$dataset');
    // } catch (e, r) {
    //   log.e('读取失败: $e, $r');
    // }

    // String thingName = 'SAIT85R-001E5E0B7F58';
    // String mcThingName = Sait85rHelper.getMBRCThingName(thingName);
    //
    // AppShadow mcShadow = DeviceShadowService.to.getDeviceShadow(mcThingName);
    // Sait85rMC sait85rMC = Sait85rMC.fromJson(mcShadow.thingShadow, mcThingName);
    //
    // String agentId;
    // if (PlatformUtils.isIOS) {
    //   agentId = sait85rMC.shadow.state?.reported?.model?.properties?.sMcTlr?.thdExtendedAddress ?? "";
    // } else {
    //   agentId = sait85rMC.shadow.state?.reported?.model?.properties?.sMcTlr?.thdBaId ?? "";
    // }
    //
    // String dataset = sait85rMC.shadow.state?.reported?.model?.properties?.sMcTlr?.thdDataset ?? "";
    //
    // debugPrint('mcThingName: $mcThingName');
    // debugPrint('agentId: $agentId');
    // debugPrint('dataset: $dataset');

    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .isPreferredCredentials(dataset);
    //   log.i('succeeded: $result');
    // } catch (e) {
    //   log.e('failed: $e');
    // }


    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .fetchPreferredThreadCredentials();
    //   log.i('succeeded: $result');
    // } catch (e) {
    //   log.e('failed: $e');
    // }


    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .fetchAllActiveThreadCredentials();
    //   log.i('succeeded: $result');
    // } catch (e) {
    //   log.e('failed: $e');
    // }


    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .fetchAllThreadCredentials();
    //   log.i('succeeded: $result');
    // } catch (e) {
    //   log.e('failed: $e');
    // }


    //
    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .deleteCredentials(agentId);
    //   log.i('succeeded: $result');
    // } catch (e) {
    //   log.e('failed: $e');
    // }


    // try {
    //   var result = await CtFlutterMatterPlugin.getInstance()
    //       .saveThreadOperationalCredentials(agentId, dataset);
    //   log.i('succeeded: $result');
    // } catch (e) {
    //   log.e('failed: $e');
    // }

    try {
      String dataset = '0e080000000000010000000300001935060004001fffe00208479be3c3132ca0d10708fdb15657977a03e80510a331e5dcfb835b1819e9da6a9f5640ee030f4f70656e5468726561642d3864366501028d6e041036d5b73965245b28f3847dca4959eeeb0c0402a0f7f8';
      await DeviceShadowService.to.updateDeviceProperties(
          thingName: 'SAIT85R-001E5E0B7F58-MCTLR-0000001E5E0B7F58',
          property: {
            'ep0:sMCtlr:sThdDataset': dataset
          },
          subId: Sait85rMC.subId
      );
      log.i('addReceiver() -> 设置Dataset成功');
    } catch (e, r) {
      log.e('addReceiver() -> 设置Dataset失败: $e, $r');
    }
  }

  Future<void> otherMatterTest() async {
    String? thingGroup = await LocalStorageService.to.getThingGroup();
    if (thingGroup == null || thingGroup.isEmpty) {
      return;
    }

    String mcThingName = Sait85rHelper.getMBRCThingName(thingGroup);
    log.i('mcThingName: $mcThingName');

    String? fabricId = await FabricHelper.getFabricId(mcThingName);
    log.i('fabricId: $fabricId');
    if (fabricId == null || fabricId.isEmpty) {
      return;
    }

    String? nodeId = await LocalStorageService.to.getNodeId();
    log.i('nodeId: $nodeId');
    if (nodeId == null || nodeId.isEmpty) {
      return;
    }

    // final fabricIndex = await CtFlutterMatterPlugin.getInstance().getFabricIndex(fabricId, nodeId);
    // log.i('fabricIndex=$fabricIndex');
    //
    // final compressedFabricId = await CtFlutterMatterPlugin.getInstance().getCompressedFabricId(fabricId);
    // log.i('compressedFabricId=$compressedFabricId');
    //
    // final controllerNodeId = await CtFlutterMatterPlugin.getInstance().getControllerNodeId(fabricId);
    // log.i('controllerNodeId=$controllerNodeId');
    //
    // final controllerPtr = await CtFlutterMatterPlugin.getInstance().getDeviceControllerPtr(fabricId);
    // log.i('controllerPtr=$controllerPtr');
    //
    // final isRunning = await CtFlutterMatterPlugin.getInstance().isRunning(fabricId);
    // log.i('isRunning=$isRunning');
    //
    // final deviceState = await CtFlutterMatterPlugin.getInstance().getDeviceState(fabricId, nodeId);
    // log.i('deviceState=$deviceState');

    final stopDevicePairing = await CtFlutterMatterPlugin.getInstance().stopDevicePairing(fabricId, nodeId);
    log.i('stopDevicePairing=$stopDevicePairing');

    // await CtFlutterMatterPlugin.getInstance().startDnssd(fabricId);
    // await CtFlutterMatterPlugin.getInstance().stopDnssd(fabricId);

    // await CtFlutterMatterPlugin.getInstance()
    //     .loadAllCertificates(true);

    // int discriminator = await CtFlutterMatterPlugin.getInstance()
    //     .generateRandomDiscriminator();
    // int setupPasscode = await CtFlutterMatterPlugin.getInstance()
    //     .generateRandomSetupPasscode();
    // log.i('discriminator=$discriminator, setupPasscode=$setupPasscode');
  }

  Future<void> getHomeByID() async {
    try {
      String homeId = '57360fe2-157a-431d-bc12-a70159cbf534';
      var result = await DevService.to.getHomeByID(homeId);
      log.i('getHomeByID succeeded: ${result.toJson()}');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> getHomeByDeviceID() async {
    try {
      String deviceId = 'SAIT85R-001E5E0B7F58';
      var result = await DevService.to.getHomeByDeviceID(deviceId);
      log.i('getHomeByDeviceID succeeded: ${result.toJson()}');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> addUserToHome() async {
    try {
      final session = await AuthService.to.fetchCognitoAuthSession();
      final userId = session.identityIdResult.value;
      log.i('userId: $userId');

      // String userId = 'eu-central-1:31280ebc-c291-cdf7-27d5-27282156d853';

      String homeId = '57360fe2-157a-431d-bc12-a70159cbf534';
      List<String> userIdList = [userId];
      bool isOwner = true;
      await DevService.to.addUserToHome(homeId, userIdList, isOwner);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> removeUserFromHome() async {
    try {
      final session = await AuthService.to.fetchCognitoAuthSession();
      final userId = session.identityIdResult.value;
      log.i('userId: $userId');

      // String userId = 'eu-central-1:31280ebc-c291-cdf7-27d5-27282156d853';

      String homeId = '00f1fe7c-aad6-4455-84f1-d25cf68c6166';
      List<String> userIdList = [userId];
      bool isOwner = true;
      await DevService.to.removeUserFromHome(homeId, userIdList, isOwner);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> deleteHome() async {
    try {
      var homeIdArray = [
        "00f1fe7c-aad6-4455-84f1-d25cf68c6166",
      ];
      for (var homeId in homeIdArray) {
        await DevService.to.deleteHome(homeId);
      }
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> addHome() async {
    try {
      var result = await DevService.to.addHome('SZHome');
      bool isAddSuccess = result.success ?? false;
      if (isAddSuccess) {
        log.i('addHome() -> succeeded');
        try {
          await DevService.to.addPropertyToHome(result.homeId!,
              'address', 'ppppp');
          log.i('addPropertyToHome() -> succeeded');
        } catch (e) {
          log.e('addPropertyToHome() -> failed: $e');
        }
      } else {
        log.e('addHome() -> failed');
      }
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> addDeviceToHome() async {
    try {
      var homeId = "27e37d37-c887-46a6-8661-6b3be49e8b81";
      var deviceId = "SAIT85R-001E5E0B8018";
      await DevService.to.addDeviceToHome(homeId, deviceId);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> removeDeviceFromHome() async {
    try {
      var homeId = "00f1fe7c-aad6-4455-84f1-d25cf68c6166";
      var deviceId = "SAIT85R-001E5E0B7F68";
      await DevService.to.removeDeviceFromHome(homeId, deviceId);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> addPropertyToHome() async {
    try {
      // var homeId = "d1a6d10c-4e64-4382-b05d-2fc73c4e0f4e";
      var homeId = "2f66b1aa-8830-4824-810a-d2c5af1e84a8";
      var name = "address";
      // var value = "香港特别行政区铜锣湾";
      var value = "深圳经济特区丹竹头";
      await DevService.to.addPropertyToHome(homeId, name, value);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> removePropertyFromHome() async {
    try {
      var homeId = "d1a6d10c-4e64-4382-b05d-2fc73c4e0f4e";
      // var homeId = "2f66b1aa-8830-4824-810a-d2c5af1e84a8";
      var name = "street";
      await DevService.to.removePropertyFromHome(homeId, name);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> changeHomeName() async {
    try {
      var homeId = "7ee895a6-3b44-4474-99b9-2ed463b3b743";
      var name = "Vacation Home";
      await DevService.to.changeHomeName(homeId, name);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> getRoomByID() async {
    try {
      String roomId = '3becc420-9aa5-4a44-a7d9-aae0a622973e';
      final result = await DevService.to.getRoomByID(roomId);
      log.i('getRoomByID succeeded: ${result.toJson()}');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> deleteRoom() async {
    try {
      var roomIdArray = [
        "b37d8ba7-e71d-4bd0-9cb5-3ce311e72abe",
      ];
      for (var roomId in roomIdArray) {
        await DevService.to.deleteRoom(roomId);
      }
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> addRoom() async {
    try {
      var homeId = "6142e60b-5c69-4fc9-a477-fe93abe9d590";
      var result = await DevService.to.addRoom(homeId, 'TestRoom2');
      log.e('addRoom succeeded: ${result.toJson()}');
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> deleteAllEmptyRooms() async {
    try {
      final response = await DevService.to
          .getUserHomes()
          .timeout(const Duration(seconds: 60));

      if (response.success ?? false) {
        final homes = response.homes ?? [];

        if (homes.isNotEmpty) {
          for (var home in homes) {
            if (home.homeId != null) {
              try {
                final response = await DevService.to
                    .getHomeRooms(home.homeId!)
                    .timeout(const Duration(seconds: 60));

                if (response.success ?? false) {
                  final rooms = response.rooms ?? [];

                  await Future.wait(
                    rooms.map((room) async {
                      final name = room.name;
                      final id = room.roomId ?? '';
                      final deviceList = room.deviceList ?? [];

                      if (id.isNotEmpty && deviceList.isEmpty) {
                        log.i(
                          'deleteAllEmptyRooms() -> deleting room: $name, $id',
                        );
                        await DevService.to.deleteRoom(id);
                      }
                    }),
                  );
                }
              } catch (e) {
                log.e('deleteAllEmptyRooms() -> failed: $e');
              }
            }
          }
        }
      }
    } catch (e) {
      log.e('失败: $e');
    }
  }

  Future<void> addDeviceToRoom() async {
    try {
      var roomId = "9cc347ad-bab8-4052-bc5d-5750337bab10";
      var deviceIdArray = [
        "SAIT85R-001E5E0B7F68-1527_0001-001E5E090C0D9C12",
        "SAIT85R-001E5E0B7F68-FFF1_8000-001E5E090C0B805A",
      ];
      for(int i = 0; i < deviceIdArray.length; i++) {
        await DevService.to.addDeviceToRoom(roomId, deviceIdArray[i]);
      }
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> removeDeviceFromRoom() async {
    try {
      var roomId = "9cc347ad-bab8-4052-bc5d-5750337bab10";
      var deviceIdArray = [
        "SAIT85R-001E5E0B7F68-FFF1_8000-001E5E090C0B805A",
      ];
      for(int i = 0; i < deviceIdArray.length; i++) {
        await DevService.to.removeDeviceFromRoom(roomId, deviceIdArray[i]);
      }
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> addPropertyToRoom() async {
    try {
      var roomId = "014ac784-77b3-4858-8515-d7db8f4509f0";
      // var name = "testName";
      // var value = "testValue";
      var name = "testName2";
      var value = "testValue2";
      await DevService.to.addPropertyToRoom(roomId, name, value);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> removePropertyFromRoom() async {
    try {
      var roomId = "014ac784-77b3-4858-8515-d7db8f4509f0";
      var name = "testName";
      await DevService.to.removePropertyFromRoom(roomId, name);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> changeRoomName() async {
    try {
      var roomId = "f0ea8e71-f9b5-460e-9d26-05e4a7ef8b42";
      var name = "Living Room";
      await DevService.to.changeRoomName(roomId, name);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> getUserHomesAndRooms() async {
    try {
      var result = await DevService.to.getUserHomes();
      result.homes?.forEach((home) async {
        await DevService.to.getHomeRooms(home.homeId!);
      });
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> listHomes() async {
    try {
      var result = await DevService.to.listHomes(200);
      // result.homes?.forEach((home) async {
      //   await DevService.to.getHomeRooms(home.homeId!);
      // });

    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }

  Future<void> listRooms() async {
    try {
      var homeId = "586c234a-8f20-464c-b8ef-dc038440063f";
      var result = await DevService.to.listRooms(200, homeId);
    } catch (e, r) {
      log.e('失败: $e, $r');
    }
  }



}
