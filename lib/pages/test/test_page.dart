import 'dart:math';
import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:flutter/material.dart';
import 'package:gaimon/gaimon.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/helpers/snackbar_helper.dart';
import 'package:habi_app/pages/add_device/add_device_bindings.dart';
import 'package:habi_app/pages/add_device/add_device_page.dart';
import 'package:habi_app/pages/add_home/add_home_bindings.dart';
import 'package:habi_app/pages/add_home/add_home_page.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_bindings.dart';
import 'package:habi_app/pages/add_receiver/add_receiver_page.dart';
import 'package:habi_app/pages/device_type/receiver/add_receiver_settings/add_receiver_settings_bindings.dart';
import 'package:habi_app/pages/device_type/receiver/add_receiver_settings/add_receiver_settings_page.dart';
import 'package:habi_app/pages/device_type/receiver/save_or_edit_schedule/save_or_edit_hot_water_schedule_bindings.dart';
import 'package:habi_app/pages/device_type/receiver/save_or_edit_schedule/save_or_edit_hot_water_schedule_page.dart';
import 'package:habi_app/pages/device_type/thermostat/add_thermostat_settings/add_thermostat_settings_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/add_thermostat_settings/add_thermostat_settings_page.dart';
import 'package:habi_app/pages/device_type/thermostat/control/thermostat_control_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/control/thermostat_control_page.dart';
import 'package:habi_app/pages/device_type/thermostat/save_or_edit_schedule/save_or_edit_it850_schedule_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/save_or_edit_schedule/save_or_edit_it850_schedule_page.dart';
import 'package:habi_app/pages/device_type/thermostat/setting/thermostat_settings_bindings.dart';
import 'package:habi_app/pages/device_type/thermostat/setting/thermostat_settings_page.dart';
import 'package:habi_app/pages/home_management/home_management_controller.dart';
import 'package:habi_app/pages/reset_password/reset_password_bindings.dart';
import 'package:habi_app/pages/reset_password/reset_password_page.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/widgets/thermostat_slider.dart';
import 'package:habi_app/widgets/write_value_widget.dart';
import 'test_controller.dart';

class TestPage extends GetView<TestController> {

  WriteValueWidget? writeValueWidget;
  Map<String, String>? writeMap;

  TestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text('Test Page'.tr),
      ),
      body: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(18.0),
          child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Text('mDNS测试 (Only Android)', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    HomeManagementController hc = Get.find<HomeManagementController>();
                    hc.startDnssd();
                  },
                  child: const Text(
                    'startDnssd',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    HomeManagementController hc = Get.find<HomeManagementController>();
                    hc.stopDnssd();
                  },
                  child: const Text(
                    'stopDnssd',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('跳转测试', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    Get.to(() => ResetPasswordPage(email: '<EMAIL>', confirmationCode: '',),
                        binding: ResetPasswordBindings()
                    );
                  },
                  child: const Text(
                    '跳转到Reset-Password界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => AddHomePage(), binding: AddHomeBindings());
                  },
                  child: const Text(
                    '跳转到Add-Home界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => AddReceiverPage(), binding: AddReceiverBindings());
                  },
                  child: const Text(
                    '跳转到add-receiver界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => AddDevicePage(), binding: AddDeviceBindings(
                      thingName: 'SAIT85R-001E5E0B7DA4',
                    ));
                  },
                  child: const Text(
                    '跳转到add-device界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => AddReceiverSettingsPage(),
                        binding: AddReceiverSettingsBindings(
                          thingName: 'SAIT85R-001E5E0B7F58',
                        ));
                  },
                  child: const Text(
                    '跳转到AddReceiverSettingsPage界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => AddThermostatSettingsPage(),
                        binding: AddThermostatSettingsBindings(
                          gwThingName: 'SAIT85R-001E5E0B7F58',
                          thingName: 'SAIT85R-001E5E0B7F58-1527_0001-001E5E090C0DA63C',
                        ));
                  },
                  child: const Text(
                    '跳转到AddThermostatSettings界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => ThermostatSettingsPage(),
                        binding: ThermostatSettingsBindings(
                          homeId: '',
                          roomId: '',
                          thingName: 'SAIT85R-001E5E0B7F68-FFF1_8000-001E5E090C0B8091',
                        ));
                  },
                  child: const Text(
                    '跳转到ThermostatSettings界面',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    Get.to(() => ThermostatControlPage(),
                        binding: ThermostatControlBindings(
                          homeId: '',
                          roomId: '',
                          thingName: 'SAIT85R-001E5E0B7F68-FFF1_8000-001E5E090C0B8095',
                        ));
                  },
                  child: const Text(
                    '跳转到ThermostatControlPage界面',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('其他测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.helperTest();
                  },
                  child: const Text(
                    'Helper Test',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    if(await Gaimon.canSupportsHaptic) {
                      print('支持');
                      Gaimon.selection();
                      // Gaimon.success();
                      // Gaimon.error();
                    } else {
                      print('不支持');
                    }
                  },
                  child: const Text(
                    '触觉反馈测试',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.sentryTest();
                  },
                  child: const Text(
                    'Sentry 测试',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.otaTest();
                  },
                  child: const Text(
                    'OTA 测试',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('MQTT测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.mqttConnect();
                  },
                  child: const Text(
                    'MQTT Connect',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.mqttDisconnect();
                  },
                  child: const Text(
                    'MQTT Disconnect',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.subscribeThing();
                  },
                  child: const Text(
                    'MQTT Subscribe Thing',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('Datetime测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.datetimeTest();
                  },
                  child: const Text(
                    'datetimeTest',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.currentDatetimeTest();
                  },
                  child: const Text(
                    'currentDatetimeTest',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('DeviceProvision测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.createUserRecord();
                  },
                  child: const Text(
                    'createUserRecord',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addControllerToUser();
                  },
                  child: const Text(
                    'addControllerToUser',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.recoverControllerToUser();
                  },
                  child: const Text(
                    'recoverControllerToUser',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removeControllerFromUser();
                  },
                  child: const Text(
                    'removeControllerFromUser',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removeUserRecord();
                  },
                  child: const Text(
                    'removeUserRecord',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.shareDeviceByOwner();
                  },
                  child: const Text(
                    'shareDeviceByOwner',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removeShareDeviceByOwner();
                  },
                  child: const Text(
                    'removeShareDeviceByOwner',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('AWS测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.fetchUserToDeviceList();
                  },
                  child: const Text(
                    'fetchUserToDeviceList',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.listThingGroups();
                  },
                  child: const Text(
                    'listThingGroups',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.listThingsInThingGroup();
                  },
                  child: const Text(
                    'listThingsInThingGroup',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getThingShadow();
                  },
                  child: const Text(
                    'getThingShadow',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.fetchControllerFabricProperty();
                  },
                  child: const Text(
                    'fetchControllerFabricProperty',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.updateUserAttributes();
                  },
                  child: const Text(
                    'updateUserAttributes',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.fetchUserAttributes();
                  },
                  child: const Text(
                    'fetchUserAttributes',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.publishProperty();
                  },
                  child: const Text(
                    'publishProperty',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.publishNewNodeID();
                  },
                  child: const Text(
                    'publishNewNodeID',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.cognitoTest();
                  },
                  child: const Text(
                    'Cognito Test',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.amplifyTest();
                  },
                  child: const Text(
                    'Amplify Test',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.reAmplifyTest();
                  },
                  child: const Text(
                    'ReAmplify Test',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('Database 测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.exportDatabase();
                  },
                  child: const Text(
                    'export database',
                  ),
                ),
                const SizedBox(height: 50,),
                const Text('LocalStorage 测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.readAllLocalData();
                  },
                  child: const Text(
                    'readAllLocalData',
                  ),
                ),
                const SizedBox(height: 10,),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.writeLocalData();
                  },
                  child: const Text(
                    'writeLocalData',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.eraseAllData();
                  },
                  child: const Text(
                    'eraseAllData',
                  ),
                ),

                const SizedBox(height: 50),
                const Text('Matter测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getControllerNodeId();
                  },
                  child: const Text(
                    'getControllerNodeId',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getRootCa();
                  },
                  child: const Text(
                    'getRootCa',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getUserNoc();
                  },
                  child: const Text(
                    'getUserNoc',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.createMatterClient();
                  },
                  child: const Text(
                    'createMatterClient',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.shutdownMatterClient();
                  },
                  child: const Text(
                    'shutdownMatterClient',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addMatterOverWiFiDevice();
                  },
                  child: const Text(
                    'addMatterOverWiFiDevice',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addThreadDeviceWithTBRDataset();
                  },
                  child: const Text(
                    'addThreadDeviceWithTBRDataset',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.readFabricsAttribute();
                  },
                  child: const Text(
                    'readFabricsAttribute',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.readAccessControlList();
                  },
                  child: const Text(
                    'readAccessControlList',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getNetworkInterfaces();
                  },
                  child: const Text(
                    'getNetworkInterfaces',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getDeviceBasicInfo();
                  },
                  child: const Text(
                    'getDeviceBasicInfo',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.writeCatValue();
                  },
                  child: const Text(
                    'writeCatValue',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.onOffControl();
                  },
                  child: const Text(
                    'OnOff',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.readPairingWindowStatus();
                  },
                  child: const Text(
                    'readPairingWindowStatus',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.readValue();
                  },
                  child: const Text(
                    'readValue',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.writeValue();
                  },
                  child: const Text(
                    'writeValue',
                  ),
                ),
                const SizedBox(height: 10),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.subscribeAttributes();
                  },
                  child: const Text(
                    'subscribeAttributes',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.subscribeAttributesAndEvents();
                  },
                  child: const Text(
                    'subscribeAttributesAndEvents',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.unsubscribes();
                  },
                  child: const Text(
                    'unsubscribes',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    String? nodeId = await LocalStorageService.to.getNodeId();
                    if (nodeId != null && nodeId.isNotEmpty) {
                      writeMap = {
                        'nodeId': nodeId,
                      };
                    }

                    WriteValueWidget widget = WriteValueWidget(writeMap: writeMap);

                    var result = await showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) {
                          return widget;
                        }
                    );
                    if(result != null) {
                      writeMap = result;
                      controller.writeCustomValue(result);
                    }
                  },
                  child: const Text(
                    'write Custom Value',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    controller.removeFabric();
                  },
                  child: const Text(
                    'removeFabric',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    controller.cancelCommissioning();
                  },
                  child: const Text(
                    'cancelCommissioning',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () async {
                    controller.unpairDevice();
                  },
                  child: const Text(
                    'unpairDevice',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.unregisterICDClient();
                  },
                  child: const Text(
                    'unregisterICDClient',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.readRegisteredClients();
                  },
                  child: const Text(
                    'readRegisteredClients',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.threadOperationalCredentialsTest();
                  },
                  child: const Text(
                    'threadOperationalCredentials Test',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.otherMatterTest();
                  },
                  child: const Text(
                    'other Matter Test',
                  ),
                ),
                const SizedBox(height: 50),
                const Text('API Home测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getHomeByID();
                  },
                  child: const Text(
                    'API-getHomeByID',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getHomeByDeviceID();
                  },
                  child: const Text(
                    'API-getHomeByDeviceID',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addUserToHome();
                  },
                  child: const Text(
                    'API-addUserToHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removeUserFromHome();
                  },
                  child: const Text(
                    'API-removeUserFromHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.deleteHome();
                  },
                  child: const Text(
                    'API-deleteHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addHome();
                  },
                  child: const Text(
                    'API-addHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addDeviceToHome();
                  },
                  child: const Text(
                    'API-addDeviceToHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removeDeviceFromHome();
                  },
                  child: const Text(
                    'API-removeDeviceFromHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addPropertyToHome();
                  },
                  child: const Text(
                    'API-addPropertyToHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removePropertyFromHome();
                  },
                  child: const Text(
                    'API-removePropertyFromHome',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.changeHomeName();
                  },
                  child: const Text(
                    'API-changeHomeName',
                  ),
                ),
                const SizedBox(height: 50),
                const Text('API Room测试区', style: TextStyle(fontSize: 20, color: Colors.red)),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getRoomByID();
                  },
                  child: const Text(
                    'API-getRoomByID',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.deleteRoom();
                  },
                  child: const Text(
                    'API-deleteRoom',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addRoom();
                  },
                  child: const Text(
                    'API-addRoom',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addDeviceToRoom();
                  },
                  child: const Text(
                    'API-addDeviceToRoom',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removeDeviceFromRoom();
                  },
                  child: const Text(
                    'API-removeDeviceFromRoom',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.addPropertyToRoom();
                  },
                  child: const Text(
                    'API-addPropertyToRoom',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.removePropertyFromRoom();
                  },
                  child: const Text(
                    'API-removePropertyFromRoom',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.changeRoomName();
                  },
                  child: const Text(
                    'API-changeRoomName',
                  ),
                ),
                const SizedBox(height: 50),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.getUserHomesAndRooms();
                  },
                  child: const Text(
                    'API-getUserHomesAndRooms',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.listHomes();
                  },
                  child: const Text(
                    'API-listHomes',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.listRooms();
                  },
                  child: const Text(
                    'API-listRooms',
                  ),
                ),
                const SizedBox(height: 10),
                MaterialButton(
                  color: AppColors.ff01A796,
                  textColor: Colors.white,
                  onPressed: () {
                    controller.deleteAllEmptyRooms();
                  },
                  child: const Text(
                    'API-deleteAllEmptyRooms',
                  ),
                ),

                const SizedBox(height: 100),

              ]
          ),
        ),
      ),
    );
  }



}



