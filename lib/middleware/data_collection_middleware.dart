import 'package:flutter/material.dart';
import 'package:get/get_navigation/src/routes/route_middleware.dart';
import 'package:habi_app/routes/app_routes.dart';
import 'package:habi_app/services/user_attributes_service.dart';

class DataCollectionMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    if (UserAttributesService.to.isDataCollectionOn) {
      return null;
    }

    return RouteSettings(
      name: Routes.dataCollection,
      arguments: {
        'redirectFrom': route,
      },
    );
  }
}
