import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AppTranslations extends Translations {

  Map<String, Map<String, String>> translations = {};

  @override
  Map<String, Map<String, String>> get keys => translations;

  Future<void> load() async {
    const String defaultLanguage = 'en';
    await loadFromAssets(defaultLanguage);
  }

  Future<void> loadFromAssets(String language) async {
    String jsonString = await rootBundle.loadString('assets/translations/$language.json');
    Map<String, dynamic> languageMap = json.decode(jsonString);
    Map<String, String> resultMap = {};
    languageMap.forEach((key, value) {
      resultMap[key] = value as String;
    });
    translations[language] = resultMap;
  }

}
