import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/dev/hb_device_versions_response.dart';
import 'package:habi_app/models/dev/hb_update_device_response.dart';
import 'package:habi_app/models/dev/hb_version.dart';
import 'package:habi_app/services/dev_service.dart';

class DeviceOTAHelper {
  static const String tag = 'DeviceOTAHelper';
  final _devService = DevService.to;

  Future<bool> checkAndUpdate(String deviceId, HbVersion currentVersion) async {
    log.i('$tag, deviceId: $deviceId, currentVersion: $currentVersion');
    try {
      HbDeviceVersionsResponse versionsResponse = await _devService.getDeviceVersions();
      if (!(versionsResponse.success ?? false)) {
        log.e('$tag, error: ${versionsResponse.errorCode}');
        return false;
      }

      List<HbVersion> serverVersions = versionsResponse.versions ?? [];
      HbVersion? serverVersion = _findServerVersion(serverVersions, currentVersion);

      if (serverVersion == null) {
        log.e('$tag, error: no server version');
        return false;
      }

      if (_isUpdateNeeded(currentVersion, serverVersion)) {
        log.i('$tag, need to update');
        return updateDevice(deviceId);
      } else {
        log.i('$tag, no need to update');
        return false;
      }
    } catch (e, s) {
      log.e('$tag, error: $e, stack: $s');
      return false;
    }
  }

  HbVersion? _findServerVersion(List<HbVersion> versions, HbVersion currentVersion) {
    for (HbVersion version in versions) {
      if (version.model == currentVersion.model) {
        return version;
      }
    }
    return null;
  }

  bool _isUpdateNeeded(HbVersion currentVersion, HbVersion serverVersion) {
    int result = currentVersion.version.compareTo(serverVersion.version);
    if (result < 0) {
      log.i('$tag, 小于');
      return true;
    } else if (result > 0) {
      log.i('$tag, 大于');
      return false;
    } else {
      log.i('$tag, 等于');
      return false;
    }
  }

  Future<bool> updateDevice(String deviceId) async {
    try {
      HBUpdateDeviceResponse updateResponse = await _devService.updateDevice([deviceId]);
      if (!(updateResponse.success ?? false)) {
        log.e('$tag, error: ${updateResponse.errorCode}');
        return false;
      }

      Map<String, dynamic> otaStatusMap = updateResponse.otaStatus ?? {};
      if (otaStatusMap.isEmpty) {
        log.e('$tag, error: empty otaStatus');
        return false;
      }

      Map<String, dynamic> deviceMap = otaStatusMap['device'] ?? {};
      if (deviceMap.isEmpty) {
        log.e('$tag, error: empty deviceMap');
        return false;
      }

      String status = deviceMap[deviceId] ?? '';
      log.i('$tag, status: $status');

      String latestVersionStr = 'already using the latest version';
      if (latestVersionStr == status) {
        return false;
      }

      return true;
    } catch (e, s) {
      log.e('$tag, error: $e, stack: $s');
      return false;
    }
  }
}
