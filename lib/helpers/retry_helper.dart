import 'package:habi_app/logger/custom_logger.dart';

class RetryHelper {
  static Future<T> execute<T>({
    required Future<T> Function() operation,
    String operationName = '',
    int maxRetries = 3,
    int delayMs = 1000,
  }) async {
    int attempts = 0;
    while (true) {
      try {
        attempts++;
        return await operation();
      } catch (e) {
        if (attempts > maxRetries) {
          log.e('$operationName失败，已达到最大重试次数 $maxRetries 次', error: e);
          rethrow;
        }
        log.w(
          '$operationName失败，正在进行第 $attempts 次重试 (共 $maxRetries 次)',
          error: e,
        );
        await Future.delayed(Duration(milliseconds: delayMs));
      }
    }
  }
}
