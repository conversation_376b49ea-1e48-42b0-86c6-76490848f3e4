import 'dart:math';

class AppNumberHelper {
  //更加灵活的四舍五入方法
  //如果你需要对小数点后的特定位数进行四舍五入
  //double value = 3.14159;
  //double roundedValue = roundDouble(value, 2);
  //print(roundedValue);
  //输出: 3.14
  static double roundDouble(double value, int places) {
    double mod = pow(10.0, places).toDouble();
    return ((value * mod).round().toDouble() / mod);
  }

  // 将输入的 value 四舍五入到最近的 0.5 的倍数。
  // 具体实现是将 value 乘以 2，然后取整（四舍五入），再除以 2。
  // print(roundToHalf(1.3)); // 输出: 1.5
  // print(roundToHalf(2.7)); // 输出: 2.5
  // print(roundToHalf(3.0)); // 输出: 3.0
  static double roundToHalf(double value) {
    return (value * 2).round() / 2;
  }

  // 将输入的 value 四舍五入到指定步长 to 的倍数。
  // 默认步长为 0.5，但可以通过参数 to 自定义步长。
  // 如果步长 to 为 0，则直接返回原始值 value。
  // print(roundValueTo(1.3)); // 输出: 1.5 （默认步长 0.5）
  // print(roundValueTo(2.7, to: 1.0)); // 输出: 3.0 （步长为 1.0）
  // print(roundValueTo(3.0, to: 2.0)); // 输出: 4.0 （步长为 2.0）
  // print(roundValueTo(3.0, to: 0)); // 输出: 3.0 （步长为 0，直接返回原值）
  static double roundValueTo(double value, {double to = 0.5}) {
    if (to == 0) {
      return value;
    }
    return (value / to).round() * to;
  }

  static double degreeCtoFConversion(double value) {
    return roundValueTo((value * 9.0 / 5.0) + 32.0, to: 1);
  }

  static double degreeFtoCConversion(double value, {double to = 0.5}) {
    return roundValueTo((value - 32) * 5.0 / 9.0, to: to);
  }

}