class RegExpHelper {

  RegExpHelper._();

  static const List<String> specialSymbol = [
    "^", "\$", "*", ".", "[", "]", "{", "}", "(", ")", "?", "”", "!", "@", "#",
    "%", "&", "/", "\\", ",", ">", "<", "'", ":", ";", "|", "_", "~", "`", "=",
    '+', "-"
  ];

  static const String userPassword = r"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#/$+%&*~,._\-\^()=\[\]{}|:;'?<>`]).{8,}$";
  static const String usPhoneNumber = r'^(\s)*((\([2-9][0-9]{2}\))|([2-9][0-9]{2}))([-\.\s]?)([0-9]{3})([-\.\s]?)([0-9]{4,50})(\s)*(?:[\-\.\ \\\/]?(?:#|ext\.?|extension|x)[\-\.\ \\\/]?(\d+))?$';
  static const String euPhoneNumber = r'^(\s)*([1-9][0-9]{5,55})(?:[\-\.\ \\\/]?(?:#|ext\.?|extension|x)[\-\.\ \\\/]?(\d+))?$';
  static const String scheduleValidation = r'^[1-9]\d*(\.\d+)?$';
  static const String wordsRegex = r'^[a-zA-Z ]+$';
}