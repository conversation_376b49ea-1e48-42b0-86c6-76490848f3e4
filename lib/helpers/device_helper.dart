import 'package:habi_app/base/base_device.dart';
import 'package:habi_app/constants/app_device_models.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/device/it850.dart';
import 'package:habi_app/models/device/sait85r_gw.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/models/device/unsupported_model.dart';
import 'package:habi_app/utility/thing_name_utils.dart';

class DeviceHelper {
  static BaseDevice createDeviceModel({
    required String thingName,
    required Map<String, dynamic> shadow,
  }) {
    try {
      final model = ThingNameUtils.extractDeviceModel(thingName);

      switch (model) {
        case AppDeviceModels.kSAIT85R:
          return Sait85rGW.fromJson(shadow, thingName);
        case AppDeviceModels.kMCTLR:
          return Sait85rMC.from<PERSON><PERSON>(shadow, thingName);
        case AppDeviceModels.kFFF18000:
        case AppDeviceModels.k15270001:
        case AppDeviceModels.k131B1A1B:
        case AppDeviceModels.k10780001:
        case AppDeviceModels.kFFF28001:
        case AppDeviceModels.k15270002:
          return IT850.fromJson(shadow, thingName);
        default:
          return UnsupportedModel(thingName: thingName);
      }
    } catch (e) {
      log.e(
        'Failed to create device model - thingName: $thingName; shadow: $shadow',
        error: e,
      );
      return UnsupportedModel(thingName: thingName);
    }
  }
}
