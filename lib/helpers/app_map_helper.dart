class AppMapHelper {
  AppMapHelper._();

  static bool contains<PERSON><PERSON><PERSON><PERSON>(Map map, String key) {
    if (map.contains<PERSON><PERSON>(key)) {
      return true;
    }

    for (var value in map.values) {
      if (value is Map) {
        if (contains<PERSON><PERSON><PERSON><PERSON>(value, key)) {
          return true;
        }
      }
    }

    return false;
  }

  static dynamic getDeepValue<PERSON><PERSON><PERSON>ey(Map map, String key) {
    if (map.contains<PERSON><PERSON>(key)) {
      return map[key];
    }

    for (var value in map.values) {
      if (value is Map) {
        var result = getDeepValueFor<PERSON><PERSON>(value, key);
        if (result != null) {
          return result;
        }
      }
    }

    return null;
  }

  static Map<String, dynamic> deepMerge(Map<String, dynamic> map1,
      Map<String, dynamic> map2) {
    Map<String, dynamic> result = {};

    void merge(Map<String, dynamic> target, Map<String, dynamic> source) {
      source.forEach((key, value) {
        if (target.containsKey(key)) {
          if (target[key] is Map<String, dynamic> &&
              value is Map<String, dynamic>) {
            merge(target[key], value);
          } else {
            target[key] = value;
          }
        } else {
          target[key] = value;
        }
      });
    }

    merge(result, map1);
    merge(result, map2);

    return result;
  }

}