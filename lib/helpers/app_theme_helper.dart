import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

ThemeData appLightTheme() {
  return ThemeData(
    brightness: Brightness.light,
    primaryColor: AppColors.ff01A796,
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: const AppBarTheme(
      centerTitle: true,
      surfaceTintColor: Colors.white,
      backgroundColor: Colors.white,
    ),
    textTheme: ThemeData.light().textTheme.apply(
      fontFamily: 'Poppins',
    ),
    radioTheme: ThemeData.light().radioTheme.copyWith(
      overlayColor: WidgetStatePropertyAll(AppColors.ff01A796),
      fillColor: WidgetStatePropertyAll(AppColors.ff01A796),
    ),
    switchTheme: ThemeData.light().switchTheme.copyWith(
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return AppColors.ff01A796;
        }
        return Colors.grey;
      }),
      thumbColor: WidgetStatePropertyAll(Colors.white),
      trackOutlineColor: WidgetStatePropertyAll(Colors.transparent),
      trackOutlineWidth: WidgetStatePropertyAll(0.0),
    ),
    primaryTextTheme: ThemeData.light().textTheme.apply(
      fontFamily: 'Poppins',
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: AppColors.ff01A796),
        overlayColor: AppColors.ff01A796
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: AppColors.ff01A796,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.ff01A796,
      ),
    ),
    textSelectionTheme: ThemeData.light().textSelectionTheme.copyWith(
      selectionColor: AppColors.ff01A796,
      cursorColor: AppColors.ff01A796,
    ),
    colorScheme: const ColorScheme.light().copyWith(
      // primary: AppColors.lightPrimary,
    ),
    dropdownMenuTheme: DropdownMenuThemeData(
      textStyle: const TextStyle(
        fontFamily: 'Poppins',
        color: AppColors.ff1C1C1C,
        fontSize: 14,
        fontWeight: AppFontWeights.regular,
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ff01A796),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ff01A796),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ff01A796)
        )
      )
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: const BorderSide(color: AppColors.ff01A796)
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: const BorderSide(color: AppColors.ff01A796)
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: const BorderSide(color: AppColors.ff01A796)
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: const BorderSide(color: AppColors.ffFF4E4E)
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24),
        borderSide: const BorderSide(color: AppColors.ffFF4E4E)
      ),
      errorStyle: const TextStyle(
        color: AppColors.ffFF4E4E,
      ),
    ),
    bottomNavigationBarTheme: ThemeData.light().bottomNavigationBarTheme.copyWith(
      elevation: 0,
      backgroundColor: AppColors.ffE7F9F7,
      selectedItemColor: AppColors.ff1C1C1C,
      unselectedItemColor: AppColors.ff868788,
      // showSelectedLabels: true,
      // showUnselectedLabels: true,
      type: BottomNavigationBarType.fixed,
      // elevation: 0,
      selectedIconTheme: const IconThemeData(
        color: AppColors.ff01A796,
      ),
      unselectedIconTheme: const IconThemeData(
        color: AppColors.ff1C1C1C,
      )
    ),
    sliderTheme: ThemeData.light().sliderTheme.copyWith(
      activeTrackColor: AppColors.ff01A796,
      inactiveTrackColor: AppColors.ffE7F9F7,
      activeTickMarkColor: AppColors.ff01A796,
      thumbColor: Colors.white,
      thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 20, elevation: 5.0),
      trackHeight: 21,
    ),
    checkboxTheme: ThemeData.light().checkboxTheme.copyWith(
      checkColor: const WidgetStatePropertyAll(Colors.white),
      overlayColor: const WidgetStatePropertyAll(AppColors.ff01A796),
      side: WidgetStateBorderSide.resolveWith(
            (Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return const BorderSide(color: AppColors.ff01A796, width: 2);
          }
          return const BorderSide(color: AppColors.ff01A796, width: 1);
        },
      ),
    ),
    extensions: const <ThemeExtension<dynamic>>[
      AppThemeExtension(
          firstColor: AppColors.ff1C1C1C,
          secondColor: Colors.white,
          thirdColor: AppColors.ffFaFaFa,
          fourthColor: AppColors.ff1C1C1C,
      )
    ]
  );
}

ThemeData appDarkTheme() {
  return ThemeData(
    brightness: Brightness.dark,
    primaryColor: AppColors.ff333333,
    scaffoldBackgroundColor: AppColors.ff1C1C1C,
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        surfaceTintColor: Colors.white,
        backgroundColor: AppColors.ff1C1C1C,
      ),
      textTheme: ThemeData.light().textTheme.apply(
        fontFamily: 'Poppins',
      ),
      radioTheme: ThemeData.light().radioTheme.copyWith(
        overlayColor: WidgetStatePropertyAll(AppColors.ff01A796),
        fillColor: WidgetStatePropertyAll(AppColors.ff01A796),
      ),
      switchTheme: ThemeData.light().switchTheme.copyWith(
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.ff01A796;
          }
          return Colors.grey;
        }),
        thumbColor: WidgetStatePropertyAll(Colors.black),
        trackOutlineColor: WidgetStatePropertyAll(Colors.transparent),
        trackOutlineWidth: WidgetStatePropertyAll(0.0),
      ),
      primaryTextTheme: ThemeData.light().textTheme.apply(
        fontFamily: 'Poppins',
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: AppColors.ff01A796,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.ff01A796,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: AppColors.ff01A796),
          overlayColor: AppColors.ff01A796
        ),
      ),
      textSelectionTheme: ThemeData.light().textSelectionTheme.copyWith(
        selectionColor: AppColors.ff01A796,
        cursorColor: AppColors.ff01A796,
      ),
      colorScheme: const ColorScheme.dark().copyWith(
        // primary: AppColors.darkPrimary,
      ),
      dropdownMenuTheme: DropdownMenuThemeData(
          textStyle: const TextStyle(
            fontFamily: 'Poppins',
            color: Colors.white,
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
          ),
          inputDecorationTheme: InputDecorationTheme(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24),
                borderSide: const BorderSide(color: AppColors.ff01A796),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24),
                borderSide: const BorderSide(color: AppColors.ff01A796),
              ),
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: const BorderSide(color: AppColors.ff01A796)
              )
          )
      ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ff01A796)
      ),
      enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ff01A796)
      ),
      focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ff01A796)
      ),
      focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ffFF4E4E)
      ),
      errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(color: AppColors.ffFF4E4E)
      ),
      errorStyle: const TextStyle(
        color: AppColors.ffFF4E4E,
      ),
    ),
      bottomNavigationBarTheme: ThemeData.light().bottomNavigationBarTheme.copyWith(
        elevation: 0,
        backgroundColor: AppColors.ff333333,
        selectedItemColor: AppColors.ffF2FFFe,
        unselectedItemColor: AppColors.ff868788,
        // showSelectedLabels: true,
        // showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        // elevation: 0,
        selectedIconTheme: const IconThemeData(
          color: AppColors.ff01A796,
        ),
        unselectedIconTheme: const IconThemeData(
          color: AppColors.ffF2FFFe,
        )
      ),
      sliderTheme: ThemeData.light().sliderTheme.copyWith(
        activeTrackColor: AppColors.ff01A796,
        inactiveTrackColor: AppColors.ffE7F9F7,
        activeTickMarkColor: AppColors.ff01A796,
        thumbColor: Colors.white,
        thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 20, elevation: 5.0),
        trackHeight: 21,
      ),
      checkboxTheme: ThemeData.light().checkboxTheme.copyWith(
        checkColor: const WidgetStatePropertyAll(Colors.black),
        overlayColor: const WidgetStatePropertyAll(AppColors.ff01A796),
        side: WidgetStateBorderSide.resolveWith(
              (Set<WidgetState> states) {
            if (states.contains(WidgetState.selected)) {
              return const BorderSide(color: AppColors.ff01A796, width: 2);
            }
            return const BorderSide(color: AppColors.ff01A796, width: 1);
          },
        ),
      ),
      extensions: const <ThemeExtension<dynamic>>[
        AppThemeExtension(
            firstColor: Colors.white,
            secondColor: AppColors.ff1C1C1C,
            thirdColor: AppColors.ff333333,
            fourthColor: AppColors.ffF2FFFe,
        )
      ]
  );
}
