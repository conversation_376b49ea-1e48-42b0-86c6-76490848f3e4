import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/auth_service.dart';

class AwsCognitoHelper {
  static Future<bool> isAdminUser() async {
    try {
      final session = await AuthService.to.fetchCognitoAuthSession();
      final claimsMap = session.userPoolTokensResult.value.accessToken.claims.toJson();
      final groups = claimsMap['cognito:groups'];
      log.d("isAdminUser() -> groups=$groups");
      if (groups != null && groups is List) {
        return groups.contains('Admin-Habi');
      }
      return false;
    } catch (e) {
      log.e("isAdminUser() -> e=$e");
      return false;
    }
  }
}