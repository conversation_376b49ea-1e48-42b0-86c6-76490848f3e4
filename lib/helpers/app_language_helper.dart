import 'package:habi_app/models/app_language.dart';

class AppLanguageHelper {
  AppLanguageHelper._();

  static List<AppLanguage> getLanguageList() {
    List<AppLanguage> languageList = [
      AppLanguage(name: 'English', code: 'en'),
      AppLanguage(name: 'Bulgarian', code: 'bg'),
      AppLanguage(name: 'Czech', code: 'cs'),
      AppLanguage(name: 'Danish', code: 'da'),
      AppLanguage(name: 'Dutch', code: 'nl'),
      AppLanguage(name: 'Estonian', code: 'et'),
      AppLanguage(name: 'Finnish', code: 'fi'),
      AppLanguage(name: 'French', code: 'fr'),
      AppLanguage(name: 'German', code: 'de'),
      AppLanguage(name: 'Greek', code: 'el'),
      AppLanguage(name: 'Latvian', code: 'lv'),
      AppLanguage(name: 'Norwegian', code: 'no'),
      AppLanguage(name: 'Polish', code: 'pl'),
      AppLanguage(name: 'Romanian', code: 'ro'),
      AppLanguage(name: 'Russian', code: 'ru'),
      AppLanguage(name: 'Serbian', code: 'sr'),
      AppLanguage(name: 'Spanish', code: 'es'),
      AppLanguage(name: 'Swedish', code: 'sv'),
      AppLanguage(name: 'Ukrainian', code: 'uk'),
    ];
    return languageList;
  }
}