import 'package:ct_flutter_matter_plugin/ct_flutter_matter_plugin.dart';
import 'package:flutter/services.dart';
import 'package:habi_app/extensions/sait85r_mc_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_shadow.dart';
import 'package:habi_app/models/device/sait85r_mc.dart';
import 'package:habi_app/models/dynamo_db/controller_fabric_property.dart';
import 'package:habi_app/services/device_shadow_service.dart';
import 'package:habi_app/services/dynamo_db_service.dart';
import 'package:habi_app/services/local_storage_service.dart';

class FabricHelper {
  static Future<String?> getFabricId(String mcThingName) async {
    log.i('getFabricId() -> mcThingName=$mcThingName');

    AppShadow mcShadow = DeviceShadowService.to.getDeviceShadow(mcThingName);
    Sait85rMC sait85rMC = Sait85rMC.fromJson(mcShadow.thingShadow, mcThingName);
    String? fabricId = sait85rMC.getFabId();
    if (fabricId != null && fabricId.isNotEmpty) {
      log.i('getFabricId() -> fabricId=$fabricId');
      return fabricId;
    }

    Map<String, dynamic>? allFabricMap = await LocalStorageService.to.getFabricMap();
    log.i('getFabricId() -> allFabricMap=$allFabricMap');
    if (allFabricMap != null && allFabricMap.isNotEmpty) {
      Map<String, dynamic>? fabricMap = allFabricMap[mcThingName];
      log.i('getFabricId() -> fabricMap=$fabricMap');
      if (fabricMap != null && fabricMap.isNotEmpty) {
        fabricId = fabricMap['fabricId'] as String;
        log.i('getFabricId() -> fabricId=$fabricId');
      }
    }

    return fabricId;
  }

  static Future<bool> checkFabric(Sait85rMC sait85rMC, String mcThingName) async {
    log.i('checkFabric() -> mcThingName: $mcThingName');

    String mcFabricId = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabID ?? "";
    String mcRootCa = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabRcAc ?? "";
    String mcIpk = sait85rMC.shadow?.state?.reported?.model?.properties?.sMcTlr?.fabIPK ?? "";

    Map<String, dynamic>? allFabricMap = await LocalStorageService.to.getFabricMap();

    if (allFabricMap == null) {
      log.e('checkFabric() -> allFabricMap is null');
      String rootCAArn = await fetchRootCAArn(mcThingName);
      if (rootCAArn.isEmpty) {
        log.e('checkFabric() -> rootCAArn is empty');
        return false;
      }
      String userNoc = await fetchUserNoc(mcFabricId, rootCAArn);
      if (userNoc.isEmpty) {
        log.e('checkFabric() -> userNoc is empty');
        return false;
      }
      Map<String, dynamic> fabricMap = {
        "fabricId": mcFabricId,
        "rootCa": mcRootCa,
        "ipk": mcIpk,
        "userNoc": userNoc,
        "rootCertArn": rootCAArn,
      };

      Map<String, dynamic> allFabricMap = <String, dynamic>{};
      allFabricMap[mcThingName] = fabricMap;
      await LocalStorageService.to.setFabricMap(allFabricMap);
      return await createMatterClient(mcThingName, fabricMap);
    }

    Map<String, dynamic>? fabricMap = allFabricMap[mcThingName];
    if (fabricMap == null) {
      log.e('checkFabric() -> localFabricMap is null');
      String rootCAArn = await fetchRootCAArn(mcThingName);
      if (rootCAArn.isEmpty) {
        log.e('checkFabric() -> rootCAArn is empty');
        return false;
      }
      String userNoc = await fetchUserNoc(mcFabricId, rootCAArn);
      if (userNoc.isEmpty) {
        log.e('checkFabric() -> userNoc is empty');
        return false;
      }
      Map<String, dynamic> fabricMap = {
        "fabricId": mcFabricId,
        "rootCa": mcRootCa,
        "ipk": mcIpk,
        "userNoc": userNoc,
        "rootCertArn": rootCAArn,
      };

      allFabricMap[mcThingName] = fabricMap;
      await LocalStorageService.to.setFabricMap(allFabricMap);
      return await createMatterClient(mcThingName, fabricMap);
    }

    String lsFabricId = fabricMap["fabricId"] as String;
    if (lsFabricId != mcFabricId) {
      log.e('checkFabric() -> lsFabricId != mcFabricId');
      String rootCAArn = await fetchRootCAArn(mcThingName);
      if (rootCAArn.isEmpty) {
        log.e('checkFabric() -> rootCAArn is empty');
        return false;
      }
      String userNoc = await fetchUserNoc(mcFabricId, rootCAArn);
      if (userNoc.isEmpty) {
        log.e('checkFabric() -> userNoc is empty');
        return false;
      }
      Map<String, dynamic> fabricMap = {
        "fabricId": mcFabricId,
        "rootCa": mcRootCa,
        "ipk": mcIpk,
        "userNoc": userNoc,
        "rootCertArn": rootCAArn,
      };

      allFabricMap[mcThingName] = fabricMap;
      await LocalStorageService.to.setFabricMap(allFabricMap);
      return createMatterClient(mcThingName, fabricMap);
    }

    log.i('checkFabric() -> use local fabric.');
    return await createMatterClient(mcThingName, fabricMap);
  }

  static Future<bool> createMatterClient(String mcThingName, Map<String, dynamic> fabricMap) async {
    log.i('createMatterClient() -> mcThingName: $mcThingName');
    log.i('createMatterClient() -> fabricMap: $fabricMap');

    String fabricId = fabricMap["fabricId"] as String;
    String rootCa = fabricMap["rootCa"] as String;
    String ipk = fabricMap["ipk"] as String;
    String userNoc = fabricMap["userNoc"] as String;
    String rootCAArn = fabricMap["rootCertArn"] as String;

    try {
      bool isSuccess = await CtFlutterMatterPlugin.getInstance()
          .createMatterClient(
        fabricId: fabricId,
        ipk: ipk,
        rootCa: rootCa,
        userNoc: userNoc,
        rootCAArn: rootCAArn
      );
      if (isSuccess) {
        log.i('createMatterClient() -> 创建MatterClient成功!');
      } else {
        log.e('createMatterClient() -> 创建MatterClient失败!');
      }
      return isSuccess;
    } catch (e) {
      log.e('createMatterClient() -> 创建MatterClient失败: $e');

      if (e is PlatformException) {
        int errorCode = 0;
        try {
          errorCode = int.parse(e.code);
        } catch (e) {
          log.e('createMatterClient() -> parse errorCode failed: $e');
        }

        log.i('createMatterClient() -> 解析出的错误码是：$errorCode');

        if (errorCode == -1) {
          log.i('createMatterClient() -> 准备重新创建MatterClient...');
          log.i('createMatterClient() -> 重新获取user-noc...');
          userNoc = await fetchUserNoc(fabricId, rootCAArn);
          if (userNoc.isEmpty) {
            log.e('createMatterClient() -> 重新获取user-noc失败');
            return false;
          }

          try {
            fabricMap["userNoc"] = userNoc;
            Map<String, dynamic> allFabricMap = await LocalStorageService.to.getFabricMap() ?? {};
            allFabricMap[mcThingName] = fabricMap;
            await LocalStorageService.to.setFabricMap(allFabricMap);

            bool isSuccess = await CtFlutterMatterPlugin.getInstance()
                .createMatterClient(
                fabricId: fabricId,
                ipk: ipk,
                rootCa: rootCa,
                userNoc: userNoc,
                rootCAArn: rootCAArn
            );
            if (isSuccess) {
              log.i('createMatterClient() -> 重新创建MatterClient成功!');
            } else {
              log.e('createMatterClient() -> 重新创建MatterClient失败!');
            }
            return isSuccess;
          } catch (e) {
            log.e('createMatterClient() -> 重新创建MatterClient失败: $e');
            return false;
          }
        }
      }

      return false;
    }
  }

  static Future<String> fetchRootCAArn(String mcThingName) async {
    String rootCAArn = "";
    try {
      log.i('fetchRootCAArn() -> 开始获取rootCAArn...');
      String deviceId = mcThingName;
      ControllerFabricProperty? property = await DynamoDBService
          .to.fetchControllerFabricProperty(deviceId);
      if (property != null) {
        rootCAArn = property.rootCertArn ?? "";
      }
      log.i('fetchRootCAArn() -> 获取rootCAArn成功: $property');
    } catch (e) {
      log.e('fetchRootCAArn() -> 获取rootCAArn失败: $e');
    }
    return rootCAArn;
  }

  static Future<String> fetchUserNoc(String fabricId, String rootCAArn) async {
    String userNoc = "";
    try {
      log.i('fetchUserNoc() -> 开始获取UserNoc...');
      userNoc = await CtFlutterMatterPlugin.getInstance()
          .getUserNoc(fabricId: fabricId, rootCAArn: rootCAArn);
      log.i('fetchUserNoc() -> 获取UserNoc成功: $userNoc');
    } catch (e) {
      log.e('fetchUserNoc() -> 获取UserNoc失败: $e');
    }
    return userNoc;
  }
}