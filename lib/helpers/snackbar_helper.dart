import 'package:flutter/material.dart';
import 'package:get/get.dart';

void showSnackBar(String message) {
  Get.showSnackbar(
    GetSnackBar(
      margin: const EdgeInsets.all(16),
      borderRadius: 40,
      message: message,
      duration: const Duration(seconds: 3),
    ),
  );
}

void showSuccessSnackBar(String message) {
  Get.showSnackbar(
    GetSnackBar(
      margin: const EdgeInsets.all(16),
      borderRadius: 40,
      message: message,
      duration: const Duration(seconds: 3),
      backgroundColor: Colors.green,
    ),
  );
}

void showErrorSnackBar(String message) {
  Get.showSnackbar(
    GetSnackBar(
      margin: const EdgeInsets.all(16),
      borderRadius: 40,
      message: message,
      duration: const Duration(seconds: 3),
      backgroundColor: Colors.red,
    ),
  );
}


