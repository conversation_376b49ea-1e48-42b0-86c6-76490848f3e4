import 'package:habi_app/models/app_country.dart';

class AppCountryHelper {
  AppCountryHelper._();

  static List<AppCountry> getCountryList() {
    List<AppCountry> countryList = [
      AppCountry('al', 'Albania', '+355'),
      AppCountry('au', 'Australia', '+61'),
      App<PERSON>ountry('at', 'Austria', '+43'),
      AppCountry('az', 'Azerbaijan', '+994'),
      AppCountry('by', 'Belarus', '+375'),
      AppCountry('be', 'Belgium', '+32'),
      App<PERSON>ountry('ba', 'Bosna and Herz', '+387'),
      App<PERSON>ountry('bg', 'Bulgaria', '+359'),
      App<PERSON>ountry('cn', 'China', '+86'),
      App<PERSON>ountry('hr', 'Croatia', '+385'),
      App<PERSON>ountry('cy', 'Cyprus', '+357'),
      App<PERSON>ountry('cz', 'Czech Republic', '+420'),
      App<PERSON>ountry('dk', 'Denmark', '+45'),
      App<PERSON>ount<PERSON>('ee', 'Estonia', '+372'),
      AppCountry('fo', 'Faroe Island', '+298'),
      AppCountry('fi', 'Finland', '+358'),
      AppCountry('fr', 'France', '+33'),
      AppCountry('ge', 'Georgia', '+995'),
      AppCountry('gl', 'Greenland', '+299'),
      AppCountry('de', 'Germany', '+49'),
      AppCountry('gr', 'Greece', '+30'),
      AppCountry('hu', 'Hungary', '+36'),
      AppCountry('is', 'Iceland', '+354'),
      AppCountry('in', 'India', '+91'),
      AppCountry('ie', 'Ireland', '+353'),
      AppCountry('it', 'Italy', '+39'),
      AppCountry('kz', 'Kazakhstan', '+7'),
      AppCountry('xk', 'Kosovo', '+383'),
      AppCountry('lv', 'Latvia', '+371'),
      AppCountry('lt', 'Lithuania', '+370'),
      AppCountry('mk', 'Macedonia', '+389'),
      AppCountry('me', 'Montenegro', '+382'),
      AppCountry('nz', 'New Zealand', '+64'),
      AppCountry('nl', 'Netherlands', '+31'),
      AppCountry('no', 'Norway', '+47'),
      AppCountry('pt', 'Portugal', '+351'),
      AppCountry('pl', 'Poland', '+48'),
      AppCountry('ro', 'Romania', '+40'),
      AppCountry('ru', 'Russia', '+7'),
      AppCountry('rs', 'Serbia', '+381'),
      AppCountry('sk', 'Slovakia', '+421'),
      AppCountry('si', 'Slovenia', '+386'),
      AppCountry('es', 'Spain', '+34'),
      AppCountry('se', 'Sweden', '+46'),
      AppCountry('ch', 'Switzerland', '+41'),
      AppCountry('ua', 'Ukraine', '+380'),
      AppCountry('gb', 'United Kingdom', '+44'),
      AppCountry('us', 'United States', '+1'),
      AppCountry('uy', 'Uruguay', '+598'),
    ];
    return countryList;
  }
}