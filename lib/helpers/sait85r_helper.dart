import 'package:habi_app/logger/custom_logger.dart';

class Sait85rHelper {

  // Matter Receiver Gateway
  static String getMRGThingName(String macAddress) {
    return 'SAIT85R-$macAddress';
  }

  // Matter Boiler Receiver Controller
  static String getMBRCThingName(String thingName, {String? macAddress}) {
    if (macAddress == null) {
      List<String> parts = thingName.split("-");
      String partAddress = parts[1];
      return '$thingName-MCTLR-0000$partAddress';
    }
    return '$thingName-MCTLR-0000$macAddress';
  }

  static String getThingGroupName(String thingName) {
    List<String> parts = thingName.split("-");
    String partAddress = parts[1];
    return 'Gateway-$partAddress';
  }

  static String getScanGroupName(String thingName) {
    try {
      List<String> parts = thingName.split("-");
      String address = parts[1];
      String partAddress = address.substring(address.length - 4, address.length);
      return 'SAIT85R_$partAddress';
    } catch (e) {
      log.i('getScanGroupName() -> e: $e');
      return '';
    }
  }

  static String getScanGroupNameByMacAddress(String address) {
    try {
      String partAddress = address.substring(address.length - 4, address.length);
      return 'SAIT85R_$partAddress';
    } catch (e) {
      log.i('getScanGroupNameByMacAddress() -> e: $e');
      return '';
    }
  }

  static Map<String, String> parseGWThingName(String thingName) {
    try {
      List<String> parts = thingName.split(RegExp(r'-'));
      if (parts.length > 1) {
        String name = parts[0];
        String macAddress = parts[1];
        return {
          'name': name,
          'macAddress': macAddress,
        };
      }
    } catch (e) {
      log.i('parseGWThingName() -> e: $e');
    }
    return {};
  }

  static Map<String, String> parseThingName(String thingName) {
    try {
      List<String> parts = thingName.split(RegExp(r'[-_]'));
      if (parts.length > 4) {
        String gwThingName = '${parts[0]}-${parts[1]}';
        String vid = parts[2];
        String pid = parts[3];
        String macAddress = parts[4];
        return {
          'gwThingName': gwThingName,
          'vid': vid,
          'pid': pid,
          'macAddress': macAddress,
        };
      }
    } catch (e) {
      log.i('parseThingName() -> e: $e');
    }
    return {};
  }

  static String formatThingName(String gwThingName, int vid, int pid, String deviceMacAddress) {
    String newVid = vid.toRadixString(16).toUpperCase();
    String newPid = pid.toRadixString(16).padLeft(4, '0').toUpperCase();
    return '$gwThingName-${newVid}_${newPid}-$deviceMacAddress';
  }

}