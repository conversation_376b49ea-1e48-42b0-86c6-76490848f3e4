import 'package:get/get.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/models/app_home.dart';
import 'package:habi_app/models/app_room.dart';
import 'package:habi_app/models/dev/hb_get_room_by_id_response.dart';
import 'package:habi_app/models/dev/hb_home.dart';
import 'package:habi_app/models/dev/hb_room.dart';
import 'package:habi_app/services/auth_service.dart';
import 'package:habi_app/services/dev_service.dart';
import 'package:habi_app/services/device_list_service.dart';

class LoadHomeHelper {

  final DeviceListService _deviceListService = DeviceListService.to;
  final DevService _devService = DevService.to;

  Future<AppHome?> load(String gwThingName) async {
    try {
      AppHome? appHome = await _findOrCreate(gwThingName);
      return appHome;
    } catch (e, s) {
      log.e('load() -> error: $e, stack: $s');
      rethrow;
    }
  }

  Future<AppHome?> _findOrCreate(String gwThingName) async {
    final response = await _devService.getUserHomes();
    if (!(response.success ?? false)) {
      throw Exception('Failed to fetch homes: ${response.errorCode}');
    }

    final List<HBHome> homes = response.homes ?? [];

    if (homes.isEmpty) {
      log.d('_finaOrCreate() -> homes is empty.');
      final thatHome = await _getHomeByDeviceId(gwThingName);
      if (thatHome != null) {
        log.d('_finaOrCreate() -> found thatHome.');
        return await _addUserToHome(thatHome, gwThingName);
      } else {
        log.d('_finaOrCreate() -> not found thatHome.');
        return await _createDefaultHome(gwThingName);
      }
    } else {
      log.d('_finaOrCreate() -> homes is not empty.');
      final List<AppHome> appHomes = await _processHomes(homes);

      final existingHome = appHomes.firstWhere(
            (home) => home.deviceList?.contains(gwThingName) ?? false,
        orElse: () => AppHome(),
      );

      if (existingHome.homeId != null) {
        log.d('_finaOrCreate() -> found existing home.');
        existingHome.rooms = await _fetchRoomsForHome(existingHome.homeId!,
            gwThingName);
        return existingHome;
      }

      log.d('_finaOrCreate() -> not found existing home.');
      final emptyHome = appHomes.firstWhere(
            (home) => home.deviceList == null || home.deviceList!.isEmpty,
        orElse: () => AppHome(),
      );

      if (emptyHome.homeId != null && emptyHome.homeId!.isNotEmpty) {
        log.d('_finaOrCreate() -> found empty home.');
        return await _addDeviceToHome(gwThingName, emptyHome.homeId!);
      }

      log.d('_finaOrCreate() -> not found empty home.');
      return await _createDefaultHome(gwThingName);
    }
  }

  Future<HBHome?> _getHomeByDeviceId(String gwThingName) async {
    try {
      final homeDeviceIdResponse = await _devService.getHomeByDeviceID(
          gwThingName);
      if (!(homeDeviceIdResponse.success ?? false)) {
        return null;
      }
      return homeDeviceIdResponse.home;
    } catch (e, s) {
      log.e('_getHomeByDeviceId() -> error: $e, stack: $s');
      return null;
    }
  }

  Future<List<AppHome>> _processHomes(List<HBHome> homes) async {
    List<String> thingsGroupList = _deviceListService.gatewayIds;
    log.i('_processHomes() -> thingsGroupList: $thingsGroupList');

    List<AppHome> appHomes = [];

    for (final home in homes) {
      log.i('_processHomes() -> deviceList: ${home.deviceList}');
      if (home.deviceList == null || home.deviceList!.isEmpty) {
        appHomes.add(AppHome.fromHBHome(home));
        continue;
      }

      String? matchedDeviceId;
      for (final deviceId in home.deviceList!) {
        if (thingsGroupList.contains(deviceId)) {
          matchedDeviceId = deviceId;
          break;
        }
      }

      log.i('_processHomes() -> matchedDeviceId: $matchedDeviceId');

      if (matchedDeviceId != null) {
        appHomes.add(AppHome.fromHBHome(home));
      } else {
        _deleteHomeById(home.homeId);
      }
    }

    return appHomes;
  }

  Future<List<AppRoom>> _fetchRoomsForHome(String homeId, String homeDeviceId) async {
    List<String> thingsList =
        _deviceListService.getDevicesByGatewayId(homeDeviceId) ?? [];
    log.i('_fetchRoomsForHome() -> before thingsList: $thingsList');
    thingsList.removeWhere((thing) => thing.contains('MCTLR'));
    log.i('_fetchRoomsForHome() -> after thingsList: $thingsList');

    List<AppRoom> appRooms = [];

    try {
      final roomResponse = await _devService.getHomeRooms(homeId);
      if (!(roomResponse.success ?? false)) {
        return appRooms;
      }

      List<HBRoom>? rooms = roomResponse.rooms;
      if (rooms == null || rooms.isEmpty) {
        log.d('_fetchRoomsForHome() -> rooms is empty.');
        if (thingsList.isNotEmpty) {
          AppRoom? appRoom = await _createDefaultRoom(homeId, thingsList);
          if (appRoom != null) {
            appRooms.add(appRoom);
          }
        }
        return appRooms;
      }

      List<String> restThingsList = [...thingsList];

      for (final room in rooms) {
        log.i('_fetchRoomsForHome() -> deviceList: ${room.deviceList}');

        List<String> newDeviceList = [];

        if (room.deviceList != null && room.deviceList!.isNotEmpty) {
          for (final deviceId in room.deviceList!) {
            if (restThingsList.contains(deviceId)) {
              restThingsList.remove(deviceId);
              newDeviceList.add(deviceId);
            } else {
              _removeDeviceFromRoom(room.roomId!, deviceId);
            }
          }
        }

        log.i('_fetchRoomsForHome() -> newDeviceList: $newDeviceList');

        AppRoom appRoom = AppRoom.fromHBRoom(room);
        appRoom.deviceList = newDeviceList;
        appRooms.add(appRoom);
      }

      log.i('_fetchRoomsForHome() -> restThingsList: $restThingsList');
      if (restThingsList.isNotEmpty) {
        await _handleUnassignedDevices(appRooms, restThingsList);
      }
    } catch (e, s) {
      log.e('_fetchRoomsForHome() -> error: $e, stack: $s');
    }

    return appRooms;
  }

  Future<void> _handleUnassignedDevices(List<AppRoom> appRooms, List<String> thingsList) async {
    if (appRooms.isEmpty || thingsList.isEmpty) {
      return;
    }

    AppRoom targetRoom = appRooms.firstWhere(
          (room) => room.deviceList == null || room.deviceList!.isEmpty,
      orElse: () => appRooms.first,
    );

    List<String> newDeviceList = targetRoom.deviceList ?? [];

    for (final deviceId in thingsList) {
      try {
        log.i('_handleUnassignedDevices() -> start adding device $deviceId to room ${targetRoom.roomId}');
        await _devService.addDeviceToRoom(targetRoom.roomId!, deviceId);
        newDeviceList.add(deviceId);
        log.i('_handleUnassignedDevices() -> added device $deviceId to room ${targetRoom.roomId}');
      } catch (e, s) {
        log.e('_handleUnassignedDevices() -> failed to add device $deviceId: $e\n$s');
      }
    }

    targetRoom.deviceList = newDeviceList;
  }

  Future<AppHome?> _addUserToHome(HBHome home, String gwThingName) async {
    try {
      final session = await AuthService.to.fetchCognitoAuthSession();
      final userId = session.identityIdResult.value;
      await _devService.addUserToHome(
          home.homeId!,
          [userId],
          true // isOwner
      );
      log.i('_addUserToHome() -> succeeded');
      AppHome appHome = AppHome.fromHBHome(home);
      appHome.rooms = await _fetchRoomsForHome(appHome.homeId!, gwThingName);
      return appHome;
    } catch (e, s) {
      log.e('_addUserToHome() -> error: $e, stack: $s');
      return null;
    }
  }

  Future<AppHome?> _createDefaultHome(String gwThingName) async {
    try {
      log.i('createDefaultHome() -> start: $gwThingName');
      final result = await _devService.addHome('vacationHome'.tr);
      bool success = result.success ?? false;
      if (!success) {
        log.e('createDefaultHome() -> create default home failed.');
        return null;
      }

      final homeId = result.homeId ?? '';
      if (homeId.isEmpty) {
        log.e('createDefaultHome() -> homeId is empty.');
        return null;
      }

      await _devService.addDeviceToHome(homeId , gwThingName);

      final getHomeByIdResult = await _devService.getHomeByID(homeId);
      bool getHomeByIdSuccess = getHomeByIdResult.success ?? false;
      if (!getHomeByIdSuccess) {
        log.e('createDefaultHome() -> get home by id failed.');
        return null;
      }

      final hbHome = getHomeByIdResult.home!;
      if (getHomeByIdResult.home == null) {
        log.e('createDefaultHome() -> get home by id is null.');
        return null;
      }

      AppHome appHome = AppHome.fromHBHome(hbHome);
      appHome.rooms = await _fetchRoomsForHome(appHome.homeId!, gwThingName);
      return appHome;
    } catch (e, s) {
      log.e('createDefaultHome() -> error: $e, stack: $s');
      return null;
    }
  }

  Future<AppRoom?> _createDefaultRoom(String homeId, List<String> thingsList) async {
    try {
      final result = await _devService.addRoom(homeId, 'livingRoom'.tr);
      if (!(result.success ?? false)) {
        log.e('_createDefaultRoom() -> create default room failed.');
        return null;
      }

      final roomId = result.roomId ?? '';
      if (roomId.isEmpty) {
        log.e('_createDefaultRoom() -> roomId is empty.');
        return null;
      }

      for (final deviceId in thingsList) {
        try {
          log.i('_addDeviceToRoom() -> deviceId: $deviceId');
          await _devService.addDeviceToRoom(roomId, deviceId);
          log.i('_addDeviceToRoom() -> succeeded');
        } catch (e, s) {
          log.e('_addDeviceToRoom() -> error: $e, stack: $s');
        }
      }

      HbGetRoomByDeviceIdResponse response = await _devService.getRoomByID(roomId);
      if (!(response.success ?? false)) {
        log.e('_getRoomByID() -> get room by id failed.');
        return null;
      }

      if (response.room == null) {
        log.e('_getRoomByID() -> get room by id is null.');
        return null;
      }

      AppRoom appRoom = AppRoom.fromHBRoom(response.room!);
      appRoom.deviceList = thingsList;
      return appRoom;
    } catch (e, s) {
      log.e('_createDefaultRoom() -> error: $e, stack: $s');
      return null;
    }
  }

  Future<AppHome?> _addDeviceToHome(String gwThingName, String homeId) async {
    try {
      await _devService.addDeviceToHome(homeId , gwThingName);

      final getHomeByIdResult = await _devService.getHomeByID(homeId);
      bool getHomeByIdSuccess = getHomeByIdResult.success ?? false;
      if (!getHomeByIdSuccess) {
        log.e('_addDeviceToHome() -> get home by id failed.');
        return null;
      }

      final hbHome = getHomeByIdResult.home!;
      if (getHomeByIdResult.home == null) {
        log.e('_addDeviceToHome() -> get home by id is null.');
        return null;
      }

      AppHome appHome = AppHome.fromHBHome(hbHome);
      appHome.rooms = await _fetchRoomsForHome(appHome.homeId!, gwThingName);
      return appHome;
    } catch (e, s) {
      log.e('_addDeviceToHome() -> error: $e, stack: $s');
      return null;
    }
  }

  Future<void> _deleteHomeById(String? homeId) async {
    if (homeId == null || homeId.isEmpty) {
      log.e('_deleteHomeById() -> home id is empty.');
      return;
    }

    try {
      log.d('_deleteHomeById() -> deleting home: $homeId');
      await _devService.deleteHome(homeId);
      log.d('_deleteHomeById() -> succeeded');
    } catch (e, s) {
      log.e('_deleteHomeById() -> error: $e, stack: $s');
    }
  }

  Future<void> _removeDeviceFromRoom(String roomId, String deviceId) async {
    log.i('_removeDeviceFromRoom() -> roomId: $roomId, deviceId: $deviceId');
    try {
      await _devService.removeDeviceFromRoom(roomId , deviceId);
      log.i('_removeDeviceFromRoom() -> succeeded');
    } catch (e, s) {
      log.e('_removeDeviceFromRoom() -> error: $e, stack: $s');
    }
  }

}
