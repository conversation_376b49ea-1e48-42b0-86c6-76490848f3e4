import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class AppPlugin {

  AppPlugin._() {
    if (kDebugMode) {
      debugPrint("AppPlugin{} -> init()");
    }
  }

  static final AppPlugin _instance = AppPlugin._();
  static AppPlugin getInstance() => _instance;
  static const MethodChannel _methodChannel = MethodChannel('ct_app_plugin');

  Future<String> writeLog() async {
    final path = await _methodChannel.invokeMethod('writeLog');
    return path;
  }
}