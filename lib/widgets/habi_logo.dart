import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';

class Habi<PERSON>ogo extends StatelessWidget {
  final Color color;
  final double? height;
  final double? width;

  const HabiLogo({
    super.key,
    this.height,
    this.width,
    this.color = AppColors.ff01A796,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: SvgPicture.asset(
        AppImagePaths.habiLogoWhite,
        colorFilter: ColorFilter.mode(
          color,
          BlendMode.srcIn,
        ),
      ),
    );
  }
}
