import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class ReceiverRunningModeWidget extends StatelessWidget {

  static const int schedule = 0;
  static const int boost = 1;
  static const int permanentHold = 2;
  bool hotWater = false;
  int runningMode = permanentHold;
  void Function(String)? onSelected;

  ReceiverRunningModeWidget({
    required this.hotWater,
    required this.runningMode,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    Widget turnOnButton;
    Widget turnOffButton;
    Widget scheduleButton;
    Widget boostButton;

    if (hotWater) {
      turnOnButton = _buildButton(
        iconPath: AppImagePaths.hotWaterTurnOn,
        textKey: "turnOn",
        borderColor: Theme.of(context)
            .extension<AppThemeExtension>()!.fourthColor,
        iconColor: Theme.of(context)
            .extension<AppThemeExtension>()!.fourthColor,
        textColor: Theme.of(context)
            .extension<AppThemeExtension>()!.fourthColor,
      );

      turnOffButton = _buildButton(
          iconPath: AppImagePaths.hotWaterTurnOff,
          textKey: "turnOff",
          borderColor: Colors.red,
          iconColor: Colors.red,
          textColor: Colors.red,
          opacity: 1.0
      );

    } else {
      turnOnButton = _buildButton(
          iconPath: AppImagePaths.hotWaterTurnOn,
          textKey: "turnOn",
          borderColor: AppColors.ff01A796,
          iconColor: AppColors.ff01A796,
          textColor: AppColors.ff01A796,
          opacity: 1.0
      );

      turnOffButton = _buildButton(
        iconPath: AppImagePaths.hotWaterTurnOff,
        textKey: "turnOff",
        borderColor: Theme.of(context)
            .extension<AppThemeExtension>()!.fourthColor,
        iconColor: Theme.of(context)
            .extension<AppThemeExtension>()!.fourthColor,
        textColor: Theme.of(context)
            .extension<AppThemeExtension>()!.fourthColor,
      );
    }

    if (runningMode == schedule) {

      if (hotWater) {
        scheduleButton = _buildButton(
            iconPath: AppImagePaths.scheduleMode,
            textKey: "schedule",
            backgroundColor: AppColors.ff01A796,
            borderColor: AppColors.ff01A796,
            iconColor: Colors.white,
            textColor: Colors.white,
            opacity: 1.0
        );
      } else {
        scheduleButton = _buildButton(
          iconPath: AppImagePaths.scheduleMode,
          textKey: "schedule",
          borderColor: Theme.of(context)
              .extension<AppThemeExtension>()!.fourthColor,
          iconColor: Theme.of(context)
              .extension<AppThemeExtension>()!.fourthColor,
          textColor: Theme.of(context)
              .extension<AppThemeExtension>()!.fourthColor,
        );
      }

      boostButton = _buildButton(
          iconPath: AppImagePaths.boostMode,
          textKey: "boost",
          borderColor: AppColors.ff01A796,
          iconColor: AppColors.ff01A796,
          textColor: AppColors.ff01A796,
          opacity: 1.0
      );

    } else if (runningMode == boost) {
      scheduleButton = _buildButton(
          iconPath: AppImagePaths.scheduleMode,
          textKey: "schedule",
          borderColor: AppColors.ff01A796,
          iconColor: AppColors.ff01A796,
          textColor: AppColors.ff01A796,
          opacity: 1.0
      );

      boostButton = _buildButton(
          iconPath: AppImagePaths.boostMode,
          textKey: "boost",
          backgroundColor: AppColors.ff01A796,
          borderColor: AppColors.ff01A796,
          iconColor: Colors.white,
          textColor: Colors.white,
          opacity: 1.0
      );
    } else {
      if (hotWater) {
        scheduleButton = _buildButton(
            iconPath: AppImagePaths.scheduleMode,
            textKey: "schedule",
            borderColor: AppColors.ff01A796,
            iconColor: AppColors.ff01A796,
            textColor: AppColors.ff01A796,
            opacity: 1.0
        );

        boostButton = _buildButton(
            iconPath: AppImagePaths.boostMode,
            textKey: "boost",
            borderColor: AppColors.ff01A796,
            iconColor: AppColors.ff01A796,
            textColor: AppColors.ff01A796,
            opacity: 1.0
        );
      } else {
        scheduleButton = _buildButton(
          iconPath: AppImagePaths.scheduleMode,
          textKey: "schedule",
          borderColor: Theme.of(context)
              .extension<AppThemeExtension>()!.fourthColor,
          iconColor: Theme.of(context)
              .extension<AppThemeExtension>()!.fourthColor,
          textColor: Theme.of(context)
              .extension<AppThemeExtension>()!.fourthColor,
        );

        boostButton = _buildButton(
            iconPath: AppImagePaths.boostMode,
            textKey: "boost",
            borderColor: AppColors.ff01A796,
            iconColor: AppColors.ff01A796,
            textColor: AppColors.ff01A796,
            opacity: 1.0
        );
      }

    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              flex: 1,
              child: turnOnButton,
            ),
            const SizedBox(width: 17),
            Expanded(
              flex: 1,
              child: turnOffButton,
            ),
          ],
        ),
        const SizedBox(height: 17),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: scheduleButton,
            ),
            const SizedBox(width: 17),
            Expanded(
              flex: 1,
              child: boostButton,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildButton({
    required String iconPath,
    required String textKey,
    Color? backgroundColor,
    required Color borderColor,
    required Color iconColor,
    required Color textColor,
    double opacity = 0.5
  }) {
    return GestureDetector(
      onTap: () {
        onSelected?.call(textKey);
      },
      child: Container(
        width: double.infinity,
        height: 34,
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(color: borderColor.withOpacity(opacity)),
          borderRadius: BorderRadius.circular(21),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child: SvgPicture.asset(
                iconPath,
                width: 13,
                height: 13,
                colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
              ),
            ),
            SizedBox(width: 5.w),
            Expanded(
              flex: 3,
              child: Text(
                textKey.tr,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: textColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

}