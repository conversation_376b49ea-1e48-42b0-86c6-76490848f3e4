import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class EnterPairingCodeWidget extends StatefulWidget {

  final Function()? onConfirm;
  final Function()? onCancel;

  const EnterPairingCodeWidget({
    super.key,
    this.onConfirm,
    this.onCancel,
  });

  @override
  State createState() => _EnterPairingCodeWidgetState();
}

class _EnterPairingCodeWidgetState extends State<EnterPairingCodeWidget> {

  final TextEditingController pairingCodeController = TextEditingController(text: '');

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    pairingCodeController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.secondColor,
            borderRadius: const BorderRadius.all(Radius.circular(18.0)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 10,),

              SizedBox(
                width: double.infinity,
                child: Opacity(
                  opacity: 0.5,
                  child: Text(
                    'pairingCode'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!
                          .firstColor,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 14.h,
              ),
              ConstrainedBox(
                constraints:
                const BoxConstraints(maxHeight: 148, minHeight: 48),
                child: TextFormField(
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!
                        .firstColor,
                  ),
                  controller: pairingCodeController,
                  keyboardType: TextInputType.number,
                ),
              ),

              const SizedBox(height: 40,),

              Row(
                children: [
                  Expanded(
                      child: Center(child: _buildConfirmButton())
                  ),
                  Expanded(
                      child: Center(child: _buildCancelButton())
                  ),
                ],
              ),

              const SizedBox(height: 10,),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton() {
    return SizedBox(
        height: 48,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(
              color: Colors.red,
              width: 1,
            ),
          ),
          onPressed: () {
            Get.back(result: pairingCodeController.text);
            widget.onConfirm?.call();
          },
          child: Text(
            'confirm'.tr.toUpperCase(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Colors.red,
            ),
          ),
        ));
  }

  Widget _buildCancelButton() {
    return SizedBox(
      height: 48,
      child: OutlinedButton(
        onPressed: () {
          Get.back();
          widget.onCancel?.call();
        },
        child: Text(
          'cancel'.tr.toUpperCase(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: AppColors.ff01A796,
          ),
        ),
      ),
    );
  }




}
