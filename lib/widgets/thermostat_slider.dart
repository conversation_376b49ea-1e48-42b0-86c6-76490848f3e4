import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/widgets/dial2_widget.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';

class ThermostatSlider extends StatelessWidget {

  final OnChange? onChange;
  final OnChange? onChangeStart;
  final OnChange? onChangeEnd;
  final double size;
  final double initialValue;
  final double min;
  final double max;
  final Color dotColor;
  final Color progressBarColor;
  List<Color>? progressBarColors;

  ThermostatSlider({
    super.key,
    required this.onChange,
    required this.onChangeStart,
    required this.onChangeEnd,
    required this.size,
    required this.initialValue,
    required this.min,
    required this.max,
    required this.dotColor,
    required this.progressBarColor,
    this.progressBarColors,
  });

  @override
  Widget build(BuildContext context) {
    return SleekCircularSlider(
      min: min,
      max: max,
      initialValue: initialValue,
      appearance: CircularSliderAppearance(
          animationEnabled: false,
          size: size,
          angleRange: 132,
          startAngle: 114,
          customWidths: CustomSliderWidths(
            trackWidth: 1.5,
            progressBarWidth: 6.w,
            handlerSize: 12.w,
          ),
          customColors: CustomSliderColors(
            progressBarColor: progressBarColor,
            progressBarColors: progressBarColors,
            dotColor: dotColor,
            trackColor: Theme.of(context)
                .extension<AppThemeExtension>()!
                .firstColor.withOpacity(0.2),
            trackColors: progressBarColors,
            hideShadow: true,
          )
      ),
      onChange: onChange,
      onChangeStart: onChangeStart,
      onChangeEnd: onChangeEnd,
      innerWidget: (double value) {
        // return const SizedBox();
        return Container(
          alignment: Alignment.center,
          child: CustomPaint(
            size: Size(size * 0.9, size * 0.9),
            painter: Dial2Widget(),
          ),
        );
      },
    );
  }

}