import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class ThermostatActionModeWidget extends StatelessWidget {
  final String icon;
  final String label;
  final VoidCallback onTap;
  final bool isConnected;
  Color? iconColor;

  ThermostatActionModeWidget({
    super.key,
    required this.icon,
    required this.label,
    required this.onTap,
    required this.isConnected,
    this.iconColor
  });

  @override
  Widget build(BuildContext context) {

    Color textColor = Theme.of(context)
        .extension<AppThemeExtension>()!.firstColor;
    if (!isConnected) {
      iconColor = Theme.of(context)
          .extension<AppThemeExtension>()!.firstColor;
    }

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          SvgPicture.asset(
            icon,
            width: 35,
            height: 35,
            colorFilter: iconColor != null ? ColorFilter.mode(iconColor!, BlendMode.srcIn) : null,
          ),
          const SizedBox(height: 4),
          Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: textColor,
              )
          ),
        ],
      ),
    );
  }

}