import 'dart:math';
import 'package:flutter/material.dart';

class DialWidget extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final Paint shadowPaint = Paint()
      ..color = Colors.grey.withOpacity(0.5)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);

    final Paint linePaint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 2;

    final double radius = size.width / 2;

    final Offset center = Offset(size.width / 2, size.height / 2);

    // 绘制阴影
    canvas.drawCircle(center, radius, shadowPaint);

    // 绘制白色背景
    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制刻度线
    for (int i = 0; i < 60; i++) {
      final double angle = (i * 6) * 3.14 / 180;
      final Offset start = Offset(
        center.dx + (radius - 15) * 0.9 * cos(angle),
        center.dy + (radius - 15) * 0.9 * sin(angle),
      );
      final Offset end = Offset(
        center.dx + (radius - 15) * cos(angle),
        center.dy + (radius - 15) * sin(angle),
      );
      canvas.drawLine(start, end, linePaint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}