import 'package:flutter/material.dart';

/// 一个显示带有<b></b>标签标记的高亮部分的文本的组件。
///
/// 示例:
/// ```dart
/// HighlightText(
///   text: 'Hello <b>World</b>!',
///   defaultStyle: TextStyle(fontSize: 16),
///   highlightStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
///   textAlign: TextAlign.center,
///   overflow: TextOverflow.ellipsis,
/// )
/// ```
class HighlightText extends StatelessWidget {
  /// 要显示的文本，可以包含<b></b>标签用于高亮显示。
  final String text;

  /// 应用于非高亮文本的样式。
  final TextStyle? defaultStyle;

  /// 应用于高亮文本（<b></b>标签内）的样式。
  final TextStyle? highlightStyle;

  /// 文本的水平对齐方式。
  final TextAlign? textAlign;

  /// 如何处理视觉溢出。
  final TextOverflow? overflow;

  /// 文本跨越的最大行数。
  final int? maxLines;

  const HighlightText({
    super.key,
    required this.text,
    this.defaultStyle,
    this.highlightStyle,
    this.textAlign,
    this.overflow,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    final DefaultTextStyle defaultTextStyle = DefaultTextStyle.of(context);
    final TextStyle effectiveDefaultStyle =
        defaultStyle ?? defaultTextStyle.style;

    return RichText(
      textAlign: textAlign ?? TextAlign.start,
      overflow: overflow ?? TextOverflow.clip,
      maxLines: maxLines,
      text: TextSpan(
        style: effectiveDefaultStyle,
        children: _parseText(text, effectiveDefaultStyle),
      ),
    );
  }

  /// 解析输入文本并返回[TextSpan]列表。
  List<TextSpan> _parseText(String text, TextStyle baseStyle) {
    if (text.isEmpty) return [];

    final List<TextSpan> spans = [];
    int currentIndex = 0;
    final int textLength = text.length;

    while (currentIndex < textLength) {
      // 查找下一个开标签
      final int openingTagIndex = text.indexOf('<b>', currentIndex);

      if (openingTagIndex == -1) {
        // 未找到更多标签，添加剩余文本
        if (currentIndex < textLength) {
          spans.add(TextSpan(
            text: text.substring(currentIndex),
            style: baseStyle,
          ));
        }
        break;
      }

      // 添加标签前的文本（如果有）
      if (openingTagIndex > currentIndex) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, openingTagIndex),
          style: baseStyle,
        ));
      }

      // 查找匹配的闭标签
      final int closingTagIndex = text.indexOf('</b>', openingTagIndex + 3);

      if (closingTagIndex == -1) {
        // 未找到闭标签，将剩余部分视为普通文本
        spans.add(TextSpan(
          text: text.substring(currentIndex),
          style: baseStyle,
        ));
        break;
      }

      // 提取并添加高亮文本
      final String highlightedText =
          text.substring(openingTagIndex + 3, closingTagIndex);
      if (highlightedText.isNotEmpty) {
        spans.add(TextSpan(
          text: highlightedText,
          style:
              highlightStyle ?? baseStyle.copyWith(fontWeight: FontWeight.bold),
        ));
      }

      currentIndex = closingTagIndex + 4; // 移动到闭标签之后
    }

    return spans;
  }
}
