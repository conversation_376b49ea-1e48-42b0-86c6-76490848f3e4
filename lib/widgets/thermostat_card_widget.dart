import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class ThermostatCardWidget extends StatelessWidget {

  final double? width;
  final double? height;
  final String? thermostatName;
  final bool? isChecked;
  final String? roomName;
  final void Function() onPlus;
  final void Function() onSubtract;
  final void Function() onCardTap;

  const ThermostatCardWidget({
    super.key,
    this.width,
    this.height,
    this.thermostatName,
    this.isChecked,
    this.roomName,
    required this.onPlus,
    required this.onSubtract,
    required this.onCardTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onCardTap,
      child: Container(
        width: width ?? double.infinity,
        height: height ?? 194.h,
        padding: EdgeInsets.symmetric(horizontal: 21.w, vertical: 21.h),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            border: Border.all(color: AppColors.ff01A796, width: 1)
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 39.h,
              child: Row(
                children: [
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: Text(
                        thermostatName ?? 'Unknown',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                          ),
                        ),
                    ),
                  ),

                  SizedBox(
                    width: 60,
                    height: 39,
                    child: Switch(
                        value: isChecked ?? false,
                        onChanged: (value){
                    
                        }
                    ),
                  )

                ],
              ),
            ),

            SizedBox(
              height: 23.h,
              child: Opacity(
                opacity: 0.5,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    roomName ?? 'Unknown',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.medium,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                  ),
                ),
              ),
            ),

            SizedBox(
              height: 37.h,
              child: Row(
                children: [
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'tempNow'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(
                    width: 66,
                    height: 37,
                    child: FilledButton(
                      onPressed: onPlus,
                      style: Theme.of(context).filledButtonTheme.style?.copyWith(
                        padding: WidgetStateProperty.all(EdgeInsets.zero)
                      ),
                      child: Text(
                        '+ 0.5'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.secondColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 8.h,),

            SizedBox(
              height: 37.h,
              child: Row(
                children: [
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '22.5',
                        style: TextStyle(
                          fontSize: 46,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                      ),
                    ),
                  ),

                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          '22.5',
                          style: TextStyle(
                            fontSize: 23,
                            fontWeight: AppFontWeights.regular,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!.firstColor,
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(
                    width: 66,
                    height: 37,
                    child: FilledButton(
                      onPressed: onPlus,
                      style: Theme.of(context).filledButtonTheme.style?.copyWith(
                          padding: WidgetStateProperty.all(EdgeInsets.zero)
                      ),
                      child: Text(
                        '- 0.5'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.secondColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          ],
        ),
      ),
    );
  }
}