import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class EnterNameWidget extends StatefulWidget {

  final String? name;

  const EnterNameWidget({super.key, this.name});

  @override
  State<StatefulWidget> createState() {
    return _EnterNameWidgetState();
  }
}

class _EnterNameWidgetState extends State<EnterNameWidget> {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    nameController.text = widget.name ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('pleaseEnterName'.tr,
          style: TextStyle(
              fontSize: 21,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor
          )
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Form(
              key: formKey,
              child: Column(
                children: [
                  SizedBox(
                    height: 14.h,
                  ),
                  ConstrainedBox(
                    constraints:
                        const BoxConstraints(maxHeight: 148, minHeight: 48),
                    child: TextFormField(
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.regular,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!
                            .firstColor,
                      ),
                      controller: nameController,
                      keyboardType: TextInputType.name,
                      validator: validateName,
                    ),
                  ),
                  SizedBox(
                    height: 14.h,
                  ),
                ],
              )
          )
        ],
      ),
      actions: <Widget>[
        TextButton(
          child: Text('cancel'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            Get.back();
          },
        ),
        TextButton(
          child: Text('confirm'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            bool validate = formKey.currentState?.validate() ?? false;
            if (!validate) {
              return;
            }
            Get.back(result: nameController.text);
          },
        ),
      ],
    );
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    return null;
  }

}
