import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class ViewErrorLog extends StatefulWidget {

  final String errorLog;

  ViewErrorLog({
    required this.errorLog,
  });

  @override
  _ViewErrorLogState createState() => _ViewErrorLogState();

}

class _ViewErrorLogState extends State<ViewErrorLog> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ExpansionPanelList(
        expansionCallback: (int index, bool isExpanded) {
          setState(() {
            _isExpanded = !_isExpanded;
          });
        },
        children: [
          ExpansionPanel(
            headerBuilder: (BuildContext context, bool isExpanded) {
              return ListTile(
                title: Text(
                    'View Error Log',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    )
                ),
              );
            },
            body: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                  widget.errorLog,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  )
              ),
            ),
            isExpanded: _isExpanded,
          ),
        ],
      ),
    );
  }
}