import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class SelectDeviceTypeWidget extends StatefulWidget {

  final Function(int) onDeviceTypeSelected;

  const SelectDeviceTypeWidget({super.key, required this.onDeviceTypeSelected});

  @override
  State<StatefulWidget> createState() {
    return _SelectDeviceTypeWidgetState();
  }

}

class _SelectDeviceTypeWidgetState extends State<SelectDeviceTypeWidget> {

  int selectedDeviceType = -1;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: AlertDialog(
        title: Text('pleaseSelectDeviceType'.tr, style: TextStyle(
            fontSize: 21,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor
          )
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            RadioListTile<int>(
              title: Text('matterOverWiFi'.tr, style: TextStyle(
                  fontSize: 14,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor)
              ),
              value: 0,
              groupValue: selectedDeviceType,
              onChanged: (int? value) async {
                setState(() {
                  selectedDeviceType = value!;
                });
                await Future.delayed(const Duration(milliseconds: 500));
                Get.back();
                widget.onDeviceTypeSelected(selectedDeviceType);
              },
            ),
            RadioListTile<int>(
              title: Text('matterOverThread'.tr, style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              )),
              value: 1,
              groupValue: selectedDeviceType,
              onChanged: (int? value) async {
                setState(() {
                  selectedDeviceType = value!;
                });
                await Future.delayed(const Duration(milliseconds: 500));
                Get.back();
                widget.onDeviceTypeSelected(selectedDeviceType);
              },
            ),
          ],
        ),
      ),
    );
  }
}