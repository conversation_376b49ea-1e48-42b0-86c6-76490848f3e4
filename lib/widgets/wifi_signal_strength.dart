import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';

class WifiSignalStrength extends StatelessWidget {
  final int signalStrength;
  final void Function()? onTap;

  WifiSignalStrength({required this.signalStrength, this.onTap});

  @override
  Widget build(BuildContext context) {
    int filledBars = 0;

    if (signalStrength >= -20 && signalStrength <= 0) {
      filledBars = 4;
    } else if (signalStrength > -50 && signalStrength < -20) {
      filledBars = 3;
    } else if (signalStrength > -80 && signalStrength <= -50) {
      filledBars = 2;
    } else if (signalStrength >= -100 && signalStrength <= -80) {
      filledBars = 1;
    }

    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(4, (index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 2.0),
            width: 8,
            height: (index + 1) * 10.0,
            color: index < filledBars ? AppColors.ff01A796 : Colors.grey,
          );
        }),
      ),
    );
  }
}