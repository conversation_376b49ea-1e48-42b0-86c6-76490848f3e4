import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class ChangeReceiverModeWarningWidget extends StatefulWidget {

  final Function() onYes;
  final Function() onCancel;
  final String warningText;

  ChangeReceiverModeWarningWidget({
    super.key,
    required this.onYes,
    required this.onCancel,
    required this.warningText,
  });

  @override
  State createState() => _ChangeReceiverWiFiWidgetState();
}

class _ChangeReceiverWiFiWidgetState extends State<ChangeReceiverModeWarningWidget> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(28.0),
        ),
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.secondColor,
              borderRadius: const BorderRadius.all(Radius.circular(6.0)),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40,),

                Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Text(
                    widget.warningText,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 40,),

                Row(
                  children: [
                    Expanded(
                        child: Center(child: _buildYesButton())
                    ),
                    Expanded(
                        child: Center(child: _buildCancelButton())
                    ),
                  ],
                ),

                const SizedBox(height: 50,),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildYesButton() {
    return SizedBox(
      width: 118,
      height: 48,
      child: OutlinedButton(
        onPressed: () {
          widget.onYes();
        },
        child: Text(
          'yes'.tr.toUpperCase(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: AppColors.ff01A796,
          ),
        ),
      ),
    );
  }

  Widget _buildCancelButton() {
    return SizedBox(
        width: 118,
        height: 48,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(
              color: Colors.red,
              width: 1,
            ),
          ),
          onPressed: () {
            widget.onCancel();
          },
          child: Text(
            'cancel'.tr.toUpperCase(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Colors.red,
            ),
          ),
        ));
  }




}
