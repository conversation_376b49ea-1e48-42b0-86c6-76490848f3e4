import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';

class HabiShadowCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final double shadowBlurRadius;
  final double shadowSpreadRadius;
  final Offset shadowOffset;
  final Color? shadowColor;
  final Color? color;
  final Border? border;

  const HabiShadowCard({
    super.key,
    required this.child,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    this.borderRadius = 30.0,
    this.shadowBlurRadius = 15.0,
    this.shadowSpreadRadius = 0.0,
    this.shadowOffset = const Offset(0.0, 10.0),
    this.shadowColor,
    this.color,
    this.border,
  });

  BoxDecoration _buildBoxDecoration(BuildContext context) {
    return BoxDecoration(
      color: color ?? Theme.of(context).colorScheme.surfaceContainerLow,
      borderRadius: BorderRadius.circular(borderRadius),
      border: border ?? Border.all(color: AppColors.ff01A796),
      boxShadow: [
        BoxShadow(
          color: shadowColor ?? AppColors.ff2F2B1A.withOpacity(0.15),
          blurRadius: shadowBlurRadius,
          spreadRadius: shadowSpreadRadius,
          offset: shadowOffset,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: _buildBoxDecoration(context),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(
          padding: padding,
          child: child,
        ),
      ),
    );
  }
}
