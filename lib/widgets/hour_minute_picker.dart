import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class HourMinutePicker extends StatelessWidget {

  final void Function(int) onHourChanged;
  final void Function(int) onMinuteChanged;
  int hourInitialItem;
  int minuteInitialItem;
  int hourMax;
  int minuteMax;
  late FixedExtentScrollController hourController;
  late FixedExtentScrollController minuteController;
  List<Widget>? hourChildren;
  List<Widget>? minuteChildren;

  HourMinutePicker({
    super.key,
    required this.onHourChanged,
    required this.onMinuteChanged,
    this.hourInitialItem = 0,
    this.minuteInitialItem = 0,
    this.hourMax = 12,
    this.minuteMax = 60,
  }) {
    hourController =  FixedExtentScrollController(initialItem: hourInitialItem);
    minuteController =  FixedExtentScrollController(initialItem: minuteInitialItem);
  }

  @override
  Widget build(BuildContext context) {
    hourChildren ??= _createHourChildren(context);
    minuteChildren ??= _createMinuteChildren(context);
    return Row(
      children: [
        Expanded(
          child: _buildTimeRangeWheel(
            controller: hourController,
            children: hourChildren!,
            onSelectedItemChanged: (index) {
              onHourChanged(index);
            },
          ),
        ),
        SizedBox(
          width: 38.w,
          child: Center(
            child: Text(
              ':',
              style: TextStyle(
                fontSize: 56,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            ),
          ),
        ),
        Expanded(
          child: _buildTimeRangeWheel(
            controller: minuteController,
            children: minuteChildren!,
            onSelectedItemChanged: (index) {
              onMinuteChanged(index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeRangeWheel({
    required ScrollController controller,
    required ValueChanged<int>? onSelectedItemChanged,
    required List<Widget> children,
  }) {
    return Column(
      children: [
        SizedBox(
          width: 118.w,
          height: 22.h,
          child: Center(
            child: SvgPicture.asset(
              AppImagePaths.triangleUp,
              width: 8,
              height: 8,
            ),
          ),
        ),
        Container(
            alignment: Alignment.center,
            width: 118.w,
            height: 108.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                    color: AppColors.ff01A796
                )
            ),
            child: ListWheelScrollView(
              controller: controller,
              physics: const FixedExtentScrollPhysics(),
              onSelectedItemChanged: onSelectedItemChanged,
              itemExtent: 98.h,
              squeeze: 1,
              diameterRatio: 3,
              children: children,
            )
        ),
        SizedBox(
          width: 118.w,
          height: 22.h,
          child: Center(
            child: SvgPicture.asset(
              AppImagePaths.triangleDown,
              width: 8,
              height: 8,
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _createHourChildren(BuildContext context) {
    final List<Widget> children = [];
    for (int i = 0; i < hourMax; i++) {
      children.add(
          SizedBox(
            key: ValueKey(i),
            width: 98.w,
            height: 98.h,
            child: Center(
              child: Text(
                '$i',
                style: TextStyle(
                  fontSize: 56,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
            ),
          )
      );
    }
    return children;
  }

  List<Widget> _createMinuteChildren(BuildContext context) {
    final List<Widget> children = [];
    for (int i = 0; i < minuteMax; i++){
      children.add(
          SizedBox(
            key: ValueKey(i),
            width: 98.w,
            height: 98.h,
            child: Center(
              child: Text(
                i < 10 ? '0$i' : '$i',
                style: TextStyle(
                  fontSize: 56,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
            ),
          )
      );
    }
    return children;
  }
}