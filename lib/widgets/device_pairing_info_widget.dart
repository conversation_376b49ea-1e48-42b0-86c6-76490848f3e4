import 'package:ct_flutter_matter_plugin/model/ct_matter_device.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class DevicePairingInfoWidget extends StatefulWidget {
  final CtMatterDevice device;

  const DevicePairingInfoWidget({super.key, required this.device});

  @override
  State<StatefulWidget> createState() {
    return _DevicePairingInfoWidgetState();
  }
}

class _DevicePairingInfoWidgetState extends State<DevicePairingInfoWidget> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController setupPinCodeController = TextEditingController(text: '');

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    setupPinCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Matter Device Info',
          style: TextStyle(
              fontSize: 21,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor
          )
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Form(
                key: formKey,
                child: Column(
                  children: [
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'vendorID'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                      const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        initialValue: widget.device.vendorID.toString(),
                        enabled: false,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        keyboardType: TextInputType.name,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'productID'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        initialValue: widget.device.productID.toString(),
                        enabled: false,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        keyboardType: TextInputType.name,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'discriminator'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        initialValue: widget.device.discriminator.toString(),
                        enabled: false,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        keyboardType: TextInputType.name,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'setupPinCode'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                      const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: setupPinCodeController,
                        keyboardType: TextInputType.number,
                        validator: _validateValue,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),

                    if (widget.device.ipAddress.isNotEmpty) _buildOnNetworkWidget()

                  ],
                )
            )
          ],
        ),
      ),
      actions: <Widget>[
        TextButton(
          child: Text('cancel'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            Get.back();
          },
        ),
        TextButton(
          child: Text('commissioning'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: ()  async {
            bool validate = formKey.currentState?.validate() ?? false;
            if (!validate) {
              return;
            }

            int setupPinCode = int.parse(setupPinCodeController.text);
            Get.back(result: setupPinCode);
          },
        ),
      ],
    );
  }

  Widget _buildOnNetworkWidget() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: Opacity(
            opacity: 0.5,
            child: Text(
              'ipAddress'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),
          ),
        ),
        SizedBox(
          height: 14.h,
        ),
        ConstrainedBox(
          constraints:
          const BoxConstraints(maxHeight: 148, minHeight: 48),
          child: TextFormField(
            initialValue: widget.device.ipAddress,
            enabled: false,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor,
            ),
            keyboardType: TextInputType.name,
            validator: _validateValue,
          ),
        ),

        SizedBox(
          height: 14.h,
        ),
        SizedBox(
          width: double.infinity,
          child: Opacity(
            opacity: 0.5,
            child: Text(
              'port'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!
                    .firstColor,
              ),
            ),
          ),
        ),
        SizedBox(
          height: 14.h,
        ),
        ConstrainedBox(
          constraints:
          const BoxConstraints(maxHeight: 148, minHeight: 48),
          child: TextFormField(
            initialValue: widget.device.port.toString(),
            enabled: false,
            style: TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor,
            ),
            keyboardType: TextInputType.name,
            validator: _validateValue,
          ),
        ),

        SizedBox(
          height: 14.h,
        ),
      ],
    );
  }

  String? _validateValue(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    if (value.length < 4) {
      return 'Invalid setupPinCode format.';
    }

    return null;
  }

}
