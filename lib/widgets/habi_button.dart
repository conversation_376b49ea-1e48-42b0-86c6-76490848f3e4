import 'package:flutter/material.dart';
import 'package:habi_app/constants/text_styles.dart';

class HabiButton extends StatelessWidget {
  final VoidCallback? onPressed;

  final Widget? icon;

  final Widget? label;

  final String? text;

  final Color textColor;

  final Color borderColor;

  final Color backgroundColor;

  final double minWidth;

  final EdgeInsetsGeometry padding;

  const HabiButton({
    super.key,
    this.onPressed,
    this.icon,
    this.label,
    this.text,
    this.textColor = Colors.white,
    this.borderColor = Colors.transparent,
    this.backgroundColor = Colors.transparent,
    this.minWidth = 100.0,
    this.padding = const EdgeInsets.all(14.0),
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(minWidth: minWidth),
      child: OutlinedButton.icon(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          backgroundColor: backgroundColor,
          side: BorderSide(color: borderColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24.0),
          ),
          padding: padding,
        ),
        icon: icon,
        label: _buildLabel(),
      ),
    );
  }

  Widget _buildLabel() {
    if (label != null) return label!;
    if (text != null) {
      return Text(
        text!,
        style: TextStyles.medium14.copyWith(color: textColor),
        textAlign: TextAlign.center,
      );
    }
    return const SizedBox.shrink();
  }
}
