import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class DeleteWidget extends StatefulWidget {

  final String title;
  final Function() onDelete;

  const DeleteWidget({
    super.key,
    required this.title,
    required this.onDelete
  });

  @override
  State<StatefulWidget> createState() {
    return _DeleteWidgetState();
  }
}

class _DeleteWidgetState extends State<DeleteWidget> {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController datasetController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18),
        child: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[

              SizedBox(height: 24.h),

              Text(widget.title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor
                  )
              ),

              SizedBox(height: 24.h),

              SizedBox(
                  width: 258,
                  height: 48,
                  child: FilledButton(
                    style: const ButtonStyle(
                      backgroundColor: WidgetStatePropertyAll(Colors.red),
                    ),
                    onPressed: () {
                      Get.back();
                      widget.onDelete();
                    },
                    child: Text(
                      'delete'.tr.toUpperCase(),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.medium,
                        color: Colors.white,
                      ),
                    ),
                  )
              ),

              SizedBox(height: 24.h),

              SizedBox(
                  height: 48,
                  child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Text(
                      'cancel'.tr,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.medium,
                        color: AppColors.ff01A796,
                      ),
                    ),
                  ),
              ),

            ],
          ),
        ),
      ),
    );
  }


}
