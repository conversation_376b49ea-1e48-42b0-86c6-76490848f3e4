import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/widgets/habi_button.dart';

/// Common dialog component
/// Supports title (optional), content, and customizable buttons
class HabiDialog extends StatelessWidget {
  /// Title text (optional)
  final String? title;

  /// Single paragraph content text
  ///
  /// Note: When multiple content types are provided, priority order is:
  /// customContent > paragraphs > content
  final String? content;

  /// Multiple paragraph content
  ///
  /// Note: When multiple content types are provided, priority order is:
  /// customContent > paragraphs > content
  final List<String>? paragraphs;

  /// Custom content widget for more complex layouts
  ///
  /// Note: When multiple content types are provided, priority order is:
  /// customContent > paragraphs > content
  final Widget? customContent;

  /// Primary button text (optional)
  final String? primaryButtonText;

  /// Secondary button text (optional, if null, secondary button will not be displayed)
  final String? secondaryButtonText;

  /// Primary button color
  final Color primaryButtonColor;

  /// Secondary button color
  final Color secondaryButtonColor;

  /// Primary button click event
  final VoidCallback? onPrimaryButtonPressed;

  /// Secondary button click event
  final VoidCallback? onSecondaryButtonPressed;

  /// Custom title style
  final TextStyle? titleStyle;

  /// Custom content style
  final TextStyle? contentStyle;

  /// Custom button style
  final TextStyle? buttonTextStyle;

  /// Dialog border radius
  final double borderRadius;

  /// Content text alignment
  final TextAlign contentTextAlign;

  /// Horizontal padding for dialog content
  final double horizontalPadding;

  /// Vertical padding for dialog content
  final double verticalPadding;

  /// Vertical spacing between dialog elements
  final double verticalSpacing;

  /// Maximum width of the dialog
  final double maxWidth;

  /// Custom child widget that replaces the default dialog content
  final Widget? child;

  /// Custom padding for the area around the dialog
  final EdgeInsets? insetPadding;

  const HabiDialog({
    super.key,
    this.title,
    this.content,
    this.paragraphs,
    this.primaryButtonText,
    this.secondaryButtonText,
    this.primaryButtonColor = AppColors.ff01A796,
    this.secondaryButtonColor = AppColors.ff868788,
    this.onPrimaryButtonPressed,
    this.onSecondaryButtonPressed,
    this.titleStyle,
    this.contentStyle,
    this.buttonTextStyle,
    this.borderRadius = 50.0,
    this.customContent,
    this.contentTextAlign = TextAlign.center,
    this.horizontalPadding = 45.0,
    this.verticalPadding = 40.0,
    this.verticalSpacing = 35.0,
    this.maxWidth = 360.0,
    this.child,
    this.insetPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: insetPadding,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: child ??
          Container(
            constraints: BoxConstraints(maxWidth: maxWidth),
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: verticalPadding,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                _buildTitle(),
                _buildContent(),
                _buildButtons(),
              ],
            ),
          ),
    );
  }

  /// Build dialog title
  Widget _buildTitle() {
    if (title == null) {
      return const SizedBox();
    }

    return Padding(
      padding: EdgeInsets.only(bottom: verticalSpacing),
      child: Text(
        title!,
        style: titleStyle ?? TextStyles.medium20FirstColor,
        textAlign: TextAlign.center,
      ),
    );
  }

  /// Build dialog content
  Widget _buildContent() {
    final defaultStyle = TextStyles.regular18FirstColor;

    if (customContent != null) {
      return customContent!;
    }

    if (paragraphs != null && paragraphs!.isNotEmpty) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: paragraphs!
            .map(
              (paragraph) => Text(
                paragraph,
                style: contentStyle ?? defaultStyle,
                textAlign: contentTextAlign,
              ),
            )
            .toList(),
      );
    }

    return Text(
      content ?? '',
      style: contentStyle ?? defaultStyle,
      textAlign: contentTextAlign,
    );
  }

  /// Build dialog buttons
  Widget _buildButtons() {
    if (primaryButtonText == null && secondaryButtonText == null) {
      return const SizedBox();
    }

    return Padding(
      padding: EdgeInsets.only(top: verticalSpacing),
      child: LayoutBuilder(
        builder: (context, constraints) {
          if (constraints.maxWidth < 200) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (secondaryButtonText != null)
                  _buildButton(
                    text: secondaryButtonText!,
                    buttonColor: secondaryButtonColor,
                    onPressed: onSecondaryButtonPressed,
                  ),
                if (primaryButtonText != null) ...[
                  if (secondaryButtonText != null) const SizedBox(height: 10),
                  _buildButton(
                    text: primaryButtonText!,
                    buttonColor: primaryButtonColor,
                    onPressed: onPrimaryButtonPressed,
                  ),
                ]
              ],
            );
          }

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (secondaryButtonText != null)
                _buildButton(
                  text: secondaryButtonText!,
                  buttonColor: secondaryButtonColor,
                  onPressed: onSecondaryButtonPressed,
                  shouldExpand: primaryButtonText == null,
                ),
              if (primaryButtonText != null)
                _buildButton(
                  text: primaryButtonText!,
                  buttonColor: primaryButtonColor,
                  onPressed: onPrimaryButtonPressed,
                  shouldExpand: secondaryButtonText == null,
                ),
            ],
          );
        },
      ),
    );
  }

  /// Build a single button with specified properties
  Widget _buildButton({
    required String text,
    required Color buttonColor,
    required VoidCallback? onPressed,
    bool shouldExpand = false,
  }) {
    final button = HabiButton(
      onPressed: () => onPressed?.call(),
      text: text,
      label: buttonTextStyle != null
          ? Text(
              text,
              style: buttonTextStyle,
              textAlign: TextAlign.center,
            )
          : null,
      textColor: buttonColor,
      borderColor: buttonColor,
    );

    if (shouldExpand) {
      return Expanded(child: button);
    }
    return button;
  }
}
