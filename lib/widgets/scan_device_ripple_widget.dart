import 'package:flutter/cupertino.dart';
import 'package:habi_app/constants/app_animation_paths.dart';
import 'package:lottie/lottie.dart';

class ScanDeviceRippleWidget extends StatefulWidget {

  double? width;
  double? height;

  ScanDeviceRippleWidget({
    super.key,
    this.width,
    this.height,
  });

  @override
  State<StatefulWidget> createState() {
    return ScanDeviceRippleWidgetState();
  }

}

class ScanDeviceRippleWidgetState extends State<ScanDeviceRippleWidget>
    with TickerProviderStateMixin {

  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Lottie.asset(
      AppAnimationPaths.ripple,
      width: widget.width ?? 342,
      height: widget.height ?? 342,
      controller: _animationController,
      onLoaded: (composition) => startAnimation(),
    );
  }

  void startAnimation() {
    _animationController
      ..duration = const Duration(seconds: 12)
      ..repeat();
  }

  void stopAnimation() {
    _animationController.stop();
  }

}