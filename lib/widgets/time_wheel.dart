import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class TimeWheel extends StatelessWidget {

  final void Function(int, String) onHourChanged;
  final void Function(int, String) onMinuteChanged;
  final FixedExtentScrollController hourController;
  final FixedExtentScrollController minuteController;
  bool is24Hour;
  List<Widget>? hourChildren;
  List<Widget>? minuteChildren;

  TimeWheel({
    super.key,
    required this.onHourChanged,
    required this.onMinuteChanged,
    required this.hourController,
    required this.minuteController,
    this.is24Hour = false,
  });

  @override
  Widget build(BuildContext context) {
    hourChildren ??= _createHourChildren(context);
    minuteChildren ??= _createMinuteChildren(context);
    return Row(
      children: [
        Expanded(
          child: _buildTimeRangeWheel(
            controller: hourController,
            children: hourChildren!,
            onSelectedItemChanged: (index) {
              if (hourChildren != null) {
                Widget hourWidget = hourChildren![index];
                ValueKey key = hourWidget.key as ValueKey;
                onHourChanged(index, key.value);
              }
            },
          ),
        ),
        SizedBox(
          width: 38.w,
          child: Center(
            child: Text(
              ':',
              style: TextStyle(
                fontSize: 56,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
            ),
          ),
        ),
        Expanded(
          child: _buildTimeRangeWheel(
            controller: minuteController,
            children: minuteChildren!,
            onSelectedItemChanged: (index) {
              if (minuteChildren != null) {
                Widget minuteWidget = minuteChildren![index];
                ValueKey key = minuteWidget.key as ValueKey;
                onMinuteChanged(index, key.value);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeRangeWheel({
    required ScrollController controller,
    required ValueChanged<int>? onSelectedItemChanged,
    required List<Widget> children,
  }) {
    return Column(
      children: [
        SizedBox(
          width: 118.w,
          height: 22.h,
          child: Center(
            child: SvgPicture.asset(
              AppImagePaths.triangleUp,
              width: 8,
              height: 8,
            ),
          ),
        ),
        Container(
            alignment: Alignment.center,
            width: 118.w,
            height: 108.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                    color: AppColors.ff01A796
                )
            ),
            child: ListWheelScrollView(
              controller: controller,
              physics: const FixedExtentScrollPhysics(),
              onSelectedItemChanged: onSelectedItemChanged,
              itemExtent: 98.h,
              squeeze: 1,
              diameterRatio: 3,
              children: children,
            )
        ),
        SizedBox(
          width: 118.w,
          height: 22.h,
          child: Center(
            child: SvgPicture.asset(
              AppImagePaths.triangleDown,
              width: 8,
              height: 8,
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _createHourChildren(BuildContext context) {
    final List<Widget> children = [];
    if (is24Hour) {
      for (int i = 0; i < 24; i++) {
        String value = i < 10 ? '0$i' : '$i';
        children.add(
            SizedBox(
              key: ValueKey(value),
              width: 98.w,
              height: 98.h,
              child: Center(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 56,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            )
        );
      }
    } else {
      for (int i = 0; i < 12; i++) {
        String value = '${i + 1}';
        children.add(
            SizedBox(
              key: ValueKey(value),
              width: 98.w,
              height: 98.h,
              child: Center(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 56,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            )
        );
      }
    }

    return children;
  }

  List<Widget> _createMinuteChildren(BuildContext context) {
    final List<Widget> children = [];
    for (int i = 0; i < 60; i++){
      String value = i < 10 ? '0$i' : '$i';
      children.add(
          SizedBox(
            key: ValueKey(value),
            width: 98.w,
            height: 98.h,
            child: Center(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 56,
                  fontWeight: AppFontWeights.regular,
                  color: Theme.of(context)
                      .extension<AppThemeExtension>()!.firstColor,
                ),
              ),
            ),
          )
      );
    }
    return children;
  }

  void selectedHourValue(String value) {
    if (is24Hour) {
      if (hourChildren != null) {
        for (int i = 0; i < hourChildren!.length; i++) {
          Widget hourWidget = hourChildren![i];
          ValueKey key = hourWidget.key as ValueKey;
          if (key.value == value) {
            hourController.jumpToItem(i);
            break;
          }
        }
      }
    } else {
      String thatValue = int.parse(value).toString();
      if (hourChildren != null) {
        for (int i = 0; i < hourChildren!.length; i++) {
          Widget hourWidget = hourChildren![i];
          ValueKey key = hourWidget.key as ValueKey;
          if (key.value == thatValue) {
            hourController.jumpToItem(i);
            break;
          }
        }
      }
    }
  }

  void selectedMinuteValue(String value) {
    if (minuteChildren != null) {
      for (int i = 0; i < minuteChildren!.length; i++) {
        Widget minuteWidget = minuteChildren![i];
        ValueKey key = minuteWidget.key as ValueKey;
        if (key.value == value) {
          minuteController.jumpToItem(i);
          break;
        }
      }
    }
  }

}