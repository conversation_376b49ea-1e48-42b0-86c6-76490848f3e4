import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class MatterCodeStickerWidget extends StatelessWidget {

  final String title;
  final String subTitle;
  void Function()? onClicked;

  MatterCodeStickerWidget({
    required this.title,
    required this.subTitle,
    this.onClicked,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 30.h,),
        Text(
          title,
          style: TextStyle(
            fontSize: 22,
            fontWeight: AppFontWeights.medium,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        const SizedBox(height: 5,),
        Text(
          subTitle,
          style: TextStyle(
            fontSize: 16,
            fontWeight: AppFontWeights.regular,
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.firstColor,
          ),
        ),
        SizedBox(height: 30.h,),
        Expanded(
          child: Row(
            children: [
              const Expanded(child: SizedBox()),
              SvgPicture.asset(
                AppImagePaths.matterQrCodeSticker,
                colorFilter: ColorFilter.mode(Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor, BlendMode.srcIn),
              ),
              Expanded(
                  child: InkWell(
                    onTap: onClicked,
                    child: SizedBox(
                      width: 50,
                      height: 50,
                      child: Center(
                        child: SvgPicture.asset(
                          AppImagePaths.arrowRight,
                        ),
                      ),
                    ),
                  )
              ),
            ],
          ),
        ),
        const Expanded(child: SizedBox()),
      ],
    );
  }
}