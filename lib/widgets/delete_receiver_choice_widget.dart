import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class DeleteReceiverChoiceWidget extends StatefulWidget {

  static const int keepAllConfig = 1;
  static const int keepConfig = 2;
  static const int factoryReset = 3;
  final Function(int value) onConfirm;

  const DeleteReceiverChoiceWidget({
    super.key,
    required this.onConfirm
  });

  @override
  State createState() => _DeleteReceiverChoiceWidgetState();
}

class _DeleteReceiverChoiceWidgetState extends State<DeleteReceiverChoiceWidget> {
  int removeSelectorValue = DeleteReceiverChoiceWidget.keepAllConfig;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(28.0),
        ),
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.secondColor,
              borderRadius: const BorderRadius.all(Radius.circular(6.0)),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40,),

                Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Text(
                    'carefulIrreversibleChoice'.tr,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 40,),

                Column(
                  children: [
                    RadioListTile(
                      value: DeleteReceiverChoiceWidget.keepAllConfig,
                      groupValue: removeSelectorValue,
                      title: Text(
                        'carefulIrreversibleChoiceOption1'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color:
                          Theme.of(context).extension<AppThemeExtension>()!.firstColor,
                        ),
                      ),
                      onChanged: (value) {
                        removeSelectorValue = value!;
                        setState(() {});
                      },
                    ),
                    RadioListTile(
                      value: DeleteReceiverChoiceWidget.factoryReset,
                      groupValue: removeSelectorValue,
                      title: Text(
                        'carefulIrreversibleChoiceOption2'.tr,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: AppFontWeights.regular,
                          color:
                          Theme.of(context).extension<AppThemeExtension>()!.firstColor,
                        ),
                      ),
                      onChanged: (value) {
                        removeSelectorValue = value!;
                        setState(() {});
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 40,),

                Row(
                  children: [
                    Expanded(
                        child: Center(child: _buildConfirmButton())
                    ),
                    Expanded(
                        child: Center(child: _buildCancelButton())
                    ),
                  ],
                ),

                const SizedBox(height: 50,),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton() {
    return SizedBox(
        height: 48,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(
              color: Colors.red,
              width: 1,
            ),
          ),
          onPressed: () {
            Get.back();
            widget.onConfirm(removeSelectorValue);
          },
          child: Text(
            'confirm'.tr.toUpperCase(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Colors.red,
            ),
          ),
        ));
  }

  Widget _buildCancelButton() {
    return SizedBox(
      height: 48,
      child: OutlinedButton(
        onPressed: () {
          Get.back();
        },
        child: Text(
          'cancel'.tr.toUpperCase(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: AppColors.ff01A796,
          ),
        ),
      ),
    );
  }




}
