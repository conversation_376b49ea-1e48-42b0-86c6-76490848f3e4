import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class AddDeviceSucceededWidget extends StatefulWidget {

  Function()? onOK;

  AddDeviceSucceededWidget({
    super.key,
    this.onOK
  });

  @override
  State createState() => _AddDeviceSucceededWidgetState();
}

class _AddDeviceSucceededWidgetState extends State<AddDeviceSucceededWidget> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(28.0),
        ),
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.secondColor,
              borderRadius: const BorderRadius.all(Radius.circular(6.0)),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40,),

                Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Text(
                    'pairingProcessWasFinalized'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 20,),

                _buildOKButton(),

                const SizedBox(height: 40,),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOKButton() {
    return SizedBox(
      width: 128.h,
      height: 48,
      child: OutlinedButton(
        onPressed: () {
          widget.onOK?.call();
        },
        child: Text(
          'ok'.tr.toUpperCase(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: AppFontWeights.regular,
            color: AppColors.ff01A796,
          ),
        ),
      ),
    );
  }




}
