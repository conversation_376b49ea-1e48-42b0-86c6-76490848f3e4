import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_image_paths.dart';

class WifiSignalStrengthV2 extends StatelessWidget {

  final int signalStrength;
  final void Function()? onTap;

  const WifiSignalStrengthV2({
    super.key,
    required this.signalStrength,
    this.onTap
  });

  @override
  Widget build(BuildContext context) {
    String iconPath = AppImagePaths.wifiLevelNo;

    if (signalStrength > -35) {
      iconPath = AppImagePaths.wifiLevelFull;
    } else if (signalStrength >= -65) {
      iconPath = AppImagePaths.wifiLevelMedium;
    } else if (signalStrength >= -95) {
      iconPath = AppImagePaths.wifiLevelWeak;
    } else {
      iconPath = AppImagePaths.wifiLevelNo;
    }

    return InkWell(
      onTap: onTap,
      child: SvgPicture.asset(
          iconPath,
          width: 35,
          height: 35,
      ),
    );
  }
}