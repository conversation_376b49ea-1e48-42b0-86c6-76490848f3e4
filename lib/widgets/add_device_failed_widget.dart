import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/widgets/view_error_log.dart';

class AddDeviceFailedWidget extends StatefulWidget {

  final String errorDescription;
  String? errorDescriptionDetails;
  Function()? onTryAgain;

  AddDeviceFailedWidget({
    super.key,
    required this.errorDescription,
    this.errorDescriptionDetails,
    this.onTryAgain
  });

  @override
  State createState() => _AddDeviceFailedWidgetState();
}

class _AddDeviceFailedWidgetState extends State<AddDeviceFailedWidget> {

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(28.0),
        ),
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!.secondColor,
              borderRadius: const BorderRadius.all(Radius.circular(6.0)),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40,),

                Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Text(
                    'pairingProcessFailed'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 20,),

                Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: Text(
                    widget.errorDescription,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: AppFontWeights.regular,
                      color: Theme.of(context)
                          .extension<AppThemeExtension>()!.firstColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                if (widget.errorDescriptionDetails != null)
                  Column(
                    children: [
                      const SizedBox(height: 40,),
                      ViewErrorLog(errorLog: widget.errorDescriptionDetails!),
                    ]
                  ),

                const SizedBox(height: 40,),

                _buildTryAgainButton(),

                const SizedBox(height: 50,),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTryAgainButton() {
    return SizedBox(
        width: 180.w,
        height: 48,
        child: OutlinedButton(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(
              color: Colors.red,
              width: 1,
            ),
          ),
          onPressed: () {
            widget.onTryAgain?.call();
          },
          child: Text(
            'tryAgain'.tr.toUpperCase(),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: AppFontWeights.regular,
              color: Colors.red,
            ),
          ),
        ));
  }





}
