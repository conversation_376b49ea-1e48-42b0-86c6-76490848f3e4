import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class EnterDatasetWidget extends StatefulWidget {

  const EnterDatasetWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return _EnterDatasetWidgetState();
  }
}

class _EnterDatasetWidgetState extends State<EnterDatasetWidget> {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController datasetController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Please enter dataset',
          style: TextStyle(
              fontSize: 21,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor
          )
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Form(
              key: formKey,
              child: Column(
                children: [
                  SizedBox(
                    height: 14.h,
                  ),
                  ConstrainedBox(
                    constraints:
                        const BoxConstraints(maxHeight: 148, minHeight: 48),
                    child: TextFormField(
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: AppFontWeights.regular,
                        color: Theme.of(context)
                            .extension<AppThemeExtension>()!
                            .firstColor,
                      ),
                      controller: datasetController,
                      keyboardType: TextInputType.name,
                      validator: _validateDataset,
                    ),
                  ),
                  SizedBox(
                    height: 14.h,
                  ),
                ],
              )
          )
        ],
      ),
      actions: <Widget>[
        TextButton(
          child: Text('cancel'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            Get.back();
          },
        ),
        TextButton(
          child: Text('confirm'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            bool validate = formKey.currentState?.validate() ?? false;
            if (!validate) {
              return;
            }
            Get.back(result: {
              'dataset': datasetController.text,
            });
          },
        ),
      ],
    );
  }

  String? _validateDataset(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    return null;
  }

}
