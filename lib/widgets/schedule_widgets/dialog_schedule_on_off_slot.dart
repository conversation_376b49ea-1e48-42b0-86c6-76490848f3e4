import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/services/local_storage_service.dart';
import 'package:habi_app/utility/date_time_utils.dart';
import 'package:habi_app/widgets/schedule_widgets/dropdown_schedule_on_off_option.dart';

class ScheduleTurnOnOffAddSlot extends StatefulWidget {
  const ScheduleTurnOnOffAddSlot({
    super.key,
    this.isEdit,
    required this.objSchedule,
    required this.onTabAdd,
    required this.isUniqueTime,
    required this.onTabDelete,
  });

  final bool? isEdit;
  final Schedule objSchedule;
  final Function(Schedule value) onTabAdd;
  final bool Function(String time, int? position) isUniqueTime;
  final Function(Schedule value) onTabDelete;

  @override
  ScheduleTurnOnOffAddSlotState createState() =>
      ScheduleTurnOnOffAddSlotState();
}

class ScheduleTurnOnOffAddSlotState extends State<ScheduleTurnOnOffAddSlot> {
  final GlobalKey<DropdownScheduleOnOffOptionState> _keyOnOff =
      GlobalKey<DropdownScheduleOnOffOptionState>();
  String onOffRadio = "0000";
  late Schedule objSchedule;
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    objSchedule = widget.objSchedule.copyWith();
    _controller.text = objSchedule.getTime();
    _setData();
    _controller.addListener(() {
      // Flutter 时间选择器一个已知问题：在 showTimePicker 使用 dial 模式时，出现先弹出键盘再收起，然后再显示表盘的问题
      // 而且收起表盘时，又会弹出键盘，所以这里加 addListener 让 App 自动收起键盘
      if (_formKey.currentState?.validate() ?? false) {
        FocusScope.of(context).unfocus();
      } else {
        FocusScope.of(context).unfocus();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: SvgPicture.asset(
                AppImagePaths.closeSlot,
                width: 16,
                height: 16,
              ),
            ),
            Text(
              'editSlot'.tr,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: AppColors.ff1C1C1C,
              ),
            ),
            InkWell(
              onTap: () {
                if (_formKey.currentState?.validate() ?? false) {
                  widget.onTabAdd(objSchedule);
                  Navigator.of(context).pop();
                  FocusScope.of(context).unfocus();
                }
              },
              child: Text(
                widget.isEdit != null && widget.isEdit! == true
                    ? 'updateSlot'.tr
                    : 'addNewSlot'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: AppFontWeights.regular,
                  color: AppColors.ff01A796,
                ),
              ),
            )
          ],
        ),
        const SizedBox(height: 30),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTime(),
            SizedBox(
              height: 58,
              width: 135,
              child: DropdownScheduleOnOffOption(
                key: _keyOnOff,
                onChanged: (String value) {
                  setState(() {
                    objSchedule.onOrOff = value;
                  });
                },
                initialValue: objSchedule.onOrOff ?? "0000",
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),
        if (widget.isEdit != null && widget.isEdit! == true) ...[
          InkWell(
            onTap: () {
              widget.onTabDelete(objSchedule);
              Navigator.of(context).pop();
            },
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  border: Border.all(color: AppColors.ffFF4E4E),
                  borderRadius: const BorderRadius.all(Radius.circular(20))),
              height: 48,
              child: Text(
                'deleteSlot'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: AppFontWeights.regular,
                  color: AppColors.ffFF4E4E,
                ),
              ),
            ),
          ),
          const SizedBox(height: 30),
        ],
      ],
    );
  }

  void _setData() {
    if (objSchedule.onOrOff != null) {
      onOffRadio = objSchedule.onOrOff ?? onOffRadio;
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    TimeOfDay? objTimeOfDay = await showTimePicker(
      onEntryModeChanged: saveTimePickerMode,
      initialEntryMode: getTimePickerMode(),
      context: context,
      initialTime: objSchedule.getSelectedTimeOfDay(),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context)
              .copyWith(alwaysUse24HourFormat: objSchedule.is24HourFormat()),
          child: child ?? Container(),
        );
      },
    );
    //&& objTimeOfDay != selectedTime
    if (objTimeOfDay != null) {
      setState(() {
        var t = objTimeOfDay.format(context);
        if (objSchedule.timeFormat24Hour == 1 &&
            objTimeOfDay.format(context).split(" ").length == 2) {
          t = DateTimeUtils.dateConverter(t, "h:mm a", "HH:mm");
        }
        objSchedule.setTime(t);
        _controller.text = objSchedule.getTime();
      });
    }
  }

  Widget _buildTime() {
    return Flexible(
      child: SizedBox(
        width: 135,
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.disabled,
          child: TextFormField(
            // key: UniqueKey(),
            controller: _controller,
            style: const TextStyle(color: AppColors.ff01A796),
            keyboardType:
            const TextInputType.numberWithOptions(decimal: true),
            onTap: () {
              _selectTime(context);
            },
            validator: (value) {
              final criteria = objSchedule.validateTime(context);
              if (criteria != null) {
                return criteria;
              }
              if (value != null && value.isNotEmpty) {
                if (widget.isUniqueTime(objSchedule.convertTime(value), objSchedule.position) == false) {
                  return "uniqueStartTime".tr;
                }
              }
              return criteria;
            },
            decoration: InputDecoration(
              errorMaxLines: 3,  //  设置错误提示的最大行数，以便显示完整的错误信息。
              hintText: "time".tr,
              // labelText: "time".tr, // 可选的标签文本
              // border: OutlineInputBorder(), // 可选的边框样式
            ),
          ),
        ),
      ),
    );
  }

  TimePickerEntryMode getTimePickerMode() {
    var localStorageService = LocalStorageService.to;
    final String isKeypadMode = localStorageService.getKeypadMode() ?? "dail";
    if (isKeypadMode != "dail") {
      return TimePickerEntryMode.input;
    }
    return TimePickerEntryMode.dial;
  }

  void saveTimePickerMode(TimePickerEntryMode mode) {
    var localStorageService = LocalStorageService.to;
    String isKeypadMode = "input";
    if (mode == TimePickerEntryMode.dial) {
      isKeypadMode = "dail";
    }
    localStorageService.setKeypadMode(isKeypadMode);
    // SharedPrefs.setBool(PREFKISKEYPADMODE, isKeypadMode);
  }
}
