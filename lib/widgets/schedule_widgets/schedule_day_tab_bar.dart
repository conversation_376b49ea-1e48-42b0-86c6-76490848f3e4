import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/text_styles.dart';

class ScheduleDayTabBar extends StatefulWidget {
  final TabController controller;

  const ScheduleDayTabBar({
    super.key,
    required this.controller,
  });

  @override
  State<ScheduleDayTabBar> createState() => _ScheduleDayTabBarState();
}

class _ScheduleDayTabBarState extends State<ScheduleDayTabBar> {
  int _currentIndex = 0;

  final List<String> _days = [
    'sun'.tr,
    'mon'.tr,
    'tue'.tr,
    'wed'.tr,
    'thu'.tr,
    'fri'.tr,
    'sat'.tr,
  ];

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.controller.index;
    widget.controller.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTabChange);
    super.dispose();
  }

  void _handleTabChange() {
    if (mounted && _currentIndex != widget.controller.index) {
      setState(() {
        _currentIndex = widget.controller.index;
      });
    }
  }

  void _onWeekdayTap() {
    widget.controller.animateTo(0);
  }

  void _onWeekendTap() {
    widget.controller.animateTo(1);
  }

  void _onDailyTap(int index) {
    widget.controller.animateTo(index);
  }

  bool _isWeekdaySelected(int index) {
    return _currentIndex == 0;
  }

  bool _isWeekendSelected(int index) {
    return _currentIndex == 1;
  }

  bool _isDailySelected(int index) {
    return _currentIndex == index;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48,
      child: Row(
        children: _buildTabs(),
      ),
    );
  }

  List<Widget> _buildTabs() {
    final tabs = <Widget>[];

    if (widget.controller.length == 7) {
      for (var i = 0; i < _days.length; i++) {
        tabs.add(
          _buildTab(
            day: _days[i],
            onTap: () => _onDailyTap(i),
            isSelected: _isDailySelected(i),
          ),
        );
      }
    } else if (widget.controller.length == 2) {
      for (var i = 0; i < _days.length; i++) {
        if (i == 0 || i == 6) {
          tabs.add(
            _buildTab(
              day: _days[i],
              onTap: () => _onWeekendTap(),
              isSelected: _isWeekendSelected(i),
            ),
          );
        } else {
          tabs.add(
            _buildTab(
              day: _days[i],
              onTap: () => _onWeekdayTap(),
              isSelected: _isWeekdaySelected(i),
            ),
          );
        }
      }
    } else if (widget.controller.length == 1) {
      for (var i = 0; i < _days.length; i++) {
        tabs.add(
          _buildTab(
            day: _days[i],
          ),
        );
      }
    }

    return tabs;
  }

  Widget _buildTab({
    required String day,
    VoidCallback? onTap,
    bool isSelected = true,
  }) {
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: Column(
          children: [
            Expanded(
              child: Opacity(
                opacity: isSelected ? 1.0 : 0.5,
                child: Center(
                  child: Text(
                    day,
                    textAlign: TextAlign.center,
                    style: TextStyles.regular14FirstColor,
                  ),
                ),
              ),
            ),
            Container(
              height: 2,
              color: isSelected
                  ? AppColors.ff01A796
                  : AppColors.ff707070.withOpacity(0.5),
            ),
          ],
        ),
      ),
    );
  }
}
