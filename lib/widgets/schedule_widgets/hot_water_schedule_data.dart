import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/app_schedules.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/models/schedule/hot_water_schedule.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/widgets/habi_button.dart';
import 'package:habi_app/widgets/habi_shadow_card.dart';

class HotWaterScheduleData extends StatelessWidget {
  const HotWaterScheduleData({
    super.key,
    required this.objList,
    required this.onTabShowDialog,
    required this.emptyMessage,
    required this.defaultSchedule,
    required this.isModifyEnable,
    required this.padding,
    this.limit = 3,
  });

  final String emptyMessage;
  final List<HotWaterSchedule> objList;
  final Function(
    HotWaterSchedule schedule,
    List<HotWaterSchedule> objList,
    bool isEdit,
  ) onTabShowDialog;
  final HotWaterSchedule defaultSchedule;
  final int limit;
  final bool isModifyEnable;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildList()),
        const SizedBox(height: 15),
        Padding(
          padding: padding,
          child: Column(
            children: [
              Opacity(
                opacity: 0.5,
                child: Center(
                  child: Text(
                    'addSlotLimit3'.tr,
                    style: TextStyles.regular16FirstColor,
                  ),
                ),
              ),
              const SizedBox(height: 15),
              _buildAddSlot(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildList() {
    if (objList.isEmpty) {
      return Padding(
        padding: padding,
        child: Container(
          alignment: Alignment.center,
          child: Text(
            emptyMessage,
            textAlign: TextAlign.center,
            style: TextStyles.regular16FirstColor,
          ),
        ),
      );
    } else {
      return ListView.builder(
        itemCount: objList.length,
        itemBuilder: _buildScheduleItem,
      );
    }
  }

  Widget _buildScheduleItem(BuildContext context, int index) {
    return HabiShadowCard(
      margin: padding.copyWith(
        bottom: index == objList.length - 1 ? 25 : 15,
      ),
      padding: const EdgeInsets.all(20.0),
      child: InkWell(
        onTap: () {
          if (!isModifyEnable) {
            return;
          }
          onTabShowDialog(
            objList[index],
            objList,
            true,
          );
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final maxWidth = constraints.maxWidth;
                  final fontSize = min(maxWidth * 0.072, 24).toDouble();
                  return _buildOnOffSchedules(
                    objList[index],
                    fontSize: fontSize,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnOffSchedules(
    HotWaterSchedule item, {
    required double fontSize,
  }) {
    final onSchedule = item.onSchedule;
    final offSchedule = item.offSchedule;
    final isValidOnSchedule =
        onSchedule.time != null && onSchedule.onOrOff != null;
    final isValidOffSchedule =
        offSchedule.time != null && offSchedule.onOrOff != null;

    if (isValidOnSchedule && isValidOffSchedule) {
      return Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildValue(onSchedule, fontSize: fontSize),
                const SizedBox(height: 10),
                _buildValue(offSchedule, fontSize: fontSize),
              ],
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildTime(onSchedule, fontSize: fontSize),
                      const SizedBox(height: 10),
                      _buildTime(offSchedule, fontSize: fontSize),
                    ],
                  ),
                ),
                const SizedBox(width: 10),
                _buildEditIcon(),
              ],
            ),
          ),
        ],
      );
    }
    if (isValidOnSchedule) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: _buildValue(onSchedule, fontSize: fontSize),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: _buildTime(onSchedule, fontSize: fontSize),
                ),
                const SizedBox(width: 10),
                _buildEditIcon(),
              ],
            ),
          ),
        ],
      );
    }
    if (isValidOffSchedule) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: _buildValue(offSchedule, fontSize: fontSize),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: _buildTime(offSchedule, fontSize: fontSize),
                ),
                const SizedBox(width: 10),
                _buildEditIcon(),
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox();
  }

  Widget _buildTime(Schedule schedule, {required double fontSize}) {
    final isOn = schedule.onOrOff == HotWaterScheduleStatus.on.value;
    return Opacity(
      opacity: isOn ? 1 : 0.5,
      child: Text(
        schedule.getTime(),
        style: TextStyles.regular.copyWith(
          color: AppColorsHelper.firstColor,
          fontSize: fontSize,
        ),
      ),
    );
  }

  Widget _buildValue(Schedule schedule, {required double fontSize}) {
    final isOn = schedule.onOrOff == HotWaterScheduleStatus.on.value;
    return Opacity(
      opacity: isOn ? 1 : 0.5,
      child: Text(
        isOn ? 'on'.tr.toUpperCase() : 'off'.tr.toUpperCase(),
        style: TextStyles.regular.copyWith(
          color: AppColorsHelper.firstColor,
          fontSize: fontSize,
        ),
      ),
    );
  }

  Widget _buildEditIcon() {
    return SvgPicture.asset(
      AppImagePaths.edit,
      colorFilter: ColorFilter.mode(
        isModifyEnable ? AppColors.ff01A796 : Colors.grey,
        BlendMode.srcIn,
      ),
      width: 22,
      height: 22,
    );
  }

  Widget _buildAddSlot() {
    Color? textColor;
    Color? backgroundColor;
    VoidCallback? onPressed;
    if (limit > objList.length && isModifyEnable) {
      textColor = Colors.white;
      backgroundColor = AppColors.ff01A796;
      onPressed = () {
        onTabShowDialog(
          defaultSchedule,
          objList,
          false,
        );
      };
    } else {
      textColor = Colors.white.withOpacity(0.5);
      backgroundColor = AppColorsHelper.firstColor.withOpacity(0.5);
    }

    return SizedBox(
      width: double.infinity,
      child: HabiButton(
        text: 'addSlot'.tr,
        textColor: textColor,
        backgroundColor: backgroundColor,
        icon: SvgPicture.asset(
          AppImagePaths.add,
          width: 16,
          height: 16,
          colorFilter: ColorFilter.mode(
            textColor,
            BlendMode.srcIn,
          ),
        ),
        onPressed: onPressed,
      ),
    );
  }
}
