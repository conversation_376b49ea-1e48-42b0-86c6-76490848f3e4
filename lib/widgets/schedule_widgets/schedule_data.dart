import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_enums.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/constants/text_styles.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';
import 'package:habi_app/models/schedule/schedule.dart';
import 'package:habi_app/widgets/habi_button.dart';
import 'package:habi_app/widgets/habi_shadow_card.dart';

class ScheduleData extends StatefulWidget {
  ScheduleData({
    super.key,
    required this.objList,
    required this.onTabShowDialog,
    required this.emptyMessage,
    required this.defaultSchedule,
    required this.isModifyEnable,
    required this.horizontalPadding,
    this.limit = 6,
  });

  final String emptyMessage;
  final List<Schedule> objList;
  final Function(Schedule schedule, List<Schedule> objList, bool isEdit) onTabShowDialog;
  final Schedule defaultSchedule;
  final int limit;
  final bool isModifyEnable;
  final EdgeInsets horizontalPadding;

  @override
  State<ScheduleData> createState() => _ScheduleDataState();
}

class _ScheduleDataState extends State<ScheduleData> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(ScheduleData oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildList()),
        const SizedBox(height: 15),
        _buildAddSlotLimit(),
        const SizedBox(height: 15),
        _buildAddSlot(),
      ],
    );
  }

  Widget _buildList() {
    if (widget.objList.isEmpty) {
      return Padding(
        padding: widget.horizontalPadding,
        child: Container(
          alignment: Alignment.center,
          child: Text(
            widget.emptyMessage,
            textAlign: TextAlign.center,
            style: TextStyles.regular16FirstColor,
          ),
        ),
      );
    } else {
      return ListView.builder(
        itemCount: widget.objList.length,
        itemBuilder: _buildScheduleItem,
      );
    }
  }

  Widget _buildScheduleItem(BuildContext context, int index) {
    return HabiShadowCard(
      margin: widget.horizontalPadding.copyWith(
        bottom: index == widget.objList.length - 1 ? 25 : 20,
      ),
      padding: const EdgeInsets.all(20.0),
      child: InkWell(
        onTap: () {
          if (!widget.isModifyEnable) {
            return;
          }
          widget.onTabShowDialog(
            widget.objList[index],
            widget.objList,
            true,
          );
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildRowTime(index),
            const Spacer(),
            ConstrainedBox(
              constraints: const BoxConstraints(
                minWidth: 85,
              ),
              child: _buildRowValue(index),
            ),
            _buildRowAction(context, index),
          ],
        ),
      ),
    );
  }

  Widget _buildRowTime(int index) {
    final Schedule element = widget.objList[index];
    return Text(
      element.getTime(),
      style: TextStyles.regular24FirstColor,
    );
  }

  Widget _buildRowValue(int index) {
    final Schedule element = widget.objList[index];
    if (element.scheduleMode == ScheduleMode.onOrOff) {
      return Text(
        element.onOrOff == "0000" ? "Off" : "On",
        style: TextStyles.regular24FirstColor,
      );
    }
    if (element.scheduleMode == ScheduleMode.heatOnly) {
      return Row(
        children: [
          SvgPicture.asset(
            AppImagePaths.thermostatHeat,
            colorFilter: ColorFilter.mode(
              AppColorsHelper.firstColor,
              BlendMode.srcIn,
            ),
            width: 24,
            height: 24,
          ),
          const SizedBox(width: 5),
          Text(
            element.getValue().replaceAll(".0", ""),
            style: TextStyles.regular24FirstColor,
          ),
        ],
      );
    }
    return const Text("");
  }

  Widget _buildRowAction(BuildContext context, int index) {
    return SizedBox(
      width: 15,
      child: Align(
        alignment: Alignment.centerRight,
        child: SvgPicture.asset(
          AppImagePaths.chevronForward,
          colorFilter: ColorFilter.mode(
              widget.isModifyEnable ? AppColors.ff01A796 : Colors.grey,
              BlendMode.srcIn),
          width: 18.84,
          height: 18.84,
        ),
      ),
    );
  }

  Widget _buildAddSlotLimit() {
    return Padding(
      padding: widget.horizontalPadding,
      child: Opacity(
        opacity: 0.5,
        child: Center(
          child: Text(
            'addSlotLimit'.tr,
            style: TextStyles.regular16FirstColor,
          ),
        ),
      ),
    );
  }

  Widget _buildAddSlot() {
    Color? textColor;
    Color? backgroundColor;
    VoidCallback? onPressed;
    if (widget.limit > widget.objList.length && widget.isModifyEnable) {
      textColor = Colors.white;
      backgroundColor = AppColors.ff01A796;
      onPressed = () {
        widget.onTabShowDialog(
          widget.defaultSchedule,
          widget.objList,
          false,
        );
      };
    } else {
      textColor = Colors.white.withOpacity(0.5);
      backgroundColor = AppColorsHelper.firstColor.withOpacity(0.5);
    }

    return Container(
      width: double.infinity,
      padding: widget.horizontalPadding,
      child: HabiButton(
        text: 'addSlot'.tr,
        textColor: textColor,
        backgroundColor: backgroundColor,
        icon: SvgPicture.asset(
          AppImagePaths.add,
          width: 16,
          height: 16,
          colorFilter: ColorFilter.mode(
            textColor,
            BlendMode.srcIn,
          ),
        ),
        onPressed: onPressed,
      ),
    );
  }
}
