import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_mode_type.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/widgets/schedule_widgets/schedule_day_tab_bar.dart';

class ScheduleTabBar extends StatefulWidget {
  const ScheduleTabBar({
    super.key,
    this.scheduleType,
    this.isLoading = false,
    this.initialIndex,
    this.scheduleHeader,
    this.tabBarPadding = EdgeInsets.zero,
    required this.allDay,
    required this.weekDay,
    required this.daily,
  });

  final int? scheduleType;
  final bool isLoading;
  final int? initialIndex;
  final Widget? scheduleHeader;
  final List<Widget> allDay;
  final List<Widget> weekDay;
  final List<Widget> daily;
  final EdgeInsets tabBarPadding;

  // final Function(int value) onTap;
  // final Schedules schedules;

  @override
  State<ScheduleTabBar> createState() => _ScheduleTabBarState();
}

class _ScheduleTabBarState extends State<ScheduleTabBar>
    with TickerProviderStateMixin {
  late TabController tabController;
  late List<Widget> _tabs;
  late List<Widget> _tabPages;
  int initialIndex = 0;

  @override
  void initState() {
    log.i(
        '当前scheduleTpye：${widget.scheduleType}   当前Index: ${widget.initialIndex}');
    initialIndex = widget.initialIndex ?? 0;
    _initTabController();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _checkTabControllerLength();
    return _buildScheduleRow();
  }

  Widget _buildScheduleRow() {
    if (widget.isLoading == true) {
      return const Padding(
        padding: EdgeInsets.all(10.0),
        child: CircularProgressIndicator(
          color: AppColors.ff01A796,
        ),
      );
    }
    return _buildScheduleLayout();
  }

  Widget _buildScheduleLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: widget.tabBarPadding,
          child: _buildTabBar(),
        ),
        SizedBox(height: 15.h),
        Padding(
          padding: widget.tabBarPadding,
          child: _buildHeader(),
        ),
        SizedBox(height: 15.h),
        Expanded(
          child: Container(
            child: _buildTabBody(),
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return ScheduleDayTabBar(
      controller: tabController,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        final estimatedTabWidth = 40 * _tabs.length;
        final isScrollable = constraints.maxWidth < estimatedTabWidth;

        log.i(
            '_buildTabBar() -> maxWidth: ${constraints.maxWidth}, estimatedTabWidth: $estimatedTabWidth, isScrollable: $isScrollable');

        return TabBar(
          controller: tabController,
          tabs: _tabs,
          isScrollable: isScrollable,
          padding: EdgeInsets.zero,
          labelPadding: EdgeInsets.zero,
          labelColor:
              Theme.of(context).extension<AppThemeExtension>()!.firstColor,
          unselectedLabelColor: Theme.of(context)
              .extension<AppThemeExtension>()!
              .firstColor
              .withOpacity(0.4),
          indicatorColor: AppColors.ff01A796,
          indicatorWeight: 2,
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: AppColors.ff707070.withOpacity(0.5),
          dividerHeight: 2,
        );
      },
    );
  }

  Widget _buildTabBody() {
    return TabBarView(
      controller: tabController,
      children: _tabPages,
    );
  }

  void _initTabController() {
    initialIndex ??= 0;
    _tabs = getTabBarListByType(type: widget.scheduleType);
    _tabPages = getTabBarPagesList(len: _tabs.length);
    if (initialIndex > _tabs.length) {
      initialIndex = 0;
    }
    tabController = TabController(
      vsync: this,
      initialIndex: initialIndex,
      length: _tabs.length,
    );
  }

  void _checkTabControllerLength() {
    final int length = getTabBarListByType(type: widget.scheduleType).length;
    if (tabController.length != length) {
      tabController.dispose();
      _initTabController();
      setState(() {});
    }
  }

  List<Widget> getTabBarListByType({int? type}) {
    final int t = type ?? ScheduleConfigurationTypes.WORKWEEK;
    switch (t) {
      case ScheduleConfigurationTypes.FULLWEEK:
        return [Tab(text: 'mon-sun'.tr)];
      case ScheduleConfigurationTypes.WORKWEEK:
        return [
          Tab(text: 'mon-fri'.tr),
          Tab(text: 'sat-sun'.tr),
        ];
      case ScheduleConfigurationTypes.DAILY:
        return [
          Tab(text: 'sun'.tr),
          Tab(text: 'mon'.tr),
          Tab(text: 'tue'.tr),
          Tab(text: 'wed'.tr),
          Tab(text: 'thu'.tr),
          Tab(text: 'fri'.tr),
          Tab(text: 'sat'.tr),
        ];

      default:
        return [];
    }
  }

  List<Widget> getTabBarPagesList({required int len}) {
    log.i('当前选中的TabBar长度是：$len');
    List<Widget> tabPages = [];
    if (len == 0) {
      return [];
    }
    if (len == 1) {
      tabPages = widget.allDay;
    }
    if (len == 2) {
      tabPages = widget.weekDay;
    }
    if (len == 7) {
      tabPages = widget.daily;
    }
    return tabPages;
  }

  Widget _buildHeader() {
    return widget.scheduleHeader ?? Container();
  }
}
