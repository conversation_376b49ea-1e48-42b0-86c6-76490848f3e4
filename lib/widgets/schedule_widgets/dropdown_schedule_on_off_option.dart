import 'package:flutter/material.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';

class DropdownScheduleOnOffOption extends StatefulWidget {

  // "0000": Off;  "0001": "On"
  String initialValue;
  final Function(String value) onChanged;

  DropdownScheduleOnOffOption({
    super.key,
    required this.initialValue,
    required this.onChanged,
  });

  @override
  DropdownScheduleOnOffOptionState createState() => DropdownScheduleOnOffOptionState();
}

class DropdownScheduleOnOffOptionState extends State<DropdownScheduleOnOffOption> {

  final List<Map<String, String>> options = [
    {
      "value": "0000",
      "text": 'off'.tr
    },
    {
      "value": "0001",
      "text": 'on'.tr
    },
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(22.0),
        border: Border.all(
          color: AppColors.ff01A796, // 边框宽度
        ),
      ),
      child: _buildDropdown(),
    );
  }

  Widget _buildDropdown() {
    return DropdownButtonHideUnderline(
      child: DropdownButton<String>(
        value: widget.initialValue,
        icon: const Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Icon(
                Icons.arrow_drop_down,
                size: 36,
                color: AppColors.ff01A796,
              ),
            ],
          ),
        ),
        // isExpanded: false,
        alignment: AlignmentDirectional.centerStart,
        // elevation: 16,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: AppFontWeights.regular,
          color: AppColors.ff01A796,
        ),
        onChanged: (String? newValue) {
          setState(() {
            if (newValue != null){
              widget.initialValue = newValue;
              widget.onChanged(newValue);
            }
          });
        },
        items: options
            .map<DropdownMenuItem<String>>((Map<String, String> option) {
          return _buildMenuItem(option);
        }).toList(),
      ),
    );
  }

  DropdownMenuItem<String> _buildMenuItem(Map<String, String> option) {
    return DropdownMenuItem<String>(
      value: option["value"],
      child: Padding(
        padding: const EdgeInsets.only(left: 8),
        child: Text(
            option['text'] ?? 'off'.tr,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: AppFontWeights.regular,
              color: AppColors.ff01A796,
            )
        ),
      ),
    );
  }
}
