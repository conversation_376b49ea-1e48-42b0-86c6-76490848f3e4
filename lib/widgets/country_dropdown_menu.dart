import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/helpers/app_country_helper.dart';
import 'package:habi_app/models/app_country.dart';

class CountryDropdownMenu extends StatelessWidget {
  final ValueChanged<String> onChanged;
  final String selectedCountryCode;
  final double? width;
  final double? menuHeight;

  const CountryDropdownMenu({
    super.key,
    required this.onChanged,
    required this.selectedCountryCode,
    this.width,
    this.menuHeight,
  });

  Widget _getFlag(String countryCode) {
    return Image.asset(
      AppImagePaths.getCountryFlag(countryCode),
    );
  }

  Widget _getCircleFlag(String countryCode) {
    if (countryCode.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.grey.withOpacity(0.5),
          width: 2,
        ),
      ),
      child: ClipOval(
        child: Image.asset(
          AppImagePaths.getCountryFlag(countryCode),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final countries = AppCountryHelper.getCountryList();
    final selectedCountry = countries.firstWhereOrNull(
      (country) => country.abbr == selectedCountryCode,
    );

    return DropdownMenu<AppCountry>(
      key: ValueKey(selectedCountryCode),
      initialSelection: selectedCountry,
      dropdownMenuEntries: countries.map((country) {
        return DropdownMenuEntry<AppCountry>(
          value: country,
          label: country.fullName,
          leadingIcon: SizedBox(
            width: 26,
            height: 26,
            child: _getFlag(country.abbr),
          ),
        );
      }).toList(),
      width: width,
      menuHeight: menuHeight,
      leadingIcon: Container(
        padding: const EdgeInsets.all(4),
        height: 48,
        width: 48,
        child: _getCircleFlag(selectedCountry?.abbr ?? ''),
      ),
      onSelected: (AppCountry? country) {
        if (country != null) {
          onChanged(country.abbr);
        }
      },
    );
  }
}
