import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/helpers/app_colors_helper.dart';

class HabiSwitch extends StatelessWidget {
  final VoidCallback? onTap;
  final bool value;
  final bool isLoading;
  final double width;
  final double height;

  const HabiSwitch({
    this.onTap,
    this.value = false,
    this.isLoading = false,
    this.width = 40.0,
    this.height = 24.0,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        width: width,
        height: height,
        child: isLoading ? _buildLoading() : _buildSwitch(),
      ),
    );
  }

  Widget _buildSwitch() {
    return GestureDetector(
      onTap: onTap,
      child: Center(
        child: value
            ? SvgPicture.asset(
                AppImagePaths.switchOn,
                height: height,
                theme: const SvgTheme(currentColor: AppColors.ff01A796),
              )
            : SvgPicture.asset(
                AppImagePaths.switchOff,
                height: height,
                theme: SvgTheme(currentColor: AppColorsHelper.firstColor),
              ),
      ),
    );
  }

  Widget _buildLoading() {
    return Center(
      child: SizedBox.square(
        dimension: height,
        child: const CircularProgressIndicator(
          color: AppColors.ff01A796,
        ),
      ),
    );
  }
}
