import 'package:flutter/material.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class LoadingContent extends StatelessWidget {

  String? message;

  LoadingContent({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black.withOpacity(0.5),
        alignment: Alignment.center,
        child: Container(
          width: 145,
          height: 145,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Theme.of(context)
                .extension<AppThemeExtension>()!.secondColor,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
          ),
          child: message != null ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(
                color: AppColors.ff01A796,
              ),
              const SizedBox(height: 12),
              Material(
                color: Colors.transparent,
                child: Text(
                  message ?? '',
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: AppFontWeights.regular,
                    color: Theme.of(context)
                        .extension<AppThemeExtension>()!.firstColor,
                  ),
                ),
              ),
            ],
          ) : const CircularProgressIndicator(color: AppColors.ff01A796,),
        )
    );
  }
}
