import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Dial2Widget extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint backgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final Paint shadowPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);

    final Paint arcShadowPaint = Paint()
      ..color = Colors.grey.withOpacity(0.20) // 调整阴影颜色为更暗的灰色
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15) // 模糊半径
      ..strokeWidth = 35.w;

    final Paint arcPaint = Paint()
      ..color = Colors.white // 保持颜色为白色
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.w;

    final double radius = size.width / 2;
    final Offset center = Offset(size.width / 2, size.height / 2);

    // 绘制阴影
    canvas.drawCircle(center, radius, shadowPaint);

    // 绘制白色背景
    canvas.drawCircle(center, radius, backgroundPaint);

    // 绘制弧线阴影
    final Rect arcShadowRect = Rect.fromCircle(center: center, radius: radius - 15.w);
    // 绘制270度的弧线阴影
    canvas.drawArc(arcShadowRect, -pi / 2, pi * 3.0, false, arcShadowPaint);

    // 绘制弧线
    final Rect arcRect = Rect.fromCircle(center: center, radius: radius - 25.w);
    // 绘制270度的弧线
    canvas.drawArc(arcRect, -pi / 2, pi * 3.0, false, arcPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
