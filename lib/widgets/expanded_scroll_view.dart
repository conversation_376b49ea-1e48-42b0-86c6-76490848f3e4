import 'package:flutter/material.dart';

/// A widget that combines [SingleChildScrollView], [ConstrainedBox], and [IntrinsicHeight]
/// to create a scrollable view that expands to fill the screen on large displays
/// and allows scrolling on smaller screens.
///
/// The content is divided into two sections:
/// - [topContent]: The content at the top of the view
/// - [bottomContent]: The content that will stick to the bottom on large screens
class ExpandedScrollView extends StatelessWidget {
  /// The content to display at the top of the view
  final Widget topContent;

  /// The content to display at the bottom of the view
  final Widget bottomContent;

  /// The padding around the entire content
  final EdgeInsets? padding;

  /// Whether to use [SafeArea] for the content
  final bool useSafeArea;

  const ExpandedScrollView({
    super.key,
    required this.topContent,
    required this.bottomContent,
    this.padding,
    this.useSafeArea = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: Padding(
              padding: padding ?? EdgeInsets.zero,
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    topContent,
                    const Spacer(),
                    bottomContent,
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );

    if (useSafeArea) {
      content = SafeArea(child: content);
    }

    return content;
  }
}
