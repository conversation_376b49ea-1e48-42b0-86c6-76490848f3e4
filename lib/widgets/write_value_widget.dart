import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class WriteValueWidget extends StatefulWidget {

  final Map<String, String>? writeMap;

  const WriteValueWidget({super.key, this.writeMap});

  @override
  State<StatefulWidget> createState() {
    return _WriteValueWidgetState();
  }
}

class _WriteValueWidgetState extends State<WriteValueWidget> {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController nodeIdController = TextEditingController(text: 'DEDEDEDE');
  final TextEditingController endpointController = TextEditingController(text: '1');
  final TextEditingController clusterIdController = TextEditingController(text: '513');
  final TextEditingController attributeIdController = TextEditingController(text: '28');
  final TextEditingController valueController = TextEditingController(text: '3');
  final List<String> valueTypeList = [
    'UnsignedInteger',
    'SignedInteger',
    'Boolean',
    'String',
    'Float',
    'Double',
    'json',
    'ByteArray',
  ];
  String valueType = 'String';

  @override
  void initState() {
    super.initState();

    if (widget.writeMap != null) {
      nodeIdController.text = widget.writeMap!['nodeId'] ?? 'DEDEDEDE';
      endpointController.text = widget.writeMap!['endpointId'] ?? '1';
      clusterIdController.text = widget.writeMap!['clusterId'] ?? '513';
      attributeIdController.text = widget.writeMap!['attributeId'] ?? '28';
      valueController.text = widget.writeMap!['value'] ?? '3';
      valueType = widget.writeMap!['type'] ?? 'String';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Custom Write Value',
          style: TextStyle(
              fontSize: 21,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor
          )
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Form(
                key: formKey,
                child: Column(
                  children: [
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'nodeId'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                      const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: nodeIdController,
                        keyboardType: TextInputType.name,
                        validator: _validateValue,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'endpoint'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: endpointController,
                        keyboardType: TextInputType.name,
                        validator: _validateValue,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'cluster'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: clusterIdController,
                        keyboardType: TextInputType.name,
                        validator: _validateValue,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'attribute'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                      const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: attributeIdController,
                        keyboardType: TextInputType.name,
                        validator: _validateValue,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'valueType'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
        
                    DropdownButton(
                      value: valueType,
                      items: valueTypeList.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          valueType = newValue!;
                        });
                      },
                    ),
        
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'value'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                      const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: valueController,
                        keyboardType: TextInputType.name,
                        validator: _validateValue,
                      ),
                    ),
                  ],
                )
            )
          ],
        ),
      ),
      actions: <Widget>[
        TextButton(
          child: Text('cancel'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            Get.back();
          },
        ),
        TextButton(
          child: Text('confirm'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: ()  async {
            bool validate = formKey.currentState?.validate() ?? false;
            if (!validate) {
              return;
            }
            Get.back(result: {
              'nodeId': nodeIdController.text,
              'endpointId': endpointController.text,
              'clusterId': clusterIdController.text,
              'attributeId': attributeIdController.text,
              'type': valueType,
              'value': valueController.text
            });
          },
        ),
      ],
    );
  }

  String? _validateValue(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    return null;
  }

}
