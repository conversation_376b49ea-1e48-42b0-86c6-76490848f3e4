import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/constants/app_image_paths.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class HabiAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String titleText;
  final void Function()? onBackPressed;

  const HabiAppBar({
    super.key,
    required this.titleText,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        titleText,
        style: TextStyle(
          fontSize: 14,
          fontWeight: AppFontWeights.regular,
          color: Theme.of(context).extension<AppThemeExtension>()!.firstColor,
        ),
      ),
      leading: IconButton(
        icon: SvgPicture.asset(
          AppImagePaths.backwardArrow,
          width: 26,
          height: 26,
        ),
        onPressed: () {
          if (onBackPressed != null) {
            onBackPressed!();
            return;
          }
          Get.back();
        },
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
