import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';
import 'package:habi_app/logger/custom_logger.dart';
import 'package:habi_app/services/global_service.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';

class DeviceOTAWidget extends StatefulWidget {

  final String subId;
  final String thingName;
  void Function()? onFinish;

  DeviceOTAWidget({
    super.key,
    required this.subId,
    required this.thingName,
    this.onFinish
  });

  @override
  State<StatefulWidget> createState() {
    return _DeviceOTAWidgetState();
  }

}

// 0 未知 当前状态尚未确定。
// 1 空闲 表示节点尚未进行软件更新。
// 2 正在查询 表示节点正在查询 OTA 提供商。
// 3 DelayedOnQuery 表示节点正在等待。
// 4 正在下载 表示节点正在下载。
// 5 正在应用 表示当前正在应用的节点。
// 6 DelayedOnApply 表示正在等待的节点
// 7 RollingBack 表示节点正在恢复到以前的版本。
// 8 DelayedOnUserConsent 延迟用户同意
class _DeviceOTAWidgetState extends State<DeviceOTAWidget> {

  static const String otaStatusKey = 'ep0:sOTA:OTAStatus_d';
  final _globalService = GlobalService.to;
  late ValueNotifier<int> _valueNotifier;
  StreamSubscription? _shadowSubscription;

  @override
  void initState() {
    super.initState();
    _valueNotifier = ValueNotifier(25);
    _shadowSubscription = _globalService.getMqttStream()
        .listen((shadow) async {
      if (shadow.thingName == widget.thingName) {
        Map<String, dynamic>? reportedMap = shadow.thingShadow['state']?['reported'];
        if (reportedMap == null) {
          return;
        }

        Map<String, dynamic>? propertiesMap = reportedMap[widget.subId]?['properties'];
        if (propertiesMap == null) {
          return;
        }

        if (propertiesMap.containsKey(otaStatusKey)) {
          log.i('收到 OTAStatus 状态变化');
          int otaStatusValue = propertiesMap[otaStatusKey];
          if (otaStatusValue == 2) {
            _valueNotifier.value = 35;
          } else if (otaStatusValue == 4) {
            _valueNotifier.value = 55;
          } else if (otaStatusValue == 0) {
            _valueNotifier.value = 100;
            Future.delayed(const Duration(milliseconds: 500), () {
              widget.onFinish?.call();
            });
          }
        }
      }
    });
  }

  @override
  void dispose() {
    _valueNotifier.dispose();
    _shadowSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        height: 500.h,
        alignment: Alignment.center,
        margin: const EdgeInsets.symmetric(horizontal: 30),
        decoration: BoxDecoration(
          color: Theme.of(context)
              .extension<AppThemeExtension>()!.secondColor,
          borderRadius: const BorderRadius.all(
            Radius.circular(28.0),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'updatingInProgress'.tr,
              style: TextStyle(
                fontSize: 18,
                fontWeight: AppFontWeights.medium,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 20,),

            Text(
              'pleaseAllowUpTo15Minutes'.tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 20,),

            ValueListenableBuilder(
                valueListenable: _valueNotifier,
                builder: (context, value, child) {
                  return CircularStepProgressIndicator(
                    totalSteps: 100,
                    currentStep: value,
                    selectedStepSize: 8,
                    stepSize: 2,
                    selectedColor: AppColors.ff01A796,
                    unselectedColor: Colors.grey,
                    padding: 0,
                    width: 135,
                    height: 135,
                    child: Center(
                      child: Text(
                        'upgrading'.tr,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: AppColors.ff868788,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }
            ),

            const SizedBox(height: 20,),

            Text(
              'updatingFirmware'.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: AppFontWeights.regular,
                color: Theme.of(context)
                    .extension<AppThemeExtension>()!.firstColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        )
    );
  }

}