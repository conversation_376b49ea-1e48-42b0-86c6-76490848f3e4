import 'dart:math';
import 'package:flutter/material.dart';

class SliderTicksWidget extends CustomPainter {
  final int tickCount; // 新增参数：刻度线总数
  final double tickLengthRatio;
  final double tickOffsetRatio; // 新增：外部偏移比例，默认为 0.05
  final Color color;
  final int highlightIndex; // 要高亮的刻度索引，可空

  SliderTicksWidget({
    this.tickCount = 90, // 默认70条（原60条+10条）
    this.tickLengthRatio = 0.95,
    this.tickOffsetRatio = 0.01, // 默认偏移比例
    this.color = Colors.black,
    this.highlightIndex = 0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double radius = size.width / 2;
    final Offset center = Offset(size.width / 2, size.height / 2);
    final double angleStep = 360 / tickCount * (pi / 180); // 每个刻度角度

    for (int i = 0; i < tickCount; i++) {
      final double angle = i * angleStep;

      // 默认长度 & 线宽
      double lengthMultiplier = 1.0;
      // 默认无偏移
      double offsetMultiplier = 0.0;

      if (i == highlightIndex) {
        lengthMultiplier = 2.0;
        offsetMultiplier = 0.03;
      } else if ((i - highlightIndex).abs() < 5) { // 判断高亮刻度周围4个刻度
        double diff = (i - highlightIndex).toDouble().abs(); // 取绝对值，统一处理上下侧
        lengthMultiplier = 2.0 - (diff * 0.2);
        offsetMultiplier = tickOffsetRatio * (4 - diff);

        // debugPrint('i: $i');
        // debugPrint('diff: $diff');
        // debugPrint('lengthMultiplier: $lengthMultiplier');
        // debugPrint('offsetMultiplier: $offsetMultiplier');
      }

      final double startRadius = radius * tickLengthRatio;
      final double endRadius = radius;
      final double actualLength = (endRadius - startRadius) * lengthMultiplier;
      final double drawStartRadius = startRadius + offsetMultiplier * radius;
      final double drawEndRadius = drawStartRadius + actualLength;

      final Offset start = Offset(
        center.dx + drawStartRadius * cos(angle),
        center.dy + drawStartRadius * sin(angle),
      );

      final Offset end = Offset(
        center.dx + drawEndRadius * cos(angle),
        center.dy + drawEndRadius * sin(angle),
      );

      final Paint paint = Paint()
        ..color = color
        ..strokeWidth = 1.0;

      canvas.drawLine(start, end, paint);
    }
  }

  @override
  bool shouldRepaint(covariant SliderTicksWidget oldDelegate) {
    return oldDelegate.highlightIndex != highlightIndex ||
        oldDelegate.tickCount != tickCount ||
        oldDelegate.color != color ||
        oldDelegate.tickLengthRatio != tickLengthRatio;
  }
}