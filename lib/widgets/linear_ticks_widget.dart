import 'package:flutter/material.dart';

class LinearTicksWidget extends CustomPainter {
  final int tickCount;
  final int highlightedIndex;
  final Color color;

  LinearTicksWidget({
    this.tickCount = 50,
    this.highlightedIndex = 0,
    this.color = Colors.black,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint linePaint = Paint()
      ..color = color
      ..strokeWidth = 1;

    double baseY = size.height / 2;
    double tickSpacing = size.width / (tickCount - 1);

    for (int i = 0; i < tickCount; i++) {
      double tickLength = 10.0;
      double offsetY = 0.0;

      if (i == highlightedIndex) {
        tickLength *= 2.0;
        offsetY = 6.0;
      } else if (i == highlightedIndex - 1 || i == highlightedIndex + 1) {
        tickLength *= 1.6;
        offsetY = 4.0;
      } else if (i == highlightedIndex - 2 || i == highlightedIndex + 2) {
        tickLength *= 1.5;
        offsetY = 2.0;
      }

      final double x = i * tickSpacing;
      final Offset start = Offset(x, baseY - offsetY);
      final Offset end = Offset(x, baseY - tickLength - offsetY);

      canvas.drawLine(start, end, linePaint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
