import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:habi_app/constants/app_colors.dart';
import 'package:habi_app/constants/app_font_weights.dart';
import 'package:habi_app/extensions/app_theme_extension.dart';

class EnterSsidAndPasswordWidget extends StatefulWidget {

  final String ssid;
  final String pwd;
  final bool obscureText;

  const EnterSsidAndPasswordWidget({
    super.key,
    required this.ssid,
    required this.pwd,
    required this.obscureText
  });

  @override
  State<StatefulWidget> createState() {
    return _EnterSsidAndPasswordWidgetState();
  }
}

class _EnterSsidAndPasswordWidgetState extends State<EnterSsidAndPasswordWidget> {

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController ssidController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  bool obscureText = false;

  @override
  void initState() {
    super.initState();
    ssidController.text = widget.ssid;
    passwordController.text = widget.pwd;
    obscureText = widget.obscureText;
  }

  @override
  void dispose() {
    ssidController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Please enter ssid and password',
          style: TextStyle(
              fontSize: 21,
              fontWeight: AppFontWeights.regular,
              color: Theme.of(context)
                  .extension<AppThemeExtension>()!
                  .firstColor
          )
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Form(
                key: formKey,
                child: Column(
                  children: [
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(maxHeight: 148, minHeight: 48),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!
                              .firstColor,
                        ),
                        controller: ssidController,
                        keyboardType: TextInputType.name,
                        validator: _validateSSID,
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: Opacity(
                        opacity: 0.5,
                        child: Text(
                          'password'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: AppFontWeights.medium,
                            color: Theme.of(context)
                                .extension<AppThemeExtension>()!
                                .firstColor,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 14.h,
                    ),
                    ConstrainedBox(
                      constraints: const BoxConstraints(
                          maxHeight: 148,
                          minHeight: 48
                      ),
                      child: TextFormField(
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: AppFontWeights.regular,
                          color: Theme.of(context)
                              .extension<AppThemeExtension>()!.firstColor,
                        ),
                        decoration: InputDecoration(
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.visibility),
                            onPressed: () {
                              setState(() {
                                obscureText = !obscureText;
                              });
                            },
                          ),
                        ),
                        controller: passwordController,
                        obscureText: obscureText,
                        validator: _validatePassword,
                      ),
                    ),
                  ],
                )
            )
          ],
        ),
      ),
      actions: <Widget>[
        TextButton(
          child: Text('cancel'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: () {
            Get.back();
          },
        ),
        TextButton(
          child: Text('confirm'.tr, style: const TextStyle(color: AppColors.ff01A796)),
          onPressed: ()  async {
            bool validate = formKey.currentState?.validate() ?? false;
            if (!validate) {
              return;
            }

            Get.back(result: {
              'ssid': ssidController.text,
              'password': passwordController.text
            });
          },
        ),
      ],
    );
  }

  String? _validateSSID(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'requiredField'.tr;
    }
    return null;
  }
}
