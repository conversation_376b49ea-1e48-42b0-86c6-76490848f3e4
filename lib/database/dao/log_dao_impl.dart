import 'package:habi_app/database/dao/log_dao.dart';
import 'package:habi_app/database/table/log_table.dart';
import 'package:habi_app/models/log_message.dart';
import 'package:sqflite/sqflite.dart';

class LogDaoImpl implements LogDao {

  final Database db;

  LogDaoImpl(this.db);

  @override
  Future<List<LogMessage>> queryAllLogs(int limit, int offset) async {
    final List<Map<String, Object?>> result = await db.query(
      LogTable.name,
      columns: [
        LogTable.id,
        LogTable.level,
        LogTable.message,
        LogTable.time,
      ],
      limit: limit,
      offset: offset,
    );
    return result.map((item) => LogMessage.fromMap(item)).toList();
  }

  @override
  Future<int> deleteAllLogs() async {
    return await db.delete(LogTable.name);
  }

  @override
  Future<int> insertLog(LogMessage logMessage) async {
    return await db.insert(LogTable.name, logMessage.toMap());
  }

}