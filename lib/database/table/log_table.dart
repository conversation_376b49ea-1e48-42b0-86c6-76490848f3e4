abstract class LogTable {
  static const String name = "hb_log";
  static const String id = "id";
  static const String level = "level";
  static const String message = "message";
  static const String time = "time";
  static const String createSQL =
      'CREATE TABLE ${LogTable.name}'
      '(${LogTable.id} Text PRIMARY KEY, '
      '${LogTable.level} TEXT, '
      '${LogTable.message} TEXT, '
      '${LogTable.time} TEXT)';
}