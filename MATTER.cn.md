# Matter源码改动

## src/lib/core/CHIPConfig.h
-#define CHIP_CONFIG_MINMDNS_MAX_PARALLEL_RESOLVES 2
+#define CHIP_CONFIG_MINMDNS_MAX_PARALLEL_RESOLVES 15

-#define CHIP_CONFIG_MDNS_RESOLVE_LOOKUP_RESULTS 1
+#define CHIP_CONFIG_MDNS_RESOLVE_LOOKUP_RESULTS 15

-#define CHIP_CONFIG_ADDRESS_RESOLVE_MIN_LOOKUP_TIME_MS 200
+#define CHIP_CONFIG_ADDRESS_RESOLVE_MIN_LOOKUP_TIME_MS 2000

-#define CHIP_CONFIG_ADDRESS_RESOLVE_MAX_LOOKUP_TIME_MS 45000
+#define CHIP_CONFIG_ADDRESS_RESOLVE_MAX_LOOKUP_TIME_MS 90000

## src/platform/Linux/SystemPlatformConfig.h
-#define CHIP_CONFIG_MDNS_RESOLVE_LOOKUP_RESULTS 5
+#define CHIP_CONFIG_MDNS_RESOLVE_LOOKUP_RESULTS 15

## src/platform/Darwin/SystemPlatformConfig.h
-#define CHIP_CONFIG_MDNS_RESOLVE_LOOKUP_RESULTS 5
+#define CHIP_CONFIG_MDNS_RESOLVE_LOOKUP_RESULTS 15




