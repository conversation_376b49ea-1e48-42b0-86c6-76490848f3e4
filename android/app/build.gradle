plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

android {
    namespace = "com.habi"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.habi.eu.smarthome"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk = 28
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
    }

    flavorDimensions = ["app"]
    productFlavors {
        dev {
            dimension "app"
            applicationId "com.habi.dev.smarthome"
            versionCode = flutterVersionCode.toInteger()
            versionName = flutterVersionName
            resValue "string", "app_name", "habi Smart Home(Dev)"
        }
        prod {
            dimension "app"
            applicationId "com.habi.eu.smarthome"
            versionCode = flutterVersionCode.toInteger()
            versionName = flutterVersionName
            resValue "string", "app_name", "habi Smart Home"
        }
        eu {
            dimension "app"
            applicationId "com.habi.eu.smarthome"
            versionCode = flutterVersionCode.toInteger()
            versionName = flutterVersionName
            resValue "string", "app_name", "habi Smart Home(Eu)"
        }
        staging {
            dimension "app"
            applicationId "com.habi.staging.smarthome"
            versionCode = flutterVersionCode.toInteger()
            versionName = flutterVersionName
            resValue "string", "app_name", "habi Smart Home(Staging)"
        }
        us {
            dimension "app"
            applicationId "com.habi.us.smarthome"
            versionCode = flutterVersionCode.toInteger()
            versionName = flutterVersionName
            resValue "string", "app_name", "habi Smart Home(Us)"
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
        }
        debug {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
        }
    }

}

flutter {
    source = "../.."
}

dependencies {
}