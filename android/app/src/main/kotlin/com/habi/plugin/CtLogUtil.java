package com.habi.plugin;

import android.content.Context;
import android.util.Log;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class CtLogUtil {
    private static final String DATE_FORMAT = "yyyy-MM-dd_HH-mm-ss";
    private static final String TAG = "CtLogUtil";

    private static File logFileDir(Context context) {
        // 获取内部存储路径：/data/data/<package>/files/
        File baseDir = context.getFilesDir();
        // 创建 log 子目录
        File logDir = new File(baseDir, "ct_log");
        if (!logDir.exists()) {
            if (logDir.mkdir()) {
                Log.d(TAG, "ct_log目录创建成功");
            } else {
                Log.e(TAG, "ct_log目录创建失败");
            }
        }
        return logDir;
    }

    public static File getLogFile(Context context) {
        File dir = logFileDir(context);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(
                DATE_FORMAT, Locale.getDefault());
        String fileName = "log_" + ZonedDateTime.now().format(formatter) + ".txt";
        return new File(dir, fileName);
    }

    public static File getZipFile(Context context) {
        File dir = logFileDir(context);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(
                DATE_FORMAT, Locale.getDefault());
        String fileName = "ct_logs_" + ZonedDateTime.now().format(formatter) + ".zip";
        return new File(dir, fileName);
    }

    public static int zipLogFile(String sourcePath, String zipPath) {
        try {
            CtZipUtils.zip(sourcePath, zipPath);
            return 0;
        } catch (IOException e) {
            e.printStackTrace();
            return -1;
        }
    }

    public static int writeLog(File file) {
        try {
            String cmd = "logcat -b main -b crash -d -v time *:V";
            Process process = Runtime.getRuntime().exec(cmd);
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8));
            StringBuilder log = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                log.append(line).append("\n");
            }

            // Clear logs
            new ProcessBuilder().command("logcat", "-c").redirectErrorStream(true).start();

            // Write to file
            try (FileWriter writer = new FileWriter(file, false)) {
                writer.write(log.toString());
            }

            return 0;
        } catch (Exception e) {
            Log.e(TAG, "写入日志失败", e);
            return -1;
        }
    }


}
