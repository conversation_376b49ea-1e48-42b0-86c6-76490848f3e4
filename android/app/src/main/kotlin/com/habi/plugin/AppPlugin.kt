package com.habi.plugin

import android.content.Context
import android.util.Log
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class AppPlugin : FlutterPlugin, ActivityAware, MethodCallHandler {

    companion object {
        private const val TAG = "AppPlugin"
        private const val METHOD_CHANNEL_NAME = "ct_app_plugin"
    }

    private var context: Context? = null
    private var channel : MethodChannel? = null
    private var scope: CoroutineScope? = null

    override fun onAttachedToEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        context = binding.applicationContext
        scope = CoroutineScope(Job() + Dispatchers.Default)
        channel = MethodChannel(binding.binaryMessenger, METHOD_CHANNEL_NAME)
        channel?.setMethodCallHandler(this)
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
    }

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    }

    override fun onDetachedFromActivityForConfigChanges() {
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
    }

    override fun onDetachedFromActivity() {
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "writeLog" -> writeLog(call, result)
            else -> result.notImplemented()
        }
    }

    private fun writeLog(call: MethodCall, result: Result) {
        scope?.launch {
            try {
                val logFile = CtLogUtil.getLogFile(context)
                val writeLogResult = CtLogUtil.writeLog(logFile)
                if (writeLogResult == 0) {
                    Log.i(TAG, "Success to write log.")
                    val zipFile = CtLogUtil.getZipFile(context)
                    val zipResult = CtLogUtil.zipLogFile(logFile.absolutePath, zipFile.absolutePath)
                    if (zipResult == 0) {
                        Log.i(TAG, "Success to zip log.")
                        result.success(zipFile.absolutePath)
                    } else {
                        result.error("-1", "Failed to zip log.", null)
                    }
                } else {
                    result.error("-1", "Failed to write log.", null)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Log.e(TAG, "Failed to write log.")
                result.error("-1", e.message, null)
            }
        }
    }
}