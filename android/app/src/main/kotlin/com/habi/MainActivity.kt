package com.habi

import android.util.Log
import com.habi.plugin.AppPlugin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity: FlutterActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        try {
            flutterEngine.plugins.add(AppPlugin())
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin AppPlugin", e)
        }
    }

}
