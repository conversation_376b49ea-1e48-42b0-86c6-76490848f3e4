{"project_info": {"project_number": "205941854005", "project_id": "habi-smart-home", "storage_bucket": "habi-smart-home.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:205941854005:android:134d2e628b099980d0467c", "android_client_info": {"package_name": "com.habi.dev.smarthome"}}, "oauth_client": [{"client_id": "205941854005-26n4m6hofu06btmhr4lc7rsvah69acg5.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAXb-Bo6szyGf3z3CHgs0-JcLcP9PSqM_s"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "205941854005-26n4m6hofu06btmhr4lc7rsvah69acg5.apps.googleusercontent.com", "client_type": 3}]}}}, {"client_info": {"mobilesdk_app_id": "1:205941854005:android:4c860ae342478518d0467c", "android_client_info": {"package_name": "com.habi.eu.smarthome"}}, "oauth_client": [{"client_id": "205941854005-26n4m6hofu06btmhr4lc7rsvah69acg5.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAXb-Bo6szyGf3z3CHgs0-JcLcP9PSqM_s"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "205941854005-26n4m6hofu06btmhr4lc7rsvah69acg5.apps.googleusercontent.com", "client_type": 3}]}}}], "configuration_version": "1"}