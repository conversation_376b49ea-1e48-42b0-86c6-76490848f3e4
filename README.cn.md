# habi_app

This project is the habi app from computime.

## 项目配置

目前项目有dev、eu、staging、us四个环境，需要配置好环境才能运行。

## 项目打包

- dev环境
flutter build apk --flavor dev -t lib/main_dev.dart --no-shrink
flutter build appbundle --flavor dev -t lib/main_dev.dart --no-shrink

- prod环境
flutter build apk --flavor prod -t lib/main_prod.dart --no-shrink
flutter build appbundle --flavor prod -t lib/main_prod.dart --no-shrink

## 打包注意事项
- 要切换到main分支（工程的子模块也要检查下，看有没有切换到main分支）
- pubspec.yaml改版本号
- about this app页面改日期

- iOS先用Flutter run一遍，看是否能正常跑通
- iOS然后检查一下编译Device，是不是选的Any ARM64 Device
- iOS然后检查一下Archive，防止打成Debug-prod的包，正常应该打成Release-prod的包

- 最后填写release notes

## 项目结构

- assets
  
  * animations(动画)
  * config(配置文件)
  * fonts(字体文件)
  * images(图片)
  * translations(国际化)

- lib

  * constants(公共静态常量)
  * database(数据库)
  * delegate(代理)
  * extensions(扩展类)
  * helpers(工具类)
  * i18n(国际化)
  * logger(日志)
  * models(数据模型)
  * pages(页面)
  * services(getx全局性的业务逻辑、持久化数据、全局状态管理。
    
             注：请谨慎使用，它们在整个应用程序生命周期中是单例存在的，它们不会随着页面或 widget 的销毁而销毁。)
  * widgets(公共UI组件)
  * initial_bindings(getx依赖注入，注：在项目时，会自动注入)
  * main_dev.dart(dev环境)
  * main_eu.dart(eu环境)
  * main_staging.dart(staging环境)
  * main_us.dart(us环境)
  
- local_packages(依赖的本地库，使用git子模块管理)


## 代码规范

1. 变量名、方法、参数名都应该是小写开头的驼峰命名法
2. 文件名: 小写+下划线
3. 类型名(类名,函数类型名):大写开头驼峰
4. 导包as后的名称为小写+下划线
5. 不要用匈牙利命名法中的kXXXX 这样的命名方式,应该去掉k
6. 超过两位的英文缩写一律按该单词为普通小写单词处理,使用小写
7. 单行字符建议不要超过80个

详见Dart编程规范：https://dart.dev/effective-dart/style

## Matter协议

CSA官网：https://csa-iot.org/all-solutions/matter/
官方源码仓库：https://github.com/project-chip/connectedhomeip
Android文档：https://project-chip.github.io/connectedhomeip-doc/guides/android_building.html
iOS文档：https://developer.apple.com/documentation/matter/mtrbasedevice

## 项目资料

文档：https://salusinc.atlassian.net/wiki/spaces/AIP/pages/3441393691/habi+App
UI设计：https://xd.adobe.com/view/4d5c71e7-085d-4948-89a2-4f1aae474ded-180f/grid

## APP主题

目前app主题有2种：light 和 dark，编写UI页面的需要适配2种主题

## APP屏幕适配

- 注：Habi的设计稿是尺寸是414*896。

- 项目采用flutter_screenutil去做屏幕适配。

- flutter_screenutil是一个适配屏幕尺寸和屏幕密度的 Flutter 插件。它可以帮助开发者确保 Flutter 应用在不同屏幕尺寸和分辨率的设备上都能保持合理的布局和尺寸。当开发跨多种设备（如不同尺寸的智能手机和平板电脑）的Flutter应用时尤其有用。

- 详细使用：https://pub.dev/packages/flutter_screenutil

