name: dart

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '11'
      - uses: subosito/flutter-action@v2.10.0
      - name: Version
        run: flutter doctor -v
      - name: Install dependencies
        run: flutter pub get
      - name: Format
        run: dart format --set-exit-if-changed .
      - name: Linter
        run: flutter analyze
