group 'net.touchcapture.qr.flutterqr'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdk 33

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        // minSdkVersion is determined by Native View.
        minSdkVersion 20
        targetSdkVersion 33
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true
        // Sets Java compatibility to Java 11
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    if (project.android.hasProperty('namespace')) {
        namespace 'net.touchcapture.qr.flutterqr'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation('com.journeyapps:zxing-android-embedded:4.3.0') { transitive = false }
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.zxing:core:3.5.2'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
}
