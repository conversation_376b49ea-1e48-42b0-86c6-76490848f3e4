#include "include/ct_flutter_ble_plugin/ct_flutter_ble_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "ct_flutter_ble_plugin.h"

void CtFlutterBlePluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  ct_flutter_ble_plugin::CtFlutterBlePlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
