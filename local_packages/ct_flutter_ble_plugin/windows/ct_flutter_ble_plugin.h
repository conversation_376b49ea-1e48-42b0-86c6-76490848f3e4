#ifndef FLUTTER_PLUGIN_CT_FLUTTER_BLE_PLUGIN_H_
#define FLUTTER_PLUGIN_CT_FLUTTER_BLE_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace ct_flutter_ble_plugin {

class CtFlutterBlePlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  CtFlutterBlePlugin();

  virtual ~CtFlutterBlePlugin();

  // Disallow copy and assign.
  CtFlutterBlePlugin(const CtFlutterBlePlugin&) = delete;
  CtFlutterBlePlugin& operator=(const CtFlutterBlePlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace ct_flutter_ble_plugin

#endif  // FLUTTER_PLUGIN_CT_FLUTTER_BLE_PLUGIN_H_
