import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:hex/hex.dart';
import 'package:pointycastle/export.dart';

class BleAesCcm {

  // 连接蓝牙设备后，将 writeCount 置 0，并修改 macOrEuid，aesKey，needEnDecrypt
  int writeCount = 0;
  String macOrEuid = "001E5E074628";
  List<int> aesKey = [];
  bool needEnDecrypt = false;

  /// AES-CCM 加密，返回密文 + TAG
  // Sanny: Payload数据约定为：加密的数据+Tag(8bytes)+nonce(12bytes)
  // 用 ccm.process 加密后的 Tag 位于加密结果的末尾。所以 decryptCCMS 返回的数据是：解密结果 +nonce(12bytes)
  Uint8List encryptCCMS({
    // required Uint8List key,
    // required Uint8List nonce,
    required Uint8List plaintext,
    int macSize = 8, // TAG长度，CCMP/WPA2 通常为8字节
  }) {
    Uint8List key = aesKey.isEmpty ? getKey() : Uint8List.fromList(aesKey);
    if (aesKey.isEmpty) {
      aesKey = key.toList();
    }

    print("encryptCCMS key: $key");

    Uint8List nonce = genNonce();
    final ccm = CCMBlockCipher(AESEngine());
    final params =
        AEADParameters(KeyParameter(key), macSize * 8, nonce, Uint8List(0));
    ccm.init(true, params); // true=加密
    // 加密数据（包含TAG）
    Uint8List ccmResult = ccm.process(plaintext);

    return mergeUint8Lists([ccmResult, nonce]);
  }

  /// AES-CCM 解密，验证 TAG 正确性
  Uint8List decryptCCMS({
    // required Uint8List key,
    // required Uint8List nonce,
    required Uint8List ciphertextWithTagAndNonce,
    int macSize = 8,
  }) {
    try{
      if (ciphertextWithTagAndNonce.length < 9) {
        throw ArgumentError(
            'AES CCMP - The received data length should longer than 8');
      }
      Uint8List key = aesKey.isEmpty ? getKey() : Uint8List.fromList(aesKey);
      if (aesKey.isEmpty) {
        aesKey = key.toList();
      }

      print("decryptCCMS key: $key");

      Uint8List nonce = ciphertextWithTagAndNonce
          .sublist(ciphertextWithTagAndNonce.length - 8);
      Uint8List ciphertextWithTag = ciphertextWithTagAndNonce.sublist(
          0, ciphertextWithTagAndNonce.length - 8);
      final ccm = CCMBlockCipher(AESEngine());
      final params =
      AEADParameters(KeyParameter(key), macSize * 8, nonce, Uint8List(0));
      ccm.init(false, params);

      // 解密并验证 TAG，TAG不对会抛异常
      return ccm.process(ciphertextWithTag);
    }catch(e){
      print("AES CCMP - Decrypt fail:  ${e.toString()}");
    }
    return Uint8List.fromList(Uint8List.fromList(utf8.encode("decrypt fail")));
  }

  /* Sanny:
       char key[32] = {
       xx, xx, xx, xx, xx, xx, xx, xx,
       0x39, 0x61, 0x34, 0x62, 0x61, 0x31, 0x39, 0x30,
       0x61, 0x63, 0x32, 0x62, 0x35, 0x31, 0x33, 0x39,
       0x62, 0x33, 0x32, 0x63, 0x33, 0x35, 0x32, 0x38
        };
      UG800 gw项目的 key:   wifi mac(6bytes)中间补2bytes(0x09 0x02）后成为8bytes 去替换上面key[32]的前8bytes
      例如： UG800 gw的wifi mac为：001E5E070CB0(6bytes), 补2bytes(0x09 0x0) => 001E5E0902070CB0, 则其key为如下
            char key[32] = {
       0x00, 0x1e, 0x5e, 0x09, 0x02, 0x07, 0x0c, 0xb0,
       0x39, 0x61, 0x34, 0x62, 0x61, 0x31, 0x39, 0x30,
       0x61, 0x63, 0x32, 0x62, 0x35, 0x31, 0x33, 0x39,
       0x62, 0x33, 0x32, 0x63, 0x33, 0x35, 0x32, 0x38
        };
      其它项目的key: zigbee mac(8bytes)直接替换上面key[32]的前8bytes
     */
  Uint8List getKey() {
    final fixedPart = [0x39, 0x61, 0x34, 0x62, 0x61, 0x31, 0x39, 0x30,
      0x61, 0x63, 0x32, 0x62, 0x35, 0x31, 0x33, 0x39,
      0x62, 0x33, 0x32, 0x63, 0x33, 0x35, 0x32, 0x38];
    // HEX.decode() 返回的是 Uint8List，而不是 List<int>
    List<int> euidList = HEX.decode(macOrEuid).toList();
    if (euidList.length == 6) {
      euidList.insertAll(3, [0x09, 0x02]);
    }
    euidList.insertAll(euidList.length, fixedPart);
    List<int> euidKey = euidList;
    print("AES CCMP - key: ${euidKey.length}");
    print(euidKey
        .map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}')
        .toList());

    return Uint8List.fromList(euidKey);
  }

  Uint8List genNonce() {
    Uint8List ranBytes = generateRandomBytes();
    Uint8List countList = getCount();
    Uint8List time = getTime();
    Uint8List nonce = mergeUint8Lists([ranBytes, countList, time]);
    return nonce;
  }

  Uint8List mergeUint8Lists(List<Uint8List> lists) {
    final bytesBuilder = BytesBuilder();
    for (final list in lists) {
      bytesBuilder.add(list);
    }
    return bytesBuilder.toBytes();
  }

  Uint8List generateRandomBytes({int length = 3}) {
    final random = Random.secure(); // 加密安全的随机数生成器
    final bytes = Uint8List(length);
    for (var i = 0; i < length; i++) {
      bytes[i] = random.nextInt(256); // 生成 0-255 的随机字节
    }
    return bytes;
  }

  // Sanny: 计算器目前 GW 和 App 分别独立计算
  // Uint8List getCount(Uint8List count) {
  //   int counter = uint8ListToInt32(count);
  //   counter = (counter + 1) & 0xFFFFFFFF;
  //   Uint8List result = int32ToUint8List(counter);
  //   return result;
  // }
  Uint8List getCount() {
    writeCount = (writeCount + 1) & 0xFFFF;
    Uint8List result = int16ToUint8List(writeCount);
    return result;
  }

  Uint8List getTime() {
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    return int24ToUint8List(timestamp);
  }

  int uint8ListToInt32(Uint8List bytes) {
    if (bytes.length != 4) {
      throw ArgumentError('AES CCMP - Uint8List length is not 4');
    }
    // 将Uint8List包装为ByteData
    var buffer = bytes.buffer;
    var byteData = ByteData.view(buffer);
    // 读取32位整数
    return byteData.getUint32(0, Endian.big); // 使用大端序
  }

  Uint8List int32ToUint8List(int value) {
    value = value & 0xFFFFFFFF;
    var buffer = ByteData(4);
    // 将32位整数写入缓冲区
    buffer.setUint32(0, value, Endian.big); // 使用大端序
    return buffer.buffer.asUint8List();
  }

  Uint8List int24ToUint8List(int value) {
    value = value & 0xFFFFFF;
    // 取出高8位（R）、中8位（G）、低8位（B）
    int r = (value >> 16) & 0xFF;
    int g = (value >> 8) & 0xFF;
    int b = value & 0xFF;
    return Uint8List.fromList([r, g, b]);
  }

  Uint8List int16ToUint8List(int value) {
    value = value & 0xFFFF;
    var buffer = ByteData(2);
    // 将32位整数写入缓冲区
    buffer.setUint16(0, value, Endian.big); // 使用大端序
    return buffer.buffer.asUint8List();
  }
}
