class CtWifi{
  CtWifi({
    this.ssid,
    this.rssi,
    this.auth,
    this.channel,
  });

  String? ssid;
  int? rssi;
  bool? auth;
  int? channel;

  @override
  String toString() {
    return 'CtWifi{ssid: $ssid, rssi: $rssi, auth: $auth, channel: $channel}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is CtWifi) {
      return other.ssid == ssid;
    }
    return false;
  }

  @override
  int get hashCode => ssid.hashCode;
}