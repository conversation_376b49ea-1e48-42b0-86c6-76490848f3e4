import 'package:ct_flutter_ble_plugin/ct_uuids.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:ct_flutter_ble_plugin/ble_aes_ccmp/ble_aes_ccmp.dart';

class CtBleOperator {

  BleAesCcm? bleAesCcm;
  List<BluetoothService>? services;

  void setBleAesCcm(BleAesCcm ccm) {
    this.bleAesCcm = ccm;
  }

  void setServices(List<BluetoothService>? services) {
    this.services = services;
  }

  //读特征值
  Future<Uint8List> readCharacteristic(String serviceUuid, String characteristicUuid) async {
    if (services == null) {
      throw Exception("services is null");
    }

    final BluetoothService service = services!
        .firstWhere((element) => element.uuid.toString() == serviceUuid);

    final BluetoothCharacteristic characteristic = service.characteristics
        .firstWhere((element) => element.uuid.toString() == characteristicUuid);

    final value = await characteristic.read();

    if (kDebugMode) {
      debugPrint("service=${service.uuid},characteristic=$characteristicUuid,value=$value");
    }

    if (bleAesCcm != null && bleAesCcm!.needEnDecrypt && !readCharacteristicFilter(characteristicUuid)) {
      return bleAesCcm!.decryptCCMS(ciphertextWithTagAndNonce: Uint8List.fromList(value));
    }

    return Uint8List.fromList(value);
  }

  //写特征值
  Future<void> writeCharacteristic(String serviceUuid, String characteristicUuid, List<int> value) async {
    if (services == null) {
      throw Exception("services is null");
    }

    final BluetoothService service = services!
        .firstWhere((element) => element.uuid.toString() == serviceUuid);

    final BluetoothCharacteristic characteristic = service.characteristics
        .firstWhere((element) => element.uuid.toString() == characteristicUuid);

    List<int> sendValue = value;
    if (bleAesCcm != null && bleAesCcm!.needEnDecrypt) {
      sendValue = bleAesCcm!.encryptCCMS(plaintext: Uint8List.fromList(value));
    }

    if (kDebugMode) {
      debugPrint("service=${service.uuid},characteristic=$characteristicUuid,value=$sendValue");
    }

    await characteristic.write(Uint8List.fromList(sendValue));
  }

  bool readCharacteristicFilter(String characteristicUuid) {
    // 暂时不做过滤，所有特征值的数据都需要加密
    // if(characteristicUuid == CtUuids.wifiEUid.toLowerCase()) {
    //   debugPrint("filter characteristic=$characteristicUuid");
    //   return true;
    // }
    return false;
  }
}
