abstract class CtUuids {

  static const String zigBeeDeviceControlUuid = "001E5E0B-0AAA-0001-0004-000000000001";
  static const String zigBeeReadDeviceNum = "001E5E0B-0AAA-0002-0004-000000000001";
  static const String zigBeeReadDeviceNumResponse = "001E5E0B-0AAA-0002-0004-000000000002";
  static const String zigBeeReadDeviceList = "001E5E0B-0AAA-0002-0004-000000000003";
  static const String zigBeeReadDeviceListResponse = "001E5E0B-0AAA-0002-0004-000000000004";
  static const String zigBeeWriteAttribute = "001E5E0B-0AAA-0002-0004-000000000005";
  static const String zigBeeReadAttribute = "001E5E0B-0AAA-0002-0004-000000000007";
  static const String zigBeeReadAttributeResponse = "001E5E0B-0AAA-0002-0004-000000000008";

  /// WiFi and Scan
  static const String wifiConfig = "001E5E0B-0AAA-0001-0002-000000000001";
  static const String wifiState = "001E5E0B-0AAA-0002-0002-000000000001";
  static const String wifiEUid = "001E5E0B-0AAA-0002-0002-000000000002";
  static const String currentSSID = "001E5E0B-0AAA-0002-0002-000000000003";

  ///Read WIFI Status
  static const String allowWifiSetup = "001E5E0B-0AAA-0002-0002-000000000004";
  static const String setupSSIDAndPassword = "001E5E0B-0AAA-0002-0002-000000000005";

  static const String ssidScan = "001E5E0B-0AAA-0002-0002-000000000006";
  static const String ssidScanStatus = "001E5E0B-0AAA-0002-0002-000000000007";
  static const String ssidScanNumberPayloadAvailable = "001E5E0B-0AAA-0002-0002-000000000008";
  static const String ssidScanPayloadNumber = "001E5E0B-0AAA-0002-0002-000000000009";
  static const String ssidScanPayload = "001E5E0B-0AAA-0002-0002-00000000000A";
  static const String csrRequest = "001E5E0B-0AAA-0002-0002-00000000000B";

  /// Cloud and IpAddress and LocalMode
  static const String cloudAndIpUuid = "001E5E0B-0AAA-0001-0001-000000000001";
  static const String cloudState = "001E5E0B-0AAA-0002-0001-000000000001";
  static const String thingName = "001E5E0B-0AAA-0002-0001-000000000002";
  static const String cloudId = "001E5E0B-0AAA-0002-0001-000000000003";
  static const String allowCloudIdSetup = "001E5E0B-0AAA-0002-0001-000000000004";
  static const String localModeSupport = "001E5E0B-0AAA-0002-0001-000000000005";
  static const String lanIPAddress = "001E5E0B-0AAA-0002-0001-000000000006";
  static const String setupCloudId = "001E5E0B-0AAA-0002-0001-000000000007";
  static const String wifiIPAddress = "001E5E0B-0AAA-0002-0001-000000000008";
  static const String communication = "001E5E0B-0AAA-0001-0001-000000000001";

}
