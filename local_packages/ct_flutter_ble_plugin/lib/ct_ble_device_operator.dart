import 'dart:typed_data';
import 'ct_ble_operator.dart';
import 'ct_uuids.dart';
import 'package:flutter/foundation.dart';

class CtBleDeviceOperator extends CtBleOperator {
  
  Future<Uint8List> readCloudState() async {
    Uint8List result = await readCharacteristic(
      CtUuids.communication.toLowerCase(),
      CtUuids.cloudState.toLowerCase(),
    );
    if (kDebugMode){
      debugPrint("readCloudState=$result");
    }
    return result;
  }

  Future<Uint8List> readWifiState() async {
    Uint8List result = await readCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.wifiState.toLowerCase(),
    );
    if (kDebugMode){
      debugPrint("readWifiState=$result");
    }
    return result;
  }

  Future<Uint8List> readWifiIPAddress() async {
    Uint8List result = await readCharacteristic(
      CtUuids.communication.toLowerCase(),
      CtUuids.wifiIPAddress.toLowerCase(),
    );
    if (kDebugMode){{
      debugPrint("readWifiIPAddress=$result");
    }}
    return result;
  }

  Future<Uint8List> readAllowWifiSetup() async {
    Uint8List result = await readCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.allowWifiSetup.toLowerCase(),
    );
    if (kDebugMode) {
      debugPrint("readAllowWifiSetup=$result");
    }
    return result;
  }

  Future<Uint8List> readSSIDScanNumberPayloadAvailable() async {
    Uint8List result = await readCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.ssidScanNumberPayloadAvailable.toLowerCase(),
    );
    if (kDebugMode) {
      debugPrint("readSSIDScanNumberPayloadAvailable=$result");
    }
    return result;
  }

  Future<Uint8List> readSSIDScanPayload() async {
    Uint8List result = await readCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.ssidScanPayload.toLowerCase(),
    );
    if (kDebugMode) {
      debugPrint("readSSIDScanPayload=$result");
    }
    return result;
  }

  Future<Uint8List> readSSIDScanStatus() async {
    Uint8List result = await readCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.ssidScanStatus.toLowerCase(),
    );
    if (kDebugMode) {
      debugPrint("readSSIDScanStatus=$result");
    }
    return result;
  }

  Future<Uint8List> readWifiEUid() async {
    Uint8List result = await readCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.wifiEUid.toLowerCase(),
    );
    if (kDebugMode) {
      debugPrint("readWifiEUid=$result");
    }
    return result;
  }

  Future<void> writeSSIDScan(List<int> value) async {
    if (kDebugMode) {
      debugPrint("writeSSIDScan=$value");
    }
    await writeCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.ssidScan.toLowerCase(),
      value,
    );
  }

  Future<void> writeSSIDAndPwd(List<int> value) async {
    if (kDebugMode) {
      debugPrint("writeSSIDAndPwd=$value");
    }
    await writeCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.setupSSIDAndPassword.toLowerCase(),
      value,
    );
  }

  Future<void> writeSSIDScanPayloadNumber(List<int> value) async {
    if (kDebugMode) {
      debugPrint("writeSSIDScanPayloadNumber=$value");
    }
    await writeCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.ssidScanPayloadNumber.toLowerCase(),
      value,
    );
  }

  Future<void> writeCSRRequest(List<int> value) async {
    if (kDebugMode) {
      debugPrint("writeCSRRequest=$value");
    }
    await writeCharacteristic(
      CtUuids.wifiConfig.toLowerCase(),
      CtUuids.csrRequest.toLowerCase(),
      value,
    );
  }



}



