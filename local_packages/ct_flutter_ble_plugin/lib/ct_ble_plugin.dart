import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'ct_controller.dart';

class CtBlePlugin {

  CtBlePlugin._(){
    _matterControllers = [];
    _streamController = StreamController.broadcast();
  }

  static final CtBlePlugin _instance = CtBlePlugin._();
  static CtBlePlugin getInstance() => _instance;

  late List<CtController> _matterControllers;
  late StreamController<List<CtController>> _streamController;
  StreamSubscription<List<ScanResult>>? _onScanResultsSubscription;

  //启动扫描 controller
  Future<void> startScanController() async {
    _matterControllers.clear();
    await FlutterBluePlus.startScan(
        withKeywords: const ["SAIT"],
        timeout: const Duration(seconds: 60),
        androidUsesFineLocation: true
    );
  }

  //停止扫描 controller
  Future<void> stopScanController() async {
    _onScanResultsSubscription?.cancel();
    await FlutterBluePlus.stopScan();
  }

  bool isScanningNow() {
    return FlutterBluePlus.isScanningNow;
  }

  //监听是否扫描到 controller
  StreamSubscription<List<CtController>> listenScanController(
      void Function(List<CtController> list) onListenScanController) {
    _matterControllers.clear();
    _onScanResultsSubscription?.cancel();
    _onScanResultsSubscription = FlutterBluePlus.onScanResults.listen((results) {
      if (results.isEmpty) {
        return;
      }

      final remoteId = results.last.device.remoteId;
      final name = results.last.device.advName;

      debugPrint('扫描到控制器: $remoteId, $name');

      ScanResult result = results.last;
      bool isExist = _matterControllers.any((controller) {
        return controller.scanResult.device.remoteId == remoteId ||
            controller.scanResult.device.advName == name;
      });
      if (isExist) {
        debugPrint('已经存在的控制器: $remoteId, $name');
        return;
      }

      CtController controller = CtController(result);
      _matterControllers.add(controller);
      _streamController.add(_matterControllers);

      debugPrint('当前控制器数量: ${_matterControllers.length}');
    });

    return _streamController.stream.listen(onListenScanController);
  }

}
