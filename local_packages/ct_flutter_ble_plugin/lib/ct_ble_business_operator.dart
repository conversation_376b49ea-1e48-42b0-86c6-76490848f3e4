import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

import 'ct_ble_device_operator.dart';
import 'ct_utils.dart';

class CtBleBusinessOperator extends CtBleDeviceOperator {

  //扫描可用的WiFi列表
  Future<List<Uint8List>> scanSSIDList({ int maxScanCount = 30, int milliseconds = 500 }) async {
    final List<Uint8List> ssidScanList = [];

    await writeSSIDScan([1]);
    await Future.delayed(const Duration(milliseconds: 5000));

    bool scanResultAvailable = false;
    for (int i = 0; i < maxScanCount; i++) {
      Uint8List ssidScanStatus = await readSSIDScanStatus();
      if (ssidScanStatus[0] == 2) {
        scanResultAvailable = true;
        break;
      }
      await Future.delayed(Duration(milliseconds: milliseconds));
    }

    if (!scanResultAvailable) {
      throw TimeoutException('scan ssid list timeout.');
    }

    Uint8List available = await readSSIDScanNumberPayloadAvailable();
    if (available.isNotEmpty) {
      final List<Uint8List> ssidScanPayload = [];

      if (kDebugMode) {
        debugPrint("available[0]=${available[0]}");
      }

      for (int i = 0; i < available[0]; i++) {
        i++;
        await writeSSIDScanPayloadNumber([i]);

        Uint8List ssid = await readSSIDScanPayload();
        if (ssid.isNotEmpty) {
          if (ssid.length > 48) {
            final Uint8List ssid1 = ssid.sublist(0, 47);
            final Uint8List ssid2 = ssid.sublist(48, ssid.length);
            ssidScanPayload.add(
              ssid1,
            );
            ssidScanPayload.add(
              ssid2,
            );
          } else {
            ssidScanPayload.add(
              ssid,
            );
          }
        }

        if (ssidScanPayload.length == available[0]) {
          break;
        }
      }

      for (final Uint8List ssid in ssidScanPayload) {
        if (ssid.isNotEmpty && ssid[0] != 0) {
          ssidScanList.add(ssid);
        }
      }

    }

    return ssidScanList;
  }

  Future<void> sendSSIDAndPwd(
    String ssid,
    String password,
  ) async {
    await writeSSIDAndPwd(CtUtils.createSSIDAndPwd(ssid, password));
  }

  Future<String> getWifiEUid() async {
    return CtUtils.getEUIDFromUINT8List(await readWifiEUid());
  }

  Future<void> sendCSRRequest() async {
    await writeCSRRequest([1]);
  }
}



