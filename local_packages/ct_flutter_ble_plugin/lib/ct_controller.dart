import 'dart:async';
import 'package:ct_flutter_ble_plugin/ble_aes_ccmp/ble_aes_ccmp.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'ct_ble_business_operator.dart';
import 'ct_utils.dart';
import 'ct_wifi.dart';

class CtController {

  ScanResult scanResult;

  CtController(this.scanResult);

  final CtBleBusinessOperator _operator = CtBleBusinessOperator();
  get name => scanResult.device.advName;
  get remoteId => scanResult.device.remoteId.toString();
  get isConnected => scanResult.device.isConnected;

  Future<void> connect() async {
    await scanResult.device.connect();
    // SAIT85R-001E5E08F31C 不支持
    // SAIT85R_001E5E08F31C 支持aes ccm加密版
    if (name.contains("_")) {
      List<String> parts = name.split('_');
      if (parts.isNotEmpty && parts[1].length > 4) {
        _initBleAesCcm(parts[1]);
      }
    }
  }

  Future<void> disconnect() async {
    await scanResult.device.disconnect();
  }

  Future<void> discoverServices() async {
    final List<BluetoothService> services = await scanResult.device.discoverServices();
    _operator.setServices(services);
  }

  StreamSubscription<BluetoothConnectionState> listenConnectionStateChange(
      void Function(BluetoothConnectionState state) listener) {
    return scanResult.device.connectionState.listen(listener);
  }

  //获取controller wifi列表
  Future<List<CtWifi>> getWiFiList() async {
    final List<Uint8List> ssidScanList = await _operator.scanSSIDList();
    if (kDebugMode) {
      debugPrint('ssidScanList=$ssidScanList, \nlength=${ssidScanList.length}');
    }

    final Set<CtWifi> wifiSet = {};
    if (ssidScanList.isNotEmpty) {
      for (Uint8List? element in ssidScanList) {
        if (element == null) {
          continue;
        }

        CtWifi? wifi;

        try {
          final String euId = CtUtils.getEUIDFromUINT8List(element);
          final int ssidLength = CtUtils.getHexToDecimal(euId.substring(0, 2));

          if (kDebugMode) {
            debugPrint('euId=$euId');
          }

          wifi = CtWifi(
            ssid: CtUtils.getDeviceSSID(euId.substring(20, (ssidLength * 2) + 20)),
            rssi: CtUtils.getRSSI(euId.substring(2, 4)),
            channel: CtUtils.getChannel(euId.substring(4, 6)),
            auth: CtUtils.isSecurity(euId.substring(6, 8)),
          );
        } catch (e, r) {
          if (kDebugMode) {
            debugPrint('create wifi failed, e=$e, r=$r');
          }
        }

        if (kDebugMode) {
          debugPrint('create wifi success: $wifi');
        }

        if (wifi != null) {
          wifiSet.add(wifi);
        }
      }
    }
    if (kDebugMode) {
      debugPrint('wifi list length: ${wifiSet.length}');
    }
    return wifiSet.toList();
  }

  Future<void> sendSSIDAndPwd(String ssid, String password) async {
    await _operator.sendSSIDAndPwd(ssid, password);
  }

  Future<void> sendCSRRequest() async {
    await _operator.sendCSRRequest();
  }

  Future<bool> isWiFiConnected() async {
    final result = await _operator.readWifiState();
    return result[0] == 2;
  }

  Future<bool> isCloudConnected() async {
    final result = await _operator.readCloudState();
    return result[0] == 2;
  }

  Future<String> getWifiEUid() async {
    return await _operator.getWifiEUid();
  }

  // 0: Wi-Fi not configured and not connected
  // 1: Wi-Fi configured and not connected
  // 2: Wi-Fi configured and connected
  // 3: Wi-Fi configured and connecting，When receiving a new WiFi configuration, WFi Status will be 3 first
  Future<int> readWifiState() async {
    final result = await _operator.readWifiState();
    return result[0];
  }

  // 0: Cloud not configured and not connected
  // 1: Cloud configured and not connected
  // 2: Cloud connected
  Future<int> readCloudState() async {
    final result = await _operator.readCloudState();
    return result[0];
  }

  void _initBleAesCcm(String macEuid) {
    BleAesCcm bleAesCcm = BleAesCcm();
    bleAesCcm.macOrEuid = macEuid;
    bleAesCcm.needEnDecrypt = true;
    bleAesCcm.writeCount = 0;
    bleAesCcm.aesKey = bleAesCcm.getKey().toList();
    _operator.setBleAesCcm(bleAesCcm);
    debugPrint('init bleAesCcm success：$macEuid');
  }

}