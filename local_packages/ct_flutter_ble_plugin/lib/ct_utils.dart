import 'dart:convert';
import 'dart:typed_data';
import 'package:hex/hex.dart';
import 'package:encrypt/encrypt.dart';

class CtUtils {
  static String convertHexToDecimal(String macAddress) {
    return int.parse(macAddress, radix: 16).toString();
  }

  static String getDeviceSSID(String hexValue) {
    return getHexToString(hexValue);
  }

  static int getRSSI(String hexValue) {
    return getHexToDecimal(hexValue);
  }

  static int getChannel(hexValue) {
    return getHexToDecimal(hexValue);
  }

  static bool isSecurity(String hexValue) {
    return getHexToDecimal(hexValue) == 1;
  }

  static int getHexToDecimal(String hexValue) {
    return int.parse(hexValue, radix: 16);
  }

  static String getHexToString(String hexValue) {
    return ascii.decode(HEX.decode(hexValue));
  }

  static String getMacAddress(String euID) {
    return ascii.decode(HEX.decode(euID));
  }

  static String getEUIDFromUINT8List(Uint8List element) {
    final StringBuffer euId = StringBuffer();
    for (final task in element) {
      euId.write(task.toRadixString(16).padLeft(2, "0"));
    }
    return euId.toString();
  }

  static List<int> createSSIDAndPwd(String ssid, String password) {
    final List<int> ssidPwdHex = [];
    final Uint8List ssidHex = Encrypted.fromUtf8(ssid).bytes;
    final Uint8List passwordHex = Encrypted.fromUtf8(password).bytes;

    //Length of SSID
    ssidPwdHex.add(ssid.length);
    //SSID
    ssidPwdHex.addAll(ssidHex);

    //If SSID is shorter than 32 characters, the remaining byte should fill with 0x00
    for (int i = ssidPwdHex.length; i <= 32; i++) {
      ssidPwdHex.add(0x00);
    }

    //Length of PASSWORD
    ssidPwdHex.add(passwordHex.length);
    //Password
    ssidPwdHex.addAll(passwordHex);

    //If Password is shorter than 63 characters, the remaining byte should fill with 0x00
    for (int i = ssidPwdHex.length; i <= 96; i++) {
      ssidPwdHex.add(0x00);
    }
    return ssidPwdHex;
  }

}