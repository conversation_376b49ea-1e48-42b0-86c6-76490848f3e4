
class CtMatterDevice {
  final int vendorID;
  final int productID;
  final int discriminator;
  final int setupPinCode;
  final String instanceName;
  final int deviceType;
  final String pairingInstruction;
  int commissioningWindowStatus = 0;
  bool commissioningMode = false;
  bool didRemoveCommissionableDevice = true;
  final String deviceName;
  final String ipAddress;
  final int port;
  final String discoveryCapabilities;

  CtMatterDevice(
      this.vendorID,
      this.productID,
      this.discriminator,
      this.setupPinCode,
      this.instanceName,
      this.deviceType,
      this.pairingInstruction,
      this.commissioningWindowStatus,
      this.commissioningMode,
      this.didRemoveCommissionableDevice,
      this.deviceName,
      this.ipAddress,
      this.port,
      this.discoveryCapabilities
  );

  @override
  String toString() {
    return 'CtMatterDevice{vendorID: $vendorID, productID: $productID, discriminator: $discriminator, setupPinCode: $setupPinCode, instanceName: $instanceName, deviceType: $deviceType, pairingInstruction: $pairingInstruction, commissioningWindowStatus: $commissioningWindowStatus, commissioningMode: $commissioningMode, didRemoveCommissionableDevice: $didRemoveCommissionableDevice, deviceName: $deviceName, ipAddress: $ipAddress, port: $port, discoveryCapabilities: $discoveryCapabilities}';
  }

  String getInstanceName() {
    if (instanceName.isNotEmpty) {
      if (instanceName != "BLE") {
        return "OnNetwork";
      }
    }
    return instanceName;
  }

}