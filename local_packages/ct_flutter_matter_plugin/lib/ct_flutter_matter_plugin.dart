import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:ct_flutter_matter_plugin/model/ct_matter_device.dart';

typedef OnDeviceAttestationCallback = void Function(
    Map<Object?, Object?> attestationInfo,
    int errorCode);

typedef OnReadCommissioningInfoCallback = void Function(
    Map<Object?, Object?> commissioningInfo);

abstract interface class OnIssueOperationalCertificateCallback {
  void onIssueNocBegin();
  void onIssueNocSuccess();
  void onIssueNocFailure();
}

class CtFlutterMatterPlugin {

  CtFlutterMatterPlugin._(){
    if (kDebugMode) {
      debugPrint("CtFlutterMatterPlugin{} -> init()");
    }
    _methodChannel.setMethodCallHandler(_methodCallHandler);
    _onScanDevicesStream = StreamController.broadcast();
    _onAttributesStream = StreamController.broadcast();
    _onAttributesAndEventsStream = StreamController.broadcast();
  }

  static final CtFlutterMatterPlugin _instance = CtFlutterMatterPlugin._();
  static const MethodChannel _methodChannel = MethodChannel('ct_flutter_matter_plugin');
  late StreamController<CtMatterDevice> _onScanDevicesStream;
  late StreamController<Map<Object?, Object?>> _onAttributesStream;
  late StreamController<Map<Object?, Object?>> _onAttributesAndEventsStream;
  Stream<CtMatterDevice> get onScanDevicesStream => _onScanDevicesStream.stream;
  Stream<Map<Object?, Object?>> get onAttributesStream => _onAttributesStream.stream;
  Stream<Map<Object?, Object?>> get onAttributesAndEventsStream => _onAttributesAndEventsStream.stream;
  static CtFlutterMatterPlugin getInstance() => _instance;
  OnDeviceAttestationCallback? _onDeviceAttestationCallback;
  OnReadCommissioningInfoCallback? _onReadCommissioningInfoCallback;
  OnIssueOperationalCertificateCallback? _onIssueOperationalCertificateCallback;

  void setOnDeviceAttestationCallback(OnDeviceAttestationCallback callback) {
    _onDeviceAttestationCallback = callback;
  }

  void setOnReadCommissioningInfoCallback(OnReadCommissioningInfoCallback callback) {
    _onReadCommissioningInfoCallback = callback;
  }

  void setOnIssueOperationalCertificateCallback(OnIssueOperationalCertificateCallback callback) {
    _onIssueOperationalCertificateCallback = callback;
  }

  Future<dynamic> _methodCallHandler(MethodCall call) async {
    if (kDebugMode) {
      debugPrint("methodCallHandler() -> method=${call.method}, "
          "arguments=${call.arguments}, runtimeType=${call.arguments.runtimeType}");
    }
    if (call.method == 'onScanDevices') {
      final Map<Object?, Object?> map = call.arguments as Map<Object?, Object?>;
      final CtMatterDevice matterDevice = CtMatterDevice(
        map['vendorID'] != null ? map['vendorID'] as int : 0,
        map['productID'] != null ? map['productID'] as int : 0,
        map['discriminator'] != null ? map['discriminator'] as int : 0,
        map['setupPinCode'] != null ? map['setupPinCode'] as int : 0,
        map['instanceName'] != null ? map['instanceName'] as String : 'BLE',
        map['deviceType'] != null ? map['deviceType'] as int : 0,
        map['pairingInstruction'] != null ? map['pairingInstruction'] as String : '',
        map['commissioningWindowStatus'] != null ? map['commissioningWindowStatus'] as int : 0,
        map['commissioningMode'] != null ? map['commissioningMode'] as bool : false,
        map['didRemoveCommissionableDevice'] != null ? map['didRemoveCommissionableDevice'] as bool : true,
        map['deviceName'] != null ? map['deviceName'] as String : '',
        map['ipAddress'] != null ? map['ipAddress'] as String : '',
        map['port'] != null ? map['port'] as int : 0,
        map['discoveryCapabilities'] != null ? map['discoveryCapabilities'] as String : 'BLE',
      );
      _onScanDevicesStream.add(matterDevice);
    } else if (call.method == 'onSubscribeAttributes') {
      final attributesData = call.arguments as Map<Object?, Object?>;
      _onAttributesStream.add(attributesData);
    } else if (call.method == 'onSubscribeAttributesAndEvents') {
      final attributesAndEventsData = call.arguments as Map<Object?, Object?>;
      _onAttributesAndEventsStream.add(attributesAndEventsData);
    } else if (call.method == 'onDeviceAttestation') {
      final attestationData = call.arguments as Map<Object?, Object?>;
      _onDeviceAttestationCallback?.call(
          attestationData['attestationInfo'] as Map<Object?, Object?>,
          attestationData['errorCode'] as int,
      );
    } else if (call.method == 'onReadCommissioningInfo') {
      final commissioningInfoData = call.arguments as Map<Object?, Object?>;
      _onReadCommissioningInfoCallback?.call(commissioningInfoData);
    } else if (call.method == 'onIssueOperationalCertificate') {
      final int status = call.arguments as int;
      if (status == 0) {
        _onIssueOperationalCertificateCallback?.onIssueNocBegin();
      } else if (status == 1) {
        _onIssueOperationalCertificateCallback?.onIssueNocSuccess();
      } else {
        _onIssueOperationalCertificateCallback?.onIssueNocFailure();
      }
    }
  }

  Future<String?> getPlatformVersion() async {
    final version = await _methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }

  Future<void> init(Map<String, dynamic> config) async {
    await _methodChannel.invokeMethod<dynamic>('init', config);
  }

  Future<bool> createMatterClient({
    required String fabricId,
    required String rootCa,
    required String ipk,
    required String userNoc,
    required String rootCAArn,
  }) async {
    final bool info = await _methodChannel.invokeMethod<dynamic>(
        'createMatterClient', {'fabricId': fabricId, 'rootCa': rootCa, 'ipk': ipk, 'userNoc': userNoc, 'rootCAArn': rootCAArn, });
    return info;
  }

  Future<Map<Object?, Object?>> getRootCa({
    required String thingName,
  }) async {
    final Map<Object?, Object?> info = await _methodChannel.invokeMethod<dynamic>(
        'getRootCa', thingName);
    return info;
  }

  Future<String> getUserNoc({
    required String fabricId,
    required String rootCAArn,
  }) async {
    final String info = await _methodChannel.invokeMethod<dynamic>(
        'getUserNoc', {'fabricId': fabricId, 'rootCAArn': rootCAArn});
    return info;
  }

  Future<void> close({ required String fabricId }) async {
    await _methodChannel.invokeMethod<dynamic>('close', {'fabricId': fabricId});
  }

  Future<Map<Object?, Object?>> parseQrCode(String qrcode) async {
    final Map<Object?, Object?> info = await _methodChannel.invokeMethod<dynamic>(
        'parseQrCode', {'qrcode':qrcode});
    return info;
  }

  Future<Map<Object?, Object?>> parseManualPairingCode(String code) async {
    final Map<Object?, Object?> info = await _methodChannel.invokeMethod<dynamic>(
        'parseManualPairingCode', {'code':code});
    return info;
  }

  Future<int> generateRandomDiscriminator() async {
    final int result = await _methodChannel.invokeMethod<dynamic>(
        'generateRandomDiscriminator');
    return result;
  }

  Future<int> generateRandomPIN() async {
    final int result = await _methodChannel.invokeMethod<dynamic>(
        'generateRandomPIN');
    return result;
  }

  Future<int> generateRandomSetupPasscode() async {
    final int result = await _methodChannel.invokeMethod<dynamic>(
        'generateRandomSetupPasscode');
    return result;
  }

  Future<void> startScanDevices(String fabricId) async {
    await _methodChannel.invokeMethod<void>('startScanDevices', {'fabricId': fabricId});
  }

  Future<void> stopScanDevices(String fabricId) async {
    await _methodChannel.invokeMethod<void>('stopScanDevices', {'fabricId': fabricId});
  }

  Future<void> discoverCommissionableNodes({ required String fabricId }) async {
    await _methodChannel.invokeMethod<void>('discoverCommissionableNodes', {'fabricId': fabricId});
  }

  Future<void> getDiscoveredDevice({ required String fabricId }) async {
    await _methodChannel.invokeMethod<void>('getDiscoveredDevice', {'fabricId': fabricId});
  }

  Future<dynamic> readThreadBorderRouterDataset(String fabricId, String nodeId) async {
    final dynamic dataset = await _methodChannel.invokeMethod<dynamic>('readThreadBorderRouterDataset',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return dataset;
  }

  Future<Map<Object?, Object?>> addWiFiDevice(String fabricId, String code, String ssid, String pwd) async {
    final Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>('addWiFiDevice',
        {'fabricId': fabricId, 'code':code, 'ssid': ssid, 'pwd': pwd});
    return result;
  }

  Future<Map<Object?, Object?>> addThreadDeviceWithTBRDataset(String fabricId, String code, String agentId, String dataset) async {
    final Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>('addThreadDeviceWithTBRDataset',
        {'fabricId': fabricId, 'code': code, 'agentId':agentId, 'dataset':dataset});
    return result;
  }

  Future<bool> continueCommissioningDevice(String fabricId, bool ignoreAttestationFailure) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('continueCommissioningDevice',
        {'fabricId': fabricId, 'ignoreAttestationFailure': ignoreAttestationFailure});
    return result;
  }

  Future<bool> writeAttribute(String fabricId, String nodeId, int endpointId, int clusterId,
      int attributeId, String type, String value, int timedWriteTimeout) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('writeAttribute',
        {'fabricId': fabricId, 'nodeId': nodeId, 'endpointId': endpointId, 'clusterId': clusterId,
          'attributeId': attributeId, 'type': type, 'value': value,
          'timedWriteTimeout': timedWriteTimeout});
    return result;
  }

  Future<Map<Object?, Object?>> readAttribute(String fabricId, String nodeId, int endpointId, int clusterId,
      int attributeId) async {
    final Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>('readAttribute',
        {'fabricId': fabricId, 'nodeId': nodeId, 'endpointId': endpointId, 'clusterId': clusterId,
          'attributeId': attributeId});
    return result;
  }

  Future<bool> writeAttributeWithWildcard(String fabricId, String nodeId, int endpointId, String clusterName,
      String commandName, dynamic parameter) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('writeAttributeWithWildcard',
        {'fabricId': fabricId, 'nodeId': nodeId, 'endpointId': endpointId, 'clusterName': clusterName,
          'commandName': commandName, 'parameter': parameter});
    return result;
  }

  Future<Map<Object?, Object?>> readAttributeWithWildcard(String fabricId, String nodeId, int endpointId, String clusterName,
      String commandName) async {
    final Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>('readAttributeWithWildcard',
        {'fabricId': fabricId, 'nodeId': nodeId, 'endpointId': endpointId, 'clusterName': clusterName,
          'commandName': commandName});
    return result;
  }

  Future<Map<Object?, Object?>> readAllAttribute(String fabricId, String nodeId, {int? endpointId, int? clusterId}) async {
    final Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>('readAllAttribute',
        {'fabricId': fabricId, 'nodeId': nodeId, 'endpointId': endpointId, 'clusterId': clusterId});
    return result;
  }

  Future<bool> subscribeAttributesAndEvents(String fabricId, String nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('subscribeAttributesAndEvents',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> subscribeAttributes(String fabricId, String nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('subscribeAttributes',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> unsubscribes(String fabricId, String nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'unsubscribes', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<void> shutdown(String fabricId) async {
    await _methodChannel.invokeMethod<dynamic>('shutdown', {'fabricId': fabricId});
  }

  Future<String> unpairDevice(String fabricId, String nodeId) async {
    final String unpaidNodeId = await _methodChannel.invokeMethod<dynamic>('unpairDevice',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return unpaidNodeId;
  }

  Future<Map<Object?, Object?>> openPairingWindow(String fabricId, String nodeId, int duration) async {
    Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>(
        'openPairingWindow', {'fabricId': fabricId, 'nodeId': nodeId, 'duration': duration});
    return result;
  }

  Future<void> openECMPairingWindow(String fabricId, String nodeId, int commissioningTimeout, int setupPinCode, int discriminator) async {
    await _methodChannel.invokeMethod<dynamic>(
        'openECMPairingWindow', {'fabricId': fabricId, 'nodeId': nodeId, 'commissioningTimeout': commissioningTimeout, 'setupPinCode': setupPinCode, 'discriminator': discriminator});
  }

  Future<int> readPairingWindowStatus(String fabricId, String nodeId) async {
    final int result = await _methodChannel.invokeMethod<dynamic>(
        'readPairingWindowStatus', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> addTrustedRootCertificate(String fabricId, String nodeId, String rootCACertificate) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'addTrustedRootCertificate', {'fabricId': fabricId, 'nodeId': nodeId, 'rootCACertificate': rootCACertificate});
    return result;
  }

  Future<String> readThreadNetworkName(String fabricId, String nodeId) async {
    final String result = await _methodChannel.invokeMethod<dynamic>(
        'readThreadNetworkName', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<String> readAccessControlList(String fabricId, String nodeId) async {
    final String result = await _methodChannel.invokeMethod<dynamic>(
        'readAccessControlList', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<Map<Object?, Object?>> getDeviceBasicInfo(String fabricId, String nodeId, int endpoint) async {
    final Map<Object?, Object?> result = await _methodChannel.invokeMethod<dynamic>(
        'getDeviceBasicInfo', {'fabricId': fabricId, 'nodeId': nodeId, 'endpoint': endpoint});
    return result;
  }

  Future<bool> writeCatValue(String fabricId, String nodeId, String catIdOperate) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'writeCatValue', {'fabricId': fabricId, 'nodeId': nodeId, 'catIdOperate': catIdOperate});
    return result;
  }

  Future<String> getNodeId(String fabricId) async {
    final String nodeId = await _methodChannel.invokeMethod<dynamic>('getNodeId', {'fabricId': fabricId});
    return nodeId;
  }

  Future<bool> hasMatterClient(String fabricId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('hasMatterClient', {'fabricId': fabricId});
    return result;
  }

  Future<bool> removeFabric(String fabricId, String nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('removeFabric',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<Map<Object?, Object?>> generateQRCodeAndManualPairingCode(Map<String, dynamic> params) async {
    final Map<Object?, Object?> map = await _methodChannel.invokeMethod<dynamic>(
        'generateQRCodeAndManualPairingCode', params);
    return map;
  }

  Future<Map<Object?, Object?>> getNetworkLocation(String fabricId, String nodeId) async {
    final Map<Object?, Object?> map = await _methodChannel.invokeMethod<dynamic>(
        'getNetworkLocation', {'fabricId': fabricId, 'nodeId': nodeId});
    return map;
  }

  Future<List<Object?>> readDeviceType(String fabricId, String nodeId) async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>('readDeviceType',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> lightOnOff(String fabricId, String nodeId, int onOff) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('lightOnOff',
        {'fabricId': fabricId, 'nodeId': nodeId, 'onOff': onOff});
    return result;
  }

  Future<bool> lightToggle(String fabricId, String nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('lightToggle',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<String> readBssid(String fabricId, String nodeId) async {
    final String result = await _methodChannel.invokeMethod<dynamic>('readBssid',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<List<Object?>> readFabricsAttribute(String fabricId, String nodeId) async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>(
        'readFabricsAttribute', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> unregisterICDClient(String fabricId, String nodeId, String unregisterNodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'unregisterICDClient', {'fabricId': fabricId, 'nodeId': nodeId, 'unregisterNodeId': unregisterNodeId});
    return result;
  }

  Future<bool> cancelCommissioning(String fabricId, int nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'cancelCommissioning', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> stopDevicePairing(String fabricId, String nodeId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'stopDevicePairing', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<List<Object?>> getNetworkInterfaces(String fabricId, String nodeId) async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>(
        'getNetworkInterfaces', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<List<Object?>> getICDClientInfo(String fabricId) async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>(
        'getICDClientInfo', {'fabricId': fabricId});
    return result;
  }

  Future<List<Object?>> readRegisteredClients(String fabricId, String nodeId) async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>(
        'readRegisteredClients', {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

  Future<bool> isPreferredCredentials(String dataset) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'isPreferredCredentials', {'dataset':dataset});
    return result;
  }

  Future<bool> fetchPreferredThreadCredentials() async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('fetchPreferredThreadCredentials');
    return result;
  }

  Future<List<Object?>> fetchAllActiveThreadCredentials() async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>('fetchAllActiveThreadCredentials');
    return result;
  }

  Future<List<Object?>> fetchAllThreadCredentials() async {
    final List<Object?> result = await _methodChannel.invokeMethod<dynamic>('fetchAllThreadCredentials');
    return result;
  }

  Future<bool> isThreadSupported() async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('isThreadSupported');
    return result;
  }

  Future<bool> saveThreadOperationalCredentials(String agentId, String dataset) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'saveThreadOperationalCredentials', {'agentId':agentId, 'dataset':dataset});
    return result;
  }

  Future<bool> deleteCredentials(String agentId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>(
        'deleteCredentials', {'agentId':agentId});
    return result;
  }

  Future<int> getFabricIndex(String fabricId, String nodeId) async {
    final int result = await _methodChannel.invokeMethod<dynamic>('getFabricIndex', {
      'fabricId': fabricId,
      'nodeId': nodeId,
    });
    return result;
  }

  Future<void> startDnssd(String fabricId) async {
    await _methodChannel.invokeMethod<dynamic>('startDnssd', {'fabricId': fabricId});
  }

  Future<void> stopDnssd(String fabricId) async {
    await _methodChannel.invokeMethod<dynamic>('stopDnssd', {'fabricId': fabricId});
  }

  Future<bool> isRunning(String fabricId) async {
    final bool result = await _methodChannel.invokeMethod<dynamic>('isRunning', {'fabricId': fabricId});
    return result;
  }

  Future<int> getDeviceControllerPtr(String fabricId) async {
    final int result = await _methodChannel.invokeMethod<dynamic>('getDeviceControllerPtr', {'fabricId': fabricId});
    return result;
  }

  Future<int> getControllerNodeId(String fabricId) async {
    final int result = await _methodChannel.invokeMethod<dynamic>('getControllerNodeId', {'fabricId': fabricId});
    return result;
  }

  Future<int> getCompressedFabricId(String fabricId) async {
    final int result = await _methodChannel.invokeMethod<dynamic>('getCompressedFabricId', {'fabricId': fabricId});
    return result;
  }

  Future<int> getDeviceState(String fabricId, String nodeId) async {
    final int result = await _methodChannel.invokeMethod<dynamic>('getDeviceState',
        {'fabricId': fabricId, 'nodeId': nodeId});
    return result;
  }

}
