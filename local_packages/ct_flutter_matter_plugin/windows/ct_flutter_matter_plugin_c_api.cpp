#include "include/ct_flutter_matter_plugin/ct_flutter_matter_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "ct_flutter_matter_plugin.h"

void CtFlutterMatterPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  ct_flutter_matter_plugin::CtFlutterMatterPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
