group 'com.computime.app.ct_flutter_matter_plugin'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.computime.app.ct_flutter_matter_plugin'
    }

    compileSdk 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    packagingOptions {
        exclude 'META-INF/main.kotlin_module'
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        test.java.srcDirs += 'src/test/kotlin'
        main.jniLibs.srcDirs = ['third_party/connectedhomeip/libs/jniLibs']
    }

    defaultConfig {
        minSdkVersion 27
    }

    dependencies {
        testImplementation 'org.jetbrains.kotlin:kotlin-test'
        testImplementation 'org.mockito:mockito-core:5.0.0'
        // flutter
        compileOnly files("$flutterRoot/bin/cache/artifacts/engine/android-arm/flutter.jar")
        implementation 'androidx.annotation:annotation:1.7.1'
        // Native libs
        implementation fileTree(dir: "third_party/connectedhomeip/libs", include: ["*.jar", "*.so"])
        // 协程核心库
        implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0"
        // 协程Android库
        implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0"
        // ktx
        implementation "androidx.core:core-ktx:1.3.0"
        // json解析
        implementation 'com.google.code.gson:gson:2.8.5'
        //base64
        implementation 'commons-codec:commons-codec:1.16.0'
        //DERSequence
        implementation 'org.bouncycastle:bcpkix-jdk15to18:1.72'
        implementation 'org.bouncycastle:bcprov-jdk15to18:1.72'
        // Connected Home
        implementation "com.google.android.gms:play-services-base:18.5.0"
        implementation "com.google.android.gms:play-services-home:16.0.0"
        implementation 'com.google.android.gms:play-services-threadnetwork:16.2.1'
        implementation 'androidx.activity:activity-ktx:1.9.2'
        implementation 'androidx.appcompat:appcompat:1.7.0'

    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
