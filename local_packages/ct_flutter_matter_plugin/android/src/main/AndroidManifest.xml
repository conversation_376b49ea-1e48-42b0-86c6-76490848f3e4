<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.computime.app.ct_flutter_matter_plugin">

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.NFC" />

    <!-- usesCleartextTraffic needed for OTBR local unencrypted communication -->
    <application android:usesCleartextTraffic="true">
        <activity
            android:name="com.rainmaker.AppAllActiveCredentialsActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name="com.rainmaker.AppPreferredCredentialsActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name="com.rainmaker.AppCommissioningActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <service
            android:name="com.rainmaker.AppCommissioningService"
            android:exported="true" />
        <service
            android:name="com.google.android.gms.metadata.ModuleDependencies"
            android:enabled="false"
            android:exported="false"
            tools:ignore="MissingClass">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>

            <meta-data
                android:name="home:0:preferred"
                android:value="" />
        </service> <!-- GPS automatically downloads scanner module when app is installed -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="barcode_ui" />
    </application>

    <queries>
        <!-- For GMS Core/Play service -->
        <package android:name="com.google.android.gms" />
        <package android:name="com.android.vending" />
        <!-- End of GMS Core/Play service-->
    </queries>

</manifest>