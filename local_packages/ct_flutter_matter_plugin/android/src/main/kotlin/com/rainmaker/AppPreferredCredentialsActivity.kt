package com.rainmaker

import android.app.Activity
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.computime.app.ct_flutter_matter_plugin.CtLog
import com.computime.app.ct_flutter_matter_plugin.R
import com.computime.app.ct_flutter_matter_plugin.byteArrayToHex
import com.google.android.gms.threadnetwork.ThreadNetwork
import com.google.android.gms.threadnetwork.ThreadNetworkCredentials

class AppPreferredCredentialsActivity : AppCompatActivity() {

    companion object{
        const val TAG = "AppPreferredCredentialsActivity"
    }

    private var preferredCredentialsLauncher: ActivityResultLauncher<IntentSenderRequest>? = null
    private var progressBar: ProgressBar? = null
    private var networkTxt: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_preferred_credentials)

        progressBar = findViewById(R.id.progress_bar)
        networkTxt = findViewById(R.id.network_name)

        preferredCredentialsLauncher = registerForActivityResult(ActivityResultContracts
            .StartIntentSenderForResult()) { result ->
            val resultCode = result.resultCode
            CtLog.i(TAG, "resultCode: $resultCode")
            if (resultCode == Activity.RESULT_OK) {
                CtLog.i(TAG, "Success，Result: ${result.data}")
                val threadNetworkCredentials = ThreadNetworkCredentials.fromIntentSenderResultData(result.data!!)
                Log.i(TAG, "panId:${threadNetworkCredentials.panId}")
                Log.i(TAG, "extendedPanId:${threadNetworkCredentials.extendedPanId.byteArrayToHex()}")
                Log.i(TAG, "networkName:${threadNetworkCredentials.networkName}")
                Log.i(TAG, "channel:${threadNetworkCredentials.channel}")
                Log.i(TAG, "dataset:${threadNetworkCredentials.activeOperationalDataset.byteArrayToHex()}")
                Log.i(TAG, "channelMasks:${threadNetworkCredentials.channelMasks}")
                Log.i(TAG, "channelPage:${threadNetworkCredentials.channelPage}")
                Log.i(TAG, "networkKey:${threadNetworkCredentials.networkKey.byteArrayToHex()}")
                Log.i(TAG, "pskc:${threadNetworkCredentials.pskc.byteArrayToHex()}")
                Log.i(TAG, "meshLocalPrefix:${threadNetworkCredentials.meshLocalPrefix.byteArrayToHex()}")
                Log.i(TAG, "activeTimestamp:${threadNetworkCredentials.activeTimestamp}")
                Log.i(TAG, "createdAtMillis:${threadNetworkCredentials.createdAtMillis}")
                Log.i(TAG, "updatedAtMillis:${threadNetworkCredentials.updatedAtMillis}")

                networkTxt?.text = "The current preferred Thread network is:\n ${threadNetworkCredentials.networkName}"

                progressBar?.visibility = View.GONE
                networkTxt?.visibility = View.VISIBLE
            } else {
                CtLog.e(TAG, "Error")
            }
        }

        ThreadNetwork.getClient(this)
            .preferredCredentials
            .addOnSuccessListener { intentSenderResult ->
                intentSenderResult.intentSender?.let {
                    Log.i(TAG, "preferred credentials found.")
                    preferredCredentialsLauncher?.launch(IntentSenderRequest.Builder(it).build())
                } ?: Log.e(TAG, "No preferred credentials found.")
            }
            .addOnFailureListener { e: Exception ->
                Log.e(TAG, "error: [${e}]")
            }
    }
}