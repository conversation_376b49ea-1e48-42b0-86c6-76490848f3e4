package com.rainmaker

class Constants {
    companion object {
        const val BEGIN_CERTIFICATE_REQUEST = "-----BEGIN CERTIFICATE REQUEST-----"
        const val END_CERTIFICATE_REQUEST = "-----<PERSON>ND CERTIFICATE REQUEST-----"

        const val BEGIN_CERTIFICATE = "-----BEGIN CERTIFICATE-----"
        const val END_CERTIFICATE = "-----END CERTIFICATE-----"

        const val PRIVILEGE_ADMIN = 5
        const val PRIVILEGE_OPERATE = 3

        const val CAT_ID_PREFIX = "FFFFFFFD"

        const val CURRENT_VERSION = "v1"

        const val KEY_CSR_TYPE = "csr_type"
        const val KEY_CSR_REQUESTS = "csr_requests"
        const val KEY_CSR = "csr"

        const val KEY_GROUP_ID = "group_id"
        const val KEY_GROUP_NAME = "group_name"
        const val KEY_GROUPS = "groups"

        const val KEY_OPERATION = "operation"
        const val KEY_OPERATION_ADD = "add"
        const val KEY_OPERATION_EDIT = "edit"
        const val KEY_OPERATION_REMOVE = "remove"
        const val KEY_OPERATION_ENABLE = "enable"
        const val KEY_OPERATION_DISABLE = "disable"
        const val KEY_OPERATION_ACTIVATE = "activate"
    }
}