package com.rainmaker

import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.math.BigInteger
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.util.Base64

class Utils {
    companion object{
        fun stripLinkLocalInIpAddress(ipAddress: String): String {
            return ipAddress.replace("%.*".toRegex(), "")
        }

        fun getCatId(catIdOperate: String): Long {
            val tempCatIdOperate = Constants.CAT_ID_PREFIX + catIdOperate
            val catId = BigDecimal(BigInteger(tempCatIdOperate, 16))
            return catId.toLong()
        }

        fun generateCertificate(cert: String?): X509Certificate {
            val encodedCert: ByteArray = Base64.getDecoder().decode(cert)
            val inputStream = ByteArrayInputStream(encodedCert)
            val certFactory = CertificateFactory.getInstance("X.509")
            return certFactory.generateCertificate(inputStream) as X509Certificate
        }

        fun generateKeypair(fabricId: String): KeyPair {
            val keyPairGenerator: KeyPairGenerator = KeyPairGenerator.getInstance(
                KeyProperties.KEY_ALGORITHM_EC, "AndroidKeyStore"
            )

            val keySpecBuilder = fabricId.let {
                KeyGenParameterSpec.Builder(
                    it,
                    KeyProperties.PURPOSE_SIGN or KeyProperties.PURPOSE_VERIFY
                ).setDigests(
                    KeyProperties.DIGEST_SHA256,
                )
            }

            keyPairGenerator.initialize(keySpecBuilder.build())
            return keyPairGenerator.generateKeyPair()
        }

        fun splitAndConcatenateCsr(input: String): String {
            val result = StringBuilder()
            result.append("\r\n")
            for (i in input.indices step 64) {
                val end = minOf(i + 64, input.length)
                result.append(input, i, end).append("\r\n")
            }
            return result.toString()
        }
    }
}