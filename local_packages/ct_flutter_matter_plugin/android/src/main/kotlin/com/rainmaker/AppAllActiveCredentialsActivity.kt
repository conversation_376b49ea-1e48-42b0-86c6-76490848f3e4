package com.rainmaker

import android.app.Activity
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ProgressBar
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.computime.app.ct_flutter_matter_plugin.CtLog
import com.computime.app.ct_flutter_matter_plugin.R
import com.computime.app.ct_flutter_matter_plugin.byteArrayToHex
import com.google.android.gms.threadnetwork.GetAllActiveCredentialsRequest
import com.google.android.gms.threadnetwork.ThreadNetwork
import com.google.android.gms.threadnetwork.ThreadNetworkCredentials

class AppAllActiveCredentialsActivity : AppCompatActivity() {

    companion object{
        const val TAG = "AppAllActiveCredentialsActivity"
    }
    
    private var preferredCredentialsLauncher: ActivityResultLauncher<IntentSenderRequest>? = null
    private var progressBar: ProgressBar? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_all_active_credentials)

        progressBar = findViewById<ProgressBar>(R.id.progress_bar)

        preferredCredentialsLauncher = registerForActivityResult(
            ActivityResultContracts
            .StartIntentSenderForResult()) { result ->
            val resultCode = result.resultCode
            CtLog.i(TAG, "resultCode: $resultCode")
            if (resultCode == Activity.RESULT_OK) {
                CtLog.i(TAG, "Success，Result: ${result.data}")

                val activeCredentials: List<ThreadNetworkCredentials> =
                    ThreadNetworkCredentials.parseListFromIntentSenderResultData(
                        result.data!!
                    )

                CtLog.i(TAG, "activeCredentials size: ${activeCredentials.size}")

                for (threadNetworkCredentials in activeCredentials) {
                    Log.i(TAG, "==========start==========")
                    Log.i(TAG, "\n")
                    Log.i(TAG, "panId:${threadNetworkCredentials.panId}")
                    Log.i(TAG, "extendedPanId:${threadNetworkCredentials.extendedPanId.byteArrayToHex()}")
                    Log.i(TAG, "networkName:${threadNetworkCredentials.networkName}")
                    Log.i(TAG, "channel:${threadNetworkCredentials.channel}")
                    Log.i(TAG, "dataset:${threadNetworkCredentials.activeOperationalDataset.byteArrayToHex()}")
                    Log.i(TAG, "channelMasks:${threadNetworkCredentials.channelMasks}")
                    Log.i(TAG, "channelPage:${threadNetworkCredentials.channelPage}")
                    Log.i(TAG, "networkKey:${threadNetworkCredentials.networkKey.byteArrayToHex()}")
                    Log.i(TAG, "pskc:${threadNetworkCredentials.pskc.byteArrayToHex()}")
                    Log.i(TAG, "meshLocalPrefix:${threadNetworkCredentials.meshLocalPrefix.byteArrayToHex()}")
                    Log.i(TAG, "activeTimestamp:${threadNetworkCredentials.activeTimestamp}")
                    Log.i(TAG, "createdAtMillis:${threadNetworkCredentials.createdAtMillis}")
                    Log.i(TAG, "updatedAtMillis:${threadNetworkCredentials.updatedAtMillis}")
                    Log.i(TAG, "\n")
                    Log.i(TAG, "==========end==========")
                }

                progressBar?.visibility = View.GONE
            } else {
                CtLog.e(TAG, "Error")
            }
        }


        val request = GetAllActiveCredentialsRequest
            .newBuilder()
            .setTimeoutMillis(30000)
            .build()

        ThreadNetwork.getClient(this)
            .getAllActiveCredentials(request)
            .addOnSuccessListener { intentSenderResult ->
                CtLog.d(TAG, "Success to fetch active thread credentials")
                intentSenderResult.intentSender?.let {
                    Log.i(TAG, "active thread credentials found.")
                    preferredCredentialsLauncher?.launch(IntentSenderRequest.Builder(it).build())
                } ?: Log.e(TAG, "No active thread credentials found.")
            }
            .addOnFailureListener { e: Exception ->
                e.printStackTrace()
                CtLog.e(TAG, "Failed to fetch active thread credentials.")
            }


    }
}