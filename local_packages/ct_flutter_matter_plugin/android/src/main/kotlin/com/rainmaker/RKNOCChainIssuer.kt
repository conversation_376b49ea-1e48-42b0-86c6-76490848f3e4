package com.rainmaker

import chip.devicecontroller.AttestationInfo
import chip.devicecontroller.CSRInfo
import chip.devicecontroller.ChipDeviceController

abstract class RKNOCChainIssuer(private val chipClient: ChipClient): ChipDeviceController.NOCChainIssuer {
    override fun onNOCChainGenerationNeeded(csrInfo: CSRInfo?, attestationInfo: AttestationInfo?) {
        this.onRKNOCChainGenerationNeeded(csrInfo, attestationInfo, chipClient)
    }

    abstract fun onRKNOCChainGenerationNeeded(csrInfo: CSRInfo?, attestationInfo: AttestationInfo?,
                                              chipClient: ChipClient)
}