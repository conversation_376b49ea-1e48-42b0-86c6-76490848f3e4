package com.rainmaker

import android.util.Log
import chip.devicecontroller.KeypairDelegate
import java.security.KeyStore
import java.security.PrivateKey
import java.security.Signature

class BaseKeypairDelegate(
    private val nocKey: ByteArray,
    private val fabricId: String,
    private val keyStore: KeyStore
): KeypairDelegate {
    /**
     * Ensure that a private key is generated when this method returns.
     * @throws KeypairDelegate.KeypairException if a private key could not be generated or resolved
     */
    @Throws(KeypairDelegate.KeypairException::class)
    override fun generatePrivateKey() {
        Log.d("BaseKeypairDelegate", "====================== generatePrivateKey")
    }

    /**
     * Returns an operational PKCS#10 CSR in DER-encoded form, signed by the underlying private key.
     * @throws KeypairDelegate.KeypairException if the CSR could not be generated
     */
    @Throws(KeypairDelegate.KeypairException::class)
    override fun createCertificateSigningRequest(): ByteArray? {
        Log.d("BaseKeypairDelegate", "======================== createCertificateSigningRequest")
        return null
    }

    /**
     * Returns the DER-encoded X.509 public key, generating a new private key if one has not already
     * been created.
     * @throws KeypairDelegate.KeypairException if a private key could not be resolved
     */
    @Throws(KeypairDelegate.KeypairException::class)
    override fun getPublicKey(): ByteArray {
        Log.d("BaseKeypairDelegate", "======================== getPublicKey")
        return nocKey
    }

    /**
     * Signs the given message with the private key (generating one if it has not yet been created)
     * using ECDSA and returns a DER-encoded signature.
     * @throws KeypairDelegate.KeypairException if a private key could not be resolved, or the message could not be
     * signed
     */
    @Throws(KeypairDelegate.KeypairException::class)
    override fun ecdsaSignMessage(message: ByteArray?): ByteArray? {
        Log.d("BaseKeypairDelegate", "======================== ecdsaSignMessage")
        val privateKey = keyStore.getKey(fabricId, null) as PrivateKey
        val signature = Signature.getInstance("SHA256withECDSA")
        signature.initSign(privateKey)
        signature.update(message)
        return signature.sign()
    }
}