package com.rainmaker

import android.app.Activity
import android.content.ComponentName
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.computime.app.ct_flutter_matter_plugin.CtLog
import com.computime.app.ct_flutter_matter_plugin.CtStatusCode
import com.computime.app.ct_flutter_matter_plugin.R
import com.google.android.gms.home.matter.Matter
import com.google.android.gms.home.matter.commissioning.CommissioningRequest

class AppCommissioningActivity: AppCompatActivity() {

    companion object{
        const val TAG = "AppCommissioningActivity"
    }

    private var commissionDeviceLauncher: ActivityResultLauncher<IntentSenderRequest>?= null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_commissioning)

        commissionDeviceLauncher = registerForActivityResult(ActivityResultContracts
            .StartIntentSenderForResult()) { result ->
            val resultCode = result.resultCode
            CtLog.i(TAG, "resultCode: $resultCode")
            if (resultCode == Activity.RESULT_OK) {
                CtLog.i(TAG, "Success，Result: ${result.data}")
                AppCommissioningManager.getAppCommissioningCallback()?.onCommissioningComplete(
                    0)
                finish()
            } else {
                CtLog.e(TAG, "Error")
                AppCommissioningManager.getAppCommissioningCallback()?.onCommissioningComplete(
                    CtStatusCode.SETUP_REQUEST_ERROR)
                finish()
            }
        }

        val code = intent.getStringExtra("code") ?: ""
        CtLog.d(TAG, "code: $code")

        val commissionDeviceRequest =
            CommissioningRequest.builder()
                .setOnboardingPayload(code)
                .setCommissioningService(
                    ComponentName(
                        this,
                        AppCommissioningService::class.java
                    )
                )
                .build()

        Matter.getCommissioningClient(this)
            .commissionDevice(commissionDeviceRequest)
            .addOnSuccessListener { r ->
                CtLog.i(TAG, "OnSuccess result: [${r}]")
                commissionDeviceLauncher?.launch(IntentSenderRequest.Builder(r).build())
            }
            .addOnFailureListener { error ->
                CtLog.i(TAG, "OnFailure result: [${error}]")
                error.printStackTrace()
                AppCommissioningManager.getAppCommissioningCallback()?.onCommissioningComplete(
                    CtStatusCode.SETUP_REQUEST_ERROR)
                finish()
            }
    }
}
