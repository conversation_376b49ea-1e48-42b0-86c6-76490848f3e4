package com.rainmaker

import android.util.Log
import chip.devicecontroller.ChipDeviceController
import chip.devicecontroller.ControllerParams
import chip.devicecontroller.GetConnectedDeviceCallbackJni.GetConnectedDeviceCallback
import chip.devicecontroller.ICDCheckInDelegate
import chip.devicecontroller.ICDClientInfo
import chip.devicecontroller.OperationalKeyConfig
import com.computime.app.ct_flutter_matter_plugin.CtLog
import com.computime.app.ct_flutter_matter_plugin.printByteArrayToHex
import org.apache.commons.codec.binary.Base64.encodeBase64
import org.apache.commons.codec.binary.Hex
import org.bouncycastle.asn1.DERBitString
import org.bouncycastle.asn1.DERSequence
import java.security.KeyStore
import java.util.Base64
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

class ChipClient(
    private val rootCa: String,
    private val fabricId: String,
    private val ipk: String,
    private val rootCAArn: String,
) {

    companion object {
        const val TAG = "ChipClient"
        const val VENDOR_ID = 0x1527
    }

    private val keyStore: KeyStore = KeyStore.getInstance("AndroidKeyStore").apply {
        load(null)
    }

    private var ipkEpochKey: ByteArray? = null
    private var nocKey: ByteArray? = null
    private lateinit var chipDeviceController: ChipDeviceController

    fun getChipDeviceController(): ChipDeviceController {
        if (!this::chipDeviceController.isInitialized) {
            CtLog.d(TAG, "init ChipDeviceController.")
            val decodedHex: ByteArray = Hex.decodeHex(ipk)
            val encodedHexB64: ByteArray = encodeBase64(decodedHex)
            val ipk = String(encodedHexB64)
            ipkEpochKey = Base64.getDecoder().decode(ipk)
            CtLog.d(TAG, "ipkEpochKey=$ipkEpochKey")

            chipDeviceController = ChipDeviceController(
                ControllerParams.newBuilder(operationalKeyConfig())
                    .setUdpListenPort(0)
                    .setControllerVendorId(VENDOR_ID)
                    .build()
            )

            chipDeviceController.setICDCheckInDelegate(
                object : ICDCheckInDelegate {
                    override fun onCheckInComplete(info: ICDClientInfo) {
                        Log.d(TAG, "onCheckInComplete : $info")
                    }

                    override fun onKeyRefreshNeeded(info: ICDClientInfo): ByteArray? {
                        Log.d(TAG, "onKeyRefreshNeeded : $info")
                        return null
                    }

                    override fun onKeyRefreshDone(errorCode: Long) {
                        Log.d(TAG, "onKeyRefreshDone : $errorCode")
                    }
                }
            )
        }

        return chipDeviceController
    }

    private fun operationalKeyConfig(): OperationalKeyConfig {
        CtLog.d(TAG, "OperationalKeyConfig called.")
        val chain = keyStore.getCertificateChain(fabricId)

        try {
            CtLog.d(TAG, "Init OperationalKeyConfig, fabricId : $fabricId")
            val sequence = DERSequence.getInstance(chain[0].publicKey.encoded)
            val subjectPublicKey = sequence.getObjectAt(1) as DERBitString
            nocKey = subjectPublicKey.bytes
            CtLog.d(TAG, "NOC PublicKey : ${nocKey?.printByteArrayToHex()}")

            val s = chain[0].toString()
            CtLog.d(TAG, "NOC : $s")
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return OperationalKeyConfig(
            BaseKeypairDelegate(nocKey!!, fabricId, keyStore),
            chain[1].encoded,
            chain[1].encoded,
            chain[0].encoded,
            ipkEpochKey
        )
    }

    suspend fun getConnectedDevicePointer(nodeId: Long): Long {
        return suspendCoroutine { continuation ->
            chipDeviceController.getConnectedDevicePointer(
                nodeId,
                object : GetConnectedDeviceCallback {
                    override fun onDeviceConnected(devicePointer: Long) {
                        CtLog.d(TAG, "Got connected device pointer")
                        continuation.resume(devicePointer)
                    }

                    override fun onConnectionFailure(nodeId: Long, error: Exception) {
                        val errorMessage = "Unable to get connected device with nodeId $nodeId."
                        CtLog.e(TAG, errorMessage)
                        continuation.resumeWithException(error)
                    }
                }
            )
        }
    }

    fun getIpkEpochKey(): ByteArray {
        return ipkEpochKey!!
    }

    fun getNocKey(): ByteArray {
        return nocKey!!
    }

    fun getRootCa(): String {
        return rootCa
    }

    fun getFabricId(): String {
        return fabricId
    }

    fun getIpk(): String {
        return ipk
    }

    fun getRootCAArn(): String {
        return rootCAArn
    }
}
