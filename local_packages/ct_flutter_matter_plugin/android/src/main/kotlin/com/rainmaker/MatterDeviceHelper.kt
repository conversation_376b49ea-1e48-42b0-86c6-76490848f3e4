package com.rainmaker

import chip.devicecontroller.ChipClusters
import chip.devicecontroller.ChipStructs
import chip.devicecontroller.ChipStructs.AccessControlClusterAccessControlEntryStruct
import com.computime.app.ct_flutter_matter_plugin.CtLog
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

class MatterDeviceHelper(private val devicePtr: Long) {

    companion object {
        private const val TAG = "MatterDeviceHelper"
    }

    suspend fun getDeviceBasicInfo(descriptorCluster: ChipClusters.DescriptorCluster) : Map<String, Any> {
        val basicInfo = mutableMapOf<String, Any>()

        val partsListAttribute = readPartsListAttribute(descriptorCluster)
        val deviceTypeListAttribute = readDeviceTypeListAttribute(descriptorCluster)
        val serverListAttribute = readServerListAttribute(descriptorCluster)
        val clientListAttribute = readClientListAttribute(descriptorCluster)

        basicInfo["partsList"] = partsListAttribute ?: emptyList<Int>()

        val deviceTypeList = mutableListOf<Number>()
        deviceTypeListAttribute.forEach {
            deviceTypeList.add(it.deviceType)
        }

        basicInfo["deviceTypeList"] = deviceTypeList
        basicInfo["serverList"] = serverListAttribute
        basicInfo["clientList"] = clientListAttribute

        return basicInfo
    }

    private suspend fun readPartsListAttribute(
        descriptorCluster: ChipClusters.DescriptorCluster,
    ): List<Int>? {
        return suspendCoroutine { continuation ->
            descriptorCluster.readPartsListAttribute(
                object : ChipClusters.DescriptorCluster.PartsListAttributeCallback {
                    override fun onSuccess(values: MutableList<Int>?) {
                        continuation.resume(values)
                    }

                    override fun onError(ex: Exception) {
                        continuation.resumeWithException(ex)
                    }
                })
        }
    }

    private suspend fun readDeviceTypeListAttribute(
        descriptorCluster: ChipClusters.DescriptorCluster,
    ): List<ChipStructs.DescriptorClusterDeviceTypeStruct> {
        return suspendCoroutine { continuation ->
            descriptorCluster.readDeviceTypeListAttribute(
                object : ChipClusters.DescriptorCluster.DeviceTypeListAttributeCallback {
                    override fun onSuccess(
                        values: List<ChipStructs.DescriptorClusterDeviceTypeStruct>
                    ) {
                        continuation.resume(values)
                    }

                    override fun onError(ex: Exception) {
                        continuation.resumeWithException(ex)
                    }
                })
        }
    }

    private suspend fun readServerListAttribute(
        descriptorCluster: ChipClusters.DescriptorCluster,
    ): List<Long> {
        return suspendCoroutine { continuation ->
            descriptorCluster.readServerListAttribute(
                object : ChipClusters.DescriptorCluster.ServerListAttributeCallback {
                    override fun onSuccess(values: MutableList<Long>) {
                        continuation.resume(values)
                    }

                    override fun onError(ex: Exception) {
                        continuation.resumeWithException(ex)
                    }
                })
        }
    }

    private suspend fun readClientListAttribute(
        descriptorCluster: ChipClusters.DescriptorCluster,
    ): List<Long> {
        return suspendCoroutine { continuation ->
            descriptorCluster.readClientListAttribute(
                object : ChipClusters.DescriptorCluster.ClientListAttributeCallback {
                    override fun onSuccess(values: MutableList<Long>) {
                        continuation.resume(values)
                    }

                    override fun onError(ex: Exception) {
                        continuation.resumeWithException(ex)
                    }
                })
        }
    }

    suspend fun writeCatValue(catIdOperate: String) {
        CtLog.i(TAG, "writeCatValue: $catIdOperate")
        
        val cluster = ChipClusters.AccessControlCluster(devicePtr, 0)
        val writeAclAttr = ArrayList<AccessControlClusterAccessControlEntryStruct>()
        val accessControlList = readAclAttribute(cluster)

        var fabricIndex = 0
        var authMode = 0

        accessControlList?.forEach {
            writeAclAttr.add(it)
            if (it.privilege == Constants.PRIVILEGE_ADMIN) {
                fabricIndex = it.fabricIndex
                authMode = it.authMode
            }
        }

        val catId = Utils.getCatId(catIdOperate)
        val subjects = ArrayList<Long>()
        subjects.add(catId)

        CtLog.i(TAG, "catId=$catId")

        val struct = AccessControlClusterAccessControlEntryStruct(
            Constants.PRIVILEGE_ADMIN,
            authMode,
            subjects,
            null,
            fabricIndex
        )

        writeAclAttr.add(struct)

        writeAclAttribute(cluster, writeAclAttr)
        CtLog.i(TAG, "writeCatValue success!")
    }

    private suspend fun readAclAttribute(cluster: ChipClusters.AccessControlCluster): MutableList<ChipStructs.AccessControlClusterAccessControlEntryStruct>? {
        return suspendCoroutine { continuation ->
            cluster.readAclAttribute(
                object : ChipClusters.AccessControlCluster.AclAttributeCallback {
                    override fun onSuccess(valueList: MutableList<ChipStructs.AccessControlClusterAccessControlEntryStruct>?) {
                        CtLog.i(TAG,  "readAclAttribute success: [$valueList]")
                        continuation.resume(valueList)
                    }

                    override fun onError(ex: Exception) {
                        CtLog.e(TAG, "readAclAttribute command failure")
                        continuation.resumeWithException(ex)
                    }
                })
        }
    }

    private suspend fun writeAclAttribute(
        cluster: ChipClusters.AccessControlCluster,
        entries: ArrayList<AccessControlClusterAccessControlEntryStruct>
    ) {
        return suspendCoroutine { continuation ->
            cluster.writeAclAttribute(
                object : ChipClusters.DefaultClusterCallback {

                    override fun onSuccess() {
                        CtLog.i(TAG,  "writeAclAttribute success")
                        continuation.resume(Unit)
                    }

                    override fun onError(ex: Exception) {
                        CtLog.e(TAG, "writeAclAttribute command failure")
                        continuation.resumeWithException(ex)
                    }
                }, entries
            )
        }
    }

}