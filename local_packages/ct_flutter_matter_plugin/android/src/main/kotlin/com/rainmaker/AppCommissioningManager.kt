package com.rainmaker

import chip.devicecontroller.ChipDeviceController
import chip.devicecontroller.ICDDeviceInfo
import chip.devicecontroller.ICDRegistrationInfo
import chip.devicecontroller.NetworkCredentials
import com.computime.app.ct_flutter_matter_plugin.CtLog
import com.computime.app.ct_flutter_matter_plugin.printByteArrayToHex
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

object AppCommissioningManager {

    private var chipDeviceController: ChipDeviceController? = null
    private var appCommissioningCallback: AppCommissioningCallback? = null
    private var commissioningNodeId: String? = null

    fun getChipDeviceController(): ChipDeviceController? {
        return chipDeviceController
    }

    fun getAppCommissioningCallback(): AppCommissioningCallback? {
        return appCommissioningCallback
    }

    fun setChipDeviceController(chipDeviceController: ChipDeviceController) {
        AppCommissioningManager.chipDeviceController = chipDeviceController
    }

    fun setAppCommissioningCallback(appCommissioningCallback: AppCommissioningCallback) {
        AppCommissioningManager.appCommissioningCallback = appCommissioningCallback
    }

    fun getCommissioningNodeId(): String? {
        return commissioningNodeId
    }

    fun setCommissioningNodeId(ndeId: String?) {
        commissioningNodeId = ndeId
    }

    suspend fun awaitEstablishPaseConnection(
        deviceId: Long,
        ipAddress: String,
        port: Int,
        setupPinCode: Long
    ) {
        return suspendCoroutine { continuation ->
            chipDeviceController?.setCompletionListener(
                object : BaseCompletionListener(), ChipDeviceController.CompletionListener {
                    override fun onConnectDeviceComplete() {
                        super.onConnectDeviceComplete()
                        continuation.resume(Unit)
                    }

                    // Note that an error in processing is not necessarily communicated via onError().
                    // onCommissioningComplete with a "code != 0" also denotes an error in processing.
                    override fun onPairingComplete(code: Long) {
                        super.onPairingComplete(code)
                        if (code != 0L) {
                            continuation.resumeWithException(
                                IllegalStateException("Pairing failed with error code [${code}]")
                            )
                        } else {
                            continuation.resume(Unit)
                        }
                    }

                    override fun onError(error: Throwable) {
                        super.onError(error)
                        continuation.resumeWithException(error)
                    }

                    override fun onICDRegistrationInfoRequired() {
                    }

                    override fun onICDRegistrationComplete(
                        errorCode: Long,
                        icdDeviceInfo: ICDDeviceInfo?
                    ) {
                    }

                    override fun onReadCommissioningInfo(
                        vendorId: Int,
                        productId: Int,
                        wifiEndpointId: Int,
                        threadEndpointId: Int
                    ) {
                        super.onReadCommissioningInfo(
                            vendorId,
                            productId,
                            wifiEndpointId,
                            threadEndpointId
                        )
                        continuation.resume(Unit)
                    }

                    override fun onCommissioningStatusUpdate(nodeId: Long, stage: String?, errorCode: Long) {
                        continuation.resume(Unit)
                    }
                })

            // Temporary workaround to remove interface indexes from ipAddress
            // due to https://github.com/project-chip/connectedhomeip/pull/19394/files
            chipDeviceController?.establishPaseConnection(
                deviceId, Utils.stripLinkLocalInIpAddress(ipAddress), port, setupPinCode
            )
        }
    }

    suspend fun awaitCommissionDevice(deviceId: Long, networkCredentials: NetworkCredentials?) {
        return suspendCoroutine { continuation ->
            chipDeviceController?.setCompletionListener(
                object : BaseCompletionListener() {
                    // Note that an error in processing is not necessarily communicated via onError().
                    // onCommissioningComplete with an "errorCode != 0" also denotes an error in processing.
                    override fun onCommissioningComplete(nodeId: Long, errorCode: Long) {
                        super.onCommissioningComplete(nodeId, errorCode)
                        CtLog.d(TAG, "onCommissioningComplete: nodeId=$nodeId, errorCode=$errorCode")
                        if (errorCode != 0L) {
                            continuation.resumeWithException(
                                IllegalStateException("Commissioning failed with error code [${errorCode}]")
                            )
                        } else {
                            val newNodeId = String.format("%X", nodeId)
                            CtLog.d(TAG, "onCommissioningComplete: newNodeId=$newNodeId")
                            setCommissioningNodeId(newNodeId)
                            continuation.resume(Unit)
                        }
                    }

                    override fun onError(error: Throwable) {
                        super.onError(error)
                        continuation.resumeWithException(error)
                    }

                    override fun onICDRegistrationInfoRequired() {
                        CtLog.d(TAG, "onICDRegistrationInfoRequired")
                        chipDeviceController?.updateCommissioningICDRegistrationInfo(
                            ICDRegistrationInfo.newBuilder().build()
                        )
                    }

                    override fun onICDRegistrationComplete(
                        errorCode: Long,
                        icdDeviceInfo: ICDDeviceInfo?
                    ) {
                        CtLog.d(
                            TAG,
                            "onICDRegistrationComplete - " +
                                    "errorCode: $errorCode, " +
                                    "symmetricKey : ${icdDeviceInfo?.symmetricKey?.printByteArrayToHex()}, " +
                                    "icdDeviceInfo : $icdDeviceInfo"
                        )
                    }
                })
            chipDeviceController?.commissionDevice(deviceId, networkCredentials)
        }
    }
}
