/*
 *   Copyright (c) 2020 Project CHIP Authors
 *   All rights reserved.
 *
 *   Licensed under the Apache License, Version 2.0 (the "License");
 *   you may not use this file except in compliance with the License.
 *   You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *   Unless required by applicable law or agreed to in writing, software
 *   distributed under the License is distributed on an "AS IS" BASIS,
 *   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *   See the License for the specific language governing permissions and
 *   limitations under the License.
 *
 */
package com.google.chip.chiptool.provisioning

/** Network type - Wi-Fi vs Thread, to be provisioned. */
enum class ProvisionNetworkType {
  THREAD,
  WIFI,
  ;

  companion object {
    fun fromName(name: String?): ProvisionNetworkType? {
      return when (name) {
        THREAD.name -> THREAD
        WIFI.name -> WIFI
        else -> null
      }
    }
  }
}
