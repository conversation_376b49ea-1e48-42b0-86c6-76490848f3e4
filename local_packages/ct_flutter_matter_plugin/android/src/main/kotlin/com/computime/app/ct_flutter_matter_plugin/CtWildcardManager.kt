package com.computime.app.ct_flutter_matter_plugin

typealias OnSuccess = (response: Any) -> Unit
typealias OnFailure = (e: Exception) -> Unit

interface CtWildcardManager {
    fun execute(nodeId: Long,
                endpointId: Int,
                clusterName: String,
                commandName: String,
                parameter: Any,
                onSuccess: OnSuccess,
                onFailure: OnFailure)
}