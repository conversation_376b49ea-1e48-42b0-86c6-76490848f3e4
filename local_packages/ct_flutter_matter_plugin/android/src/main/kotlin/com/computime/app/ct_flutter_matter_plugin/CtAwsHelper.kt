package com.computime.app.ct_flutter_matter_plugin

import android.net.Uri
import com.rainmaker.Constants
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.URL
import java.util.Base64
import javax.net.ssl.HttpsURLConnection
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

class CtAwsHelper(
    private val scheme: String,
    private val host: String,
    private val path: String
) {

    companion object{
        private const val TAG = "CtAwsHelper"
        private const val ROOT_CA = "rootCA"
        private const val USER_NOC = "userNOC"
        private const val NODE_NOC = "nodeNOC"
    }

    suspend fun getRootCa(thingName: String) : Map<String, String>{
        return suspendCoroutine { continuation ->
            try {
                val uri = Uri.Builder()
                    .scheme(scheme)
                    .authority(host)
                    .path("$path/$ROOT_CA")
                    .appendQueryParameter("thing_name", thingName)
                    .build()

                CtLog.i(TAG, "V2 getRootCa url: $uri")

                val url = URL(uri.toString())
                val conn = url.openConnection() as HttpsURLConnection

                conn.requestMethod = "GET"
                conn.connectTimeout = 30000
                conn.readTimeout = 30000

                // 检查响应代码
                val responseCode = conn.responseCode
                if (responseCode == HttpsURLConnection.HTTP_OK) {
                    val inputStream = conn.inputStream
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = StringBuilder()

                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }

                    reader.close()
                    // 处理成功的响应
                    CtLog.i(TAG, "get remote rootCA: $response")

                    val jsonObject = JSONObject(response.toString())
                    val success = jsonObject.optBoolean("success")
                    if (!success) {
                        CtLog.e(TAG, "get remote rootCA error.")
                        continuation.resumeWithException(Exception("get remote rootCA error."))
                        return@suspendCoroutine
                    }

                    val payloadObject = jsonObject.optJSONObject("payload")
                    if (payloadObject == null){
                        CtLog.e(TAG, "get remote rootCA error.")
                        continuation.resumeWithException(Exception("get remote rootCA error."))
                        return@suspendCoroutine
                    }

                    var rootCa = payloadObject.optString("rootCA")
                    if (rootCa.isEmpty()){
                        CtLog.e(TAG, "get remote rootCA error.")
                        continuation.resumeWithException(Exception("get remote rootCA error."))
                        return@suspendCoroutine
                    }

                    rootCa = rootCa.replace(Constants.BEGIN_CERTIFICATE, "")
                    rootCa = rootCa.replace(Constants.END_CERTIFICATE, "")
                    rootCa = rootCa.replace("\n", "")

                    val map = mutableMapOf<String, String>()
                    map["rootCa"] = rootCa
                    map["rootCaCertArn"] = payloadObject.optString("certArn")
                    map["rootCaCertUrl"] = payloadObject.optString("certUrl")
                    map["ipk"] = payloadObject.optString("ipk")
                    map["fabricId"] = payloadObject.optString("fabricId")

                    continuation.resume(map)
                } else {
                    // 处理错误响应
                    val inputStream = conn.errorStream
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = StringBuilder()

                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }

                    reader.close()
                    CtLog.e(TAG, "get remote rootCA error: $response")
                    continuation.resumeWithException(Exception("get remote rootCA error."))
                }
            } catch (e: Exception) {
                e.printStackTrace()
                continuation.resumeWithException(e)
            }
        }
    }

    suspend fun getUserNoc(csr: String, rootCAArn: String) : Map<String, String>{
        return getNoc(USER_NOC, csr, rootCAArn)
    }

    suspend fun getNodeNoc(csr: String, rootCAArn: String) : Map<String, String>{
        return getNoc(NODE_NOC, csr, rootCAArn)
    }
    
    private suspend fun getNoc(tag: String, csr: String, rootCAArn: String) : Map<String, String> {
        return suspendCoroutine { continuation ->
            try {
                val encodedByteCSR = Base64.getEncoder().encode(csr.toByteArray())
                val encodedCSR = String(encodedByteCSR)

                val uri = Uri.Builder()
                    .scheme(scheme)
                    .authority(host)
                    .path("$path/$tag")
                    .encodedQuery("csr=$encodedCSR&rootCAArn=$rootCAArn")
                    .build()

                CtLog.i(TAG, "V2 $tag url: $uri")

                val url = URL(uri.toString())
                val conn = url.openConnection() as HttpsURLConnection

                conn.requestMethod = "GET"
                conn.connectTimeout = 30000
                conn.readTimeout = 30000

                // 检查响应代码
                val responseCode = conn.responseCode
                if (responseCode == HttpsURLConnection.HTTP_OK) {
                    val inputStream = conn.inputStream
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = StringBuilder()

                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }

                    reader.close()
                    // 处理成功的响应
                    CtLog.i(TAG, "get remote noc: $response")

                    val jsonObject = JSONObject(response.toString())
                    val success = jsonObject.optBoolean("success")
                    if (!success) {
                        CtLog.e(TAG, "get remote noc error.")
                        continuation.resumeWithException(Exception("get remote noc error."))
                        return@suspendCoroutine
                    }

                    val payloadObject = jsonObject.optJSONObject("payload")
                    if (payloadObject == null){
                        CtLog.e(TAG, "get remote noc error.")
                        continuation.resumeWithException(Exception("get remote noc error."))
                        return@suspendCoroutine
                    }
                    var noc = payloadObject.optString("noc")
                    CtLog.i(TAG, "get remote noc = $noc")

                    val nodeId = payloadObject.optString("node_id")
                    CtLog.i(TAG, "get remote nodeId = $nodeId")

                    if (noc.isEmpty()){
                        CtLog.e(TAG, "get remote noc error.")
                        continuation.resumeWithException(Exception("get remote noc error."))
                        return@suspendCoroutine
                    }

                    noc = noc.replace(Constants.BEGIN_CERTIFICATE, "")
                    noc = noc.replace(Constants.END_CERTIFICATE, "")
                    noc = noc.replace("\n", "")

                    val map = mutableMapOf<String, String>()
                    map["noc"] = noc
                    map["nodeId"] = nodeId
                    map["certArn"] = payloadObject.optString("certArn")
                    map["certUrl"] = payloadObject.optString("certUrl")

                    continuation.resume(map)
                } else {
                    // 处理错误响应
                    val inputStream = conn.errorStream
                    val reader = BufferedReader(InputStreamReader(inputStream))
                    val response = StringBuilder()

                    var line: String?
                    while (reader.readLine().also { line = it } != null) {
                        response.append(line)
                    }

                    reader.close()
                    CtLog.e(TAG, "get remote noc error: $response")
                    continuation.resumeWithException(Exception("get remote noc error."))
                }
            } catch (e: Exception) {
                e.printStackTrace()
                continuation.resumeWithException(e)
            }
        }
    }
}