package com.computime.app.ct_flutter_matter_plugin

fun String.hexToByteArray(): ByteArray {
    return chunked(2).map { byteStr -> byteStr.toUByte(16).toByte() }.toByteArray()
}
fun String.dsToByteArray(): ByteArray {
    return chunked(2).map { it.toInt(16).toByte() }.toByteArray()
}
fun ByteArray.byteArrayToHex(): String =
    joinToString(separator = "") { eachByte -> "%02x".format(eachByte) }

fun ByteArray.printByteArrayToHex(): String {
    val hexString = this.joinToString(separator = " ") {
            byte -> byte.toInt().and(0xFF).toString(16).uppercase().padStart(2, '0')
    }
    return hexString
}


