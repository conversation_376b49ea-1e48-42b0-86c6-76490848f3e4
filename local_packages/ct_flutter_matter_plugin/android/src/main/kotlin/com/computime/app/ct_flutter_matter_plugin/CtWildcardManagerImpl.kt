package com.computime.app.ct_flutter_matter_plugin

import chip.clusterinfo.ClusterCommandCallback
import chip.clusterinfo.ClusterInfo
import chip.clusterinfo.CommandResponseInfo
import chip.clusterinfo.DelegatedClusterCallback
import chip.clusterinfo.InteractionInfo
import chip.devicecontroller.ChipClusters
import chip.devicecontroller.ClusterInfoMapping
import com.rainmaker.ChipClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class CtWildcardManagerImpl(
    val chipClient: ChipClient,
    val coroutineScope: CoroutineScope
):CtWildcardManager {
    
    companion object{
        private const val TAG = "CtClusterManagerImpl"
    }

    private val clusterMap: Map<String, ClusterInfo> = ClusterInfoMapping().clusterMap
    private var selectedClusterInfo: ClusterInfo?=null
    private var selectedCluster: ChipClusters.BaseChipCluster?=null
    private var selectedCommandCallback: DelegatedClusterCallback?=null
    private var selectedInteractionInfo: InteractionInfo?=null
    override fun execute(nodeId: Long,
                         endpointId: Int, 
                         clusterName: String, 
                         commandName: String, 
                         parameter: Any, 
                         onSuccess: OnSuccess,
                         onFailure: OnFailure) {

        CtLog.i(TAG, "parameter=${parameter}")
        selectedClusterInfo = clusterMap[clusterName]
        if (selectedClusterInfo == null) {
            onFailure(Exception("clusterName not found"))
            return
        }
        selectedInteractionInfo = selectedClusterInfo!!.commands[commandName]
        if (selectedInteractionInfo == null){
            onFailure(Exception("commandName not found"))
            return
        }

        coroutineScope.launch {
            try {
                val devicePtr = chipClient.getConnectedDevicePointer(nodeId)
                selectedCluster = selectedClusterInfo!!.createClusterFunction.create(devicePtr, endpointId)

                selectedInteractionInfo!!.commandParameters?.forEach { (paramName, paramInfo) ->
                    CtLog.i(TAG, "commandParameters, paramName=${paramName}")
                    CtLog.i(TAG, "commandParameters, paramInfoName=${paramInfo.name}, " +
                            "paramInfoType=${paramInfo.type}, " +
                            "paramInfoUnderlyingType=${paramInfo.underlyingType}")
                }

                selectedCommandCallback = selectedInteractionInfo!!.commandCallbackSupplier.get()
                selectedCommandCallback!!.setCallbackDelegate(object : ClusterCommandCallback {
                    override fun onSuccess(responseValues: Map<CommandResponseInfo, Any>) {
                        CtLog.i(TAG, "Command success!, responseValues=${responseValues}")
                        if (responseValues.isEmpty()) {
                            onSuccess(true)
                        } else {
                            val map = HashMap<String, Any>()
                            responseValues.forEach { (paramName, paramValue) ->
                                CtLog.i(TAG, "responseValues, name=${paramName.name}, type=${paramName.type}")
                                CtLog.i(TAG, "responseValues, paramValue=${paramValue}")
                                map[paramName.name] = paramValue
                            }
                            onSuccess(map)
                        }
                    }

                    override fun onFailure(exception: Exception) {
                        CtLog.i(TAG, "Command failed!, error=${exception}")
                        onFailure(exception)
                    }
                })

                val map = HashMap<String, Any>()
                map["value"] = parameter
                CtLog.i(TAG, "parameterMap=${map}")
                val parameterType = "class ${parameter.javaClass.name}"
                CtLog.i(TAG, "parameterType=${parameterType}")

                selectedInteractionInfo!!
                    .getCommandFunction()
                    .invokeCommand(selectedCluster, selectedCommandCallback, map)
            } catch (e: Exception) {
                onFailure(e)
            }
        }
    }
}