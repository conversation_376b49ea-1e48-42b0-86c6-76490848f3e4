package com.computime.app.ct_flutter_matter_plugin

import android.content.Context
import chip.devicecontroller.ChipDeviceController
import chip.devicecontroller.CommissionParameters
import chip.devicecontroller.ICDDeviceInfo
import chip.devicecontroller.ICDRegistrationInfo
import chip.devicecontroller.NetworkCredentials
import chip.platform.AndroidChipPlatform
import com.google.chip.chiptool.GenericChipDeviceListener
import com.google.chip.chiptool.NetworkCredentialsParcelable
import com.google.chip.chiptool.bluetooth.BluetoothManager
import com.google.chip.chiptool.setuppayloadscanner.CHIPDeviceInfo
import com.google.chip.chiptool.util.DeviceIdUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import matter.onboardingpayload.OnboardingPayloadParser
import java.util.Base64


class CtAddDeviceManagerImpl(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
    private val controller: ChipDeviceController,
) : CtAddDeviceManager {

    companion object {
        private const val TAG = "CtAddDeviceManagerImpl"
        private const val DEVICE_ATTESTATION_FAILED_TIMEOUT = 1000
    }

    private var networkCredentialsParcelable: NetworkCredentialsParcelable? = null
    private var deviceInfo: CHIPDeviceInfo? = null
    private var setupCode: String? = null
    private var deviceAttestationCallback: DeviceAttestationCallback? = null
    private var readCommissioningInfoCallback: ReadCommissioningInfoCallback? = null
    private var commissioningCompleteCallback: CommissioningCompleteCallback? = null

    override fun addWiFiDevice(code:String,
                               ssid:String,
                               pwd:String,
                               deviceAttestationCallback: DeviceAttestationCallback,
                               readCommissioningInfoCallback: ReadCommissioningInfoCallback,
                               commissioningCompleteCallback: CommissioningCompleteCallback,
    ) {
        this.setupCode = code
        this.deviceAttestationCallback = deviceAttestationCallback
        this.readCommissioningInfoCallback = readCommissioningInfoCallback
        this.commissioningCompleteCallback = commissioningCompleteCallback

        try {
            val payload =
                if (code.startsWith("MT:")) {
                    OnboardingPayloadParser().parseQrCode(code)
                } else {
                    OnboardingPayloadParser().parseManualPairingCode(code)
                }
            deviceInfo = CHIPDeviceInfo.fromSetupPayload(payload)
        } catch (e: Exception){
            e.printStackTrace()
            this.commissioningCompleteCallback?.let { it("", CtStatusCode.PARSING_QRCODE_ERROR) }
            return
        }

        coroutineScope.launch {
            try {
                networkCredentialsParcelable = NetworkCredentialsParcelable.forWiFi(
                    NetworkCredentialsParcelable.WiFiCredentials(ssid, pwd))
                pairDeviceWithCode()
            } catch(e: Exception) {
                <EMAIL>?.let {
                    it("", CtStatusCode.COMMISSION_NODE_ERROR)
                }
            }
        }

    }

    override fun addThreadDevice(
        code: String,
        dataset: String,
        deviceAttestationCallback: DeviceAttestationCallback,
        readCommissioningInfoCallback: ReadCommissioningInfoCallback,
        commissioningCompleteCallback: CommissioningCompleteCallback,
    ) {
        this.setupCode = code
        this.deviceAttestationCallback = deviceAttestationCallback
        this.readCommissioningInfoCallback = readCommissioningInfoCallback
        this.commissioningCompleteCallback = commissioningCompleteCallback

        try {
            val payload =
                if (code.startsWith("MT:")) {
                    OnboardingPayloadParser().parseQrCode(code)
                } else {
                    OnboardingPayloadParser().parseManualPairingCode(code)
                }
            deviceInfo = CHIPDeviceInfo.fromSetupPayload(payload)
        } catch (e: Exception){
            e.printStackTrace()
            this.commissioningCompleteCallback?.let { it("", CtStatusCode.PARSING_QRCODE_ERROR) }
            return
        }

        val newDataset: ByteArray?

        try {
            newDataset = Base64.getDecoder().decode(dataset)
        } catch (e: Exception){
            e.printStackTrace()
            this.commissioningCompleteCallback?.let { it("", CtStatusCode.PARSING_DATASET_ERROR) }
            return
        }

        CtLog.i(TAG, "newDataset: $newDataset")

        coroutineScope.launch {
            try {
                networkCredentialsParcelable = NetworkCredentialsParcelable.forThread(
                    NetworkCredentialsParcelable.ThreadCredentials(newDataset)
                )
                pairDeviceWithCode()
            } catch(e: Exception) {
                <EMAIL>?.let {
                    it("", CtStatusCode.COMMISSION_NODE_ERROR)
                }
            }
        }

    }

    private fun pairDeviceWithCode() {
        CtLog.i(TAG, "pairDeviceWithCode...")

        controller.setCompletionListener(ConnectionCallback())

        setAttestationDelegate()

        var network: NetworkCredentials? = null

        val wifi = networkCredentialsParcelable!!.wiFiCredentials
        if (wifi != null) {
            network = NetworkCredentials.forWiFi(
                NetworkCredentials.WiFiCredentials(wifi.ssid, wifi.password))
        }

        val thread = networkCredentialsParcelable!!.threadCredentials
        if (thread != null) {
            network = NetworkCredentials.forThread(
                NetworkCredentials.ThreadCredentials(thread.operationalDataset))
        }

        val deviceId = DeviceIdUtil.getNextAvailableId(context)
        CtLog.i(TAG, "deviceId: $deviceId")

        val commissionParameters = CommissionParameters.Builder()
            .setNetworkCredentials(network)
            .build()

        controller.pairDeviceWithCode(
            deviceId,
            setupCode,
            false,
            false,
            commissionParameters
        )

        DeviceIdUtil.setNextAvailableId(context, deviceId + 1)
    }

    inner class ConnectionCallback : GenericChipDeviceListener() {

        override fun onReadCommissioningInfo(
            vendorId: Int,
            productId: Int,
            wifiEndpointId: Int,
            threadEndpointId: Int
        ) {
            CtLog.d(TAG, "onReadCommissioningInfo() -> $vendorId, $productId, " +
                    "$wifiEndpointId, $threadEndpointId")
            readCommissioningInfoCallback?.let { it(vendorId, productId) }
        }

        override fun onCommissioningStatusUpdate(nodeId: Long, stage: String, errorCode: Long) {
            CtLog.d(TAG, "onCommissioningStatusUpdate() -> nodeId=$nodeId, stage=$stage, " +
                    "errorCode=$errorCode")
        }

        override fun onNotifyChipConnectionClosed() {
            CtLog.d(TAG, "onNotifyChipConnectionClosed()")
        }

        override fun onConnectDeviceComplete() {
            CtLog.d(TAG, "onConnectDeviceComplete()")
        }

        override fun onStatusUpdate(status: Int) {
            CtLog.d(TAG, "onStatusUpdate() -> status=$status")
        }

        override fun onCommissioningComplete(nodeId: Long, errorCode: Long) {
            CtLog.d(TAG, "onCommissioningComplete() -> nodeId=$nodeId, errorCode=$errorCode")
            if (errorCode == CtStatusCode.PAIRING_SUCCESS) {
                val newNodeId = String.format("%X", nodeId)
                CtLog.i(TAG, "onCommissioningComplete() -> newNodeId=$newNodeId")
                commissioningCompleteCallback?.let { it(newNodeId, errorCode) }
            } else {
                commissioningCompleteCallback?.let { it("", errorCode) }
            }
        }

        override fun onPairingComplete(code: Long) {
            CtLog.d(TAG, "onPairingComplete() -> code=$code")
        }

        override fun onOpCSRGenerationComplete(csr: ByteArray) {
            CtLog.d(TAG, "onOpCSRGenerationComplete() -> csr=${String(csr)}")
        }

        override fun onPairingDeleted(code: Long) {
            CtLog.d(TAG, "onPairingDeleted() -> code=$code")
        }

        override fun onCloseBleComplete() {
            CtLog.d(TAG, "onCloseBleComplete()")
        }

        override fun onError(error: Throwable?) {
            error?.printStackTrace()
            CtLog.d(TAG, "onError() -> msg=${error?.message}")
            commissioningCompleteCallback?.let { it("", CtStatusCode.UNKNOWN_ERROR) }
        }

        override fun onICDRegistrationInfoRequired() {
            CtLog.d(TAG, "onICDRegistrationInfoRequired()")
            controller.updateCommissioningICDRegistrationInfo(
                ICDRegistrationInfo.newBuilder().build()
            )
        }

        override fun onICDRegistrationComplete(errorCode: Long, icdDeviceInfo: ICDDeviceInfo) {
            CtLog.d(TAG,
                "onICDRegistrationComplete() -> " +
                        "errorCode: $errorCode, " +
                        "symmetricKey : ${icdDeviceInfo.symmetricKey.printByteArrayToHex()}, " +
                        "icdDeviceInfo : $icdDeviceInfo"
            )
        }
    }

    private fun setAttestationDelegate() {
        controller.setDeviceAttestationDelegate(
            DEVICE_ATTESTATION_FAILED_TIMEOUT) {
                devicePtr,
                attestationInfo,
                errorCode ->

            val vid = attestationInfo?.vendorId ?: 0
            val pid = attestationInfo?.productId ?: 0

            CtLog.i(TAG, "devicePtr: $devicePtr" +
                    ", vid: $vid" +
                    ", pid: $pid" +
                    ", errorCode: $errorCode")

            deviceAttestationCallback?.let { it(devicePtr, attestationInfo, errorCode) }

            if (errorCode == CtStatusCode.PAIRING_SUCCESS) {
                CtLog.i(TAG, "Device Attestation Successful")
                coroutineScope.launch {
                    withContext(Dispatchers.Main) {
                        CtLog.i(TAG, "Switch to the Main thread...")
                        try {
                            CtLog.i(TAG, "Continue commissioning...")
                            controller.continueCommissioning(
                                devicePtr,
                                true
                            )
                        } catch (e: Exception) {
                            e.printStackTrace()
                            commissioningCompleteCallback?.let { it("", CtStatusCode.DEVICE_ATTESTATION_ERROR) }
                        }
                    }
                }
            } else {
                CtLog.e(TAG, "Device Attestation Failed")
            }
        }
    }


}