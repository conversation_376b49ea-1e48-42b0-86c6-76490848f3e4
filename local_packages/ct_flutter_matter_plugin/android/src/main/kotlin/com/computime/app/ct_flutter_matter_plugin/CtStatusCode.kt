package com.computime.app.ct_flutter_matter_plugin

class CtStatusCode {
    companion object{
        const val PAIRING_SUCCESS = 0L
        const val UNKNOWN_ERROR = -1L
        const val PARSING_QRCODE_ERROR = -2L
        const val PARSING_DATASET_ERROR = -3L
        const val PAIRING_DEVICE_ERROR = -4L
        const val NON_SUPPORT_ERROR = -5L
        const val DEVICE_ATTESTATION_ERROR = -6L
        const val COMMISSION_NODE_ERROR = -7L
        const val SETUP_REQUEST_ERROR = -8L
        const val DEVICE_NOT_FOUND_ERROR = -10L
        const val BLUETOOTH_LE_DISCONNECTED_ERROR = -11L
        const val PARAMETER_ERROR = -20L
    }
}