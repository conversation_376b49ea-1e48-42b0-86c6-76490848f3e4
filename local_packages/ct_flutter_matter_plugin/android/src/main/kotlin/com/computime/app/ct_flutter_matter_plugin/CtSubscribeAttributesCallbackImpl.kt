package com.computime.app.ct_flutter_matter_plugin

import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope

class CtSubscribeAttributesCallbackImpl(
    coroutineScope: CoroutineScope,
    methodChannel : MethodChannel,
) : CtSubscribeCallbackImpl(coroutineScope, methodChannel) {
    override fun getInvokeMethodName(): String {
        return "onSubscribeAttributes"
    }
}

