package com.computime.app.ct_flutter_matter_plugin

import chip.devicecontroller.AttestationInfo

typealias DeviceAttestationCallback = (devicePtr: Long,
                                       attestationInfo: AttestationInfo?,
                                       errorCode: Long) -> Unit

typealias ReadCommissioningInfoCallback = (vendorId: Int, productId: Int) -> Unit

typealias CommissioningCompleteCallback = (nodeId: String,
                                           errorCode: Long) -> Unit

interface CtAddDeviceManager {
     fun addWiFiDevice(code:String,
                       ssid:String,
                       pwd:String,
                       deviceAttestationCallback: DeviceAttestationCallback,
                       readCommissioningInfoCallback: ReadCommissioningInfoCallback,
                       commissioningCompleteCallback: CommissioningCompleteCallback,
     )

    fun addThreadDevice(code:String,
                        dataset: String,
                        deviceAttestationCallback: DeviceAttestationCallback,
                        readCommissioningInfoCallback: ReadCommissioningInfoCallback,
                        commissioningCompleteCallback: CommissioningCompleteCallback,
    )

}