package com.computime.app.ct_flutter_matter_plugin

import android.bluetooth.BluetoothAdapter
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.ProducerScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull

class CtBluetoothManager{
    companion object {
        private const val TAG = "chip.CtBluetoothManager"
    }

    private val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    private var scanCallback: ScanCallback? = null
    private var timeMillis: Long = 60000

    fun setTimeMillis(timeMillis: Long) {
        this.timeMillis = timeMillis
    }

    suspend fun startScanDevices(callback: (Map<String, Any>) -> Unit) {
        val scanner =
            bluetoothAdapter.bluetoothLeScanner
                ?: run {
                    CtLog.e(TAG, "No bluetooth scanner found")
                    return
                }
        withTimeoutOrNull(timeMillis) {
            callbackFlow<Map<String, Any>>{
                scanCallback = getCtScanCallback(this)
                val scanSettings =
                    ScanSettings.Builder().setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY).build()
                CtLog.i(TAG, "Starting Bluetooth scan")
                scanner.startScan(listOf(), scanSettings, scanCallback!!)
                awaitClose { scanCallback?.let { scanner.stopScan(it) } }
            }.collect{
                withContext(Dispatchers.Main) {
                    callback(it)
                }
            }
        }
    }

    suspend fun stopScanDevices() {
        val scanner =
            bluetoothAdapter.bluetoothLeScanner
                ?: run {
                    CtLog.e(TAG, "No bluetooth scanner found")
                    return
                }
        if (scanCallback == null) {
            return
        }
        scanner.stopScan(scanCallback!!)
        scanCallback = null
    }

    private fun getCtScanCallback(producerScope: ProducerScope<Map<String,Any>>): ScanCallback {
        val scanCallback =
            object : ScanCallback() {
                override fun onScanResult(callbackType: Int, result: ScanResult) {
                    val device = result.device
                    result.scanRecord?.bytes?.let {
                        if (isMatterDevice(it)) {
                            CtLog.i(TAG, "ByteArray: ${result.scanRecord?.bytes?.printByteArrayToHex()}")
                            val discriminator = parseDiscriminator(it)
                            val productID = parseProductID(it)
                            val vendorID = parseVendorID(it)
                            CtLog.i(TAG, "Address: ${device.address}, " +
                                    "vendorID:${vendorID}, " +
                                    "productID:${productID}, " +
                                    "discriminator:${discriminator}"
                            )
                            if (producerScope.channel.isClosedForSend) {
                                CtLog.w(TAG, "Bluetooth device was scanned, but channel is" +
                                        " already closed")
                            } else {
                                val map = mutableMapOf<String, Any>()
                                map["vendorID"] = vendorID
                                map["productID"] = productID
                                map["discriminator"] = discriminator
                                map["instanceName"] = "BLE"
                                map["discoveryCapabilities"] = "BLE"
                                map["commissioningWindowStatus"] = 2
                                val isSuccess = producerScope.trySend(map).isSuccess
                                CtLog.i(TAG, "isSuccess=${isSuccess}")
                            }
                        }
                    }

                }

                override fun onScanFailed(errorCode: Int) {
                    CtLog.e(TAG, "Scan failed $errorCode")
                }
            }

        return scanCallback
    }

    private fun parseDiscriminator(byteArray: ByteArray): Int {
        if (byteArray.size < 10) {
            return 0
        }

        val byte8 = byteArray[8].toInt() and 0xFF
        val byte9 = byteArray[9].toInt() and 0xFF

        val combined = (byte9 shl 8) or byte8
        val low12Bits = combined and 0x0FFF

//        CtLog.i(TAG,"parseDiscriminator() -> byte8=0x$byte8")
//        CtLog.i(TAG,"parseDiscriminator() -> byte9=0x$byte9")
//        CtLog.i(TAG,"parseDiscriminator() -> combined=0x$combined")

        return low12Bits
    }

    private fun parseProductID(byteArray: ByteArray): Int {
        if (byteArray.size < 13) {
            return 0
        }

        val byte12 = byteArray[12].toInt() and 0xFF
        val byte13 = byteArray[13].toInt() and 0xFF

        val combined = (byte13 shl 8) or byte12

//        CtLog.i(TAG,"parseProductID() -> byte12=0x$byte12")
//        CtLog.i(TAG,"parseProductID() -> byte13=0x$byte13")
//        CtLog.i(TAG,"parseProductID() -> combined=0x$combined")

        return combined
    }

    private fun parseVendorID(byteArray: ByteArray): Int {
        if (byteArray.size < 11) {
            return 0
        }

        val byte10 = byteArray[10].toInt() and 0xFF
        val byte11 = byteArray[11].toInt() and 0xFF

        val combined = (byte11 shl 8) or byte10

//        CtLog.i(TAG,"parseVendorID() -> byte10=0x$byte10")
//        CtLog.i(TAG,"parseVendorID() -> byte11=0x$byte11")
//        CtLog.i(TAG,"parseVendorID() -> combined=0x$combined")

        return combined
    }

    private fun isMatterDevice(scanRecord: ByteArray): Boolean {
        if (scanRecord.size < 6) {
            CtLog.i(TAG, "scanRecord size < 9")
            return false
        }
        val byte5 = scanRecord[5].toInt() and 0xFF
        val byte6 = scanRecord[6].toInt() and 0xFF
        val combined = (byte5 shl 8) or byte6

//        CtLog.i(TAG,"isMatterDevice() -> byte5=0x${byte5}")
//        CtLog.i(TAG,"isMatterDevice() -> byte6=0x${byte6}")
//        CtLog.i(TAG,"isMatterDevice() -> combined=0x$combined")

        return combined == 0xF6FF
    }
    
    
}