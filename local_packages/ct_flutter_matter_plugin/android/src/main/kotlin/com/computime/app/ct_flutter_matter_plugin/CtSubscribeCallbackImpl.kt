package com.computime.app.ct_flutter_matter_plugin

import chip.devicecontroller.ChipIdLookup
import chip.devicecontroller.ReportCallback
import chip.devicecontroller.ResubscriptionAttemptCallback
import chip.devicecontroller.SubscriptionEstablishedCallback
import chip.devicecontroller.model.ChipAttributePath
import chip.devicecontroller.model.ChipEventPath
import chip.devicecontroller.model.NodeState
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import matter.tlv.TlvReader

open class CtSubscribeCallbackImpl(
    val coroutineScope: CoroutineScope,
    val methodChannel : MethodChannel,
): ReportCallback, SubscriptionEstablishedCallback, ResubscriptionAttemptCallback {

    companion object {
        private const val TAG = "CtSubscribeCallbackImpl"
    }

    private var subscriptionEstablished = false
    override fun onError(
        attributePath: ChipAttributePath?,
        eventPath: ChipEventPath?,
        e: Exception
    ) {
        e.printStackTrace()
    }

    override fun onReport(nodeState: NodeState?) {
        CtLog.i(TAG, "onReport() -> state:${nodeState?.endpointStates}")

        if (subscriptionEstablished) {
            val map = mutableMapOf<String,Any>()

            nodeState?.let {
                it.endpointStates.forEach {ep ->
                    CtLog.i(TAG, "onReport() -> Endpoint{} key=${ep.key}, value=${ep.value}")
                    ep.value.clusterStates.forEach { cluster ->
                        CtLog.i(TAG, "onReport() -> Cluster{} key=${cluster.key}, value=${cluster.value}")
                        cluster.value.attributeStates.forEach { attribute ->
                            val key = ChipIdLookup.attributeIdToName(cluster.key, attribute.key)
                            val value = TlvReader(attribute.value.tlv).nextElement().value.toAny()
                            CtLog.i(TAG, "onReport() -> Attribute{} key=${key}, value=${value}")
                            map[key] = value ?: ""
                        }
                    }
                }
            }

            coroutineScope.launch {
                CtLog.i(TAG, "Switch to the Main thread...")
                withContext(Dispatchers.Main) {
                    methodChannel.invokeMethod(getInvokeMethodName(), map)
                }
            }

        }
    }

    protected open fun getInvokeMethodName(): String {
        return "onSubscribeAttributes"
    }

    override fun onDone() {
        super.onDone()
        CtLog.i(TAG, "onDone()")
    }

    override fun onSubscriptionEstablished(subscriptionId: Long) {
        subscriptionEstablished = true
        CtLog.i(TAG, "onSubscriptionEstablished() -> " +
                "Subscription to device established : ${subscriptionId.toULong()}")
    }

    override fun onResubscriptionAttempt(
        terminationCause: Long,
        nextResubscribeIntervalMsec: Long
    ) {
        CtLog.i(TAG, "onResubscriptionAttempt() -> terminationCause:$terminationCause, " +
                "nextResubscribeIntervalMsec:$nextResubscribeIntervalMsec")
    }
}