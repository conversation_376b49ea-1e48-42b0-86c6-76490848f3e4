package com.computime.app.ct_flutter_matter_plugin;

public class CtSetupPayloadUtils {
    // Matter 规范的最大PIN值，需根据实际定义设置
    private static final int SETUP_PIN_CODE_MAX = 99999998;

    public static Number generateRandomDiscriminator() {
        int minValue = 1;
        int maxValue = 0xFFE; // 4094
        java.util.Random random = new java.util.Random();
        // nextInt(bound) 生成 [0, bound)，所以要加1
        return random.nextInt(maxValue - minValue + 1) + minValue;
    }

    public static int generateRandomPIN() {
        java.security.SecureRandom random = new java.security.SecureRandom();
        int setupPIN;
        do {
            setupPIN = random.nextInt(SETUP_PIN_CODE_MAX) + 1; // [1, MAX]
        } while (!isValidSetupPIN(setupPIN));
        return setupPIN;
    }

    public static boolean isValidSetupPIN(int setupPIN) {
        final int kSetupPINCodeUndefinedValue = 0;
        final int kSetupPINCodeMaximumValue = 99999998;

        if (setupPIN == kSetupPINCodeUndefinedValue || setupPIN > kSetupPINCodeMaximumValue ||
                setupPIN == 11111111 || setupPIN == 22222222 || setupPIN == 33333333 ||
                setupPIN == 44444444 || setupPIN == 55555555 || setupPIN == 66666666 ||
                setupPIN == 77777777 || setupPIN == 88888888 || setupPIN == 12345678 ||
                setupPIN == 87654321) {
            return false;
        }
        return true;
    }
}
