package com.computime.app.ct_flutter_matter_plugin

import android.util.Log

class CtLog {
    companion object{
        private var DEBUG = true
        fun i(tag: String , message: String){
           if (DEBUG) Log.i(tag, message)
        }
        fun d(tag: String , message: String){
           if (DEBUG) Log.d(tag, message)
        }
        fun w(tag: String , message: String){
           if (DEBUG) Log.w(tag, message)
        }
        fun e(tag: String , message: String){
           if (DEBUG) Log.e(tag, message)
        }
    }
}