package com.computime.app.ct_flutter_matter_plugin

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.util.Log
import chip.devicecontroller.AttestationInfo
import chip.devicecontroller.CSRInfo
import chip.devicecontroller.ChipClusters
import chip.devicecontroller.ChipClusters.IcdManagementCluster
import chip.devicecontroller.ChipClusters.OnOffCluster
import chip.devicecontroller.ChipClusters.ThreadNetworkDiagnosticsCluster
import chip.devicecontroller.ChipClusters.WiFiNetworkDiagnosticsCluster
import chip.devicecontroller.ChipDeviceController
import chip.devicecontroller.ChipIdLookup
import chip.devicecontroller.ChipStructs
import chip.devicecontroller.ClusterIDMapping.AdministratorCommissioning
import chip.devicecontroller.ClusterIDMapping.OperationalCredentials
import chip.devicecontroller.ControllerParams
import chip.devicecontroller.InvokeCallback
import chip.devicecontroller.OpenCommissioningCallback
import chip.devicecontroller.ReportCallback
import chip.devicecontroller.UnpairDeviceCallback
import chip.devicecontroller.WriteAttributesCallback
import chip.devicecontroller.model.AttributeWriteRequest
import chip.devicecontroller.model.ChipAttributePath
import chip.devicecontroller.model.ChipEventPath
import chip.devicecontroller.model.ChipPathId
import chip.devicecontroller.model.InvokeElement
import chip.devicecontroller.model.NodeState
import chip.devicecontroller.model.Status
import chip.platform.AndroidBleManager
import chip.platform.AndroidChipPlatform
import chip.platform.ChipMdnsCallbackImpl
import chip.platform.DiagnosticDataProviderImpl
import chip.platform.NsdManagerServiceBrowser
import chip.platform.NsdManagerServiceResolver
import chip.platform.PreferencesConfigurationManager
import chip.platform.PreferencesKeyValueStoreManager
import com.google.android.gms.threadnetwork.ThreadBorderAgent
import com.google.android.gms.threadnetwork.ThreadNetwork
import com.google.android.gms.threadnetwork.ThreadNetworkCredentials
import com.google.chip.chiptool.setuppayloadscanner.CHIPDeviceInfo
import com.google.chip.chiptool.util.toAny
import com.rainmaker.AppAllActiveCredentialsActivity
import com.rainmaker.AppPreferredCredentialsActivity
import com.rainmaker.ChipClient
import com.rainmaker.Constants
import com.rainmaker.MatterDeviceHelper
import com.rainmaker.RKNOCChainIssuer
import com.rainmaker.Utils
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import matter.onboardingpayload.DiscoveryCapability
import matter.onboardingpayload.ManualOnboardingPayloadGenerator
import matter.onboardingpayload.OnboardingPayloadParser
import matter.onboardingpayload.QRCodeOnboardingPayloadGenerator
import matter.tlv.AnonymousTag
import matter.tlv.ContextSpecificTag
import matter.tlv.TlvReader
import matter.tlv.TlvWriter
import org.bouncycastle.operator.ContentSigner
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.pkcs.PKCS10CertificationRequestBuilder
import org.bouncycastle.pkcs.jcajce.JcaPKCS10CertificationRequestBuilder
import java.math.BigInteger
import java.security.KeyPair
import java.security.KeyStore
import java.security.PrivateKey
import java.security.cert.Certificate
import java.util.Base64
import java.util.Optional
import javax.security.auth.x500.X500Principal
import kotlin.random.Random


/** CtFlutterMatterPlugin */
class CtFlutterMatterPlugin: FlutterPlugin, ActivityAware, MethodCallHandler{
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity

  private val coroutineScope: CoroutineScope by lazy(LazyThreadSafetyMode.NONE) {
    CoroutineScope(Job() + Dispatchers.Default)
  }

  private var keyStore: KeyStore? = null
  private var awsHelper: CtAwsHelper? = null
  private var devicePtr: Long = 0L
  private lateinit var context: Context
  private lateinit var activity: Activity
  private lateinit var channel : MethodChannel
  private lateinit var androidPlatform: AndroidChipPlatform
  private lateinit var bluetoothManager: CtBluetoothManager
  private lateinit var chipClientCache: MutableMap<String, ChipClient>

  companion object {
    private const val TAG = "CtFlutterMatterPlugin"
    private const val METHOD_CHANNEL_NAME = "ct_flutter_matter_plugin"
  }

  override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    context = flutterPluginBinding.applicationContext
    channel = MethodChannel(flutterPluginBinding.binaryMessenger, METHOD_CHANNEL_NAME)
    channel.setMethodCallHandler(this)
    chipClientCache = mutableMapOf()
  }

  override fun onMethodCall(call: MethodCall, result: Result) {
    CtLog.i(TAG, "onMethodCall() -> method=${call.method}, arguments=${call.arguments}")
    if (call.method == "getPlatformVersion") {
      result.success("Android ${android.os.Build.VERSION.RELEASE}")
    } else if (call.method == "init") {
      init(call, result)
    } else if (call.method == "createMatterClient") {
      createMatterClient(call, result)
    } else if (call.method == "getRootCa") {
      getRootCa(call, result)
    } else if (call.method == "getUserNoc") {
      getUserNoc(call, result)
    } else if (call.method == "close") {
      close(call, result)
    } else if (call.method == "parseQrCode") {
      parseQrCode(call, result)
    } else if (call.method == "parseManualPairingCode") {
      parseManualPairingCode(call, result)
    } else if (call.method == "startScanDevices") {
      startScanDevices(call, result)
    } else if (call.method == "stopScanDevices") {
      stopScanDevices(call, result)
    } else if (call.method == "discoverCommissionableNodes") {
      discoverCommissionableNodes(call, result)
    } else if (call.method == "getDiscoveredDevice") {
      getDiscoveredDevice(call, result)
    } else if (call.method == "readThreadBorderRouterDataset") {
      readThreadBorderRouterDataset(call, result)
    } else if (call.method == "addWiFiDevice") {
      addWiFiDevice(call, result)
    } else if (call.method == "addThreadDeviceWithTBRDataset") {
      addThreadDeviceWithTBRDataset(call, result)
    } else if (call.method == "continueCommissioningDevice") {
      continueCommissioningDevice(call, result)
    } else if (call.method == "readAllAttribute") {
      readAllAttribute(call, result)
    } else if (call.method == "writeAttribute") {
      writeAttribute(call, result)
    } else if (call.method == "readAttribute") {
      readAttribute(call, result)
    } else if (call.method == "writeAttributeWithWildcard") {
      writeAttributeWithWildcard(call, result)
    } else if (call.method == "readAttributeWithWildcard") {
      readAttributeWithWildcard(call, result)
    } else if (call.method == "subscribeAttributesAndEvents") {
      subscribeAttributesAndEvents(call, result)
    } else if (call.method == "subscribeAttributes") {
      subscribeAttributes(call, result)
    } else if (call.method == "unsubscribes") {
      unsubscribes(call, result)
    } else if (call.method == "unpairDevice"){
      unpairDevice(call, result)
    } else if (call.method == "shutdown"){
      shutdown(call, result)
    } else if (call.method == "openPairingWindow"){
      openPairingWindow(call, result)
    } else if (call.method == "openECMPairingWindow"){
      openECMPairingWindow(call, result)
    } else if (call.method == "readPairingWindowStatus"){
      readPairingWindowStatus(call, result)
    } else if (call.method == "addTrustedRootCertificate"){
      addTrustedRootCertificate(call, result)
    } else if (call.method == "readThreadNetworkName"){
      readThreadNetworkName(call, result)
    } else if (call.method == "readAccessControlList"){
      readAccessControlList(call, result)
    } else if (call.method == "getDeviceBasicInfo"){
      getDeviceBasicInfo(call, result)
    } else if (call.method == "writeCatValue"){
      writeCatValue(call, result)
    } else if (call.method == "getNodeId"){
      getNodeId(call, result)
    } else if (call.method == "hasMatterClient"){
      hasMatterClient(call, result)
    } else if (call.method == "removeFabric"){
      removeFabric(call, result)
    } else if (call.method == "generateQRCodeAndManualPairingCode"){
      generateQRCodeAndManualPairingCode(call, result)
    } else if (call.method == "generateRandomDiscriminator"){
      generateRandomDiscriminator(call, result)
    } else if (call.method == "generateRandomPIN"){
      generateRandomPIN(call, result)
    } else if (call.method == "generateRandomSetupPasscode"){
      generateRandomSetupPasscode(call, result)
    } else if (call.method == "getNetworkLocation"){
      getNetworkLocation(call, result)
    } else if (call.method == "readDeviceType"){
      readDeviceType(call, result)
    } else if (call.method == "lightOnOff"){
      lightOnOff(call, result)
    } else if (call.method == "lightToggle"){
      lightToggle(call, result)
    } else if (call.method == "readBssid"){
      readBssid(call, result)
    } else if (call.method == "readFabricsAttribute") {
      readFabricsAttribute(call, result)
    } else if (call.method == "unregisterICDClient") {
      unregisterICDClient(call, result)
    } else if (call.method == "stopDevicePairing") {
      stopDevicePairing(call, result)
    } else if (call.method == "getNetworkInterfaces") {
      getNetworkInterfaces(call, result)
    } else if (call.method == "getICDClientInfo") {
      getICDClientInfo(call, result)
    } else if (call.method == "readRegisteredClients") {
      readRegisteredClients(call, result)
    } else if (call.method == "isThreadSupported") {
      isThreadSupported(call, result)
    } else if (call.method == "isPreferredCredentials") {
      isPreferredCredentials(call, result)
    } else if (call.method == "fetchPreferredThreadCredentials") {
      fetchPreferredThreadCredentials(call, result)
    } else if (call.method == "fetchAllActiveThreadCredentials") {
      fetchAllActiveThreadCredentials(call, result)
    } else if (call.method == "fetchAllThreadCredentials") {
      fetchAllThreadCredentials(call, result)
    } else if (call.method == "saveThreadOperationalCredentials") {
      saveThreadOperationalCredentials(call, result)
    } else if (call.method == "deleteCredentials") {
      deleteCredentials(call, result)
    } else if (call.method == "getFabricIndex") {
      getFabricIndex(call, result)
    } else if (call.method == "startDnssd") {
      startDnssd(call, result)
    } else if (call.method == "stopDnssd") {
      stopDnssd(call, result)
    } else if (call.method == "isRunning") {
      isRunning(call, result)
    } else if (call.method == "getDeviceControllerPtr") {
      getDeviceControllerPtr(call, result)
    } else if (call.method == "getControllerNodeId") {
      getControllerNodeId(call, result)
    } else if (call.method == "getCompressedFabricId") {
      getCompressedFabricId(call, result)
    } else if (call.method == "getDeviceState") {
      getDeviceState(call, result)
    } else {
      result.notImplemented()
    }
  }

  override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
    channel.setMethodCallHandler(null)
    chipClientCache.clear()
  }

  private fun getBluetoothManager() : CtBluetoothManager {
    if (!this::bluetoothManager.isInitialized) {
      this.bluetoothManager = CtBluetoothManager()
    }
    return this.bluetoothManager
  }

  private fun init(call: MethodCall, result: Result) {
    try {
      val scheme = call.argument("scheme") ?: ""
      val host = call.argument("host") ?: ""
      val path = call.argument("path") ?: ""
      awsHelper = CtAwsHelper(scheme, host, path)
      CtLog.i(TAG, "init AWS Fabric Success")
    } catch (e: Exception){
      e.printStackTrace()
      CtLog.e(TAG, "init AWS Fabric Failed")
    }

    if (!this::androidPlatform.isInitialized) {
      try {
        ChipDeviceController.loadJni()
        androidPlatform = AndroidChipPlatform(
          AndroidBleManager(context),
          PreferencesKeyValueStoreManager(context),
          PreferencesConfigurationManager(context),
          NsdManagerServiceResolver(context),
          NsdManagerServiceBrowser(context),
          ChipMdnsCallbackImpl(),
          DiagnosticDataProviderImpl(context)
        )
        CtLog.i(TAG, "init Matter Jni Success")
      } catch (e: Exception){
        e.printStackTrace()
        CtLog.e(TAG, "init Matter Jni Failed")
      }
    }

    result.success(true)
  }

  /*private fun deleteEntry(call: MethodCall, result: Result) {
    val fabricId: String = call.arguments as String
    try {
      keyStore?.deleteEntry(fabricId)
      result.success(true)
      CtLog.i(TAG, "deleteEntry Success")
    } catch (e: Exception){
      e.printStackTrace()
      result.error("deleteEntry", e.message, null)
      CtLog.e(TAG, "deleteEntry Failed")
    }
  }

  private fun isKeyEntry(call: MethodCall, result: Result) {
    val fabricId: String = call.arguments as String
    try {
      val isKeyEntry = keyStore?.isKeyEntry(fabricId) ?: false
      result.success(isKeyEntry)
      CtLog.i(TAG, "isKeyEntry Success")
    } catch (e: Exception){
      e.printStackTrace()
      result.error("isKeyEntry", e.message, null)
      CtLog.e(TAG, "isKeyEntry Failed")
    }
  }*/

  private fun createMatterClient(call: MethodCall, result: Result) {
    val fabricId = call.argument("fabricId") ?: ""
    val rootCa = call.argument("rootCa") ?: ""
    val ipk = call.argument("ipk") ?: ""
    val userNoc = call.argument("userNoc") ?: ""
    val rootCAArn = call.argument("rootCAArn") ?: ""

    if (rootCa.isEmpty() || ipk.isEmpty() || fabricId.isEmpty() || userNoc.isEmpty() || rootCAArn.isEmpty()) {
      CtLog.e(TAG, "args is null.")
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    try {
      keyStore = KeyStore.getInstance("AndroidKeyStore")
      keyStore!!.load(null)
      CtLog.i(TAG, "Success to get KeyStore instance")
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), e.message, null)
      CtLog.e(TAG, "Failed to get KeyStore instance")
      return
    }

    try {
      val certificates = arrayOfNulls<Certificate>(2)
      certificates[0] = Utils.generateCertificate(userNoc)
      certificates[1] = Utils.generateCertificate(rootCa)

//      CtLog.i(TAG, "userNoc=\n${certificates[0]}")
//      CtLog.i(TAG, "rootCa=\n${certificates[1]}")

      // 获取私钥
      val key = keyStore!!.getKey(fabricId, null)
      // 获取私钥
      val privateKey = key as PrivateKey
      // 获取公钥
      val publicKey = keyStore!!.getCertificate(fabricId).publicKey
      // 创建KeyPair
      val keyPair: KeyPair = KeyPair(publicKey, privateKey)

      keyStore!!.setKeyEntry(
        fabricId,
        keyPair.private,
        null,
        certificates
      )

      val chipClient = ChipClient(
        rootCa = rootCa,
        fabricId = fabricId,
        ipk = ipk,
        rootCAArn = rootCAArn,
      )

      val chipDeviceController = chipClient.getChipDeviceController()
      chipDeviceController.setNOCChainIssuer(object: RKNOCChainIssuer(chipClient) {
        override fun onRKNOCChainGenerationNeeded(
          csrInfo: CSRInfo?,
          attestationInfo: AttestationInfo?,
          chipClient: ChipClient
        ) {
          CtLog.d(TAG, "NOCChainIssuer{} -> Received callback for CSR")
          onIssueOperationalCertificate(0)
          if (csrInfo == null) {
            CtLog.e(TAG, "NOCChainIssuer{} -> csrInfo=null")
            handleNocGenerationFailure(chipDeviceController)
            return
          }
          generateNodeCsr(csrInfo, chipClient)
        }
      })
      chipClientCache[fabricId] = chipClient

      CtLog.i(TAG, "create matter client success!")
      result.success(true)
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun close(call: MethodCall, result: Result) {
    val fabricId = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "fabricId is empty", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), e.message, null)
      return
    }

    try {
      chipDeviceController.close()
      result.success(true)
    } catch (e: Exception) {
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun parseQrCode(call: MethodCall, result: Result) {
    val qrcode: String = call.argument("qrcode") ?: ""
    try {
      val deviceInfo = CHIPDeviceInfo.fromSetupPayload(
        OnboardingPayloadParser().parseQrCode(qrcode))
      val map = mutableMapOf<String, Any>()
      map["version"] = deviceInfo.version
      map["vendorId"] = deviceInfo.vendorId
      map["productId"] = deviceInfo.productId
      map["commissioningFlow"] = deviceInfo.commissioningFlow
      map["discriminator"] = deviceInfo.discriminator
      map["isShortDiscriminator"] = deviceInfo.isShortDiscriminator
      map["setupPinCode"] = deviceInfo.setupPinCode
      map["ipAddress"] = deviceInfo.ipAddress ?: ""
      map["port"] = deviceInfo.port

      CtLog.i(TAG, "discoveryCapabilities:${deviceInfo.discoveryCapabilities}")

      var discoveryCapabilities = "BLE"
      deviceInfo.discoveryCapabilities.forEach {
        if (it == DiscoveryCapability.BLE) {
          discoveryCapabilities = "BLE"
        } else if (it == DiscoveryCapability.SOFT_AP) {
          discoveryCapabilities = "SoftAP"
        } else if (it == DiscoveryCapability.ON_NETWORK) {
          discoveryCapabilities = "onNetwork"
        }
      }
      map["discoveryCapabilities"] = discoveryCapabilities
      result.success(map)
    } catch (e: Exception){
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }
  private fun parseManualPairingCode(call: MethodCall, result: Result) {
    val code: String = call.argument("code") ?: ""
    try {
      val deviceInfo = CHIPDeviceInfo.fromSetupPayload(
        OnboardingPayloadParser().parseManualPairingCode(code))
      val map = mutableMapOf<String, Any>()
      map["version"] = deviceInfo.version
      map["vendorId"] = deviceInfo.vendorId
      map["productId"] = deviceInfo.productId
      map["commissioningFlow"] = deviceInfo.commissioningFlow
      map["discriminator"] = deviceInfo.discriminator
      map["isShortDiscriminator"] = deviceInfo.isShortDiscriminator
      map["setupPinCode"] = deviceInfo.setupPinCode
      map["ipAddress"] = deviceInfo.ipAddress ?: ""
      map["port"] = deviceInfo.port

      CtLog.i(TAG, "discoveryCapabilities:${deviceInfo.discoveryCapabilities}")

      var discoveryCapabilities = "BLE"
      deviceInfo.discoveryCapabilities.forEach {
        if (it == DiscoveryCapability.BLE) {
          discoveryCapabilities = "BLE"
        } else if (it == DiscoveryCapability.SOFT_AP) {
          discoveryCapabilities = "SoftAP"
        } else if (it == DiscoveryCapability.ON_NETWORK) {
          discoveryCapabilities = "OnNetwork"
        }
      }
      map["discoveryCapabilities"] = discoveryCapabilities

      result.success(map)
    } catch (e: Exception){
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun getRootCa(call: MethodCall, result: Result) {
    val thingName: String = call.arguments as String

    if (awsHelper == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "awsHelper = null", null)
      return
    }

    coroutineScope.launch {
      try {
        val rootCaData = awsHelper!!.getRootCa(thingName)
        result.success(rootCaData)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun generateUserCsr(fabricId: String) : String? {
    CtLog.i(TAG, "generateUserCsr() -> fabricId=$fabricId")
    try {
      val keyPair: KeyPair = Utils.generateKeypair(fabricId)
      val p10Builder: PKCS10CertificationRequestBuilder = JcaPKCS10CertificationRequestBuilder(
        X500Principal(""), keyPair.public)
      val csBuilder = JcaContentSignerBuilder("SHA256withECDSA")
      var signer: ContentSigner? = null
      var csrContent: String? = null

      try {
        signer = csBuilder.build(keyPair.private)
      } catch (e: Exception) {
        e.printStackTrace()
        return null
      }

      val csr = p10Builder.build(signer)

      try {
        csrContent = Base64.getEncoder().encodeToString(csr.encoded)
      } catch (e: Exception) {
        e.printStackTrace()
        return null
      }

      CtLog.i(TAG, "generateUserCsr() -> userCSRContent=$csrContent")

      val finalCsr = Constants.BEGIN_CERTIFICATE_REQUEST + Utils.splitAndConcatenateCsr(csrContent!!) + Constants.END_CERTIFICATE_REQUEST

      CtLog.i(TAG, "generateUserCsr() -> userCSR=$finalCsr")

      return finalCsr
    } catch (e: Exception){
      e.printStackTrace()
      return null
    }
  }

  private fun getUserNoc(call: MethodCall, result: Result) {
    val fabricId = call.argument("fabricId") ?: ""
    val rootCAArn = call.argument("rootCAArn") ?: ""

    if (fabricId.isEmpty() || rootCAArn.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "fabricId or rootCAArn is empty", null)
      return
    }

    if (awsHelper == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "awsHelper = null", null)
      return
    }

//    try {
//      keyStore = KeyStore.getInstance("AndroidKeyStore")
//      keyStore!!.load(null)
//      CtLog.i(TAG, "Success to get KeyStore instance")
//    } catch (e: Exception){
//      e.printStackTrace()
//      result.error("getUserNoc", e.message, null)
//      CtLog.e(TAG, "Failed to get KeyStore instance")
//      return
//    }

    coroutineScope.launch {
      try {
        val csr: String? = generateUserCsr(fabricId)
        if (csr == null) {
          result.error(CtStatusCode.PARAMETER_ERROR.toString(), "generateUserCsr error", null)
          return@launch
        }

        val userNocData = awsHelper!!.getUserNoc(csr, rootCAArn)
        val userNoc = userNocData["noc"]
        if (userNoc.isNullOrEmpty()){
          result.error(CtStatusCode.PARAMETER_ERROR.toString(), "getUserNoc error", null)
          return@launch
        }

        result.success(userNoc)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun generateNodeCsr(csrInfo: CSRInfo?, chipClient: ChipClient) {
    val tempCsr = Base64.getEncoder().encodeToString(csrInfo!!.csr)
    val finalCsr = Constants.BEGIN_CERTIFICATE_REQUEST + Utils.splitAndConcatenateCsr(tempCsr) + Constants.END_CERTIFICATE_REQUEST
    CtLog.i(TAG, "generateNodeCsr() -> nodeCSR=$finalCsr")
    getNodeNoc(finalCsr, chipClient)
  }

  private fun getNodeNoc(csr: String, chipClient: ChipClient) {
    val chipDeviceController = chipClient.getChipDeviceController()
    if (awsHelper == null) {
      handleNocGenerationFailure(chipDeviceController)
      return
    }

    coroutineScope.launch {
      var retryCount = 0
      val maxRetries = 3
      var success = false
      var nodeNoc: String? = null
      var nodeId: String? = null

      while (retryCount < maxRetries && !success) {
        try {
          val nodeNocMap = awsHelper!!.getNodeNoc(csr, chipClient.getRootCAArn())
          nodeNoc = nodeNocMap["noc"]
          nodeId = nodeNocMap["nodeId"]

          if (!nodeNoc.isNullOrEmpty()) {
            success = true
          } else {
            CtLog.w(TAG, "Empty response, retry ${retryCount + 1}/$maxRetries")
          }
        } catch (e: Exception) {
          CtLog.e(TAG, "getNodeNoc failed (attempt ${retryCount + 1}): ${e.message}")
          if (retryCount < maxRetries - 1) {
            val delayTime = 3000L
            delay(delayTime)
          }
          retryCount++
        }
      }

      if (success && nodeNoc != null && nodeId != null) {
        handleNocSuccess(chipDeviceController, nodeNoc, nodeId, chipClient)
      } else {
        handleNocGenerationFailure(chipDeviceController)
      }
    }
  }

  private fun handleNocSuccess(
    chipDeviceController: ChipDeviceController,
    nodeNoc: String,
    nodeId: String,
    chipClient: ChipClient
  ) {
    CtLog.i(TAG, "nodeId: $nodeId")
    try {
      val chain = arrayOf(
        Utils.generateCertificate(nodeNoc),
        Utils.generateCertificate(chipClient.getRootCa())
      )

      val result = chipDeviceController.onNOCChainGeneration(
        ControllerParams.newBuilder()
          .setRootCertificate(chain[1].encoded)
          .setIntermediateCertificate(chain[1].encoded)
          .setOperationalCertificate(chain[0].encoded)
          .setOperationalCertificate(Utils.generateCertificate(nodeNoc).encoded)
          .setIpk(chipClient.getIpkEpochKey())
          .build()
      )
      CtLog.i(TAG, "NOCChainGenerated success, result: $result")

      onIssueOperationalCertificate(1)
    } catch (e: Exception) {
      CtLog.e(TAG, "Failed to process NOC chain: ${e.message}")
      handleNocGenerationFailure(chipDeviceController)
    }
  }

  // 0 start
  // 1 success
  // 2 failure
  private fun onIssueOperationalCertificate(status: Int) {
    coroutineScope.launch {
      withContext(Dispatchers.Main) {
        channel.invokeMethod("onIssueOperationalCertificate", status)
      }
    }
  }

  private fun handleNocGenerationFailure(chipDeviceController: ChipDeviceController) {
    val result = chipDeviceController.onNOCChainGeneration(
      ControllerParams.newBuilder().build()
    )
    CtLog.e(TAG, "NOCChainGenerated failed after retries, result: $result")
    onIssueOperationalCertificate(2)
  }

  private fun startScanDevices(call: MethodCall, result: Result) {
    val timeout: Int = call.argument("timeout") ?: 60000
    coroutineScope.launch {
      getBluetoothManager().setTimeMillis(timeout.toLong())
      getBluetoothManager().startScanDevices{
        channel.invokeMethod("onScanDevices", it)
      }
    }
    result.success(true)
  }

  private fun stopScanDevices(call: MethodCall, result: Result) {
    coroutineScope.launch {
      getBluetoothManager().stopScanDevices()
    }
    result.success(true)
  }

  private fun discoverCommissionableNodes(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "fabricId is empty", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        chipDeviceController.discoverCommissionableNodes()
        result.success(true)
      } catch (e: Exception) {
        e.printStackTrace()
       result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun getDiscoveredDevice(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "fabricId is empty", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        for (i in 0..10) {
          CtLog.i(TAG, "index:${i}")
          val device = chipDeviceController.getDiscoveredDevice(i)
          if (device == null) {
            continue
          }
          CtLog.i(TAG, "device:${device}")

          val map = mutableMapOf<String, Any>()
          map["vendorID"] = device.vendorId
          map["productID"] = device.productId
          map["instanceName"] = device.instanceName
          map["discriminator"] = device.discriminator
          map["deviceType"] = device.deviceType
          map["pairingInstruction"] = device.pairingInstruction
          map["commissioningWindowStatus"] = device.commissioningMode.ordinal
          map["deviceName"] = device.deviceName
          map["ipAddress"] = device.ipAddress ?: ""
          map["port"] = device.port
          map["discoveryCapabilities"] = "OnNetwork"

          withContext(Dispatchers.Main) {
            channel.invokeMethod("onScanDevices", map)
          }
        }
        result.success(true)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readThreadBorderRouterDataset(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }
    
    try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        coroutineScope.launch {
          val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
          val attributePath =
            ChipAttributePath.newInstance(
              0,
              0x131BFC02L,
              0x0L
            )
          val callback = object : ReportCallback {
            override fun onError(
              attributePath: ChipAttributePath?,
              eventPath: ChipEventPath?,
              e: java.lang.Exception
            ) {
              e.printStackTrace()
              result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
            }

            override fun onReport(nodeState: NodeState?) {
              CtLog.i(TAG, "readThreadBorderRouterDataset() -> state:${nodeState}")
              if (nodeState != null) {
                val attrValue = nodeState
                  .getEndpointState(attributePath.endpointId.id.toInt())!!
                  .getClusterState(attributePath.clusterId.id)!!
                  .getAttributeState(attributePath.attributeId.id)

                if (attrValue != null) {
                  val dataset = attrValue.value as ByteArray?
                  result.success(dataset!!)
                } else {
                  result.error(CtStatusCode.PARAMETER_ERROR.toString(), "attribute value is null", null)
                }
              } else {
                result.error(CtStatusCode.PARAMETER_ERROR.toString(), "nodeState is null", null)
              }
            }
          }
          chipDeviceController.readAttributePath(callback, devicePointer, listOf(
            attributePath
          ), 0)
        }
    } catch (e: Exception){
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun addWiFiDevice(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val code: String = call.argument("code") ?: ""
    val ssid: String = call.argument("ssid") ?: ""
    val pwd: String = call.argument("pwd") ?: ""

    if (fabricId.isEmpty() || code.isEmpty() || ssid.isEmpty() || pwd.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val deviceManager = CtAddDeviceManagerImpl(context, coroutineScope, chipDeviceController)

    val deviceAttestationCallback: DeviceAttestationCallback = { devicePtr,
                                                                 attestationInfo,
                                                                 errorCode ->
      this.devicePtr = devicePtr

      val vid = attestationInfo?.vendorId ?: 0;
      val pid = attestationInfo?.productId ?: 0;

      val infoMap = mapOf(
        "vendorId" to vid,
        "productId" to pid
      )

      val map = mapOf(
        "attestationInfo" to infoMap,
        "errorCode" to errorCode
      )

      coroutineScope.launch {
        withContext(Dispatchers.Main) {
          channel.invokeMethod("onDeviceAttestation", map)
        }
      }
    }

    val readCommissioningInfoCallback: ReadCommissioningInfoCallback = { vid,
                                                                         pid ->
      val infoMap = mapOf(
        "vendorId" to vid,
        "productId" to pid
      )

      coroutineScope.launch {
        withContext(Dispatchers.Main) {
          channel.invokeMethod("onReadCommissioningInfo", infoMap)
        }
      }
    }

    val commissioningCompleteCallback: CommissioningCompleteCallback = { nodeId, errorCode ->
      result.success(mapOf("nodeId" to nodeId, "errorCode" to errorCode))
    }

    deviceManager.addWiFiDevice(code, ssid, pwd, deviceAttestationCallback, readCommissioningInfoCallback, commissioningCompleteCallback)
  }

  private fun addThreadDeviceWithTBRDataset(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val code: String = call.argument("code") ?: ""
    val dataset: String = call.argument("dataset") ?: ""

    if (fabricId.isEmpty() || code.isEmpty() || dataset.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val deviceManager = CtAddDeviceManagerImpl(context, coroutineScope, chipDeviceController)

    val deviceAttestationCallback: DeviceAttestationCallback = { devicePtr,
                                                                 attestationInfo,
                                                                 errorCode ->
      this.devicePtr = devicePtr

      val vid = attestationInfo?.vendorId ?: 0;
      val pid = attestationInfo?.productId ?: 0;

      val infoMap = mapOf(
        "vendorId" to vid,
        "productId" to pid
      )

      val map = mapOf(
        "attestationInfo" to infoMap,
        "errorCode" to errorCode
      )

      coroutineScope.launch {
        withContext(Dispatchers.Main) {
          channel.invokeMethod("onDeviceAttestation", map)
        }
      }
    }

    val readCommissioningInfoCallback: ReadCommissioningInfoCallback = { vid,
                                                                         pid ->
      val infoMap = mapOf(
        "vendorId" to vid,
        "productId" to pid
      )

      coroutineScope.launch {
        withContext(Dispatchers.Main) {
          channel.invokeMethod("onReadCommissioningInfo", infoMap)
        }
      }
    }

    val commissioningCompleteCallback: CommissioningCompleteCallback = { nodeId, errorCode ->
      result.success(mapOf("nodeId" to nodeId, "errorCode" to errorCode))
    }

    deviceManager.addThreadDevice(code, dataset, deviceAttestationCallback, readCommissioningInfoCallback, commissioningCompleteCallback)

//    AppCommissioningManager.setChipDeviceController(chipDeviceController)
//    AppCommissioningManager.setAppCommissioningCallback(object : AppCommissioningCallback {
//      override fun onCommissioningComplete(errorCode: Long) {
//        val map = mutableMapOf<String, Any>()
//        map["nodeId"] = AppCommissioningManager.getCommissioningNodeId() ?: ""
//        map["errorCode"] = errorCode
//        result.success(map)
//      }
//    })
//
//    val intent = Intent(activity, AppCommissioningActivity::class.java)
//    intent.putExtra("code", code)
//    activity.startActivity(intent)
  }

  private fun continueCommissioningDevice(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val ignoreAttestationFailure: Boolean = call.argument("ignoreAttestationFailure") ?: false

    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    try {
      CtLog.i(TAG, "continueCommissioningDevice() -> devicePtr: ${this.devicePtr}")
      chipDeviceController.continueCommissioning(this.devicePtr, ignoreAttestationFailure)
      CtLog.i(TAG, "continueCommissioningDevice() -> success")
      result.success(true)
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.DEVICE_ATTESTATION_ERROR.toString(), e.message, null)
      CtLog.e(TAG, "continueCommissioningDevice() -> error: ${e.message}")
    }

  }

  private fun readAllAttribute(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpointId: Number? = call.argument("endpointId")
    val clusterId: Number? = call.argument("clusterId")

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val endpointChipPathId = if (endpointId != null)
      ChipPathId.forId(endpointId.toLong()) else ChipPathId.forWildcard()
    val clusterChipPathId = if (clusterId != null)
      ChipPathId.forId(clusterId.toLong()) else ChipPathId.forWildcard()
    val attributeChipPathId = ChipPathId.forWildcard()

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val callback = object : ReportCallback {
          override fun onError(
            attributePath: ChipAttributePath?,
            eventPath: ChipEventPath?,
            e: java.lang.Exception
          ) {
            e.printStackTrace()
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
          }

          override fun onReport(nodeState: NodeState?) {
            CtLog.i(TAG, "onReport() -> state:${nodeState?.endpointStates}")
            val map = mutableMapOf<String,Any>()
            nodeState?.let {
              it.endpointStates.forEach { ep ->
                CtLog.i(
                  TAG,
                  "onReport() -> Endpoint{${ep.key}}"
                )
                ep.value.clusterStates.forEach { cluster ->
                  CtLog.i(
                    TAG,
                    "onReport() -> Cluster{${cluster.key}}"
                  )
                  cluster.value.attributeStates.forEach { attribute ->
                    val key = ChipIdLookup.attributeIdToName(cluster.key, attribute.key)
                    CtLog.i(
                      TAG,
                      "onReport() -> Attribute{${key}}"
                    )

                    val value = TlvReader(attribute.value.tlv).nextElement().value.toAny()
                    map[key] = value ?: ""

//                    val value = attribute.value.value
//                    map[key] = value

                  }
                }
              }
            }
            result.success(map)
          }
        }
        chipDeviceController.readAttributePath(callback, devicePointer, listOf(
          ChipAttributePath.newInstance(
            endpointChipPathId,
            clusterChipPathId,
            attributeChipPathId
          )
        ), 0)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun writeAttribute(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpointId: Number = call.argument("endpointId") ?: 0
    val clusterId: Number = call.argument("clusterId") ?: 0
    val attributeId: Number = call.argument("attributeId") ?: 0
    val type: String = call.argument("type") ?: ""
    val value: String = call.argument("value") ?: ""
    val timedWriteTimeout: Number = call.argument("timedWriteTimeout") ?: 0

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val tlvWriter = TlvWriter()
    var writeRequest : AttributeWriteRequest? = null
    if (type == "UnsignedInteger") {
      tlvWriter.put(AnonymousTag, value.toUInt())
    } else if (type == "SignedInteger") {
      tlvWriter.put(AnonymousTag, value.toInt())
    } else if (type == "Boolean") {
      tlvWriter.put(AnonymousTag, value.toBoolean())
    } else if (type == "String") {
      tlvWriter.put(AnonymousTag, value)
    } else if (type == "Float") {
      tlvWriter.put(AnonymousTag, value.toFloat())
    } else if (type == "Double") {
      tlvWriter.put(AnonymousTag, value.toDouble())
    } else if (type == "json") {
      writeRequest = AttributeWriteRequest.newInstance(
        endpointId.toInt(),
        clusterId.toLong(),
        attributeId.toLong(),
        value
      )
    } else if (type == "ByteArray") {
      tlvWriter.put(AnonymousTag, value.chunked(2).map { it.toInt(16) and 0xFF }
        .map { it.toByte() }.toByteArray())
    } else {
      CtLog.i(TAG, "Unsupported type.")
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "Unsupported type", null)
      return
    }

    if (writeRequest == null) {
      writeRequest = AttributeWriteRequest.newInstance(
        endpointId.toInt(),
        clusterId.toLong(),
        attributeId.toLong(),
        tlvWriter.getEncoded()
      )
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val callback = object : WriteAttributesCallback {
          override fun onError(attributePath: ChipAttributePath?, e: java.lang.Exception?) {
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e?.message, null)
          }

          override fun onResponse(attributePath: ChipAttributePath?, status: Status?) {
            val tempStatus = status?.status ?: Status.Code.Failure
            CtLog.i(TAG, "writeAttribute() -> status=${tempStatus}")
            if (tempStatus == Status.Code.Success) {
              result.success(true)
            } else {
              result.error(CtStatusCode.PARAMETER_ERROR.toString(), "writeAttribute error, status=$tempStatus", null)
            }
          }
        }
        chipDeviceController.write(callback, devicePointer, listOf(writeRequest), timedWriteTimeout.toInt(), 0)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readAttribute(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpointId: Number = call.argument("endpointId") ?: 0
    val clusterId: Number = call.argument("clusterId") ?: 0
    val attributeId: Number = call.argument("attributeId") ?: 0

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val callback = object : ReportCallback {
          override fun onError(
            attributePath: ChipAttributePath?,
            eventPath: ChipEventPath?,
            e: java.lang.Exception
          ) {
            e.printStackTrace()
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
          }

          override fun onReport(nodeState: NodeState?) {
            CtLog.i(TAG, "onReport() -> state:${nodeState?.endpointStates}")
            val map = mutableMapOf<String,Any>()
            nodeState?.let {
              it.endpointStates.forEach { ep ->
                CtLog.i(
                  TAG,
                  "onReport() -> Endpoint{} key=${ep.key}, value=${ep.value}"
                )
                ep.value.clusterStates.forEach { cluster ->
                  CtLog.i(
                    TAG,
                    "onReport() -> Cluster{} key=${cluster.key}, value=${cluster.value}"
                  )
                  cluster.value.attributeStates.forEach { attribute ->
                    val key = ChipIdLookup.attributeIdToName(cluster.key, attribute.key)
                    val value = TlvReader(attribute.value.tlv).nextElement().value.toAny()
                    CtLog.i(
                      TAG,
                      "onReport() -> Attribute{} key=${key}, value=${value}"
                    )
                    map[key] = value ?: ""
                  }
                }
              }
            }
            result.success(map)
          }
        }
        val attributePath = ChipAttributePath.newInstance(
          endpointId.toInt(),
          clusterId.toLong(),
          attributeId.toLong()
        )
        chipDeviceController.readAttributePath(callback, devicePointer, listOf(attributePath), 0)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun writeAttributeWithWildcard(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpointId: Number = call.argument("endpointId") ?: 0
    val clusterName: String = call.argument("clusterName") ?: ""
    val commandName: String = call.argument("commandName") ?: ""
    val parameter: Any = call.argument("parameter") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    val newNodeId = BigInteger(nodeId, 16).toLong()
    val wildcardManager = CtWildcardManagerImpl(chipClient, coroutineScope)
    wildcardManager.execute(newNodeId, endpointId.toInt(), clusterName, commandName, parameter,
      fun(response: Any) {
      result.success(response)
    }, fun(e: Exception) {
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    })

  }

  private fun readAttributeWithWildcard(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpointId: Number = call.argument("endpointId") ?: 0
    val clusterName: String = call.argument("clusterName") ?: ""
    val commandName: String = call.argument("commandName") ?: ""
    val parameter: Any = call.argument("parameter") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    val newNodeId = BigInteger(nodeId, 16).toLong()
    val wildcardManager = CtWildcardManagerImpl(chipClient, coroutineScope)
    wildcardManager.execute(newNodeId, endpointId.toInt(), clusterName, commandName, parameter,
      fun(response: Any) {
      result.success(response)
    }, fun(e: Exception) {
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    })

  }

  private fun subscribeAttributesAndEvents(call: MethodCall, result: Result){
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val callback = CtSubscribeAttributesAndEventsCallbackImpl(coroutineScope, channel)
        chipDeviceController.subscribeToPath(
          callback,
          callback,
          callback,
          devicePointer,
          listOf(
            ChipAttributePath.newInstance(
              ChipPathId.forWildcard(),
              ChipPathId.forWildcard(),
              ChipPathId.forWildcard())
          ),
          listOf(
            ChipEventPath.newInstance(
              ChipPathId.forWildcard(),
              ChipPathId.forWildcard(),
              ChipPathId.forWildcard()
            )
          ),
          0,
          0,
          false,
          false,
          0
        )

        result.success(true)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun subscribeAttributes(call: MethodCall, result: Result){
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val callback = CtSubscribeAttributesCallbackImpl(coroutineScope, channel)
        chipDeviceController.subscribeToAttributePath(
          callback,
          callback,
          devicePointer,
          listOf(
            ChipAttributePath.newInstance(
            ChipPathId.forWildcard(),
            ChipPathId.forWildcard(),
            ChipPathId.forWildcard())
          ),
          0,
          0,
          0
          )

        result.success(true)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun unsubscribes(call: MethodCall, result: Result){
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    try {
      chipDeviceController.shutdownSubscriptions()
      result.success(true)
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), "unsubscribes error", null)
    }
  }

  private fun unpairDevice(call: MethodCall, result: Result){
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    try {
      val newNodeId = BigInteger(nodeId, 16).toLong()
      chipDeviceController.unpairDeviceCallback(newNodeId, object: UnpairDeviceCallback{
        override fun onError(status: Int, remoteDeviceId: Long) {
          CtLog.i(TAG, "onError() -> status=${status}, remoteDeviceId=${remoteDeviceId}")
          result.error(CtStatusCode.PARAMETER_ERROR.toString(), "unpair device error, status=${status}", null)
        }

        override fun onSuccess(remoteDeviceId: Long) {
          CtLog.i(TAG, "onSuccess() -> remoteDeviceId=${remoteDeviceId}")
          result.success(nodeId)
        }
      })
    } catch (e: Exception) {
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }


  private fun shutdown(call: MethodCall, result: Result){
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    if (chipClientCache.contains(fabricId)) {
      val chipClient = chipClientCache.remove(fabricId)
      CtLog.i(TAG, "shutdown() -> preparing to remove the chip-client...")

      try {
        chipClient?.getChipDeviceController()?.shutdownCommissioning()
        CtLog.i(TAG, "shutdown() -> remove chip-client success!")
      } catch (e: Exception) {
        e.printStackTrace()
      }

      result.success(true)
    } else {
      CtLog.e(TAG, "shutdown() -> chip-client not found")
      result.success(false)
    }
  }


  private fun openPairingWindow(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val duration: Number = call.argument("duration") ?: 0

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        chipDeviceController.openPairingWindowCallback(
          devicePointer,
          duration.toInt(),
          object : OpenCommissioningCallback {
            override fun onError(status: Int, deviceId: Long) {
              CtLog.e(TAG, "OpenBasicCommissioning Fail! \nnodeId: $deviceId\nstatus: $status")
              result.error(CtStatusCode.PARAMETER_ERROR.toString(), "OpenBasicCommissioning Fail!", null)
            }

            override fun onSuccess(deviceId: Long, manualPairingCode: String?, qrCode: String?) {
              CtLog.i(TAG, "openPairingWindow Success! \ndeviceId: $deviceId, \nmanualPairingCode: $manualPairingCode, \nqrCode: $qrCode")
              val map = mapOf(
                "manualPairingCode" to manualPairingCode,
                "qrCode" to qrCode
              )
              result.success(map)
            }
          }
        )
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun openECMPairingWindow(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val commissioningTimeout: Int = call.argument("commissioningTimeout") ?: 0
    val setupPinCode: Long = (call.argument("setupPinCode") ?: 0).toLong()
    val discriminator: Int = call.argument("discriminator") ?: 0
    val iteration = 10000L
    val salt = Random.nextBytes(32)
    val timedInvokeTimeoutMs = 10000

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val administratorCommissioningCluster = ChipClusters.AdministratorCommissioningCluster(
          devicePointer,
          0
        )
        val verifier = chipDeviceController.computePaseVerifier(devicePointer, setupPinCode, iteration, salt)
        administratorCommissioningCluster.openCommissioningWindow(
          object : ChipClusters.DefaultClusterCallback {
            override fun onError(error: java.lang.Exception?) {
              result.error(CtStatusCode.PARAMETER_ERROR.toString(), error?.message ?: "open ECM pairing window error.", null)
            }

            override fun onSuccess() {
              result.success(true)
            }
          },
          commissioningTimeout,
          verifier.pakeVerifier,
          discriminator,
          iteration,
          salt,
          timedInvokeTimeoutMs
        )
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readPairingWindowStatus(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpointId = 0
    val clusterId = AdministratorCommissioning.ID
    val attributeId = AdministratorCommissioning.Attribute.WindowStatus.id
    val attributePath = ChipAttributePath.newInstance(endpointId, clusterId, attributeId)

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        chipDeviceController.readAttributePath(
          object : ReportCallback {
            override fun onReport(nodeState: NodeState?) {
              val tlv =
                nodeState
                  ?.getEndpointState(endpointId)
                  ?.getClusterState(clusterId)
                  ?.getAttributeState(attributeId)
                  ?.tlv
              val status = tlv?.let { TlvReader(it).toAny() }
              CtLog.i(TAG, "readPairingWindowStatus Success: $status")
              result.success(status)
            }

            override fun onError(
              attributePath: ChipAttributePath?,
              eventPath: ChipEventPath?,
              e: Exception
            ) {
              CtLog.e(TAG, "readPairingWindowStatus Error: ${e.message}")
              result.error(CtStatusCode.PARAMETER_ERROR.toString(), e.message, null)
            }
          },
          devicePointer,
          listOf(attributePath),
          0
        )
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun addTrustedRootCertificate(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val rootCACertificate: String = call.argument("rootCACertificate") ?: ""
    if (fabricId.isEmpty() || nodeId.isEmpty() || rootCACertificate.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val operationalCredentialsCluster =
          ChipClusters.OperationalCredentialsCluster(devicePointer, 0)
        val callback = object: ChipClusters.DefaultClusterCallback {
          override fun onError(error: java.lang.Exception?) {
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
          }

          override fun onSuccess() {
            result.success(true)
          }
        }
        operationalCredentialsCluster.addTrustedRootCertificate(callback, rootCACertificate.toByteArray())
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readThreadNetworkName(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val cluster = ThreadNetworkDiagnosticsCluster(devicePointer, 0)
        cluster.readNetworkNameAttribute(object: ThreadNetworkDiagnosticsCluster.NetworkNameAttributeCallback {
          override fun onError(error: java.lang.Exception?) {
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
          }

          override fun onSuccess(value: String?) {
            result.success(value ?: "")
          }
        })
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readAccessControlList(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val cluster = ChipClusters.AccessControlCluster(devicePointer, 0)
        cluster.readAclAttribute(
          object : ChipClusters.AccessControlCluster.AclAttributeCallback {
            override fun onSuccess(valueList: MutableList<ChipStructs.AccessControlClusterAccessControlEntryStruct>?) {
              CtLog.i(TAG,  "readAccessControlList success: $valueList")
              valueList?.forEach {
                it.subjects?.forEach {
                  CtLog.i(TAG,  "subject: $it")
                }
              }
              result.success("")
            }
            override fun onError(e: Exception) {
              e.printStackTrace()
              result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
            }
          })
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun getDeviceBasicInfo(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val endpoint = call.argument("endpoint") ?: 0

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val descriptorCluster = ChipClusters.DescriptorCluster(devicePointer, endpoint)
        val helper = MatterDeviceHelper(devicePointer)
        val basicInfo = helper.getDeviceBasicInfo(descriptorCluster)
        CtLog.i(TAG, "getDeviceBasicInfo Success: $basicInfo")
        result.success(basicInfo)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun writeCatValue(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val catIdOperate = call.argument("catIdOperate") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty() || catIdOperate.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val helper = MatterDeviceHelper(devicePointer)
        helper.writeCatValue(catIdOperate)
        CtLog.i(TAG, "writeCatValue Success!")
        result.success(true)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun getNodeId(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }
    result.success("")
  }

  private fun hasMatterClient(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }
    val chipClient: ChipClient? = chipClientCache[fabricId]
    result.success(chipClient != null)
  }

  private fun removeFabric(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }
    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val tlvWriter = TlvWriter()
        tlvWriter.startStructure(AnonymousTag)
        tlvWriter.put(
          ContextSpecificTag(OperationalCredentials.RemoveFabricCommandField.FabricIndex.id),
          chipDeviceController.fabricIndex
        )
        tlvWriter.endStructure()
        val invokeElement =
          InvokeElement.newInstance(
            0,
            OperationalCredentials.ID,
            OperationalCredentials.Command.RemoveFabric.id,
            tlvWriter.getEncoded(),
            null
          )

        chipDeviceController.invoke(
          object : InvokeCallback {
            override fun onError(ex: Exception?) {
              result.error(CtStatusCode.UNKNOWN_ERROR.toString(), ex?.message ?: "", null)
            }

            override fun onResponse(invokeElement: InvokeElement?, code: Long) {
              CtLog.i(TAG, "response : $invokeElement, code : $code")
              CtLog.i(TAG, "remove fabric success!")
              result.success(true)
            }
          },
          devicePointer,
          invokeElement,
          0,
          0
        )
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun generateQRCodeAndManualPairingCode(call: MethodCall, result: Result) {
    val vendorId: Int = call.argument("vendorId") ?: 0
    val productId: Int = call.argument("productId") ?: 0
    val discriminator: Int = call.argument("discriminator") ?: 0
    val setupPinCode: Number = call.argument("setupPinCode") ?: 0
    val commissioningFlow: Int = call.argument("commissioningFlow") ?: 0
    val discoveryCapabilities: String = call.argument("discoveryCapabilities") ?: "BLE"
    val isShortDiscriminator: Boolean = call.argument("isShortDiscriminator") ?: false
    val ipAddress: String = call.argument("ipAddress") ?: ""
    val port: Int = call.argument("port") ?: 5540

    val discoveryCapabilitiesSet: MutableSet<DiscoveryCapability>
    if (discoveryCapabilities == "BLE") {
      discoveryCapabilitiesSet = mutableSetOf(DiscoveryCapability.BLE)
    } else if (discoveryCapabilities == "OnNetwork") {
      discoveryCapabilitiesSet = mutableSetOf(DiscoveryCapability.ON_NETWORK)
    } else if (discoveryCapabilities == "SoftAP") {
      discoveryCapabilitiesSet = mutableSetOf(DiscoveryCapability.SOFT_AP)
    } else {
      discoveryCapabilitiesSet = mutableSetOf(
        DiscoveryCapability.BLE,
        DiscoveryCapability.ON_NETWORK,
        DiscoveryCapability.SOFT_AP
      )
    }

    CtLog.i(TAG,"generateQRCodeAndManualPairingCode() -> discoveryCapabilities: ${discoveryCapabilities}")

    try {
      val chipDeviceInfo = CHIPDeviceInfo(
        vendorId = vendorId,
        productId = productId,
        discriminator = discriminator,
        setupPinCode = setupPinCode.toLong(),
        commissioningFlow = commissioningFlow,
        discoveryCapabilities = discoveryCapabilitiesSet,
        isShortDiscriminator = isShortDiscriminator,
        ipAddress = ipAddress,
        port = port
      )
      val onBoardingPayload = chipDeviceInfo.toSetupPayload()
      val qrCodeOnboardingPayloadGenerator = QRCodeOnboardingPayloadGenerator(onBoardingPayload)
      val manualOnboardingPayloadGenerator = ManualOnboardingPayloadGenerator(onBoardingPayload)
      val qrCode = qrCodeOnboardingPayloadGenerator.payloadBase38RepresentationWithAutoTLVBuffer()
      val manualPairingCode = manualOnboardingPayloadGenerator.payloadDecimalStringRepresentation()
      CtLog.i(TAG, "QRCode: $qrCode, manualPairingCode: $manualPairingCode")
      val map = mapOf("qrCode" to qrCode, "manualPairingCode" to manualPairingCode)
      result.success(map)
    } catch (e: Exception) {
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun generateRandomDiscriminator(call: MethodCall, result: Result) {
      try {
        val discriminator = CtSetupPayloadUtils.generateRandomDiscriminator()
        result.success(discriminator)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
  }

  private fun generateRandomPIN(call: MethodCall, result: Result) {
      try {
        val pin = CtSetupPayloadUtils.generateRandomPIN()
        result.success(pin)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
  }

  private fun generateRandomSetupPasscode(call: MethodCall, result: Result) {
    this.generateRandomPIN(call, result)
  }

  private fun getNetworkLocation(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val networkLocation = chipDeviceController.getNetworkLocation(devicePointer)
        val map = mapOf(
          "ipAddress" to networkLocation.ipAddress,
          "port" to networkLocation.port,
          "interfaceIndex" to networkLocation.interfaceIndex,
        )
        CtLog.i(TAG, "getNetworkLocation Success: $map")
        result.success(map)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readDeviceType(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val descriptorCluster = ChipClusters.DescriptorCluster(devicePointer, 0)
        descriptorCluster.readDeviceTypeListAttribute(object : ChipClusters
        .DescriptorCluster.DeviceTypeListAttributeCallback {
          override fun onSuccess(
            values: List<ChipStructs.DescriptorClusterDeviceTypeStruct>
          ) {
            val deviceTypeList = mutableListOf<Number>()
            values.forEach {
              deviceTypeList.add(it.deviceType)
            }
            CtLog.i(TAG, "readDeviceType Success: $deviceTypeList")
            result.success(deviceTypeList)
          }
          override fun onError(e: Exception) {
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
          }
        })
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun lightOnOff(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val onOff: Int = call.argument("onOff") ?: 0

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val cluster = OnOffCluster(devicePointer, 1)

        val callback = object : ChipClusters.DefaultClusterCallback {
          override fun onError(e: java.lang.Exception?) {
            result.error(CtStatusCode.PARAMETER_ERROR.toString(), e?.message ?: "", null)
          }

          override fun onSuccess() {
            result.success(true)
          }
        }

        if (onOff == 0) {
          cluster.off(callback)
        } else {
          cluster.on(callback)
        }

      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.PARAMETER_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun lightToggle(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val cluster = OnOffCluster(devicePointer, 1)

        val callback = object : ChipClusters.DefaultClusterCallback {
          override fun onError(e: java.lang.Exception?) {
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e?.message ?: "", null)
          }

          override fun onSuccess() {
            result.success(true)
          }
        }

        cluster.toggle(callback)
      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readBssid(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)

        WiFiNetworkDiagnosticsCluster(devicePointer, 0).readBssidAttribute(
          object: WiFiNetworkDiagnosticsCluster.BssidAttributeCallback{
            override fun onError(error: Exception?) {
              result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
            }

            override fun onSuccess(value: ByteArray?) {
              if (value != null) {
                val bssid = String(value, Charsets.UTF_8)
                CtLog.i(TAG, "readBssid Success: $bssid")
                result.success(bssid)
              } else {
                result.error(CtStatusCode.UNKNOWN_ERROR.toString(), "byte array is null", null)
              }
            }
          }
        )

      } catch (e: Exception) {
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readFabricsAttribute(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val operationalCredentialsCluster =
          ChipClusters.OperationalCredentialsCluster(devicePointer, 0)
        operationalCredentialsCluster.readFabricsAttribute(object: ChipClusters.OperationalCredentialsCluster.FabricsAttributeCallback{
          override fun onError(error: java.lang.Exception?) {
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
          }

          override fun onSuccess(list: MutableList<ChipStructs.OperationalCredentialsClusterFabricDescriptorStruct>?) {
            if (list != null) {
              CtLog.i(TAG, "readFabricsAttribute() -> list=${list}")
              val resultList = mutableListOf<Map<String, Any>>()
              for (item in list) {
                val map = mutableMapOf<String, Any>()
                map["fabricId"] = item.fabricID
                map["fabricIndex"] = item.fabricIndex
                map["vendorId"] = item.vendorID
                map["label"] = item.label
                map["nodeId"] = item.nodeID
                map["rootPublicKey"] = item.rootPublicKey
                resultList.add(map)
              }
              result.success(resultList)
            } else {
              result.error(CtStatusCode.UNKNOWN_ERROR.toString(), "value is null", null)
            }
          }
        })
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun unregisterICDClient(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    val unregisterNodeId: String = call.argument("unregisterNodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty() || unregisterNodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val newUnregisterNodeId = BigInteger(unregisterNodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val cluster = IcdManagementCluster(devicePointer, 0)
        cluster.unregisterClient(object : ChipClusters.DefaultClusterCallback {
          override fun onError(error: java.lang.Exception?) {
            CtLog.i(TAG, "unregisterClient Error: ${error?.message}")
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
          }

          override fun onSuccess() {
            CtLog.i(TAG, "unregisterClient Success")
            result.success(true)
          }
        }, newUnregisterNodeId, Optional.empty())
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun stopDevicePairing(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val remoteNodeId = BigInteger(nodeId, 16).toLong()
        CtLog.i(TAG, "stopDevicePairing() -> remoteNodeId=${remoteNodeId}")
        chipDeviceController.stopDevicePairing(remoteNodeId)
        result.success(true)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun getNetworkInterfaces(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        ChipClusters.GeneralDiagnosticsCluster(devicePointer, 0)
          .readNetworkInterfacesAttribute(object: ChipClusters.GeneralDiagnosticsCluster.NetworkInterfacesAttributeCallback {
            override fun onError(error: java.lang.Exception?) {
              CtLog.i(TAG, "getNetworkInterfaces Error: ${error?.message}")
              result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
            }

            override fun onSuccess(value: MutableList<ChipStructs.GeneralDiagnosticsClusterNetworkInterface>?) {
              CtLog.i(TAG, "getNetworkInterfaces Success: ${value}")
              val mutableList = mutableListOf<Map<String, Any>>()
              value?.forEach {
                val map = mutableMapOf<String, Any>()
                map["name"] = it.name
                map["isOperational"] = it.isOperational
                map["offPremiseServicesReachableIPv4"] = it.offPremiseServicesReachableIPv4 ?: false
                map["offPremiseServicesReachableIPv6"] = it.offPremiseServicesReachableIPv6 ?: false
                map["hardwareAddress"] = it.hardwareAddress
                map["IPv4Addresses"] = it.IPv4Addresses
                map["IPv6Addresses"] = it.IPv6Addresses
                map["type"] = it.type
                mutableList.add(map)
              }
              result.success(mutableList)
            }
          })
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun getICDClientInfo(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""

    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    coroutineScope.launch {
      try {
        val mutableList = mutableListOf<Map<String, Any>>()
        chipDeviceController.icdClientInfo.forEach{
          val map = mutableMapOf<String, Any>()
          map["peerNodeId"] = it.peerNodeId
          map["icdAesKey"] = it.icdAesKey
          map["icdHmacKey"] = it.icdHmacKey
          map["offset"] = it.offset
          map["monitoredSubject"] = it.monitoredSubject
          map["startCounter"] = it.startCounter
        }
        result.success(mutableList)
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun readRegisteredClients(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""

    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }

    coroutineScope.launch {
      try {
        val newNodeId = BigInteger(nodeId, 16).toLong()
        val devicePointer = chipClient.getConnectedDevicePointer(newNodeId)
        val cluster = IcdManagementCluster(devicePointer, 0)
        cluster.readRegisteredClientsAttribute(object: IcdManagementCluster.RegisteredClientsAttributeCallback{
          override fun onError(error: java.lang.Exception?) {
            CtLog.e(TAG, "readRegisteredClients Error: ${error?.message}")
            result.error(CtStatusCode.UNKNOWN_ERROR.toString(), error?.message, null)
          }

          override fun onSuccess(value: MutableList<ChipStructs.IcdManagementClusterMonitoringRegistrationStruct>?) {
            CtLog.i(TAG, "readRegisteredClients Success: ${value}")
            val mutableList = mutableListOf<Map<String, Any>>()
            value?.forEach {
              val map = mutableMapOf<String, Any>()
              map["fabricIndex"] = it.fabricIndex
              map["checkInNodeID"] = it.checkInNodeID
              map["monitoredSubject"] = it.monitoredSubject
              mutableList.add(map)
            }
            result.success(mutableList)
          }
        })
      } catch (e: Exception){
        e.printStackTrace()
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
    }
  }

  private fun getFabricIndex(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""

    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val fabricIndex = chipDeviceController.fabricIndex
    CtLog.d(TAG, "getFabricIndex success: $fabricIndex")
    result.success(fabricIndex)
  }

  private fun startDnssd(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""

    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    try {
      chipDeviceController.startDnssd()
      result.success(null)
    } catch (e: Exception) {
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun isRunning(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""

    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    result.success(true)
  }

  private fun stopDnssd(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""

    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    try {
      chipDeviceController.stopDnssd()
      result.success(null)
    } catch (e: Exception) {
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
    }
  }

  private fun getDeviceControllerPtr(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val deviceControllerPtr = chipDeviceController.deviceControllerPtr
    CtLog.i(TAG, "getDeviceControllerPtr success: $deviceControllerPtr")
    result.success(deviceControllerPtr)
  }

  private fun getControllerNodeId(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val controllerNodeId = chipDeviceController.controllerNodeId
    CtLog.i(TAG, "getControllerNodeId success: $controllerNodeId")
    result.success(controllerNodeId)
  }

  private fun getCompressedFabricId(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    if (fabricId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


    var chipDeviceController: ChipDeviceController? = null

    try {
      chipDeviceController = chipClient.getChipDeviceController()
    } catch (e: Exception){
      e.printStackTrace()
      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      return
    }

    val compressedFabricId = chipDeviceController.compressedFabricId
    CtLog.i(TAG, "getCompressedFabricId success: $compressedFabricId")
    result.success(compressedFabricId)
  }

  private fun getDeviceState(call: MethodCall, result: Result) {
    val fabricId: String = call.argument("fabricId") ?: ""
    val nodeId: String = call.argument("nodeId") ?: ""
    if (fabricId.isEmpty() || nodeId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    val chipClient: ChipClient? = chipClientCache[fabricId]
    if (chipClient == null) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "chipClient is null", null)
      return
    }


//    var chipDeviceController: ChipDeviceController? = null
//    try {
//      chipDeviceController = chipClient.getChipDeviceController()
//    } catch (e: Exception){
//      e.printStackTrace()
//      result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
//      return
//    }
//    chipDeviceController.getDeviceBeingCommissionedPointer()

    result.success(1)
  }

  private fun isThreadSupported(call: MethodCall, result: Result) {
    result.success(true)
  }

  private fun isPreferredCredentials(call: MethodCall, result: Result) {
    val dataset: String = call.argument("dataset") ?: ""

    if (dataset.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    if (!this::activity.isInitialized) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "activity is null.", null)
      return
    }

    val threadNetworkCredentials =
      ThreadNetworkCredentials.fromActiveOperationalDataset(
        dataset.dsToByteArray()
      )

    ThreadNetwork.getClient(activity)
      .isPreferredCredentials(threadNetworkCredentials)
      .addOnSuccessListener {
        // NOT_FOUND = -1; NOT_MATCHED = 0; MATCHED = 1;
        val isPreferredCredentialsResult = it.toInt()
        CtLog.d(TAG, "isPreferredCredentials success: $isPreferredCredentialsResult")
        if (isPreferredCredentialsResult == 1) {
          result.success(true)
        } else {
          result.success(false)
        }
      }
      .addOnFailureListener { e: Exception ->
        e.printStackTrace()
        CtLog.e(TAG, "isPreferredCredentials Failed")
        result.success(false)
      }
  }

  private fun fetchPreferredThreadCredentials(call: MethodCall, result: Result) {
    if (!this::activity.isInitialized) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "activity is null.", null)
      return
    }

    val intent = Intent(activity, AppPreferredCredentialsActivity::class.java)
    activity.startActivity(intent)

    result.success(true)
  }

  private fun fetchAllActiveThreadCredentials(call: MethodCall, result: Result) {
    if (!this::activity.isInitialized) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "activity is null.", null)
      return
    }

    val intent = Intent(activity, AppAllActiveCredentialsActivity::class.java)
    activity.startActivity(intent)

    result.success(mutableListOf<Map<String, Any>>())
  }

  private fun fetchAllThreadCredentials(call: MethodCall, result: Result) {
    if (!this::activity.isInitialized) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "activity is null.", null)
      return
    }

    ThreadNetwork.getClient(activity)
      .allCredentials
      .addOnSuccessListener {
        CtLog.d(TAG, "Success to fetch thread credentials, size: ${it.size}")
        val threadCredentialsList = mutableListOf<Map<String, Any>>()
        it.forEach { item ->
          val threadCredentialMap = mutableMapOf<String, Any>()
          item?.let {
            Log.i(TAG, "==========start==========")
            Log.i(TAG, "\n")
            Log.i(TAG, "panId:${it.panId}")
            Log.i(TAG, "extendedPanId:${it.extendedPanId.byteArrayToHex()}")
            Log.i(TAG, "networkName:${it.networkName}")
            Log.i(TAG, "channel:${it.channel}")
            Log.i(TAG, "dataset:${it.activeOperationalDataset.byteArrayToHex()}")
            Log.i(TAG, "channelMasks:${it.channelMasks}")
            Log.i(TAG, "channelPage:${it.channelPage}")
            Log.i(TAG, "networkKey:${it.networkKey.byteArrayToHex()}")
            Log.i(TAG, "pskc:${it.pskc.byteArrayToHex()}")
            Log.i(TAG, "meshLocalPrefix:${it.meshLocalPrefix.byteArrayToHex()}")
            Log.i(TAG, "activeTimestamp:${it.activeTimestamp}")
            Log.i(TAG, "createdAtMillis:${it.createdAtMillis}")
            Log.i(TAG, "updatedAtMillis:${it.updatedAtMillis}")
            Log.i(TAG, "\n")
            Log.i(TAG, "==========end==========")

            threadCredentialMap["panID"] = it.panId
            threadCredentialMap["extendedPANID"] = it.extendedPanId.byteArrayToHex()
            threadCredentialMap["networkName"] = it.networkName
            threadCredentialMap["channel"] = it.channel
            threadCredentialMap["activeOperationalDataSet"] = it.activeOperationalDataset.byteArrayToHex()
            threadCredentialMap["channelPage"] = it.channelPage
            threadCredentialMap["networkKey"] = it.networkKey.byteArrayToHex()
            threadCredentialMap["pskc"] = it.pskc.byteArrayToHex()
            threadCredentialMap["meshLocalPrefix"] = it.meshLocalPrefix.byteArrayToHex()
            threadCredentialMap["creationDate"] = it.createdAtMillis
            threadCredentialMap["lastModificationDate"] = it.updatedAtMillis
          }
        }
        result.success(threadCredentialsList)
      }
      .addOnFailureListener { e: Exception ->
        e.printStackTrace()
        CtLog.e(TAG, "Failed to fetch thread credentials.")
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
  }

  private fun saveThreadOperationalCredentials(call: MethodCall, result: Result) {
    val agentId: String = call.argument("agentId") ?: ""
    val dataset: String = call.argument("dataset") ?: ""

    if (agentId.isEmpty() || dataset.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    if (!this::activity.isInitialized) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "activity is null.", null)
      return
    }

    val threadNetworkCredentials =
      ThreadNetworkCredentials.fromActiveOperationalDataset(
        dataset.dsToByteArray()
      )

    val threadBorderAgent =
      ThreadBorderAgent.newBuilder(agentId.dsToByteArray())
        .build()

    ThreadNetwork.getClient(activity)
      .addCredentials(threadBorderAgent, threadNetworkCredentials)
      .addOnSuccessListener {
        CtLog.d(TAG, "Success to add thread credentials.")
        result.success(true)
      }
      .addOnFailureListener { e: Exception ->
        e.printStackTrace()
        CtLog.e(TAG, "Failed to add thread credentials.")
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
  }

  private fun deleteCredentials(call: MethodCall, result: Result) {
    val agentId: String = call.argument("agentId") ?: ""

    if (agentId.isEmpty()) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "args is null.", null)
      return
    }

    if (!this::activity.isInitialized) {
      result.error(CtStatusCode.PARAMETER_ERROR.toString(), "activity is null.", null)
      return
    }

    val threadBorderAgent =
      ThreadBorderAgent.newBuilder(agentId.dsToByteArray())
        .build()

    ThreadNetwork.getClient(activity)
      .removeCredentials(threadBorderAgent)
      .addOnSuccessListener {
        CtLog.d(TAG, "Success to remove thread credentials.")
        result.success(true)
      }
      .addOnFailureListener { e: Exception ->
        e.printStackTrace()
        CtLog.e(TAG, "Failed to remove thread credentials.")
        result.error(CtStatusCode.UNKNOWN_ERROR.toString(), e.message, null)
      }
  }

  override fun onAttachedToActivity(apb: ActivityPluginBinding) {
    activity = apb.activity
  }

  override fun onDetachedFromActivityForConfigChanges() {
  }

  override fun onReattachedToActivityForConfigChanges(apb: ActivityPluginBinding) {
  }

  override fun onDetachedFromActivity() {
  }


}
