package com.computime.app.ct_flutter_matter_plugin

import chip.devicecontroller.ResubscriptionAttemptCallback
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope

class CtSubscribeAttributesAndEventsCallbackImpl(
    coroutineScope: CoroutineScope,
    methodChannel : MethodChannel,
) : CtSubscribeCallbackImpl(coroutineScope, methodChannel), ResubscriptionAttemptCallback {

    companion object {
        private const val TAG = "CtSubscribeAttributesAndEventsCallbackImpl"
    }

    override fun onResubscriptionAttempt(
        terminationCause: Long,
        nextResubscribeIntervalMsec: Long
    ) {
        CtLog.i(TAG, "onResubscriptionAttempt() -> terminationCause:$terminationCause, " +
                "nextResubscribeIntervalMsec:$nextResubscribeIntervalMsec")
    }

    override fun getInvokeMethodName(): String {
        return "onSubscribeAttributesAndEvents"
    }
}