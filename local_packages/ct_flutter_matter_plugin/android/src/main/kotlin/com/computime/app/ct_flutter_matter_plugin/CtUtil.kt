package com.computime.app.ct_flutter_matter_plugin

fun makeThreadOperationalDataset(
    channel: Int,
    panId: Int,
    xpanId: ByteArray,
    masterKey: ByteArray
): ByteArray {
    val NUM_CHANNEL_BYTES = 3
    val NUM_PANID_BYTES = 2
    val NUM_XPANID_BYTES = 8
    val NUM_MASTER_KEY_BYTES = 16
    val TYPE_CHANNEL = 0 // Type of Thread Channel TLV.
    val TYPE_PANID = 1 // Type of Thread PAN ID TLV.
    val TYPE_XPANID = 2 // Type of Thread Extended PAN ID TLV.
    val TYPE_MASTER_KEY = 5 // Type of Thread Network Master Key TLV.

    // channel
    var dataset = byteArrayOf(TYPE_CHANNEL.toByte(), NUM_CHANNEL_BYTES.toByte())
    dataset += 0x00.toByte() // Channel Page 0.
    dataset += (channel.shr(8) and 0xFF).toByte()
    dataset += (channel and 0xFF).toByte()

    // PAN ID
    dataset += TYPE_PANID.toByte()
    dataset += NUM_PANID_BYTES.toByte()
    dataset += (panId.shr(8) and 0xFF).toByte()
    dataset += (panId and 0xFF).toByte()

    // Extended PAN ID
    dataset += TYPE_XPANID.toByte()
    dataset += NUM_XPANID_BYTES.toByte()
    dataset += xpanId

    // Network Master Key
    dataset += TYPE_MASTER_KEY.toByte()
    dataset += NUM_MASTER_KEY_BYTES.toByte()
    dataset += masterKey

    return dataset
}

fun castStringToType(data: Any): Any {
    return when (data) {
        is Int -> data.toInt()
        is Boolean -> data.toString().toBoolean()
        is ByteArray -> data.toString().encodeToByteArray()
        is Long -> data.toString().toLong()
        is Short -> data.toString().toShort()
        is Double -> data.toString().toDouble()
        is Float -> data.toString().toFloat()
        is String -> data
        else -> {
            CtLog.e("castStringToType()","Unknown type.")
            return data
        }
    }
}
