# CtFlutterMatterPlugin

## 插件简介
    该插件提供了一系列用于与Matter设备交互的方法。下面将对每个方法进行详细的介绍

## 如何使用
    1.本地导入，在项目pubspec.yaml配置：
    ct_flutter_matter_plugin:
        path: /xxx/xxx/apps/ct_flutter_matter_plugin

## 功能列表

##### getPlatformVersion()
    获取平台版本信息

##### init()
    初始化Matter插件，此操作会加载资源

##### close()
    添加设备成功以后释放资源

    注: 此方法仅用于Android

##### parseQrCode(String qrcode)
    解析二维码

    参数
    qrcode: 二维码字符串

    返回值
    payload
    
    返回值举例
    {
        version: 1,
        vendorId: 7089,
        productId: 4566,
        setupPinCode: 854684345,
        discriminator: 4005,
        commissioningFlow: 0,
    }

##### parseManualPairingCode(String code)
    解析手动配对码

    参数
    code: 手动配对码字符串

    返回值
    payload
    
    返回值举例
    {
        version: 1,
        vendorId: 7089,
        productId: 4566,
        setupPinCode: 854684345,
        discriminator: 4005,
        commissioningFlow: 0,
    }

##### getRootCa(String thingName)
    获取root证书

    参数
    thingName: 影子名称

    返回值
    Map<String, Any>

    返回值举例
    {
        "rootCa": null,
        "rootCaCertArn": null,
        "fabricId": null,
        "ipk": null,
    }

##### getUserNoc(String fabricId, String rootCaCertArn)
    获取user-noc证书

    参数
    fabricId
    rootCaCertArn

    返回值
    Map<String, Any>

    返回值举例
    {
        "userNoc": null,
    }

##### activateMatterClient(String rootCa, String fabricId, String ipk, String userNoc)
    激活Matter客户端

    参数
    rootCa: 根证书
    fabricId: 节点ID
    ipk: IPK
    userNoc: 用户NOC

    返回值
    Boolean

    返回值举例
    true/false

##### startScanDevices()
    启动扫描周围的Matter设备

    扫描类型
    BLE：基于蓝牙BLE
    WIFI：基于Soft-AP 
    Ethernet：基于以太网

    注：目前只支持BLE

##### stopScanDevices()
    停止扫描周围的Matter设备

##### addWiFiDevice(String code, String ssid, String pwd)
    添加WiFi设备

    参数
    code: 二维码或手动配对码
    ssid: WiFi SSID
    pwd: WiFi 密码

    返回值
    nodeId: 节点ID

##### addThreadDevice(String channel, String panId, String xpanId, String masterKey)
    添加Thread设备

    参数
    channel: 通信频道
    panId: 网络标识符
    xpanId: 扩展网络标识符
    masterKey: 网络加密密钥

    返回值
    nodeId: 节点ID

##### addThreadDeviceWithBorderRouterId(int borderRouterId)
    添加Thread设备

    参数
    borderRouterId: 边界路由器Id

    返回值
    nodeId: 节点ID

##### writeAttribute(String nodeId, int endpointId, int clusterId, int attributeId, String type, 
##### dynamic value, int timedWriteTimeout)
    写入单个属性

    参数
    nodeId: 节点ID
    endpointId: 端点ID
    clusterId: 集群ID
    attributeId: 属性ID
    type: 属性值类型（SignedInteger，UnsignedInteger，Boolean，String，Float，Double，json）
    value: 属性值
    timedWriteTimeout: 超时时间

    参数举例
    nodeId: 1                        (节点ID)  
    endpointId: 1                    (端点ID)   
    clusterId: 513                  （Thermostat 513）
    attributeId: 28                 （SystemMode 28）
    type: ”UnsignedInteger“         （UnsignedInteger 无符号整形）
    value: 3                         (cooling 3, heating 4)
    timedWriteTimeout: 10000         (10000 = 10s 超时)

    返回值
    Boolean

    返回值举例
    true/false


##### readAttribute(String nodeId, int endpointId, int clusterId, int attributeId)
    读取单个属性

    参数
    nodeId: 节点ID
    endpointId: 端点ID
    clusterId: 集群ID
    attributeId: 属性ID

    返回值
    Map<String, Any>

    返回值举例
    {
        "LocalTemperature": 2500,
    }


##### writeAttributeWithWildcard(String nodeId, int endpointId, String clusterName, 
##### String commandName, dynamic parameter)
    使用通配符写入单个属性

    参数
    nodeId: 节点ID
    endpointId: 端点ID
    clusterName: 集群名称
    commandName: 命令名称
    parameter: 参数

    参数举例
    nodeId: 1
    endpointId: 1
    clusterName: thermostat
    commandName: writeSystemModeAttribute
    parameter: 3

    返回值
    Boolean

    返回值举例
    true/false

    注: 此方法仅用于Android

##### readAttributeWithWildcard(String nodeId, int endpointId, String clusterName, String commandName)
    使用通配符读取单个属性

    参数
    nodeId: 节点ID
    endpointId: 端点ID
    clusterName: 集群名称
    commandName: 命令名称

    参数举例
    nodeId: 1
    endpointId: 1
    clusterName: thermostat
    commandName: readSystemModeAttribute
    parameter: 3

    返回值
    Map<String, Any>

    返回值举例
    {
        "LocalTemperature": 2500,
    }

    注: 此方法仅用于Android

##### readAllAttribute(String nodeId, int endpointId, int clusterId)
    读取设备属性，有以下两种情况：
    如果endpointID、clusterID全部为null，则读取设备上的所有属性
    如果endpointID、clusterID都非null，则读取单个端点和单个集群所匹配的属性

    参数
    nodeId: 节点ID (必选)
    endpointId: 端点ID（可选）
    clusterId: 集群ID（可选）

    返回值
    Map<String, Any>

    返回值举例
    {
        "Setpoint": 2500,
        "SystemMode": 3,
        "xxx": xxx,
    }


##### subscribeAttributesAndEvents(String nodeId)
    订阅一个设备的属性和事件，这将收到这个设备下所有的属性和事件报告

    参数
    nodeId: 节点ID

    返回值
    Boolean

    返回值举例
    true/false


##### subscribeAttributes(String nodeId)
    订阅一个设备的属性，这将收到这个设备下所有的属性报告

    参数
    nodeId: 节点ID

    返回值
    Boolean

    返回值举例
    true/false


##### unsubscribes()
    取消所有订阅


##### shutdown()
    关闭Matter插件，此操作会释放资源


##### unpairDevice(String nodeId)
    取消设备配对

    参数
    nodeId: 节点ID

    返回值
    nodeId: 被移除的节点ID

##### openPairingWindow(String nodeId, int duration)
    打开设备配对窗口

    参数
    nodeId: 节点ID
    duration: 配对窗口的持续时间
    

##### readPairingWindowStatus(String nodeId)
    读取设备配对窗口的状态，有以下几种状态：

    ‘0x00’-调试窗口未打开
    '0x01‘-调测窗口当前处于打开状态
    '0x02'-调试窗口已经打开，但是已经过期

    参数
    nodeId: 节点ID

    返回值
    int
    



