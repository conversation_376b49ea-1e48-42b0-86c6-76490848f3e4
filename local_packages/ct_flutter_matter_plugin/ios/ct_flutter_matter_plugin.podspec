#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint ct_flutter_matter_plugin.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'ct_flutter_matter_plugin'
  s.version          = '0.0.1'
  s.summary          = 'This project is the flutter matter plugin from computime.'
  s.description      = <<-DESC
This project is the flutter matter plugin from computime.
                       DESC
  s.homepage         = 'https://bitbucket.org/ct-bitbucket/ct-flutter-matter-plugin'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'NealonCao' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '16.4'
  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'
end
