import Flutter
import UIKit

public class CtFlutterMatterPlugin: NSObject, FlutterPlugin, RKOperationalCertificateIssuer {
    
    private var methodChannel: FlutterMethodChannel? = nil
    private var awsHelper: CtAwsHelper? = nil
    private var chipClientCache: Dictionary<String, RKChipClient>? = nil
    private var opaqueDeviceHandle: UnsafeMutableRawPointer? = nil
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "ct_flutter_matter_plugin", binaryMessenger: registrar.messenger())
        let instance = CtFlutterMatterPlugin()
        instance.methodChannel = channel
        instance.chipClientCache = [:]
        registrar.addMethodCallDelegate(instance, channel: channel)
    }

    public func detachFromEngine(for registrar: any FlutterPluginRegistrar) {
        CtLog.i("detachFromEngine()")
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        CtLog.i("CtFlutterMatterPlugin{} -> handle() -> call.method=\(call.method),call.arguments=\(String(describing: call.arguments))")
        switch call.method {
        case "getPlatformVersion":
            result("iOS " + UIDevice.current.systemVersion)
        case "init":
            initialize(call: call, result: result)
        case "createMatterClient":
            createMatterClient(call: call, result: result)
        case "getRootCa":
            getRootCa(call: call, result: result)
        case "getUserNoc":
            getUserNoc(call: call, result: result)
        case "close":
            close(call: call, result: result)
        case "parseQrCode":
            parseQrCode(call: call, result: result)
        case "parseManualPairingCode":
            parseManualPairingCode(call: call, result: result)
        case "generateRandomDiscriminator":
            generateRandomDiscriminator(call: call, result: result)
        case "generateRandomPIN":
            generateRandomPIN(call: call, result: result)
        case "generateRandomSetupPasscode":
            generateRandomSetupPasscode(call: call, result: result)
        case "startScanDevices":
            startScanDevices(call: call, result: result)
        case "stopScanDevices":
            stopScanDevices(call: call, result: result)
        case "discoverCommissionableNodes":
            discoverCommissionableNodes(call: call, result: result)
        case "getDiscoveredDevice":
            getDiscoveredDevice(call: call, result: result)
        case "addWiFiDevice":
            addWiFiDevice(call: call, result: result)
        case "addThreadDeviceWithTBRDataset":
            addThreadDeviceWithTBRDataset(call: call, result: result)
        case "continueCommissioningDevice":
            continueCommissioningDevice(call: call, result: result)
        case "readAllAttribute":
            readAllAttribute(call: call, result: result)
        case "readAttribute":
            readAttribute(call: call, result: result)
        case "writeAttribute":
            writeAttribute(call: call, result: result)
        case "readAttributeWithWildcard":
            readAttributeWithWildcard(call: call, result: result)
        case "writeAttributeWithWildcard":
            writeAttributeWithWildcard(call: call, result: result)
        case "subscribeAttributesAndEvents":
            subscribeAttributesAndEvents(call: call, result: result)
        case "subscribeAttributes":
            subscribeAttributes(call: call, result: result)
        case "unsubscribes":
            unsubscribes(call: call, result: result)
        case "unpairDevice":
            unpairDevice(call: call, result: result)
        case "shutdown":
            shutdown(call: call, result: result)
        case "openPairingWindow":
            openPairingWindow(call: call, result: result)
        case "openECMPairingWindow":
            openECMPairingWindow(call: call, result: result)
        case "readPairingWindowStatus":
            readPairingWindowStatus(call: call, result: result)
        case "readThreadNetworkName":
            readThreadNetworkName(call: call, result: result)
        case "readAccessControlList":
            readAccessControlList(call: call, result: result)
        case "getDeviceBasicInfo":
            getDeviceBasicInfo(call: call, result: result)
        case "writeCatValue":
            writeCatValue(call: call, result: result)
        case "getNodeId":
            getNodeId(call: call, result: result)
        case "hasMatterClient":
            hasMatterClient(call: call, result: result)
        case "removeFabric":
            removeFabric(call: call, result: result)
        case "generateQRCodeAndManualPairingCode":
            generateQRCodeAndManualPairingCode(call: call, result: result)
        case "getNetworkLocation":
            getNetworkLocation(call: call, result: result)
        case "readDeviceType":
            readDeviceType(call: call, result: result)
        case "lightOnOff":
            lightOnOff(call: call, result: result)
        case "lightToggle":
            lightToggle(call: call, result: result)        
        case "readBssid":
            readBssid(call: call, result: result)        
        case "readFabricsAttribute":
            readFabricsAttribute(call: call, result: result)        
        case "unregisterICDClient":
            unregisterICDClient(call: call, result: result)        
        case "cancelCommissioning":
            cancelCommissioning(call: call, result: result)
        case "getNetworkInterfaces":
            getNetworkInterfaces(call: call, result: result)
        case "readRegisteredClients":
            readRegisteredClients(call: call, result: result)
        case "isThreadSupported":
            isThreadSupported(call: call, result: result)
        case "isPreferredCredentials":
            isPreferredCredentials(call: call, result: result)
        case "fetchPreferredThreadCredentials":
            fetchPreferredThreadCredentials(call: call, result: result)
        case "fetchAllActiveThreadCredentials":
            fetchAllActiveThreadCredentials(call: call, result: result)
        case "fetchAllThreadCredentials":
            fetchAllThreadCredentials(call: call, result: result)
        case "saveThreadOperationalCredentials":
            saveThreadOperationalCredentials(call: call, result: result)        
        case "deleteCredentials":
            deleteCredentials(call: call, result: result)
        case "getFabricIndex":
            getFabricIndex(call: call, result: result)
        case "startDnssd":
            startDnssd(call: call, result: result)
        case "stopDnssd":
            stopDnssd(call: call, result: result)
        case "getDeviceControllerPtr":
            getDeviceControllerPtr(call: call, result: result)
        case "getControllerNodeId":
            getControllerNodeId(call: call, result: result)
        case "getCompressedFabricId":
            getCompressedFabricId(call: call, result: result)
        case "isRunning":
            isRunning(call: call, result: result)
        case "getDeviceState":
            getDeviceState(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func initialize(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let scheme = arguments["scheme"] as? String ?? ""
        let host = arguments["host"] as? String ?? ""
        let path = arguments["path"] as? String ?? ""
        
        self.awsHelper = CtAwsHelper(scheme: scheme, host: host, path: path)
        
        result(true)
    }
    
    private func createMatterClient(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        guard let fabricId = arguments["fabricId"] as? String,
              fabricId.isEmpty == false,
              let rootCa = arguments["rootCa"] as? String,
              rootCa.isEmpty == false,
              let rootCACertArn = arguments["rootCAArn"] as? String,
              rootCACertArn.isEmpty == false,
              let ipk = arguments["ipk"] as? String,
              ipk.isEmpty == false,
              let userNoc = arguments["userNoc"] as? String,
              userNoc.isEmpty == false else {
            result(FlutterError(code: "-20", message: "One or more arguments are missing or invalid", details: nil))
            return
        }

        let chipClient = RKChipClient(rootCa: rootCa, rootCACertArn: rootCACertArn, fabricId: fabricId, ipk: ipk, userNoc: userNoc, operationalCertificateIssuer: self)
        
        if chipClient.getDeviceController() != nil {
            self.chipClientCache?[fabricId] = chipClient
            result(true)
        } else {
            result(FlutterError(code: "-1", message: "Failed on createController!", details: nil))
        }
        
    }
    
    private func getRootCa(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let thingName = call.arguments as? String else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        guard let thatAwsHelper = self.awsHelper else {
            result(FlutterError(code: "-20", message: "aws helper = nil", details: nil))
            return
        }
        
        thatAwsHelper.getRootCa(thingName: thingName) { data in
            if let dict = data {
                result(dict)
            } else {
                result(FlutterError(code: "-1", message: "get root-ca error", details: nil))
            }
        }
    }
    
    private func getUserNoc(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let rootCACertArn = arguments["rootCAArn"] as? String ?? ""
        
        guard let thatAwsHelper = self.awsHelper else {
            result(FlutterError(code: "-20", message: "aws helper = nil", details: nil))
            return
        }
        
        RKUtil.generateUserCSR(groupId: fabricId) { csr in
            if let thatCsr = csr {
                CtLog.i("getUserNoc() -> thatCsr = \(thatCsr)")
                let csrContent = thatCsr.replacingOccurrences(of: "\n", with: "")
                let userCsr = "\(RKConstants.BEGIN_CERTIFICATE_REQUEST)" + RKUtil.splitAndConcatenateCsr(input: csrContent) + "\(RKConstants.END_CERTIFICATE_REQUEST)"
                CtLog.i("getUserNoc() -> userCsr = \(userCsr)")
                thatAwsHelper.getUserNoc(csr: userCsr, rootCAArn: rootCACertArn) { data in
                    if let dict = data {
                        let userNoc = dict["noc"]
                        result(userNoc)
                    } else {
                        result(FlutterError(code: "-1", message: "get user-noc error", details: nil))
                    }
                }
            } else {
                result(FlutterError(code: "-1", message: "generate user-csr error", details: nil))
            }
        }
    }
    
    public func issueOperationalCertificate(forRequest csrInfo: MTROperationalCSRInfo,
                                            attestationInfo: MTRDeviceAttestationInfo,
                                            chipClient: RKChipClient,
                                            controller: MTRDeviceController,
                                            completion: @escaping (MTROperationalCertificateChain?, (any Error)?) -> Void) {
        self.onIssueOperationalCertificate(status: 0)
        
        guard let thatAwsHelper = self.awsHelper else {
            CtLog.e("issueOperationalCertificate() -> aws helper = nil")
            completion(nil, RKError.requestNodeCSRError)
            self.onIssueOperationalCertificate(status: 2)
            return
        }
        
        let csrContent = csrInfo.csr.base64EncodedString()
        let nodeCsr = "\(RKConstants.BEGIN_CERTIFICATE_REQUEST)" + RKUtil.splitAndConcatenateCsr(input: csrContent) + "\(RKConstants.END_CERTIFICATE_REQUEST)"
        CtLog.i("issueOperationalCertificate() -> nodeCsr = \(nodeCsr)")
        
        var retryCount = 0
        let maxRetries = 3
        
        func attemptGetNodeNoc() {
            CtLog.i("issueOperationalCertificate() -> attemptGetNodeNoc start, retryCount=\(retryCount)")
            thatAwsHelper.getNodeNoc(csr: nodeCsr, rootCAArn: chipClient.rootCACertArn) { data in
                if let dict = data {
                    guard let nodeNoc = dict["noc"] as? String else {
                        CtLog.e("issueOperationalCertificate() -> nodeNoc = nil")
                        completion(nil, RKError.requestNodeCSRError)
                        self.onIssueOperationalCertificate(status: 2)
                        return
                    }
                    
                    if let nodeId = dict["nodeId"] as? String {
                        chipClient.nodeId = nodeId
                        CtLog.i("issueOperationalCertificate() -> nodeId=\(nodeId)");
                    }
                    
                    guard let nocData = RKUtil.convertPEMString(toDER: nodeNoc),
                          let rootCertData = RKUtil.convertPEMString(toDER: chipClient.rootCa),
                          let catIdAdmin = RKUtil.catIdAdminDecimal() else {
                        CtLog.e("issueOperationalCertificate() -> nocData = nil and rootCertData = nil and catIdAdmin = nil")
                        completion(nil, RKError.requestNodeCSRError)
                        self.onIssueOperationalCertificate(status: 2)
                        return
                    }
                    
                    CtLog.i("issueOperationalCertificate() -> nocData=\(nocData)");
                    CtLog.i("issueOperationalCertificate() -> rootCertData=\(rootCertData)");
                    CtLog.i("issueOperationalCertificate() -> catIdAdmin=\(catIdAdmin)");
                    
                    let params = MTROperationalCertificateChain(
                        operationalCertificate: nocData,
                        intermediateCertificate: nil,
                        rootCertificate: rootCertData,
                        adminSubject: NSNumber(value: catIdAdmin)
                    )
                    
                    CtLog.i("issueOperationalCertificate() -> attemptGetNodeNoc end, retryCount=\(retryCount)")
                    completion(params, nil)
                    
                    self.onIssueOperationalCertificate(status: 1)
                } else {
                    if retryCount < maxRetries {
                        retryCount += 1
                        CtLog.i("issueOperationalCertificate() -> Retry attempt, retryCount=\(retryCount)")
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            attemptGetNodeNoc()
                        }
                    } else {
                        CtLog.e("issueOperationalCertificate() -> Maximum number of retries reached, maxRetries=(\(maxRetries))")
                        completion(nil, RKError.requestNodeCSRError)
                        self.onIssueOperationalCertificate(status: 2)
                    }
                }
            }
        }

        attemptGetNodeNoc()
    }
    
    // 0 start
    // 1 success
    // 2 failure
    private func onIssueOperationalCertificate(status: Int) {
        DispatchQueue.main.async {
            self.methodChannel?.invokeMethod("onIssueOperationalCertificate", arguments: status)
        }
    }
    
    private func close(call: FlutterMethodCall, result: @escaping FlutterResult) {
        CtLog.i("close()")
        result(true)
    }
    
    private func parseQrCode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        if let qrcode = arguments["qrcode"] as? String {
            CtLog.i("parseQrCode() -> qrcode=\(qrcode)")
            do {
                let setupPayload: MTRSetupPayload = try MTRSetupPayload(onboardingPayload: qrcode)
                
                var discoveryCapabilities: String = "BLE"
                if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.BLE {
                    discoveryCapabilities = "BLE"
                } else if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.onNetwork {
                    discoveryCapabilities = "onNetwork"
                } else if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.softAP {
                    discoveryCapabilities = "SoftAP"
                } else if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.allMask {
                    discoveryCapabilities = "allMask"
                }
                
                let dict: [String: Any] = [
                    "version":setupPayload.version,
                    "vendorId":setupPayload.vendorID,
                    "productId":setupPayload.productID,
                    "commissioningFlow":setupPayload.commissioningFlow.rawValue,
                    "discriminator":setupPayload.discriminator,
                    "setupPinCode":setupPayload.setupPasscode,
                    "discoveryCapabilities": discoveryCapabilities,
                    "isShortDiscriminator":setupPayload.hasShortDiscriminator,
                ]
                
                CtLog.i("parseQrCode() -> dict=\(dict)")
                result(dict)
            } catch {
                result(FlutterError(code: "-1", message: "parse qrcode error", details: nil))
            }
        } else {
            result(FlutterError(code: "-1", message: "QR code is missing", details: nil))
        }
    }
    
    private func parseManualPairingCode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        if let code = arguments["code"] as? String {
            CtLog.i("parseManualPairingCode() -> code=\(code)")
            do {
                let setupPayload: MTRSetupPayload = try MTRSetupPayload(onboardingPayload: code)
                
                var discoveryCapabilities: String = "BLE"
                if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.BLE {
                    discoveryCapabilities = "BLE"
                } else if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.onNetwork {
                    discoveryCapabilities = "onNetwork"
                } else if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.softAP {
                    discoveryCapabilities = "SoftAP"
                } else if setupPayload.discoveryCapabilities == MTRDiscoveryCapabilities.allMask {
                    discoveryCapabilities = "allMask"
                }
                
                let dict: [String: Any] = [
                    "version":setupPayload.version,
                    "vendorId":setupPayload.vendorID,
                    "productId":setupPayload.productID,
                    "commissioningFlow":setupPayload.commissioningFlow.rawValue,
                    "discriminator":setupPayload.discriminator,
                    "setupPinCode":setupPayload.setupPasscode,
                    "discoveryCapabilities": discoveryCapabilities,
                    "isShortDiscriminator":setupPayload.hasShortDiscriminator,
                ]
                
                CtLog.i("parseManualPairingCode() -> dict=\(dict)")
                result(dict)
            } catch {
                result(FlutterError(code: "-1", message: "parse code error", details: nil))
            }
        } else {
            result(FlutterError(code: "-1", message: "code is missing", details: nil))
        }
    }
    
    private func generateRandomDiscriminator(call: FlutterMethodCall, result: @escaping FlutterResult) {
        // Define the range for valid discriminator values
        let minValue = 1
        let maxValue = 0xFFE  // 4094 in decimal

        // Generate a random number within the range
        let randomDiscriminator = Int.random(in: minValue...maxValue)
        let discriminator = NSNumber(value: randomDiscriminator)
        result(discriminator)
    }
    
    private func generateRandomPIN(call: FlutterMethodCall, result: @escaping FlutterResult) {
        let pin = MTRSetupPayload.generateRandomPIN()
        result(pin)
    }
    
    private func generateRandomSetupPasscode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        let setupPasscode = MTRSetupPayload.generateRandomSetupPasscode()
        result(setupPasscode)
    }
    
    private func startScanDevices(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        if #available(iOS 17.0, *) {
            CtLog.i("startScanDevices() -> iOS 17.0")
            let queue = DispatchQueue(label: "com.habi.scanDevices.queue")
            let delegate = CtScanDevicesDelegate(methodChannel: methodChannel)
            deviceController.startBrowse(forCommissionables: delegate, queue: queue)
            result(true)
        } else {
            CtLog.i("startScanDevices() -> iOS 16.4")
            result(false)
        }
    }
    
    private func stopScanDevices(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        if #available(iOS 17.0, *) {
            CtLog.i("stopScanDevices() -> iOS 17.0")
            deviceController.stopBrowseForCommissionables()
            result(true)
        } else {
            CtLog.i("stopScanDevices() -> iOS 16.4")
            result(false)
        }
    }    
    
    private func discoverCommissionableNodes(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(true)
    }
     
    private func getDiscoveredDevice(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(true)
    }
    
    private func addWiFiDevice(call: FlutterMethodCall, result: @escaping FlutterResult){
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let qrcode = arguments["code"] as? String ?? ""
        let ssid = arguments["ssid"] as? String ?? ""
        let pwd = arguments["pwd"] as? String ?? ""
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        let manager = CtAddDeviceManager(deviceController: deviceController)
        manager.addWiFiDevice(qrcode: qrcode, ssid: ssid, pwd: pwd, deviceAttestationCallback: { opaqueDeviceHandle, attestationDeviceInfo, errorCode in
            self.opaqueDeviceHandle = opaqueDeviceHandle
            
            var dict: [String: Any] = [:]
            var infoDict: [String: Any] = [:]
            
            infoDict["vendorId"] = attestationDeviceInfo?.vendorID ?? 0
            infoDict["productId"] = attestationDeviceInfo?.productID ?? 0
            
            dict["attestationInfo"] = infoDict
            dict["errorCode"] = errorCode
            
            DispatchQueue.main.async {
                self.methodChannel?.invokeMethod("onDeviceAttestation", arguments: dict)
            }
        }, readCommissioningInfoCallback: { vendorId, productId in
            var dict: [String: Any] = [:]
            dict["vendorId"] = vendorId
            dict["productId"] = productId
            DispatchQueue.main.async {
                self.methodChannel?.invokeMethod("onReadCommissioningInfo", arguments: dict)
            }
        }, commissioningCompleteCallback: { nodeId, errorCode, stepDict in
            var dict: [String: Any] = [:]
            dict["nodeId"] = nodeId
            dict["errorCode"] = errorCode
            dict["stepDict"] = stepDict
            result(dict)
        })
    }
    
    private func addThreadDeviceWithTBRDataset(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let qrcode = arguments["code"] as? String ?? ""
        let agentId = arguments["agentId"] as? String ?? ""
        let dataset = arguments["dataset"] as? String ?? ""
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        let manager = CtAddDeviceManager(deviceController: deviceController)
        manager.addThreadDevice(qrcode: qrcode, agentId: agentId, dataset: dataset, deviceAttestationCallback: { opaqueDeviceHandle, attestationDeviceInfo, errorCode in
            self.opaqueDeviceHandle = opaqueDeviceHandle
            
            var dict: [String: Any] = [:]
            var infoDict: [String: Any] = [:]
            
            infoDict["vendorId"] = attestationDeviceInfo?.vendorID ?? 0
            infoDict["productId"] = attestationDeviceInfo?.productID ?? 0
            
            dict["attestationInfo"] = infoDict
            dict["errorCode"] = errorCode
            
            DispatchQueue.main.async {
                self.methodChannel?.invokeMethod("onDeviceAttestation", arguments: dict)
            }
        }, readCommissioningInfoCallback: { vendorId, productId in
            var dict: [String: Any] = [:]
            dict["vendorId"] = vendorId
            dict["productId"] = productId
            DispatchQueue.main.async {
                self.methodChannel?.invokeMethod("onReadCommissioningInfo", arguments: dict)
            }
        }, commissioningCompleteCallback: { nodeId, errorCode, stepDict in
            var dict: [String: Any] = [:]
            dict["nodeId"] = nodeId
            dict["errorCode"] = errorCode
            dict["stepDict"] = stepDict
            result(dict)
        })
    }
    
    private func continueCommissioningDevice(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let ignoreAttestationFailure = arguments["ignoreAttestationFailure"] as? Bool ?? false
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        CtLog.i("continueCommissioningDevice() -> ignoreAttestationFailure=\(ignoreAttestationFailure)")
        
        if let handle = self.opaqueDeviceHandle {
            do {
                try deviceController.continueCommissioningDevice(handle, ignoreAttestationFailure: ignoreAttestationFailure)
                result(true)
            } catch {
                result(FlutterError(code: String(CtStatusCode.deviceAttestationError), message: error.localizedDescription, details: nil))
            }
        } else {
            result(FlutterError(code: "-20", message: "opaqueDeviceHandle = nil", details: nil))
        }
    }
    
    private func readAllAttribute(call: FlutterMethodCall, result: @escaping FlutterResult){
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        var endpointId: NSNumber?
        var clusterId: NSNumber?
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        if let tempEndpointIdInt = arguments["endpointId"] as? Int {
            endpointId = NSNumber(value: tempEndpointIdInt)
        }
        
        if let tempClusterIdInt = arguments["clusterId"] as? Int {
            clusterId = NSNumber(value: tempClusterIdInt)
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.readAttributes(withEndpointID: endpointId, clusterID: clusterId, attributeID: nil, params: nil, queue: DispatchQueue.main, completion: {(values,error) in
                if let thatError = error {
                    CtLog.i("readAllAttribute() -> read all attribute error: \(thatError)")
                    result(FlutterError(code: "-1", message: "read all attribute error: \(thatError)", details: nil))
                } else {
                    var dict: [String: Any] = [:]
                    if let thatValues = values {
                        CtLog.i("readAllAttribute() -> read all attribute success:: \(thatValues)")
                        if #available(iOS 17.6, *) {
                            CtLog.i("readAllAttribute() -> iOS 17.6")
                            for item in thatValues {
                                do {
                                    let report = try MTRAttributeReport(responseValue: item)
                                    
                                    let clusterIDType = MTRClusterIDType(rawValue: report.path.cluster.uint32Value)
                                    CtLog.i("readAllAttribute() -> clusterIDType=\(clusterIDType!)")
                                    let attributeIDType = MTRAttributeIDType(rawValue: report.path.attribute.uint32Value)
                                    CtLog.i("readAllAttribute() -> attributeIDType=\(attributeIDType!)")
                                    let attributeNameResult = MTRAttributeNameForID(clusterIDType!, attributeIDType!)
                                    CtLog.i("readAllAttribute() -> attributeNameResult=\(attributeNameResult!)")
                                    
                                    let attributeName = attributeNameResult!
                                    let attributeValue = report.value ?? ""
                                    dict[attributeName] = attributeValue
                                    
                                    CtLog.i("readAllAttribute() -> name=\(attributeName), value=\(attributeValue)")
                                } catch {
                                    CtLog.e("readAllAttribute() -> init MTRAttributeReport error.")
                                }
                            }
                           
                        } else if #available(iOS 17.0, *) {
                            CtLog.i("readAllAttribute() -> iOS 17.0")
                            for item in thatValues {
                                do {
                                    let report = try MTRAttributeReport(responseValue: item)
                                    let attributeName =  CtUtil.getAttributeNameForID(report.path.cluster.intValue, report.path.attribute.intValue)
                                    let attributeValue = report.value ?? ""
                                    dict[attributeName] = attributeValue
                                    CtLog.i("readAllAttribute() -> name=\(attributeName), value=\(attributeValue)")
                                } catch {
                                    CtLog.e("readAllAttribute() -> init MTRAttributeReport error.")
                                }
                            }
                        } else if #available(iOS 16.4, *){
                            CtLog.i("readAllAttribute() -> iOS 16.4")
                            for itemDict in thatValues {
                                if let path = itemDict[MTRAttributePathKey], let pathObj = path as? MTRAttributePath, let data = itemDict[MTRDataKey], let dataDict = data as? [String: Any] {
                                    let attributeName =  CtUtil.getAttributeNameForID(pathObj.cluster.intValue, pathObj.attribute.intValue)
                                    let attributeValue = dataDict[MTRValueKey] ?? ""
                                    dict[attributeName] = attributeValue
                                    CtLog.i("readAllAttribute() -> name=\(attributeName), value=\(attributeValue)")
                                } else {
                                    CtLog.e("readAllAttribute() -> init MTRAttributeReport error.")
                                }
                            }
                        }
                    } else {
                        CtLog.i("readAllAttribute() -> read all attribute success:: []")
                    }
                    result(dict)
                }
            })
        }
    }
    
    private func readAttributeWithWildcard(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(FlutterError(code: "-1", message: "Unsupported.", details: nil))
    }
    
    private func writeAttributeWithWildcard(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(FlutterError(code: "-1", message: "Unsupported.", details: nil))
    }
    
    private func readAttribute(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        let endpointId = arguments["endpointId"] as? Int ?? 0
        let clusterId = arguments["clusterId"] as? Int ?? 0
        let attributeId = arguments["attributeId"] as? Int ?? 0
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.readAttributes(withEndpointID: NSNumber(value: endpointId), clusterID: NSNumber(value: clusterId), attributeID: NSNumber(value: attributeId), params: nil, queue: DispatchQueue.main, completion: {(values, error) in
                if let thatError = error {
                    CtLog.i("readAttribute() -> read attribute error: \(thatError)")
                    result(FlutterError(code: "-1", message: "read attribute error: \(thatError)", details: nil))
                } else {
                    var dict: [String: Any] = [:]
                    if let thatValues = values {
                        CtLog.i("readAttribute() -> read attribute success: \(thatValues)")
                        if #available(iOS 17.6, *) {
                            CtLog.i("readAttribute() -> iOS 17.6")
                            if let itemDict = thatValues.first {
                                do {
                                    let report = try MTRAttributeReport(responseValue: itemDict)
                                    
                                    let clusterIDType = MTRClusterIDType(rawValue: report.path.cluster.uint32Value)
                                    CtLog.i("readAttribute() -> clusterIDType=\(clusterIDType!)")
                                    let attributeIDType = MTRAttributeIDType(rawValue: report.path.attribute.uint32Value)
                                    CtLog.i("readAttribute() -> attributeIDType=\(attributeIDType!)")
                                    let attributeNameResult = MTRAttributeNameForID(clusterIDType!, attributeIDType!)
                                    CtLog.i("readAttribute() -> attributeNameResult=\(attributeNameResult!)")
                                    
                                    let attributeName = attributeNameResult!
                                    let attributeValue = report.value ?? ""
                                    dict[attributeName] = attributeValue
                                    
                                    CtLog.i("readAttribute() -> name=\(attributeName), value=\(attributeValue)")
                                } catch {
                                    CtLog.e("readAttribute() -> init MTRAttributeReport error.")
                                }
                            }
                        } else if #available(iOS 17.0, *) {
                            CtLog.i("readAttribute() -> iOS 17.0")
                            if let itemDict = thatValues.first {
                                do {
                                    let report = try MTRAttributeReport(responseValue: itemDict)
                                    let attributeName =  CtUtil.getAttributeNameForID(report.path.cluster.intValue, report.path.attribute.intValue)
                                    let attributeValue = report.value ?? ""
                                    dict[attributeName] = attributeValue
                                    CtLog.i("readAttribute() -> name=\(attributeName), value=\(attributeValue)")
                                } catch {
                                    CtLog.e("readAttribute() -> init MTRAttributeReport error.")
                                }
                            }
                        } else if #available(iOS 16.4, *){
                            CtLog.i("readAttribute() -> iOS 16.4")
                            if let itemDict = thatValues.first, let path = itemDict[MTRAttributePathKey], let pathObj = path as? MTRAttributePath, let data = itemDict[MTRDataKey], let dataDict = data as? [String: Any] {
                                let attributeName =  CtUtil.getAttributeNameForID(pathObj.cluster.intValue, pathObj.attribute.intValue)
                                let attributeValue = dataDict[MTRValueKey] ?? ""
                                dict[attributeName] = attributeValue
                                CtLog.i("readAttribute() -> name=\(attributeName), value=\(attributeValue)")
                            } else {
                                CtLog.e("readAttribute() -> init MTRAttributeReport error.")
                            }
                        }
                    } else {
                        CtLog.e("readAttribute() -> read attribute success: []")
                    }
                    result(dict)
                }
            })
        }
        
    }
    
    private func writeAttribute(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        let endpointId = arguments["endpointId"] as? Int ?? 0
        let clusterId = arguments["clusterId"] as? Int ?? 0
        let attributeId = arguments["attributeId"] as? Int ?? 0
        let type = arguments["type"] as? String ?? ""
        let value = arguments["value"] as? String ?? ""
        let timedWriteTimeout = arguments["timedWriteTimeout"] as? Int ?? 0
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        var writeDic: [String: Any]? = nil
        if type == "UnsignedInteger" {
            if let uintValue = UInt(value) {
                writeDic = [
                    "type": "UnsignedInteger",
                    "value": uintValue
                ]
            } else {
                CtLog.e("writeAttribute() -> Parsing uint error")
            }
        } else if type == "SignedInteger" {
            if let intValue = Int(value) {
                writeDic = [
                    "type": "SignedInteger",
                    "value": intValue
                ]
            } else {
                CtLog.e("writeAttribute() -> Parsing int error")
            }
        } else if type == "Boolean" {
            let boolValue = (value.lowercased() == "true")
            writeDic = [
                "type":" Boolean",
                "value": boolValue
            ]
        } else if type == "String" {
            writeDic = [
                "type":" UTF8String",
                "value": value
            ]
        } else if type == "Float" {
            if let floatValue = Float(value) {
                writeDic = [
                    "type": "Float",
                    "value": floatValue
                ]
            } else {
                CtLog.e("writeAttribute() -> Parsing float error")
            }
        } else if type == "Double" {
            if let doubleValue = Double(value) {
                writeDic = [
                    "type": "Double",
                    "value": doubleValue
                ]
            } else {
                CtLog.e("writeAttribute() -> Parsing double error")
            }
        } else {
            CtLog.i("writeAttribute() -> Unsupported type.")
        }
        
        guard writeDic != nil else {
            result(FlutterError(code: "-20", message: "Unsupported type.", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        CtLog.i("nodeId-uint64=\(uint64Value)")
        CtLog.i("write-dic=\(writeDic!)")
        
        DispatchQueue.main.async {
            //这种方式写入值给device会报错：Domain=MTRInteractionErrorDomain Code=126 "The sender of the action or command does not have authorization or access."
            
            /*let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.writeAttribute(withEndpointID: NSNumber(value: endpointId), clusterID: NSNumber(value: clusterId), attributeID: NSNumber(value: attributeId), value: writeDic!, timedWriteTimeout: NSNumber(value:timedWriteTimeout), queue: DispatchQueue.main, completion: {(values, error) in
                if let error = error {
                    CtLog.i("writeAttribute() -> Error writing attributes: \(error)")
                    result(FlutterError(code: "-1", message: "Error writing attributes", details: error.localizedDescription))
                } else if let values = values {
                    CtLog.i("writeAttribute() -> Write attributes successfully: \(values)")
                    result(true)
                } else {
                    CtLog.i("writeAttribute() -> Write attributes successfully: []")
                    result(true)
                }
            })*/
            
            //这种方式写入值不会报错
            let device = MTRDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.writeAttribute(withEndpointID: endpointId as NSNumber, clusterID: clusterId as NSNumber, attributeID: attributeId as NSNumber, value: writeDic!, expectedValueInterval: 10000, timedWriteTimeout: NSNumber(value: timedWriteTimeout))
            result(true)
        }
    }
    
    private func subscribeAttributesAndEvents(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        let params = MTRSubscribeParams(minInterval: NSNumber(value: 1), maxInterval: NSNumber(value: 10))
        params.shouldResubscribeAutomatically = false
        params.shouldReplaceExistingSubscriptions = false
        
        var subscriptionEstablished = false
        
        
        let workItem = DispatchWorkItem {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            let clusterStateCacheContainer = MTRClusterStateCacheContainer()
            device.subscribe(with: DispatchQueue.main, params: params, clusterStateCacheContainer: clusterStateCacheContainer, attributeReportHandler: { [weak self] values in
                CtLog.i("subscribeAttributesAndEvents() -> values: \(values)")
                CtLog.i("subscribeAttributesAndEvents() -> subscriptionEstablished: \(subscriptionEstablished)")
                
                if subscriptionEstablished {
                    let thatValues = values
                    var dict: [String: Any] = [:]
                    if #available(iOS 17.6, *) {
                        CtLog.i("subscribeAttributesAndEvents() -> iOS 17.6")
                        for item in thatValues {
                            if let report = item as? MTRAttributeReport {
                                let clusterIDType = MTRClusterIDType(rawValue: report.path.cluster.uint32Value)
                                CtLog.i("subscribeAttributesAndEvents() -> clusterIDType=\(clusterIDType!)")
                                let attributeIDType = MTRAttributeIDType(rawValue: report.path.attribute.uint32Value)
                                CtLog.i("subscribeAttributesAndEvents() -> attributeIDType=\(attributeIDType!)")
                                let attributeNameResult = MTRAttributeNameForID(clusterIDType!, attributeIDType!)
                                CtLog.i("subscribeAttributesAndEvents() -> attributeNameResult=\(attributeNameResult!)")
                                
                                let attributeName = attributeNameResult!
                                let attributeValue = report.value ?? ""
                                dict[attributeName] = attributeValue
                                
                                CtLog.i("subscribeAttributesAndEvents() -> name=\(attributeName), value=\(attributeValue)")
                            }
                        }
                    } else {
                        CtLog.i("subscribeAttributesAndEvents() -> NOT iOS 17.6")
                        for item in thatValues {
                            if let report = item as? MTRAttributeReport {
                                let attributeName =  CtUtil.getAttributeNameForID(report.path.cluster.intValue, report.path.attribute.intValue)
                                let attributeValue = report.value ?? ""
                                dict[attributeName] = attributeValue
                                CtLog.i("subscribeAttributesAndEvents() -> name=\(attributeName), value=\(attributeValue)")
                            }
                        }
                    }
                    self?.methodChannel?.invokeMethod("onSubscribeAttributesAndEvents", arguments: dict)
                }

            }, eventReportHandler: { values in
                CtLog.i("subscribeAttributesAndEvents() -> Event: \(values)")
            }, errorHandler: { error in
                CtLog.i("subscribeAttributesAndEvents() -> Error: \(error)")
            }, subscriptionEstablished: {
                CtLog.i("subscribeAttributesAndEvents() -> Established")
                subscriptionEstablished = true
            }, resubscriptionScheduled: { error, resubscriptionDelay in
                CtLog.i("subscribeAttributesAndEvents() -> ResubscriptionScheduled")
            })
            
            result(true)
        }
        
        DispatchQueue.main.async(execute: workItem)
    }
    
    private func subscribeAttributes(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        let params = MTRSubscribeParams(minInterval: NSNumber(value: 1), maxInterval: NSNumber(value: 10))
        params.shouldResubscribeAutomatically = false
        params.shouldReplaceExistingSubscriptions = false
        
        var subscriptionEstablished = false
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.subscribeToAttributes(withEndpointID: nil, clusterID: nil, attributeID: nil, params: params, queue: DispatchQueue.main, reportHandler: { [weak self] values, error in
                if let thatError = error {
                    CtLog.i("subscribeAttributes() -> error: \(thatError)")
                } else {
                    if let thatValues = values {
                        CtLog.i("subscribeAttributes() -> values: \(thatValues)")
                        if subscriptionEstablished {
                            var dict: [String: Any] = [:]
                            if #available(iOS 17.6, *) {
                                CtLog.i("subscribeAttributes() -> iOS 17.6")
                                for item in thatValues {
                                    do {
                                        let report = try MTRAttributeReport(responseValue: item)
                                        
                                        let clusterIDType = MTRClusterIDType(rawValue: report.path.cluster.uint32Value)
                                        CtLog.i("subscribeAttributes() -> clusterIDType=\(clusterIDType!)")
                                        let attributeIDType = MTRAttributeIDType(rawValue: report.path.attribute.uint32Value)
                                        CtLog.i("subscribeAttributes() -> attributeIDType=\(attributeIDType!)")
                                        let attributeNameResult = MTRAttributeNameForID(clusterIDType!, attributeIDType!)
                                        CtLog.i("subscribeAttributes() -> attributeNameResult=\(attributeNameResult!)")
                                        
                                        let attributeName = attributeNameResult!
                                        let attributeValue = report.value ?? ""
                                        dict[attributeName] = attributeValue
                                        
                                        CtLog.i("subscribeAttributes() -> name=\(attributeName), value=\(attributeValue)")
                                    } catch {
                                        CtLog.i("subscribeAttributes() -> init MTRAttributeReport error.")
                                    }
                                }
                            } else if #available(iOS 17.0, *){
                                CtLog.i("subscribeAttributes() -> iOS 17.0")
                                for item in thatValues {
                                    do {
                                        let report = try MTRAttributeReport(responseValue: item)
                                        let attributeName =  CtUtil.getAttributeNameForID(report.path.cluster.intValue, report.path.attribute.intValue)
                                        let attributeValue = report.value ?? ""
                                        dict[attributeName] = attributeValue
                                        CtLog.i("subscribeAttributes() -> name=\(attributeName), value=\(attributeValue)")
                                    } catch {
                                        CtLog.i("subscribeAttributes() -> init MTRAttributeReport error.")
                                    }
                                }
                            } else if #available(iOS 16.4, *){
                                CtLog.i("subscribeAttributes() -> iOS 16.4")
                                for itemDict in thatValues {
                                    if let path = itemDict[MTRAttributePathKey], let pathObj = path as? MTRAttributePath, let data = itemDict[MTRDataKey], let dataDict = data as? [String: Any] {
                                        let attributeName =  CtUtil.getAttributeNameForID(pathObj.cluster.intValue, pathObj.attribute.intValue)
                                        let attributeValue = dataDict[MTRValueKey] ?? ""
                                        dict[attributeName] = attributeValue
                                        CtLog.i("subscribeAttributes() -> name=\(attributeName), value=\(attributeValue)")
                                    } else {
                                        CtLog.e("subscribeAttributes() -> init MTRAttributeReport error.")
                                    }
                                }
                            }
                            self?.methodChannel?.invokeMethod("onSubscribeAttributes", arguments: dict)
                        }
                    } else {
                        CtLog.i("subscribeAttributes() -> values: []")
                    }
                }
            }, subscriptionEstablished: {
                CtLog.i("subscribeAttributes() -> Established")
                subscriptionEstablished = true
            })
            
            result(true)
        }
    }
    
    private func unsubscribes(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.deregisterReportHandlers(with: DispatchQueue.main) {
                CtLog.i("unsubscribes() -> successful")
                result(true)
            }
        }
    }
    
    
    private func unpairDevice(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let opCredsCluster = MTRBaseClusterOperationalCredentials(device: device, endpointID: NSNumber(value: 0), queue: DispatchQueue.main) {
                opCredsCluster.readAttributeCurrentFabricIndex(completion: { (value, error)in
                    if let error = error {
                        CtLog.i("unpairDevice() -> readAttributeCurrentFabricIndex Error: \(error)")
                        result(FlutterError(code: "-1", message: "readAttributeCurrentFabricIndex Error", details: error.localizedDescription))
                    } else if let fabricIndex = value {
                        CtLog.i("unpairDevice() -> readAttributeCurrentFabricIndex successfully: \(fabricIndex)")
                        
                        let params = MTROperationalCredentialsClusterRemoveFabricParams()
                        params.fabricIndex = fabricIndex
                        
                        opCredsCluster.removeFabric(with: params, completion: {(params, error) in
                            if let removeFabricError = error {
                                CtLog.e("unpairDevice() -> removeFabric Error: \(removeFabricError)")
                                result(FlutterError(code: "-1", message: "removeFabric Error", details: nil))
                            } else {
                                CtLog.i("unpairDevice() -> removeFabric successfully: \(nodeId)")
                                result(nodeId)
                            }
                        })
                        
                    } else {
                        CtLog.i("unpairDevice() -> fabricIndex == null")
                        result(FlutterError(code: "-1", message: "fabricIndex == null", details: nil))
                    }
                })
            } else {
                CtLog.i("unpairDevice() -> init MTRBaseClusterOperationalCredentials error")
                result(FlutterError(code: "-1", message: "init MTRBaseClusterOperationalCredentials error", details: nil))
            }
            
        }
    }
    
    private func shutdown(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        
        if let chipClient = self.chipClientCache?.removeValue(forKey: fabricId) {
            if let deviceController = chipClient.getDeviceController() {
                deviceController.shutdown()
                CtLog.i("shutdown success!")
                result(true)
            } else {
                CtLog.i("shutdown error, deviceController = nil")
                result(false)
            }
        } else {
            CtLog.i("shutdown error, chipClient = nil")
            result(false)
        }
    }
    
    private func openPairingWindow(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        let duration = arguments["duration"] as? Int ?? 0
        
        CtLog.i("openPairingWindow() -> duration: \(duration)")

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            let thatDuration = NSNumber(value: duration)
            
            if #available(iOS 17.0, *) {
                CtLog.i("openPairingWindow() -> 进入iOS17.0")
                let discriminator = RKUtil.generateRandomDiscriminator()
                device.openCommissioningWindow(withDiscriminator: discriminator, duration: thatDuration, queue: DispatchQueue.main) { setupPayload, error in
                    if let error = error {
                        result(FlutterError(code: "-1", message: "openPairingWindow Error", details: error.localizedDescription))
                    } else {
                        var dict: [String: Any] = [:]
                        
                        let manualEntryCode = setupPayload?.manualEntryCode() ?? ""
                        CtLog.i("manualEntryCode=\(manualEntryCode)")
                        dict["manualPairingCode"] = manualEntryCode
                        
                        if #available(iOS 17.6, *) {
                            let qrCodeString = setupPayload?.qrCodeString() ?? ""
                            CtLog.i("qrCodeString=\(qrCodeString)")
                            dict["qrCode"] = qrCodeString
                        } else {
                            dict["qrCode"] = manualEntryCode
                        }
                        
                        result(dict)
                    }
                }
            } else if #available(iOS 16.2, *) {
                CtLog.i("openPairingWindow() -> 进入iOS16.2")
                let setupPasscode = MTRSetupPayload.generateRandomSetupPasscode()
                let discriminator = RKUtil.generateRandomDiscriminator()
                device.openCommissioningWindow(withSetupPasscode: setupPasscode, discriminator: discriminator, duration: thatDuration, queue: DispatchQueue.main) { setupPayload, error in
                    if let error = error {
                        result(FlutterError(code: "-1", message: "openPairingWindow Error", details: error.localizedDescription))
                    } else {
                        var dict: [String: Any] = [:]
                        
                        let manualEntryCode = setupPayload?.manualEntryCode() ?? ""
                        CtLog.i("manualEntryCode=\(manualEntryCode)")
                        dict["manualPairingCode"] = manualEntryCode
                        dict["qrCode"] = manualEntryCode
                        
                        result(dict)
                    }
                }
            } else {
                result(FlutterError(code: "-3", message: "Unsupported iOS version", details: nil))
            }

        }
    }
    
    private func openECMPairingWindow(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        let commissioningTimeout = arguments["commissioningTimeout"] as? Int ?? 0
        let setupPinCode = arguments["setupPinCode"] as? Int ?? 0
        let discriminator = arguments["discriminator"] as? Int ?? 0
        let iterations = 15000
        let timedInvokeTimeoutMs = 10000
        
        CtLog.i("openECMPairingWindow() -> commissioningTimeout: \(commissioningTimeout)")

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        guard let saltData = RKConstants.threadSaltData.data(using: .utf8), let pakeVerifierData = RKConstants.threadPAKEVerifierData.data(using: .utf8) else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        guard let salt = Data(base64Encoded: saltData), let pakeVerifier = Data(base64Encoded: pakeVerifierData) else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        
        let params = MTRAdministratorCommissioningClusterOpenCommissioningWindowParams()
        params.pakePasscodeVerifier = pakeVerifier
        params.salt = salt
        params.discriminator = NSNumber(value: discriminator)
        params.iterations = NSNumber(value: iterations)
        params.commissioningTimeout = NSNumber(value: commissioningTimeout)
        params.timedInvokeTimeoutMs = NSNumber(value: timedInvokeTimeoutMs)
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            let endpointId = NSNumber(value: 0)
            let commissioningWindowCluster = MTRBaseClusterAdministratorCommissioning(device: device, endpointID: endpointId, queue: DispatchQueue.main)
            commissioningWindowCluster?.openWindow(with: params, completion: { error in
                if let error = error {
                    result(FlutterError(code: "-1", message: "openECMPairingWindow Error", details: error.localizedDescription))
                } else {
                    result(true)
                }
            })
        }
    }
    
    private func readPairingWindowStatus(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        let endpointId = 0
        let clusterId = MTRClusterIDType.administratorCommissioningID.rawValue
        let attributeId = MTRAttributeIDType.clusterAdministratorCommissioningAttributeWindowStatusID.rawValue

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            device.readAttributes(withEndpointID: NSNumber(value: endpointId), clusterID: NSNumber(value: clusterId), attributeID: NSNumber(value: attributeId), params: nil, queue: DispatchQueue.main, completion: {(values, error) in
                if let error = error {
                    CtLog.i("readPairingWindowStatus() -> error: \(error)")
                    result(FlutterError(code: "-1", message: "readPairingWindowStatus Error: \(error)", details: error.localizedDescription))
                } else if let values = values {
                    CtLog.i("readPairingWindowStatus() -> values: \(values)")
                    if #available(iOS 17.0, *) {
                        CtLog.i("readPairingWindowStatus() -> iOS 17.0")
                        do {
                            let report = try MTRAttributeReport(responseValue: values[0])
                            result(report.value)
                        } catch {
                            result(FlutterError(code: "-1", message: "readPairingWindowStatus Error: \(error)", details: nil))
                        }
                    } else if #available(iOS 16.4, *) {
                        CtLog.i("readPairingWindowStatus() -> iOS 16.4")
                        if let itemDict = values.first, let data = itemDict[MTRDataKey], let dataDict = data as? [String: Any]{
                            let attributeValue = dataDict[MTRValueKey] ?? ""
                            result(attributeValue)
                        } else {
                            result(FlutterError(code: "-1", message: "readPairingWindowStatus Error", details: nil))
                        }
                    }
                } else {
                    CtLog.i("readPairingWindowStatus() -> readPairingWindowStatus Error.")
                    result(FlutterError(code: "-1", message: "readPairingWindowStatus Error", details: nil))
                }
            })
        }
        
        
    }
    
    private func readThreadNetworkName(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterThreadNetworkDiagnostics(device: device, endpointID: 0, queue: DispatchQueue.main){
                cluster.readAttributeNetworkName { data, error in
                    if let _ = error {
                        CtLog.e("readThreadNetworkName() -> error=\(error!)")
                        result(FlutterError(code: "-1", message: error!.localizedDescription, details: nil))
                    } else {
                        CtLog.i("readThreadNetworkName() -> value=\(data ?? "")")
                        result(data)
                    }
                }
            } else {
                result(FlutterError(code: "-1", message: "readThreadNetworkName Error", details: nil))
            }
        }
        
        
    }
    
    private func readAccessControlList(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterAccessControl(device: device, endpointID: 0, queue: DispatchQueue.main){
                cluster.readAttributeACL(with: nil, completion: { value, error in
                    guard error == nil else {
                        CtLog.e("readAccessControlList() -> error=\(error!)")
                        result(FlutterError(code: "-1", message: error!.localizedDescription, details: nil))
                        return
                    }
                    CtLog.i("readAccessControlList() -> value=\(value!)")
                    if let aclArray = value as? [MTRAccessControlClusterAccessControlEntryStruct], aclArray.count > 0 {
                        
                        aclArray.forEach { acl in
                            acl.subjects?.forEach({ subject in
                                CtLog.i("subject: \(subject)")
                            })
                        }
                        
                        result("")
                    } else {
                        result(FlutterError(code: "-1", message: "value = null", details: nil))
                    }
                })
            } else {
                result(FlutterError(code: "-1", message: "readAccessControlList Error", details: nil))
            }
        }
        
        
    }
    
    private func getDeviceBasicInfo(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterDescriptor(device: device, endpointID: 0, queue: DispatchQueue.main){
        
                var dict: [String: Any] = [:]
                var count = 4
                cluster.readAttributePartsList { data, error in
                    count = count-1
                    guard error == nil else {
                        CtLog.e("readAttributePartsList error.")
                        return
                    }
        
                    CtLog.i("partsList=\(data!)")
                    dict["partsList"] = data!
                    if (count == 0) {
                        result(dict)
                    }
                }
                
                cluster.readAttributeDeviceTypeList { data, error in
                    count = count-1
                    guard error == nil else {
                        CtLog.e("readAttributeDeviceTypeList error.")
                        return
                    }
               
                    CtLog.i("deviceTypeList=\(data!)")
                    
                    var deviceTypeList: [Int] = []
                    data?.forEach({ item in
                        let deviceTypeItem = item as! MTRDescriptorClusterDeviceTypeStruct
                        deviceTypeList.append(Int(truncating: deviceTypeItem.deviceType))
                    })
                    
                    dict["deviceTypeList"] = deviceTypeList
                    
                    if (count == 0) {
                        result(dict)
                    }
                }
                
                cluster.readAttributeServerList { data, error in
                    count = count-1
                    guard error == nil else {
                        CtLog.e("readAttributeServerList error.")
                        return
                    }
     
                    CtLog.i("serverList=\(data!)")
                    dict["serverList"] = data!
                    if (count == 0) {
                        result(dict)
                    }
                }
                
                cluster.readAttributeClientList { data, error in
                    count = count-1
                    guard error == nil else {
                        CtLog.e("readAttributeClientList error.")
                        return
                    }
        
                    CtLog.i("clientList=\(data!)")
                    dict["clientList"] = data!
                    if (count == 0) {
                        result(dict)
                    }
                }
                
            } else {
                result(FlutterError(code: "-1", message: "getDeviceBasicInfo Error", details: nil))
            }
        }
        
    } 
    
    private func writeCatValue(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(true)
    }    
    
    private func getNodeId(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result("")
    }
    
    private func hasMatterClient(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let hasClient = self.chipClientCache?[fabricId] != nil
        result(hasClient)
    }
    
    private func removeFabric(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterOperationalCredentials(device: device, endpointID: 0, queue: DispatchQueue.main){
                cluster.readAttributeCurrentFabricIndex { value, error in
                    if let error = error {
                        result(FlutterError(code: "-1", message: "removeFabric Error", details: error.localizedDescription))
                    } else {
                        if let fabricIndex = value {
                            CtLog.i("fabricIndex=\(fabricIndex)")
                            let params = MTROperationalCredentialsClusterRemoveFabricParams()
                            params.fabricIndex = fabricIndex
                            cluster.removeFabric(with: params) { data, error in
                                if let error = error {
                                    result(FlutterError(code: "-1", message: "removeFabric Error", details: error.localizedDescription))
                                } else {
                                    CtLog.i("remove fabric success!")
                                    result(true)
                                }
                            }
                        } else {
                            result(FlutterError(code: "-1", message: "removeFabric Error", details: nil))
                        }
                    }
                }
            } else {
                result(FlutterError(code: "-1", message: "removeFabric Error", details: nil))
            }
        }
    }
    
    private func generateQRCodeAndManualPairingCode(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let vendorId = arguments["vendorId"] as? NSNumber ?? 0
        let productId = arguments["productId"] as? NSNumber ?? 0
        let discriminator = arguments["discriminator"] as? NSNumber ?? 0
        let setupPinCode = arguments["setupPinCode"] as? NSNumber ?? 0
        let commissioningFlow = arguments["commissioningFlow"] as? NSNumber ?? 0
        let discoveryCapabilities = arguments["discoveryCapabilities"] as? String ?? "BLE"
        let isShortDiscriminator = arguments["isShortDiscriminator"] as? Bool ?? false
        
        CtLog.i("generateQRCodeAndManualPairingCode() -> discoveryCapabilities: \(discoveryCapabilities)")
        
        let setupPayload = MTRSetupPayload(setupPasscode: setupPinCode, discriminator: discriminator)
        setupPayload.vendorID = vendorId
        setupPayload.productID = productId
        setupPayload.commissioningFlow = .standard
        setupPayload.discoveryCapabilities = .BLE
        setupPayload.hasShortDiscriminator = isShortDiscriminator
        
        if commissioningFlow == 0 {
            setupPayload.commissioningFlow = .standard
        } else if commissioningFlow == 1 {
            setupPayload.commissioningFlow = .userActionRequired
        } else if commissioningFlow == 2 {
            setupPayload.commissioningFlow = .custom
        } else if commissioningFlow == 3 {
            setupPayload.commissioningFlow = .invalid
        }
        
        if discoveryCapabilities == "BLE" {
            setupPayload.discoveryCapabilities = .BLE
        } else if discoveryCapabilities == "OnNetwork" {
            setupPayload.discoveryCapabilities = .onNetwork
        } else if discoveryCapabilities == "SoftAP" {
            setupPayload.discoveryCapabilities = .softAP
        } else {
            setupPayload.discoveryCapabilities = .allMask
        }
        
        var dict: [String: Any] = [:]
        
        let manualEntryCode = setupPayload.manualEntryCode() ?? ""
        CtLog.i("manualEntryCode=\(manualEntryCode)")
        dict["manualPairingCode"] = manualEntryCode
        
        if #available(iOS 17.6, *) {
            let qrCodeString = setupPayload.qrCodeString() ?? ""
            CtLog.i("qrCodeString=\(qrCodeString)")
            dict["qrCode"] = qrCodeString
        } else {
            dict["qrCode"] = manualEntryCode
        }
        
        result(dict)
    }
    
    private func getNetworkLocation(call: FlutterMethodCall, result: @escaping FlutterResult) {
        let dict: [String: Any] = [
            "ipAddress": "",
            "port": "",
            "interfaceIndex": "",
        ]
        result(dict)
    }
    
    private func readDeviceType(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
    
        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterDescriptor(device: device, endpointID: 0, queue: DispatchQueue.main){
                cluster.readAttributeDeviceTypeList { data, error in
                    guard error == nil else {
                        CtLog.e("readDeviceType error.")
                        return
                    }
                    
                    CtLog.i("deviceTypeList=\(data!)")
                    
                    var deviceTypeList: [Int] = []
                    data?.forEach({ item in
                        let deviceTypeItem = item as! MTRDescriptorClusterDeviceTypeStruct
                        deviceTypeList.append(Int(truncating: deviceTypeItem.deviceType))
                    })
                    
                    result(deviceTypeList)
                }
            } else {
                result(FlutterError(code: "-1", message: "readDeviceType Error", details: nil))
            }
        }
    }
    
    private func lightOnOff(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""
        let onOff = arguments["onOff"] as? Int ?? 0

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterOnOff(device: device, endpointID: 0, queue: DispatchQueue.main){
                if onOff == 0 {
                    cluster.off { error in
                        if let _ = error {
                            result(false)
                        } else {
                            result(true)
                        }
                    }
                } else {
                    cluster.on { error in
                        if let _ = error {
                            result(false)
                        } else {
                            result(true)
                        }
                    }
                }
            } else {
                result(FlutterError(code: "-1", message: "lightOnOff Error", details: nil))
            }
        }
    }
    
    private func lightToggle(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterOnOff(device: device, endpointID: 0, queue: DispatchQueue.main){
                cluster.toggle { error in
                    if let _ = error {
                        result(false)
                    } else {
                        result(true)
                    }
                }
            } else {
                result(FlutterError(code: "-1", message: "lightToggle Error", details: nil))
            }
        }
    }
    
    private func readBssid(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        DispatchQueue.main.async {
            let device = MTRDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            let clusterWiFiNetworkDiagnostics = MTRClusterWiFiNetworkDiagnostics(device: device, endpointID: 0, queue: DispatchQueue.main)
            let params = MTRReadParams()
            if let bssidDict = clusterWiFiNetworkDiagnostics?.readAttributeBSSID(with: params) {
                CtLog.i("readBssid() -> bssidDict=\(bssidDict)")
                result("")
            } else {
                result(FlutterError(code: "-1", message: "readAttributeBSSID Error", details: nil))
            }
        }
    }
    
    private func readFabricsAttribute(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            let cluster = MTRBaseClusterOperationalCredentials(device: device, endpointID: 0, queue: DispatchQueue.main)
            let params = MTRReadParams()
            cluster?.readAttributeFabrics(with: params, completion: { data, error in
                if let _ = error {
                    result(FlutterError(code: "-1", message: error?.localizedDescription, details: nil))
                } else {
                    CtLog.i("readFabricsAttribute() -> data=\(String(describing: data))")
                    var resultArray: [[String: Any]] = []
                    
                    if let thatData = data {
                        for item in thatData {
                            var dict: [String: Any] = [:]
                            if let thatItem = item as? MTROperationalCredentialsClusterFabricDescriptorStruct {
                                dict["fabricId"] = thatItem.fabricID
                                dict["fabricIndex"] = thatItem.fabricIndex
                                dict["vendorId"] = thatItem.vendorID
                                dict["label"] = thatItem.label
                                dict["rootPublicKey"] = thatItem.rootPublicKey
                            }
                            resultArray.append(dict)
                        }
                    }
              
                    result(resultArray)
                }
            })
        }
    } 
    
    private func unregisterICDClient(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        
//        DispatchQueue.main.async {
//            let device = MTRDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
//            if let cluster = MTRClusterICDManagement(device: device, endpointID: 0, queue: DispatchQueue.main) {
//               
//            } else {
//                result(FlutterError(code: "-1", message: "getNetworkInterfaces Error", details: nil))
//            }
//        }
   
        
        result(true)
    }    
    
    private func cancelCommissioning(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? Int ?? 0

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            do {
                try deviceController.cancelCommissioning(forNodeID: NSNumber(value: nodeId))
                result(true)
            } catch {
                result(FlutterError(code: "-1", message: "cancel commissioning error: \(error.localizedDescription)", details: nil))
            }
        }
    } 
    
    private func getNetworkInterfaces(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterGeneralDiagnostics(device: device, endpointID: 0, queue: DispatchQueue.main) {
                cluster.readAttributeNetworkInterfaces(completion: { data, error in
                    if let _ = error {
                        CtLog.i("getNetworkInterfaces Error: \(String(describing: error))")
                        result(FlutterError(code: "-1", message: error?.localizedDescription, details: nil))
                    } else {
                        CtLog.i("getNetworkInterfaces Success: \(String(describing: data))")
                        var resultArray: [[String: Any]] = []
                        data?.forEach({ item in
                            if let networkInterface = item as? MTRGeneralDiagnosticsClusterNetworkInterface {
                                var dict: [String: Any] = [:]
                                dict["name"] = networkInterface.name
                                dict["isOperational"] = networkInterface.isOperational
                                dict["offPremiseServicesReachableIPv4"] = networkInterface.offPremiseServicesReachableIPv4
                                dict["offPremiseServicesReachableIPv6"] = networkInterface.offPremiseServicesReachableIPv6
                                dict["hardwareAddress"] = networkInterface.hardwareAddress
                                dict["iPv4Addresses"] = networkInterface.iPv4Addresses
                                dict["iPv6Addresses"] = networkInterface.iPv6Addresses
                                dict["type"] = networkInterface.type
                                resultArray.append(dict)
                            }
                        })
                        result(resultArray)
                    }
                })
            } else {
                result(FlutterError(code: "-1", message: "getNetworkInterfaces Error", details: nil))
            }
        }
    }
    
    private func readRegisteredClients(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        var resultArray: [[String: Any]] = []
        result(resultArray)
        
//        DispatchQueue.main.async {
//            if #available(iOS 17.5, *) {
//               
//            }
//            
//            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
//            if let cluster = MTRBaseClusterICDManagement(device: device, endpointID: 0, queue: DispatchQueue.main) {
//                
//               
//            } else {
//                result(FlutterError(code: "-1", message: "readRegisteredClients Error", details: nil))
//            }
//            
//            let device = MTRDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
//            if let cluster = MTRClusterICDManagement(device: device, endpointID: 0, queue: DispatchQueue.main) {
//                
//               
//            } else {
//                result(FlutterError(code: "-1", message: "readRegisteredClients Error", details: nil))
//            }
//        }
        
    }
    
    private func getFabricIndex(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRBaseDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            if let cluster = MTRBaseClusterOperationalCredentials(device: device, endpointID: 0, queue: DispatchQueue.main){
                cluster.readAttributeCurrentFabricIndex { value, error in
                    if let error = error {
                        result(FlutterError(code: "-1", message: "getFabricIndex Error", details: error.localizedDescription))
                    } else {
                        if let fabricIndex = value {
                            CtLog.i("fabricIndex=\(fabricIndex)")
                            result(fabricIndex)
                        } else {
                            result(FlutterError(code: "-1", message: "getFabricIndex Error", details: nil))
                        }
                    }
                }
            } else {
                result(FlutterError(code: "-1", message: "getFabricIndex Error", details: nil))
            }
        }
    }
    
    
    private func startDnssd(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(nil)
    }
    
    private func stopDnssd(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(nil)
    }
    
    private func getDeviceControllerPtr(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(-1)
    }
    
    private func getControllerNodeId(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        let controllerNodeID = deviceController.controllerNodeID ?? -1
        CtLog.i("controllerNodeID: \(controllerNodeID)")
        result(controllerNodeID)
    }
    
    private func getCompressedFabricId(call: FlutterMethodCall, result: @escaping FlutterResult) {
        result(-1)
    }
    
    private func isRunning(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        let isRunning = deviceController.isRunning
        CtLog.i("isRunning: \(isRunning)")
        result(isRunning)
    }
    
    private func getDeviceState(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let fabricId = arguments["fabricId"] as? String ?? ""
        let nodeId = arguments["nodeId"] as? String ?? ""

        guard let chipClient = self.chipClientCache?[fabricId] else {
            result(FlutterError(code: "-20", message: "chipClient = nil", details: nil))
            return
        }
        
        guard let deviceController = chipClient.getDeviceController() else {
            result(FlutterError(code: "-20", message: "deviceController = nil", details: nil))
            return
        }
        
        guard let uint64Value = nodeId.hexToDecimal else {
            result(FlutterError(code: "-20", message: "hexToDecimal failed", details: nil))
            return
        }
        
        DispatchQueue.main.async {
            let device = MTRDevice(nodeID: NSNumber(value: uint64Value), controller: deviceController)
            result(device.state.rawValue)
        }
    }
    
    private func isThreadSupported(call: FlutterMethodCall, result: @escaping FlutterResult) {
        RKThreadCredentialsManager.shared.isThreadSupported { flag in
            CtLog.i("isThreadSupported=\(flag)")
            result(flag)
        }
    }    
    
    private func isPreferredCredentials(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let dataset = arguments["dataset"] as? String ?? ""
        
        guard let threadDataset = dataset.hexadecimal else {
            result(FlutterError(code: "-20", message: "dataset parsing failure", details: nil))
            return
        }
        
        CtLog.i("threadDataset=\(String(describing: dataset.hexadecimal as? NSData))")
        
        RKThreadCredentialsManager.shared.isPreferredCredentials(activeOpsDataset: threadDataset) { flag in
            CtLog.i("isPreferredCredentials=\(flag)")
            result(flag)
        }
    }
    
    private func fetchPreferredThreadCredentials(call: FlutterMethodCall, result: @escaping FlutterResult) {
        RKThreadCredentialsManager.shared.fetchPreferredThreadCredentials { credentials in
            var dict: [String: Any] = [:]
            if let thatCredentials = credentials {
                CtLog.i("==========start==========")
                CtLog.i("\n")
                CtLog.i("channel=\(String(describing: thatCredentials.channel))")
                CtLog.i("borderAgentID=\(String(describing: thatCredentials.borderAgentID?.hexadecimalString))")
                CtLog.i("extendedPANID=\(String(describing: thatCredentials.extendedPANID?.hexadecimalString))")
                CtLog.i("networkName=\(String(describing: thatCredentials.networkName))")
                CtLog.i("networkKey=\(String(describing: thatCredentials.networkKey?.hexadecimalString))")
                CtLog.i("panID=\(String(describing: thatCredentials.panID?.hexadecimalString))")
                CtLog.i("pskc=\(String(describing: thatCredentials.pskc?.hexadecimalString))")
                CtLog.i("dataset=\(String(describing: thatCredentials.activeOperationalDataSet?.hexadecimalString))")
                CtLog.i("creationDate=\(String(describing: thatCredentials.creationDate))")
                CtLog.i("lastModificationDate=\(String(describing: thatCredentials.lastModificationDate))")
                CtLog.i("\n")
                CtLog.i("==========end==========")

                dict["channel"] = thatCredentials.channel
                dict["borderAgentID"] = thatCredentials.borderAgentID?.hexadecimalString ?? ""
                dict["extendedPANID"] = thatCredentials.extendedPANID?.hexadecimalString ?? ""
                dict["networkName"] = thatCredentials.networkName
                dict["networkKey"] = thatCredentials.networkKey?.hexadecimalString ?? ""
                dict["panID"] = thatCredentials.panID?.hexadecimalString ?? ""
                dict["pskc"] = thatCredentials.pskc?.hexadecimalString ?? ""
                dict["activeOperationalDataSet"] = thatCredentials.activeOperationalDataSet?.hexadecimalString ?? ""
                dict["creationDate"] = thatCredentials.creationDate?.timeIntervalSince1970 ?? 0
                dict["lastModificationDate"] = thatCredentials.lastModificationDate?.timeIntervalSince1970 ?? 0
            }
            result(dict)
        }
    }
    
    private func fetchAllActiveThreadCredentials(call: FlutterMethodCall, result: @escaping FlutterResult) {
        RKThreadCredentialsManager.shared.fetchAllActiveThreadCredentials { credentialsSet in
            if let thatCredentialsSet = credentialsSet {
                var allData: [[String: Any]] = []
                for thatCredentials in thatCredentialsSet {
                    CtLog.i("==========start==========")
                    CtLog.i("\n")
                    CtLog.i("channel=\(String(describing: thatCredentials.channel))")
                    CtLog.i("borderAgentID=\(String(describing: thatCredentials.borderAgentID?.hexadecimalString))")
                    CtLog.i("extendedPANID=\(String(describing: thatCredentials.extendedPANID?.hexadecimalString))")
                    CtLog.i("networkName=\(String(describing: thatCredentials.networkName))")
                    CtLog.i("networkKey=\(String(describing: thatCredentials.networkKey?.hexadecimalString))")
                    CtLog.i("panID=\(String(describing: thatCredentials.panID?.hexadecimalString))")
                    CtLog.i("pskc=\(String(describing: thatCredentials.pskc?.hexadecimalString))")
                    CtLog.i("dataset=\(String(describing: thatCredentials.activeOperationalDataSet?.hexadecimalString))")
                    CtLog.i("creationDate=\(String(describing: thatCredentials.creationDate))")
                    CtLog.i("lastModificationDate=\(String(describing: thatCredentials.lastModificationDate))")
                    CtLog.i("\n")
                    CtLog.i("==========end==========")

                    
                    var dict: [String: Any] = [:]
                    dict["channel"] = thatCredentials.channel
                    dict["borderAgentID"] = thatCredentials.borderAgentID?.hexadecimalString ?? ""
                    dict["extendedPANID"] = thatCredentials.extendedPANID?.hexadecimalString ?? ""
                    dict["networkName"] = thatCredentials.networkName
                    dict["networkKey"] = thatCredentials.networkKey?.hexadecimalString ?? ""
                    dict["panID"] = thatCredentials.panID?.hexadecimalString ?? ""
                    dict["pskc"] = thatCredentials.pskc?.hexadecimalString ?? ""
                    dict["activeOperationalDataSet"] = thatCredentials.activeOperationalDataSet?.hexadecimalString ?? ""
                    dict["creationDate"] = thatCredentials.creationDate?.timeIntervalSince1970 ?? 0
                    dict["lastModificationDate"] = thatCredentials.lastModificationDate?.timeIntervalSince1970 ?? 0
                    allData.append(dict)
                }
                result(allData)
            }
        }
    }
       
    private func fetchAllThreadCredentials(call: FlutterMethodCall, result: @escaping FlutterResult) {
        RKThreadCredentialsManager.shared.fetchAllThreadCredentials { credentialsSet in
            if let thatCredentialsSet = credentialsSet {
                var allData: [[String: Any]] = []
                for thatCredentials in thatCredentialsSet {
                    CtLog.i("==========start==========")
                    CtLog.i("\n")
                    CtLog.i("channel=\(String(describing: thatCredentials.channel))")
                    CtLog.i("borderAgentID=\(String(describing: thatCredentials.borderAgentID?.hexadecimalString))")
                    CtLog.i("extendedPANID=\(String(describing: thatCredentials.extendedPANID?.hexadecimalString))")
                    CtLog.i("networkName=\(String(describing: thatCredentials.networkName))")
                    CtLog.i("networkKey=\(String(describing: thatCredentials.networkKey?.hexadecimalString))")
                    CtLog.i("panID=\(String(describing: thatCredentials.panID?.hexadecimalString))")
                    CtLog.i("pskc=\(String(describing: thatCredentials.pskc?.hexadecimalString))")
                    CtLog.i("dataset=\(String(describing: thatCredentials.activeOperationalDataSet?.hexadecimalString))")
                    CtLog.i("creationDate=\(String(describing: thatCredentials.creationDate))")
                    CtLog.i("lastModificationDate=\(String(describing: thatCredentials.lastModificationDate))")
                    CtLog.i("\n")
                    CtLog.i("==========end==========")

                    var dict: [String: Any] = [:]
                    dict["channel"] = thatCredentials.channel
                    dict["borderAgentID"] = thatCredentials.borderAgentID?.hexadecimalString ?? ""
                    dict["extendedPANID"] = thatCredentials.extendedPANID?.hexadecimalString ?? ""
                    dict["networkName"] = thatCredentials.networkName
                    dict["networkKey"] = thatCredentials.networkKey?.hexadecimalString ?? ""
                    dict["panID"] = thatCredentials.panID?.hexadecimalString ?? ""
                    dict["pskc"] = thatCredentials.pskc?.hexadecimalString ?? ""
                    dict["activeOperationalDataSet"] = thatCredentials.activeOperationalDataSet?.hexadecimalString ?? ""
                    dict["creationDate"] = thatCredentials.creationDate?.timeIntervalSince1970 ?? 0
                    dict["lastModificationDate"] = thatCredentials.lastModificationDate?.timeIntervalSince1970 ?? 0
                    allData.append(dict)
                }
                result(allData)
            }
        }
    }
    
    private func saveThreadOperationalCredentials(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let agentId = arguments["agentId"] as? String ?? ""
        let dataset = arguments["dataset"] as? String ?? ""
        
        guard let threadAgentId = agentId.hexadecimal else {
            result(FlutterError(code: "-20", message: "agentId parsing failure", details: nil))
            return
        }
        
        guard let threadDataset = dataset.hexadecimal else {
            result(FlutterError(code: "-20", message: "dataset parsing failure", details: nil))
            return
        }
        
        CtLog.i("threadAgentId=\(String(describing: agentId.hexadecimal as? NSData))")
        CtLog.i("threadDataset=\(String(describing: dataset.hexadecimal as? NSData))")
        
        RKThreadCredentialsManager.shared.saveThreadOperationalCredentials(activeOpsDataset: threadDataset, borderAgentId: threadAgentId) { flag in
            result(flag)
        }
    }    
    
    
    private func deleteCredentials(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let arguments = call.arguments as? [String: Any] else {
            result(FlutterError(code: "-20", message: "arguments are not valid", details: nil))
            return
        }
        
        let agentId = arguments["agentId"] as? String ?? ""
        
        guard let threadAgentId = agentId.hexadecimal else {
            result(FlutterError(code: "-20", message: "agentId parsing failure", details: nil))
            return
        }
        
        CtLog.i("threadAgentId=\(String(describing: agentId.hexadecimal as? NSData))")
        
        RKThreadCredentialsManager.shared.deleteCredentials(borderAgentId: threadAgentId) { flag in
            result(flag)
        }
    }
    
}
