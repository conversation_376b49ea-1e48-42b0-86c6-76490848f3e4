//
//  CtAddDeviceManager.swift
//  ct_flutter_matter_plugin
//
//  Created by Jie<PERSON><PERSON> on 2024/5/22.
//

import Foundation
import MatterSupport

public class CtAddDeviceManager: NSObject {
    
    private var deviceController: MTRDeviceController? = nil
    private var params: MTRCommissioningParameters? = nil
    private var deviceId: UInt64 = 1
    private var retryCount: Int = 3
    private var deviceAttestationCallback: DeviceAttestationCallback? = nil
    private var readCommissioningInfoCallback: ReadCommissioningInfoCallback? = nil
    private var commissioningCompleteCallback: CommissioningCompleteCallback? = nil
    
    init(deviceController: MTRDeviceController) {
        self.deviceController = deviceController
    }
    
    private func executeSetupRequest(setupPayload: MTRSetupPayload) {
        let ecosystemName = "habi"
        let homeName = "My Home"
        
        Task {
            let topology = MatterAddDeviceRequest.Topology(ecosystemName: ecosystemName, homes: [MatterAddDeviceRequest.Home(displayName: homeName)])
            var setupRequest = MatterAddDeviceRequest(topology: topology, setupPayload: setupPayload)
            setupRequest.showDeviceCriteria = .allDevices
            setupRequest.shouldScanNetworks = true
            do {
                try await setupRequest.perform()
                
                if let payload = RKMatterExtensionStorage.shared.getOnboardingPayload() {
                    
                    let deviceName = RKMatterExtensionStorage.shared.getDeviceName()
                    let commissioningId = RKMatterExtensionStorage.shared.getCommissioningId()
                    CtLog.i("addThreadDevice() -> payload=\(payload)")
                    CtLog.i("addThreadDevice() -> deviceName: \(deviceName ?? "")")
                    CtLog.i("addThreadDevice() -> commissioningId: \(commissioningId ?? "")")
                    
                    let newSetupPayload: MTRSetupPayload;
                    do {
                        newSetupPayload = try MTRSetupPayload(onboardingPayload: payload)
                        CtLog.i("addThreadDevice() -> Parse QRCode Success, payload=\(newSetupPayload)")
                        self.handleCommissioningSession(setupPayload: newSetupPayload)
                    } catch {
                        CtLog.e("addThreadDevice() -> Parse QRCode Error: \(error.localizedDescription)")
                        self.commissioningCompleteCallback?("", CtStatusCode.parsingQRCodeError, [:])
                        return
                    }
                    
                } else {
                    CtLog.e("addThreadDevice() -> payload=nil")
                    self.commissioningCompleteCallback?("", CtStatusCode.parsingQRCodeError, [:])
                }

            } catch {
                CtLog.e("addThreadDevice() -> setupRequest perform Error: \(error.localizedDescription)")
                self.commissioningCompleteCallback?("", CtStatusCode.setupRequestError, [:])
            }
        }
    }
    
    private func handleCommissioningSession(setupPayload: MTRSetupPayload) {
        CtLog.i("handleCommissioningSession() -> discriminator=\(setupPayload.discriminator)")
        CtLog.i("handleCommissioningSession() -> setupPasscode=\(setupPayload.setupPasscode)")
        
        guard let deviceController = self.deviceController else {
            CtLog.e("handleCommissioningSession() -> deviceController = nil")
            self.commissioningCompleteCallback?("", CtStatusCode.unknownError, [:])
            return
        }
        
        let queue = DispatchQueue(label: "com.csa.matter.qrcodevc.callback")
        deviceController.setDeviceControllerDelegate(self, queue: queue)
        deviceId = RKDeviceStorage.shared.getNextAvailableDeviceID()
        do {
            try deviceController.setupCommissioningSession(with: setupPayload, newNodeID: deviceId as NSNumber)
            RKDeviceStorage.shared.setNextAvailableDeviceID(deviceId + 1)
            CtLog.i("handleCommissioningSession() -> pair device execute, deviceId=\(deviceId)")
        } catch {
            CtLog.e("handleCommissioningSession() -> pair device error: \(error.localizedDescription)")
            self.commissioningCompleteCallback?("", CtStatusCode.pairingDeviceError, [:])
        }
    }
    
    private func reHandleCommissioningSession() {
        if (self.retryCount <= 0) {
            CtLog.d("reHandleCommissioningSession() -> Retry over...")
            self.commissioningCompleteCallback?("", MTRError.timeout.rawValue, [:])
            return;
        }
        
        self.retryCount -= 1;
        CtLog.i("reHandleCommissioningSession() -> Start retrying, The remaining number of times:\(self.retryCount)")

        if let payload = RKMatterExtensionStorage.shared.getOnboardingPayload() {
            CtLog.i("reHandleCommissioningSession() -> payload=\(payload)")
            
            let newSetupPayload: MTRSetupPayload;
            do {
                newSetupPayload = try MTRSetupPayload(onboardingPayload: payload)
                CtLog.i("reHandleCommissioningSession() -> Parse QRCode Success, payload=\(newSetupPayload)")
                self.handleCommissioningSession(setupPayload: newSetupPayload)
            } catch {
                CtLog.e("reHandleCommissioningSession() -> Parse QRCode Error: \(error.localizedDescription)")
                self.commissioningCompleteCallback?("", CtStatusCode.parsingQRCodeError, [:])
                return
            }
            
        } else {
            CtLog.e("reHandleCommissioningSession() -> payload=nil")
            self.commissioningCompleteCallback?("", CtStatusCode.parsingQRCodeError, [:])
        }
    }
 
}

extension CtAddDeviceManager: CtAddDeviceProtocol {
    
    func addWiFiDevice(qrcode: String, ssid: String, pwd: String,
                       deviceAttestationCallback: @escaping DeviceAttestationCallback,
                       readCommissioningInfoCallback: @escaping ReadCommissioningInfoCallback,
                       commissioningCompleteCallback: @escaping CommissioningCompleteCallback
    ) {
        CtLog.i("addWiFiDevice() -> qrcode=\(qrcode), ssid=\(ssid), pwd=\(pwd)")
        self.deviceAttestationCallback = deviceAttestationCallback
        self.readCommissioningInfoCallback = readCommissioningInfoCallback
        self.commissioningCompleteCallback = commissioningCompleteCallback
    
        let setupPayload: MTRSetupPayload;
        do {
            setupPayload = try MTRSetupPayload(onboardingPayload: qrcode)
            CtLog.i("addWiFiDevice() -> Parse QRCode Success, payload=\(setupPayload)")
        } catch {
            CtLog.e("addWiFiDevice() -> Parse QRCode Error: \(error.localizedDescription)")
            self.commissioningCompleteCallback?("", CtStatusCode.parsingQRCodeError, [:])
            return
        }
        
//        let params = MTRCommissioningParameters()
//        params.wifiSSID = ssid.data(using: .utf8)
//        params.wifiCredentials = pwd.data(using: .utf8)
//        params.deviceAttestationDelegate = self
//        params.failSafeTimeout = 600
        
        let params = MTRCommissioningParameters()
        params.deviceAttestationDelegate = self
        params.failSafeTimeout = 300
        
        self.params = params
        self.executeSetupRequest(setupPayload: setupPayload)
    }
    
    func addThreadDevice(qrcode: String, agentId: String, dataset: String,
                         deviceAttestationCallback: @escaping DeviceAttestationCallback,
                         readCommissioningInfoCallback: @escaping ReadCommissioningInfoCallback,
                         commissioningCompleteCallback: @escaping CommissioningCompleteCallback
    ) {
        CtLog.i("addThreadDevice() -> qrcode=\(qrcode), agentId=\(agentId), dataset=\(dataset)")
        self.deviceAttestationCallback = deviceAttestationCallback
        self.readCommissioningInfoCallback = readCommissioningInfoCallback
        self.commissioningCompleteCallback = commissioningCompleteCallback
    
        let setupPayload: MTRSetupPayload;
        do {
            setupPayload = try MTRSetupPayload(onboardingPayload: qrcode)
            CtLog.i("addThreadDevice() -> Parse QRCode Success, payload=\(setupPayload)")
        } catch {
            CtLog.e("addThreadDevice() -> Parse QRCode Error: \(error.localizedDescription)")
            self.commissioningCompleteCallback?("", CtStatusCode.parsingQRCodeError, [:])
            return
        }
        
        CtLog.i("addThreadDevice() -> discriminator=\(setupPayload.discriminator)")
        
        guard let threadOperationalDataset = Data(base64Encoded: dataset, options: []) else {
            CtLog.e("addThreadDevice() -> dataset parsing failure")
            self.commissioningCompleteCallback?("", CtStatusCode.parsingDatasetError, [:])
            return
        }
        
        if let decodedString = String(data: threadOperationalDataset, encoding: .utf8) {
            CtLog.i("addThreadDevice() -> decoded dataset=\(decodedString)")
        }
        
//        let params = MTRCommissioningParameters()
//        params.threadOperationalDataset = threadOperationalDataset
//        params.deviceAttestationDelegate = self
//        params.failSafeTimeout = 600
//        self.params = params
//        self.handleCommissioningSession(setupPayload: setupPayload)
        
        guard let threadAgentId = agentId.hexadecimal else {
            CtLog.e("addThreadDevice() -> agentId parsing failure")
            self.commissioningCompleteCallback?("", CtStatusCode.parsingDatasetError, [:])
            return
        }
        
        RKMatterExtensionStorage.shared.saveBorderAgentId(borderAgentId: threadAgentId)
        
        let params = MTRCommissioningParameters()
        params.deviceAttestationDelegate = self
        params.failSafeTimeout = 300
 
        self.params = params
        self.executeSetupRequest(setupPayload: setupPayload)
    }
}

extension CtAddDeviceManager: MTRDeviceControllerDelegate {
    
    public func controller(_ controller: MTRDeviceController, commissioningComplete error: Error?, nodeID: NSNumber?) {
        CtLog.i("commissioningComplete() -> iOS17.0")
        
        guard error == nil else {
            CtLog.e("commissioningComplete() -> error=\(error!)")
            if let matterError = error as? MTRError {
                CtLog.e("commissioningComplete() -> matterError=\(matterError.errorCode)")
                self.reHandleCommissioningSession()
                return
            }
            
            self.commissioningCompleteCallback?("", CtStatusCode.commissionNodeError, [:])
            return
        }
        
        if let tempNodeID = nodeID?.uint64Value {
            let newNodeId = String(format: "%016lX", tempNodeID)
            CtLog.i("commissioningComplete() -> nodeID=\(tempNodeID)，newNodeId=\(newNodeId)")
            self.commissioningCompleteCallback?(newNodeId, 0, [:])
        } else {
            CtLog.e("commissioningComplete() -> tempNodeID=nil")
            self.commissioningCompleteCallback?("", CtStatusCode.commissionNodeError, [:])
        }
        
    }
    
    @available(iOS 17.6, *)
    public func controller(_ controller: MTRDeviceController, commissioningComplete error: (any Error)?, nodeID: NSNumber?, metrics: MTRMetrics) {
        CtLog.i("commissioningComplete() -> iOS17.6")
        
        var stepDict: [String: Any] = [:]
        for itemKey in metrics.allKeys {
            CtLog.i("commissioningComplete() -> key=\(itemKey)")
            if let itemData = metrics.metricData(forKey: itemKey) {
                var stepItemDict: [String: Any] = [:]
                stepItemDict["value"] = itemData.value
                stepItemDict["errorCode"] = itemData.errorCode
                stepItemDict["duration"] = itemData.duration
                
                stepDict[itemKey] = stepItemDict
                
                CtLog.i("commissioningComplete() -> value=\(String(describing: itemData.value))")
                CtLog.i("commissioningComplete() -> errorCode=\(String(describing: itemData.errorCode))")
                CtLog.i("commissioningComplete() -> duration=\(String(describing: itemData.duration))")
            }
        }
        
        guard error == nil else {
            CtLog.e("commissioningComplete() -> error=\(error!)")
            if let matterError = error as? MTRError {
                CtLog.e("commissioningComplete() -> matterError=\(matterError.errorCode)")
                self.reHandleCommissioningSession()
                return
            }
            
            self.commissioningCompleteCallback?("", CtStatusCode.commissionNodeError, stepDict)
            return
        }
        
        if let tempNodeID = nodeID?.uint64Value {
            let newNodeId = String(format: "%016lX", tempNodeID)
            CtLog.i("commissioningComplete() -> nodeID=\(tempNodeID)，newNodeId=\(newNodeId)")
            self.commissioningCompleteCallback?(newNodeId, 0, stepDict)
        } else {
            CtLog.e("commissioningComplete() -> tempNodeID=nil")
            self.commissioningCompleteCallback?("", CtStatusCode.commissionNodeError, stepDict)
        }

    }
    
    @available(iOS 18.4, *)
    public func controller(_ controller: MTRDeviceController, read info: MTRCommissioneeInfo) {
        let vid = info.productIdentity.vendorID;
        let pid = info.productIdentity.productID;
        CtLog.i("readCommissioningInfo() -> 18.4 vendorID=\(vid), productID=\(pid)")
        self.readCommissioningInfoCallback?(vid.intValue, pid.intValue)
    }
    
    @available(iOS 17.0, *)
    public func controller(_ controller: MTRDeviceController, readCommissioningInfo info: MTRProductIdentity) {
        CtLog.i("readCommissioningInfo() -> 17.0 vendorID=\(info.vendorID), productID=\(info.productID)")
        self.readCommissioningInfoCallback?(info.vendorID.intValue, info.productID.intValue)
    }
    
    public func controller(_ controller: MTRDeviceController, commissioningSessionEstablishmentDone error: Error?) {
        guard error == nil else {
            CtLog.e("commissioningSessionEstablishmentDone() -> error=\(error!)")
            return
        }
        guard let deviceController = self.deviceController else {
            CtLog.e("commissioningSessionEstablishmentDone() -> deviceController=nil")
            return
        }
        
        let id = deviceId as NSNumber;
        CtLog.i("commissioningSessionEstablishmentDone() -> deviceId=\(id)")
        
        do {
            let device = try deviceController.deviceBeingCommissioned(withNodeID: id)
            CtLog.i("commissioningSessionEstablishmentDone() -> sessionTransportType=\(device.sessionTransportType)")
        } catch {
            CtLog.e("commissioningSessionEstablishmentDone() -> Failed to get Device, with error=\(error)")
        }

        do {
            try deviceController.commissionNode(withID: id, commissioningParams: self.params!)
            CtLog.i("commissioningSessionEstablishmentDone() -> Commission node...")
        } catch {
            CtLog.e("commissioningSessionEstablishmentDone() -> Failed to commission Device, with error=\(error)")
            self.commissioningCompleteCallback?("", CtStatusCode.commissionNodeError, [:])
        }
    }
              
    public func controller(_ controller: MTRDeviceController, statusUpdate status: MTRCommissioningStatus) {
        CtLog.i("statusUpdate() -> status=\(status)")
    }
    
    @available(iOS 18.2, *)
    public func controller(_ controller: MTRDeviceController, suspendedChangedTo suspended: Bool) {
        CtLog.i("suspendedChangedTo() -> suspended=\(suspended)")
    }
    
    @available(iOS 18.4, *)
    public func devicesChanged(for controller: MTRDeviceController) {
        CtLog.i("devicesChanged()")
    }
    
    @available(iOS 18.5, *)
    public func controller(_ controller: MTRDeviceController, commissioneeHasReceivedNetworkCredentials nodeID: NSNumber) {
        CtLog.i("commissioneeHasReceivedNetworkCredentials() -> nodeID=\(nodeID)")
    }
    
}

extension CtAddDeviceManager: MTRDeviceAttestationDelegate {
    
    public func deviceAttestationCompleted(for controller: MTRDeviceController, opaqueDeviceHandle: UnsafeMutableRawPointer, attestationDeviceInfo: MTRDeviceAttestationDeviceInfo, error: Error?) {
        CtLog.i("deviceAttestationCompleted() -> Device Attestation Completed, opaqueDeviceHandle: \(opaqueDeviceHandle), attestationDeviceInfo: \(attestationDeviceInfo)")
        
        if let thatError = error {
            CtLog.d("deviceAttestationCompleted() -> error: \(thatError)")
        }
        
        let vid = attestationDeviceInfo.vendorID ?? 0
        let pid = attestationDeviceInfo.productID ?? 0
        CtLog.d("deviceAttestationCompleted() -> vid: \(vid), pid: \(pid)")
        
        self.deviceAttestationCallback?(nil, attestationDeviceInfo, 0);
        
        do {
            CtLog.d("deviceAttestationCompleted() -> Continue commissioning...")
            try controller.continueCommissioningDevice(opaqueDeviceHandle, ignoreAttestationFailure: true)
        } catch {
            self.commissioningCompleteCallback?("", CtStatusCode.deviceAttestationError, [:])
        }
    }
    
}
