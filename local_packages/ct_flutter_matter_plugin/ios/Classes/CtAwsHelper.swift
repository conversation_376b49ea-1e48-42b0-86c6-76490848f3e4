//
//  CtAwsHelper.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/19.
//

import Foundation


class CtAwsHelper {
    static let rootCa = "rootCA"
    static let userNoc = "userNOC"
    static let nodeNoc = "nodeNOC"
    
    var scheme: String;
    var host: String;
    var path: String;
    
    init(scheme: String, host: String, path: String) {
        self.scheme = scheme
        self.host = host
        self.path = path
    }
    
    func getRootCa(thingName: String, completionHandler: @escaping ([String: Any]?) -> Void) {
        var urlComponents = URLComponents()
        urlComponents.scheme = self.scheme
        urlComponents.host = self.host
        urlComponents.path = "\(self.path)/\(CtAwsHelper.rootCa)"
        urlComponents.queryItems = [
            URLQueryItem(name: "thing_name", value: thingName),
        ]
        
        guard let url = urlComponents.url else {
            completionHandler(nil)
            CtLog.e("rootCa url error")
            return
        }
        
        CtLog.i("v2 getRootCa url: \(url)")
        
        let configuration = URLSessionConfiguration.default
        configuration.requestCachePolicy = .reloadIgnoringLocalCacheData
        let session = URLSession(configuration: configuration)

        let task = session.dataTask(with: url) { data, response, error in
            if let error = error {
                CtLog.e("请求出错 Error: \(error)")
                completionHandler(nil)
                return
            }
            if let response = response as? HTTPURLResponse {
                CtLog.i("状态码为: \(response.statusCode)")
            }
            if let data = data {
                let str = String(data: data, encoding: .utf8)
                CtLog.i("请求成功 Response: \(str ?? "")")
                guard let jsonData = str?.data(using: .utf8) else {
                    completionHandler(nil)
                    return
                }
                
                do {
                    let jsonObject = try JSONSerialization.jsonObject(with: jsonData)
                    guard let result = jsonObject as? [String: Any] else {
                        completionHandler(nil)
                        CtLog.e("result = null")
                        return
                    }
                    guard let success = result["success"] as? Bool, success == true else {
                        completionHandler(nil)
                        CtLog.e("success = false")
                        return
                    }
                    guard let payload = result["payload"] as? [String: Any] else {
                        completionHandler(nil)
                        CtLog.e("payload = null")
                        return
                    }
                    guard let rootCa = payload["rootCA"] as? String, rootCa.isEmpty == false else {
                        completionHandler(nil)
                        CtLog.e("rootCa = null")
                        return
                    }                    
                    
                    var newRootCa = rootCa.replacingOccurrences(of: RKConstants.BEGIN_CERTIFICATE, with: "")
                    newRootCa = newRootCa.replacingOccurrences(of: RKConstants.END_CERTIFICATE, with: "")
                    newRootCa = newRootCa.replacingOccurrences(of: "\n", with: "")
                    
                    var dict: [String: Any] = [:]
                    dict["rootCa"] = newRootCa
                    dict["rootCaCertArn"] = payload["certArn"] as? String
                    dict["rootCaCertUrl"] = payload["certUrl"] as? String
                    dict["ipk"] = payload["ipk"] as? String
                    dict["fabricId"] = payload["fabricId"] as? String

                    completionHandler(dict)
                        
                } catch {
                    CtLog.e("Error parsing JSON: \(error.localizedDescription)")
                    completionHandler(nil)
                }
            } else {
                completionHandler(nil)
            }
        }
        task.resume()
    }
    
    func getUserNoc(csr: String, rootCAArn: String, completionHandler: @escaping ([String: Any]?) -> Void) {
        getNoc(tag: "userNOC", csr: csr, rootCAArn: rootCAArn, completionHandler: completionHandler)
    }
    
    func getNodeNoc(csr: String, rootCAArn: String, completionHandler: @escaping ([String: Any]?) -> Void) {
        getNoc(tag: "nodeNOC", csr: csr, rootCAArn: rootCAArn, completionHandler: completionHandler)
    }
    
    func getNoc(tag: String, csr: String, rootCAArn: String, completionHandler: @escaping ([String: Any]?) -> Void) {
        let encodedByteCSR = Data(csr.utf8).base64EncodedString()
        let encodedCSR = String(encodedByteCSR)
        
        var urlComponents = URLComponents()
        urlComponents.scheme = self.scheme
        urlComponents.host = self.host
        urlComponents.path = "\(self.path)/\(tag)"
        urlComponents.queryItems = [
            URLQueryItem(name: "csr", value: encodedCSR),
            URLQueryItem(name: "rootCAArn", value: rootCAArn)
        ]
        
        guard let url = urlComponents.url else {
            completionHandler(nil)
            CtLog.e("\(tag) url error")
            return
        }
        
        CtLog.i("v2 \(tag) url: \(url)")
        
        let configuration = URLSessionConfiguration.default
        configuration.requestCachePolicy = .reloadIgnoringLocalCacheData
        let session = URLSession(configuration: configuration)
        
        let task = session.dataTask(with: url) { data, response, error in
            if let error = error {
                CtLog.e("请求出错 Error: \(error)")
                completionHandler(nil)
                return
            }
            if let response = response as? HTTPURLResponse {
                CtLog.i("状态码为: \(response.statusCode)")
            }
            if let data = data {
                let str = String(data: data, encoding: .utf8)
                CtLog.i("请求成功 Response: \(str ?? "")")
                guard let jsonData = str?.data(using: .utf8) else {
                    completionHandler(nil)
                    return
                }
                
                do {
                    let jsonObject = try JSONSerialization.jsonObject(with: jsonData)
                    guard let result = jsonObject as? [String: Any] else {
                        completionHandler(nil)
                        CtLog.e("result = null")
                        return
                    }
                    guard let success = result["success"] as? Bool, success == true else {
                        completionHandler(nil)
                        CtLog.e("success = false")
                        return
                    }
                    guard let payload = result["payload"] as? [String: Any] else {
                        completionHandler(nil)
                        CtLog.e("payload = null")
                        return
                    }
                    guard let noc = payload["noc"] as? String, noc.isEmpty == false else {
                        completionHandler(nil)
                        CtLog.e("noc = null")
                        return
                    }
                    
                    let nodeId = payload["node_id"] as? String ?? ""
             
                    var newNoc = noc.replacingOccurrences(of: RKConstants.BEGIN_CERTIFICATE, with: "")
                    newNoc = newNoc.replacingOccurrences(of: RKConstants.END_CERTIFICATE, with: "")
                    newNoc = newNoc.replacingOccurrences(of: "\n", with: "")
                    
                    var dict: [String: Any] = [:]
                    dict["noc"] = newNoc
                    dict["nodeId"] = nodeId
                    dict["certArn"] = payload["certArn"] as? String
                    dict["certUrl"] = payload["certUrl"] as? String

                    completionHandler(dict)
                        
                } catch {
                    CtLog.e("Error parsing JSON: \(error.localizedDescription)")
                    completionHandler(nil)
                }
            } else {
                completionHandler(nil)
            }
        }
        task.resume()
    }
}
