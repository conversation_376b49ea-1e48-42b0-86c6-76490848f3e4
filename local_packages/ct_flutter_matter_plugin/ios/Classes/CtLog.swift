//
//  CtLog.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/22.
//

import Foundation

public class CtLog {
    static let DEBUG = true
    static func i(_ msg: String) {
        if DEBUG {
            debugPrint(msg)
        }
    }
    static func d(_ msg: String) {
        if DEBUG {
            debugPrint(msg)
        }
    }
    static func w(_ msg: String) {
        if DEBUG {
            debugPrint(msg)
        }
    }
    static func e(_ msg: String) {
        if DEBUG {
            debugPrint(msg)
        }
    }
}
