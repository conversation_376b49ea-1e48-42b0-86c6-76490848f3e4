//
//  CtAddDeviceProtocol.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/22.
//

import Foundation

typealias DeviceAttestationCallback = (UnsafeMutableRawPointer?, MTRDeviceAttestationDeviceInfo?, Int)-> Void

typealias ReadCommissioningInfoCallback = (Int, Int)-> Void

typealias CommissioningCompleteCallback = (String, Int, Dictionary<String, Any>)-> Void

protocol CtAddDeviceProtocol {
    func addWiFiDevice(qrcode:String, 
                       ssid:String,
                       pwd:String,
                       deviceAttestationCallback: @escaping DeviceAttestationCallback,
                       readCommissioningInfoCallback: @escaping ReadCommissioningInfoCallback,
                       commissioningCompleteCallback: @escaping CommissioningCompleteCallback)
    
    func addThreadDevice(qrcode:String,
                         agentId:String,
                         dataset:String,
                         deviceAttestationCallback: @escaping DeviceAttestationCallback,
                         readCommissioningInfoCallback: @escaping ReadCommissioningInfoCallback,
                         commissioningCompleteCallback: @escaping CommissioningCompleteCallback)
}
