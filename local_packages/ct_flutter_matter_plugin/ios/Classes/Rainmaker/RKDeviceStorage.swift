//
//  RKDeviceStorage.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON>e<PERSON><PERSON> on 2024/9/20.
//

import Foundation

class RKDeviceStorage {
    
    static var shared: RKDeviceStorage = RKDeviceStorage()
    
    func getNextAvailableDeviceID() -> UInt64 {
        let nextAvailableDeviceIdentifier: UInt64 = 1
        if let value = UserDefaults.standard.value(forKey: RKConstants.chipDeviceId) as? UInt64 {
            return value
        }
        UserDefaults.standard.set(nextAvailableDeviceIdentifier, forKey: RKConstants.chipDeviceId)
        return nextAvailableDeviceIdentifier
    }
    
    func setNextAvailableDeviceID(_ id: UInt64) {
        UserDefaults.standard.set(id, forKey: RKConstants.chipDeviceId)
    }
    
    func getCurrentDeviceId() -> UInt64 {
        return (getNextAvailableDeviceID()-1)
    }
    
}

