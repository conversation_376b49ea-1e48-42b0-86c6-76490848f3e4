//
//  RKUtil.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/20.
//

import Foundation

class RKUtil {
    
    static func catIdAdminDecimal() -> UInt64? {
        if let catIdAdmin = "\(RKConstants.prefixCATId)\(RKConstants.catIdAdmin)".hexToDecimal {
            return catIdAdmin
        }
        return nil
    }
    
    static func catIdOperateDecimal() -> UInt64? {
        if let catIdOperate = "\(RKConstants.prefixCATId)\(RKConstants.catIdOperate)".hexToDecimal {
            return catIdOperate
        }
        return nil
    }
    
    static func generateUserCSR(groupId: String, completion: @escaping (String?) -> Void) {
        let csrKeys = MTRCSRKeys(groupId: groupId)
        if let data = try? MTRCertificates.createCertificateSigningRequest(csrKeys) {
            let str = data.base64EncodedString()
            completion(str)
        } else {
            completion(nil)
        }
    }
    
    static func splitAndConcatenateCsr(input: String) -> String {
        var result = ""
        result.append("\r\n")
        
        var i = input.startIndex
        while i < input.endIndex {
            let end = input.index(i, offsetBy: 64, limitedBy: input.endIndex) ?? input.endIndex
            result.append(contentsOf: input[i..<end])
            result.append("\r\n")
            i = end
        }
        
        return result
    }
    
    static func convertPEMString(toDER pem: String) -> Data? {
        var result = pem.replacingOccurrences(of: RKConstants.BEGIN_CERTIFICATE, with: "")
        result = result.replacingOccurrences(of: RKConstants.END_CERTIFICATE, with: "")
        result = result.replacingOccurrences(of: "\n", with: "")
        if let data = Data(base64Encoded: result, options: []) {
            return data
        }
        return nil
    }
    
    static func generateRandomDiscriminator() -> NSNumber {
        // Define the range for valid discriminator values
        let minValue = 1
        let maxValue = 0xFFE  // 4094 in decimal

        // Generate a random number within the range
        let randomDiscriminator = Int.random(in: minValue...maxValue)
        
        return NSNumber(value: randomDiscriminator)
    }
}
