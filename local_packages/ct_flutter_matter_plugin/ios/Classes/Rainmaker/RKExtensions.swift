//
//  RKExtensions.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/20.
//

import Foundation

extension String {

    var hexadecimal: Data? {
        var data = Data(capacity: self.count / 2)
        let regex = try! NSRegularExpression(pattern: "[0-9a-f]{1,2}", options: .caseInsensitive)
        regex.enumerateMatches(in: self, range: NSRange(startIndex..., in: self)) { match, _, _ in
            let byteString = (self as NSString).substring(with: match!.range)
            let num = UInt8(byteString, radix: 16)!
            data.append(num)
        }
        guard data.count > 0 else { return nil }
        return data
    }

    var hexToDecimal: UInt64? {
        let scanner = Scanner(string: self)
        var value: UInt64 = 0
        if scanner.scanHexInt64(&value) {
            return value
        }
        return nil
    }
}

extension Data {
    
    var hexadecimalString: String {
        return map { String(format: "%02hhx", $0) }.joined()
    }
    
    var hexString: String {
        return self.base64EncodedString()
    }
    
    var bytes: [UInt8] {
        return [UInt8](self)
    }
}

extension Array where Element == UInt8 {
    
    public func toHexString() -> String {
        return `lazy`.reduce("") {
            var s = String($1, radix: 16)
            if s.count == 1 {
                s = "0" + s
            }
            return $0 + s
        }
    }
}
