//
//  RKChipClient.swift
//  Pods
//
//  Created by <PERSON>e<PERSON><PERSON> on 2024/9/20.
//

import Foundation

public class RKChipClient: MTROperationalCertificateIssuer {

    public var rootCa: String
    public var rootCACertArn: String
    public var fabricId: String
    public var ipk: String
    public var userNoc: String
    public var nodeId: String? = nil
    private var deviceController: MTRDeviceController? = nil
    private var operationalCertificateIssuer: RKOperationalCertificateIssuer? = nil
    
    init(rootCa: String, rootCACertArn: String, fabricId: String, ipk: String, userNoc: String, operationalCertificateIssuer: RKOperationalCertificateIssuer) {
        self.rootCa = rootCa
        self.rootCACertArn = rootCACertArn
        self.fabricId = fabricId
        self.ipk = ipk
        self.userNoc = userNoc
        self.operationalCertificateIssuer = operationalCertificateIssuer
    }
    
    func getDeviceController() -> MTRDeviceController? {
        if self.deviceController != nil {
            return self.deviceController;
        }
        
        let storage = RKMatterStorage.shared
        let factory = MTRDeviceControllerFactory.sharedInstance()
        let factoryParams = MTRDeviceControllerFactoryParams(storage: storage)
        factoryParams.productAttestationAuthorityCertificates = nil;
        
        do {
            try factory.start(factoryParams)
        } catch {
            CtLog.e("factory start failed.")
            return nil
        }
        
        guard let rootCaDerBytes = RKUtil.convertPEMString(toDER: self.rootCa) else {
            CtLog.e("rootCaDerBytes = null")
            return nil
        }
        
        guard let userNocDerBytes = RKUtil.convertPEMString(toDER: self.userNoc) else {
            CtLog.e("userNocDerBytes = null")
            return nil
        }
        
        let keys = MTRCSRKeys(groupId: self.fabricId)
        var finalIPK = keys.ipk
        
        CtLog.i("rootCa=\(self.rootCa)")
        CtLog.i("----------")
        CtLog.i("userNoc=\(self.userNoc)")
        CtLog.i("----------")
        CtLog.i("ipk=\(self.ipk)")
        CtLog.i("----------")
        
        if let ipkString = self.ipk.hexadecimal {
            finalIPK = ipkString
            CtLog.i("final-ipk=\(ipkString.hexadecimalString)")
        }
        
        let params = MTRDeviceControllerStartupParams(ipk: finalIPK,
                                                    operationalKeypair: keys,
                                                    operationalCertificate: userNocDerBytes,
                                                    intermediateCertificate: nil,
                                                    rootCertificate: rootCaDerBytes)
        params.vendorID = NSNumber(value: CtConstants.vendorId)
        params.operationalCertificateIssuer = self
        params.operationalCertificateIssuerQueue = DispatchQueue(label: "com.csa.matter.qrcodevc.callback")
        
        if let controller = try? factory.createController(onNewFabric: params) {
            self.deviceController = controller
            CtLog.i("Success on onNewFabric!")
        } else if let controller = try? factory.createController(onExistingFabric: params) {
            self.deviceController = controller
            CtLog.i("Success on onExistingFabric!")
        } else {
            CtLog.e("Failed on createController!")
        }
        
        return self.deviceController
    }
    
    public func issueOperationalCertificate(forRequest csrInfo: MTROperationalCSRInfo, attestationInfo: MTRDeviceAttestationInfo, controller: MTRDeviceController, completion: @escaping (MTROperationalCertificateChain?, (any Error)?) -> Void) {
        self.operationalCertificateIssuer?.issueOperationalCertificate(
            forRequest: csrInfo,
            attestationInfo: attestationInfo,
            chipClient: self,
            controller: controller,
            completion: completion
        )
    }
    
    public var shouldSkipAttestationCertificateValidation: Bool {
        return true
    }
    
    
}
