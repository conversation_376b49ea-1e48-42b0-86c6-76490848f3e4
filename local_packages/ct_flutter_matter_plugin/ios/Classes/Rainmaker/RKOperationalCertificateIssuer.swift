//
//  RKOperationalCertificateIssuer.swift
//  ct_flutter_matter_plugin
//
//  Created by JieCao on 2024/11/10.
//

import Foundation

public protocol RKOperationalCertificateIssuer {
    func issueOperationalCertificate(forRequest csrInfo: MTROperationalCSRInfo, 
                                     attestationInfo: MTRDeviceAttestationInfo,
                                     chipClient: RKChipClient,
                                     controller: MTRDeviceController,
                                     completion: @escaping (MTROperationalCertificateChain?, (any Error)?) -> Void
    )
    
}
