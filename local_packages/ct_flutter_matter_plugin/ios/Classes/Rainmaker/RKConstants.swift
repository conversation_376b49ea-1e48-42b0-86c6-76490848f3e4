//
//  RKConstants.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/20.
//

import Foundation

class RKConstants {
    static let BEGIN_CERTIFICATE_REQUEST = "-----BEGIN CERTIFICATE REQUEST-----"
    static let END_CERTIFICATE_REQUEST = "-----END CERTIFICATE REQUEST-----"
    
    static let BEGIN_CERTIFICATE = "-----BEGIN CERTIFICATE-----"
    static let END_CERTIFICATE = "-----END CERTIFICATE-----"
    
    static let prefixCATId = "FFFFFFFD"
    
    static let catIdAdmin = "AB120003";
    static let catIdOperate = "AB120003";
    
    static let emptyString = ""
    static let chipDeviceId: String = "ChipDeviceId"
    
    static let threadSaltData = "Y8T4twDbVKTkppiUS5lpH8xi9qX8HTjkE1yMSL7uwhA="
    static let threadPAKEVerifierData = "6lws/Pp8n7nm9l3B/vX0bYkRk/KgKM614w4wDeDrRMcELNYqAYSnZ3ewoimHKTbU/obSirNe+L4msaUU09ibgUfWDgvMR7GaLG3BWDqI9BpRgyjPfz0qTt193G7r3oYwbg=="
    
    static let groupIdKey = "group.com.habi.smarthome"
    static let borderAgentIdKey = "boder.agent.id"
    static let onboardingPayloadKey: String = "onboarding.payload"
    static let devicesName: String = "device.name"
    static let commissioningId: String = "commissioning.id"
    static let certDeclarationKey: String = "certificate.declaration.key"
    static let attestationInfoKey: String = "attestation.information.key"
}
