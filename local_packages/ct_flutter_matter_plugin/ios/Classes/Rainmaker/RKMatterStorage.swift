//
//  RKMatterStorage.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/20.
//

import Foundation

class RKMatterStorage: NSObject, MTRStorage {
    
    static var shared = RKMatterStorage()
    private var device: String = RKConstants.emptyString
    
    func storageData(forKey key: String) -> Data? {
        if let value = UserDefaults.standard.value(forKey: device+key) as? Data {
            return value
        }
        return nil
    }
    
    func setStorageData(_ value: Data, forKey key: String) -> Bool {
        UserDefaults.standard.set(value, forKey: device+key)
        return true
    }
    
    func removeStorageData(forKey key: String) -> Bool {
        UserDefaults.standard.removeObject(forKey: device+key)
        return true
    }
}
