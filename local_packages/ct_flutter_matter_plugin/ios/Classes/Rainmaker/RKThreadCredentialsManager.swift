//
//  RKThreadCredentialsManager.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/16.
//

import Foundation
import ThreadNetwork

class RKThreadCredentialsManager: NSObject {

    static let shared = RKThreadCredentialsManager()
    let client = THClient()
    
    func isThreadSupported(_ completion: @escaping (Bool) -> Void) {
        self.client.isPreferredNetworkAvailable { result in
            completion(result)
        }
    }
    
    func isPreferredCredentials(activeOpsDataset: Data, completion: @escaping (Bool) -> Void) {
        self.client.checkPreferredNetwork(forActiveOperationalDataset: activeOpsDataset) { result in
            completion(result)
        }
    }
    
    func fetchAllActiveThreadCredentials(_ completion: @escaping (Set<THCredentials>?) -> Void) {
        self.client.retrieveAllActiveCredentials { credentialsSet, _ in
            completion(credentialsSet)
        }
    }    
    
    func fetchAllThreadCredentials(_ completion: @escaping (Set<THCredentials>?) -> Void) {
        self.client.retrieveAllCredentials { credentialsSet, _ in
            completion(credentialsSet)
        }
    }
    
    func fetchPreferredThreadCredentials(_ completion: @escaping (THCredentials?) -> Void) {
        self.client.retrievePreferredCredentials { credentials, _ in
            completion(credentials)
        }
    }
    
    func fetchPreferredThreadOperationalDataset(_ completion: @escaping (Data?) -> Void) {
        self.fetchPreferredThreadCredentials { credentials in
            if let tod = credentials?.activeOperationalDataSet {
                completion(tod)
            } else {
                completion(nil)
            }
        }
    }
    
    func saveThreadOperationalCredentials(activeOpsDataset: Data, borderAgentId: Data, completion: @escaping(Bool) -> Void) {
        self.client.storeCredentials(forBorderAgent: borderAgentId, activeOperationalDataSet: activeOpsDataset) { error in
            guard let _ = error else {
                completion(true)
                return
            }
            completion(false)
        }
    }    
    
    func deleteCredentials(borderAgentId: Data, completion: @escaping(Bool) -> Void) {
        self.client.deleteCredentials(forBorderAgent: borderAgentId) { error in
            guard let _ = error else {
                completion(true)
                return
            }
            completion(false)
        }
    }

}
