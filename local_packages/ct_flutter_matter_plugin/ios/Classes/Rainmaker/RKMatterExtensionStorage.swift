//
//  RKMatterExtensionStorage.swift
//  ct_flutter_matter_plugin
//
//  Created by Jie<PERSON>ao on 2024/12/16.
//

import Foundation

class RKMatterExtensionStorage {
    static var shared = RKMatterExtensionStorage()
    
    func removeCertDeclaration() {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: RKConstants.certDeclarationKey)
    }
 
    func saveCertDeclaration(certDeclaration: Data) {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.set(certDeclaration, forKey: RKConstants.certDeclarationKey)
    }
    
    func getCertDeclaration() -> Data? {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: RKConstants.certDeclarationKey) {
            return value as? Data;
        }
        return nil
    }
    
    func removeAttestationInfo() {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: RKConstants.attestationInfoKey)
    }

    func saveAttestationInfo(attestationInfo: Data) {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.set(attestationInfo, forKey: RKConstants.attestationInfoKey)
    }

    func getAttestationInfo() -> Data? {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: RKConstants.attestationInfoKey) {
            return value as? Data;
        }
        return nil
    }
    
    func removeBorderAgentId() {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: RKConstants.borderAgentIdKey)
    }
    
    func saveBorderAgentId(borderAgentId: Data) {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.set(borderAgentId, forKey: RKConstants.borderAgentIdKey)
    }
    
    func getBorderAgentId() -> Data? {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: RKConstants.borderAgentIdKey) {
            return value as? Data;
        }
        return nil
    }
    
    func removeOnboardingPayload() {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: RKConstants.onboardingPayloadKey)
    }

    func saveOnboardingPayload(onboardingPayload: String) {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.set(onboardingPayload, forKey: RKConstants.onboardingPayloadKey)
    }
   
    func getOnboardingPayload() -> String? {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: RKConstants.onboardingPayloadKey) {
            return value as? String;
        }
        return nil
    }
    
    func removeDeviceName() {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: RKConstants.devicesName)
    }

    func saveDeviceName(deviceName: String) {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.set(deviceName, forKey: RKConstants.devicesName)
    }

    func getDeviceName() -> String? {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: RKConstants.devicesName) {
            return value as? String;
        }
        return nil
    }
    
    func removeCommissioningId() {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.removeObject(forKey: RKConstants.commissioningId)
    }

    func saveCommissioningId(commissioningId: String) {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        sharedUserDefaults?.set(commissioningId, forKey: RKConstants.commissioningId)
    }

    func getCommissioningId() -> String? {
        let sharedUserDefaults = UserDefaults(suiteName: RKConstants.groupIdKey)
        if let value = sharedUserDefaults?.object(forKey: RKConstants.commissioningId) {
            return value as? String;
        }
        return nil
    }
}
