//
//  CtScanDevicesDelegate.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/18.
//

import Flutter
import Foundation

@available(iOS 17.0, *)
public class CtScanDevicesDelegate: NSObject, MTRCommissionableBrowserDelegate {
    
    private var methodChannel:FlutterMethodChannel? = nil
    
    init(methodChannel:FlutterMethodChannel?) {
        self.methodChannel = methodChannel
    }
    
    public func controller(_ controller: MTRDeviceController, didFindCommissionableDevice device: MTRCommissionableBrowserResult) {
        CtLog.i("didFindCommissionableDevice() -> vendorID=\(device.vendorID)")
        CtLog.i("didFindCommissionableDevice() -> productID=\(device.productID)")
        CtLog.i("didFindCommissionableDevice() -> discriminator=\(device.discriminator)")
        CtLog.i("didFindCommissionableDevice() -> instanceName=\(device.instanceName)")
        CtLog.i("didFindCommissionableDevice() -> commissioningMode=\(device.commissioningMode)")

        var dict = [String : Any]()
        dict["vendorID"] = device.vendorID
        dict["productID"] = device.productID
        dict["discriminator"] = device.discriminator
        dict["instanceName"] = device.instanceName
        dict["commissioningMode"] = device.commissioningMode
        dict["didRemoveCommissionableDevice"] = false
    
        DispatchQueue.main.async {
            self.methodChannel?.invokeMethod("onScanDevices", arguments: dict)
        }
    }
    
    public func controller(_ controller: MTRDeviceController, didRemoveCommissionableDevice device: MTRCommissionableBrowserResult) {
        CtLog.i("didRemoveCommissionableDevice() -> vendorID=\(device.vendorID)")
        CtLog.i("didRemoveCommissionableDevice() -> productID=\(device.productID)")
        CtLog.i("didRemoveCommissionableDevice() -> discriminator=\(device.discriminator)")
        CtLog.i("didRemoveCommissionableDevice() -> instanceName=\(device.instanceName)")
        CtLog.i("didRemoveCommissionableDevice() -> commissioningMode=\(device.commissioningMode)")
        
        var dict = [String : Any]()
        dict["vendorID"] = device.vendorID
        dict["productID"] = device.productID
        dict["discriminator"] = device.discriminator
        dict["instanceName"] = device.instanceName
        dict["commissioningMode"] = device.commissioningMode
        dict["didRemoveCommissionableDevice"] = true

        DispatchQueue.main.async {
            self.methodChannel?.invokeMethod("onScanDevices", arguments: dict)
        }
    }
    
}
