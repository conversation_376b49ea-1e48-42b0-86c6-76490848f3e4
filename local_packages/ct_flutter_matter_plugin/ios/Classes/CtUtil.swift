//
//  CtUtil.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/6/13.
//

import Foundation

class CtUtil {
    
    static func getClusterNameForID(_ clusterID: Int) -> String {
        var clusterName: String
        switch(clusterID) {
           case 0x0000001D:
               clusterName = "Descriptor"
               break
           case 0x0000001F:
               clusterName = "AccessControl"
               break
           case 0x00000028:
               clusterName = "BasicInformation"
               break
           case 0x00000030:
               clusterName = "GeneralCommissioning"
               break
           case 0x00000031:
               clusterName = "NetworkCommissioning"
               break
           case 0x00000033:
               clusterName = "GeneralDiagnostics"
               break
           case 0x0000003C:
               clusterName = "AdministratorCommissioning"
               break
           case 0x0000003E:
               clusterName = "OperationalCredentials"
               break
           case 0x0000003F:
               clusterName = "GroupKeyManagement"
               break
           case 0x00000036:
               clusterName = "WiFiNetworkDiagnostics"
               break
           case 0x0000002A:
               clusterName = "OtaSoftwareUpdateRequestor"
               break
           case 0x00000201:
               clusterName = "Thermostat"
               break
           default:
               clusterName = "Unknown"
               break
           }
           return clusterName
       }

       static func getAttributeNameForID(_ clusterID: Int, _ attributeID: Int) -> String {
           var attributeName: String
           switch(clusterID) {
           case 0x00000028:
               switch(attributeID){
               case 0x00000002:
                   attributeName = "VendorID"
                   break
               case 0x00000004:
                   attributeName = "ProductID"
                   break
               default:
                   attributeName = "Unknown"
                   break
               }
           case 0x00000201: //thermostat
               switch(attributeID){
               case 0x00000000:
                   attributeName = "LocalTemperature"
                   break
               case 0x00000001:
                   attributeName = "OutdoorTemperature"
                   break
               case 0x00000002:
                   attributeName = "Occupancy"
                   break
               case 0x00000003:
                   attributeName = "AbsMinHeatSetpointLimit"
                   break
               case 0x00000004:
                   attributeName = "AbsMaxHeatSetpointLimit"
                   break
               case 0x00000005:
                   attributeName = "AbsMinCoolSetpointLimit"
                   break
               case 0x00000006:
                   attributeName = "AbsMaxCoolSetpointLimit"
                   break
               case 0x00000007:
                   attributeName = "PICoolingDemand"
                   break
               case 0x00000008:
                   attributeName = "PIHeatingDemand"
                   break
               case 0x00000009:
                   attributeName = "HVACSystemTypeConfiguration"
                   break
               case 0x000000010:
                   attributeName = "LocalTemperatureCalibration"
                   break
               case 0x000000011:
                   attributeName = "OccupiedCoolingSetpoint"
                   break
               case 0x000000012:
                   attributeName = "OccupiedHeatingSetpoint"
                   break
               case 0x000000013:
                   attributeName = "UnoccupiedCoolingSetpoint"
                   break
               case 0x000000014:
                   attributeName = "UnoccupiedHeatingSetpoint"
                   break
               case 0x000000015:
                   attributeName = "MinHeatSetpointLimit"
                   break
               case 0x000000016:
                   attributeName = "MaxHeatSetpointLimit"
                   break
               case 0x000000017:
                   attributeName = "MinCoolSetpointLimit"
                   break
               case 0x000000018:
                   attributeName = "MaxCoolSetpointLimit"
                   break
               case 0x000000019:
                   attributeName = "MinSetpointDeadBand"
                   break
               case 0x00000001A:
                   attributeName = "RemoteSensing"
                   break
               case 0x00000001B:
                   attributeName = "ControlSequenceOfOperation"
                   break
               case 0x0000001C:
                   attributeName = "SystemMode"
                   break
               case 0x0000001E:
                   attributeName = "ThermostatRunningMode"
                   break
               case 0x00000020:
                   attributeName = "StartOfWeek"
                   break
               case 0x00000021:
                   attributeName = "NumberOfWeeklyTransitions"
                   break
               case 0x00000022:
                   attributeName = "NumberOfDailyTransitions"
                   break
               case 0x00000023:
                   attributeName = "TemperatureSetpointHold"
                   break
               case 0x00000024:
                   attributeName = "TemperatureSetpointHoldDuration"
                   break
               case 0x00000025:
                   attributeName = "ThermostatProgrammingOperationMode"
                   break
               case 0x00000029:
                   attributeName = "ThermostatRunningState"
                   break
               case 0x00000030:
                   attributeName = "SetpointChangeSource"
                   break
               case 0x00000031:
                   attributeName = "SetpointChangeAmount"
                   break
               case 0x00000032:
                   attributeName = "SetpointChangeSourceTimestamp"
                   break
               case 0x00000034:
                   attributeName = "OccupiedSetback"
                   break
               case 0x00000035:
                   attributeName = "OccupiedSetbackMin"
                   break
               case 0x00000036:
                   attributeName = "OccupiedSetbackMax"
                   break
               case 0x00000037:
                   attributeName = "UnoccupiedSetback"
                   break
               case 0x00000038:
                   attributeName = "UnoccupiedSetbackMin"
                   break
               case 0x00000039:
                   attributeName = "UnoccupiedSetbackMax"
                   break
               case 0x0000003A:
                   attributeName = "EmergencyHeatDelta"
                   break
               case 0x00000040:
                   attributeName = "ACType"
                   break
               case 0x00000041:
                   attributeName = "ACCapacity"
                   break
               case 0x00000042:
                   attributeName = "ACRefrigerantType"
                   break
               case 0x00000043:
                   attributeName = "ACCompressorType"
                   break
               case 0x00000044:
                   attributeName = "ACErrorCode"
                   break
               case 0x00000045:
                   attributeName = "ACLouverPosition"
                   break
               case 0x00000046:
                   attributeName = "ACCoilTemperature"
                   break
               case 0x00000047:
                   attributeName = "ACCapacityformat"
                   break
               case 0x00000048:
                   attributeName = "PresetTypes"
                   break
               case 0x0000004A:
                   attributeName = "NumberOfPresets"
                   break
               case 0x0000004B:
                   attributeName = "NumberOfSchedules"
                   break
               case 0x0000004C:
                   attributeName = "NumberOfScheduleTransitions"
                   break
               case 0x0000004D:
                   attributeName = "NumberOfScheduleTransitionPerDay"
                   break
               case 0x0000004E:
                   attributeName = "ActivePresetHandle"
                   break
               case 0x0000004F:
                   attributeName = "ActiveScheduleHandle"
                   break
               case 0x00000050:
                   attributeName = "Presets"
                   break
               case 0x00000051:
                   attributeName = "Schedules"
                   break
               case 0x00000052:
                   attributeName = "PresetsSchedulesEditable"
                   break
               case 0x00000053:
                   attributeName = "TemperatureSetpointHoldPolicy"
                   break
               case 0x00000054:
                   attributeName = "SetpointHoldExpiryTimestamp"
                   break
               case 0x00000055:
                   attributeName = "QueuedPreset"
                   break
               case 0x0000FFF8:
                   attributeName = "GeneratedCommandList"
                   break
               case 0x0000FFF9:
                   attributeName = "AcceptedCommandList"
                   break
               case 0x0000FFFA:
                   attributeName = "EventList"
                   break
               case 0x0000FFFB:
                   attributeName = "AttributeList"
                   break
               case 0x0000FFFC:
                   attributeName = "FeatureMap"
                   break
               case 0x0000FFFD:
                   attributeName = "ClusterRevision"
                   break
               default:
                   attributeName = "Unknown"
                   break
               }
               break
           default:
               attributeName = "Unknown"
               break
           }
           return attributeName
       }

    static func getNetworkName(discriminator: NSNumber) -> String {
        let peripheralDiscriminator = String(format: "%04u", discriminator.uint16Value)
        let peripheralFullName = "CHIP-\(peripheralDiscriminator)"
        return peripheralFullName
    }
    
}
