//
//  CtChipClient.swift
//  ct_flutter_matter_plugin
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/5/22.
//

import Foundation

public class CtChipClient {
    static let sharedInstance = CtChipClient()
    private var deviceController: MTRDeviceController?
    
    private init(){
        
    }
    
    func getMTRDeviceController()-> MTRDeviceController?{
        if (self.deviceController == nil) {
            CtLog.i("Create MTRDeviceController")
            let storage = CHIPToolPersistentStorageDelegate()
            let factory = MTRDeviceControllerFactory.sharedInstance()
            let factoryParams = MTRDeviceControllerFactoryParams(storage: storage)
            
            do {
                try factory.start(factoryParams)
            } catch {
                CtLog.e("factory start failed.")
                return nil
            }
            
            let keys = FabricKeys()
            let params = MTRDeviceControllerStartupParams(ipk: keys.ipk, fabricID: 1, nocSigner: keys)
            let vendorId: NSNumber = 0xFFF1
            params.vendorID = vendorId

            if let controller = try? factory.createController(onNewFabric: params) {
                self.deviceController = controller
                CtLog.i("Success on onNewFabric!")
            } else if let controller = try? factory.createController(onExistingFabric: params) {
                self.deviceController = controller
                CtLog.i("Success on onExistingFabric!")
            } else {
                CtLog.e("Failed on createController!")
            }
        }
        return self.deviceController
    }
    
    
    func restartMTRController()-> MTRDeviceController? {
        if let tempController = self.deviceController {
            tempController.shutdown()
        }
   
        return self.getMTRDeviceController()
    }
    
}
