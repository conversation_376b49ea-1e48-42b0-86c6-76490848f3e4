/**
 *    Copyright (c) 2022 Project CHIP Authors
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *        http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */

#import <Foundation/Foundation.h>
#import <Matter/Matter.h>

NS_ASSUME_NONNULL_BEGIN

@interface MTRCSRKeys : NSObject <MTRKeypair>

@property (readonly, nonatomic, strong) NSData * ipk;

@property (readonly, nonatomic) NSData * publicKeyData;

- (instancetype)initWithGroupId:(NSString *) grpId;
- (SecKeyRef) getPublicKeyForGroupId:(NSString *) grpId;
- (Sec<PERSON>eyRef) getPrivateKeyForGroupId:(NSString *) grpId;
- (NSData * _Nullable) getPublicKeyDataForGroupId:(NSString *) grpId;
- (NSData * _Nullable) getPrivateKeyDataForGroupId:(NSString *) grpId;

@end

NS_ASSUME_NONNULL_END
