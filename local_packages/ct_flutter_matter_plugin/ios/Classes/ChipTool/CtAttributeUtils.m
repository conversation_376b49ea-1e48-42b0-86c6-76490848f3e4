//
//  CtAttributeUtils.m
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/29.
//

#import "CtAttributeUtils.h"

@implementation CtAttributeUtils

-(NSString *)getClusterName:(uint32_t)clusterId
{
#if __has_include(<MTRClusterNames.h>)
    NSLog(@"存在，执行相关逻辑");
    MTRClusterIDType clusterIDType = (MTRClusterIDType)clusterId;
    NSString * clusterName = MTRClusterNameForID(clusterIDType);
    return clusterName;
#else
    NSLog(@"不存在，跳过相关逻辑");
    return @"unknown";
#endif
}

-(NSString *)getAttributeName:(uint32_t)clusterId attributeId:(uint32_t)attributeId
{
#if __has_include(<MTRClusterNames.h>)
    NSLog(@"存在，执行相关逻辑");
    MTRClusterIDType clusterIDType = (MTRClusterIDType)clusterId;
    MTRAttributeIDType attributeIDType = (MTRAttributeIDType)attributeId;
    NSString * attributeName = MTRAttributeNameForID(clusterIDType, attributeIDType);
    return attributeName;
#else
    NSLog(@"不存在，跳过相关逻辑");
    return @"unknown";
#endif
}

@end
