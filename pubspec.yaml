name: habi_app
description: "This project is the habi app from computime."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 0.35.1+73

environment:
  sdk: '>=3.4.1 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  dio: ^5.4.3+1
  get:
  get_storage: ^2.1.1
  aws_iot_api: ^2.0.0
  aws_iot_data_api: ^2.0.0
  aws_signature_v4: ^0.5.0
  aws_dynamodb_api: ^2.0.0
  amplify_flutter: ^1.6.0
  amplify_auth_cognito: ^1.6.0
  amazon_cognito_identity_dart_2: ^3.6.0
  mqtt_client: ^10.2.0
  uuid: ^4.3.3
  flutter_svg: ^2.0.9
  device_info_plus: ^10.1.0
  package_info_plus: ^6.0.0
  path_provider: ^2.1.3
  permission_handler: ^11.3.1
  logger: ^2.1.0
  intl: ^0.19.0
  timezone: ^0.9.2
  archive: ^3.4.10
  share_plus: ^7.2.2
  path: ^1.8.3
  pinput: ^5.0.0
  lottie: ^3.1.2
  qr_code_scanner:
    path: ./local_packages/qr_code_scanner
  ct_flutter_ble_plugin:
    path: ./local_packages/ct_flutter_ble_plugin
  ct_flutter_matter_plugin:
    path: ./local_packages/ct_flutter_matter_plugin
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_screenutil: ^5.9.3
  sleek_circular_slider: ^2.0.1
  flutter_blue_plus: 1.35.5
  smooth_page_indicator: ^1.2.0+3
  encrypt: ^5.0.3
  hex: ^0.2.0
  sqflite: ^2.3.2
  pretty_qr_code: ^3.3.0
  easy_refresh: ^3.4.0
  network_info_plus: ^6.1.0
  auto_size_text: ^3.0.0
  url_launcher: ^6.3.1
  amplify_analytics_pinpoint: ^1.8.0
  amplify_push_notifications_pinpoint: ^1.8.0
  app_settings: 5.1.1
  flutter_app_badge_control: ^0.0.2
  sentry_flutter: ^8.14.2
  collection: ^1.18.0
  geolocator: ^12.0.0
  step_progress_indicator: ^1.0.2
  gaimon: ^1.4.0
  flutter_local_notifications: ^18.0.1
  connectivity_plus: ^6.1.3
  google_api_availability: ^5.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter


  change_app_package_name: ^1.2.0
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  hive_generator: 2.0.1
  build_runner: 2.4.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/animations/
    - assets/translations/
    - assets/config/
    - assets/images/
    - assets/images/flags/
    - assets/demo/
    - assets/demo/thing_groups/
    - assets/demo/thing_shadows/
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins/Poppins-Bold.ttf
          weight: 700
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
