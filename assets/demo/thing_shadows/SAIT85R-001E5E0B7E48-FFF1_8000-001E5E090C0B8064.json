{"state": {"desired": {"version": 1, "001E5E090C0B8064": {"properties": {"ep1:sAWSReg:SetRegistration": 11}}, "11": {"properties": {"ep1:sMDO:sDeviceName": "My Thermostat", "ep1:sNetComm:sReadRSSI": 1, "ep1:sTimeH:SetScheduleType": 2, "ep1:sTimeH:SetSchedule1": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule2": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule3": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule4": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule5": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule6": "01FFFFFF08002100FFFFFFFFFFFF16302800FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule7": "01FFFFFF08002100FFFFFFFFFFFF16302800FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:sScheduleEn": 1}}}, "reported": {"connected": "true", "version": 1, "cloud_conn": 1, "node_conn": 1, "11": {"model": "FFF1_8000", "properties": {"ep1:sOTA:OTAStatus_d": 0, "ep1:sTimeH:ScheduleEn": 1, "ep1:sAWSReg:Registration": 11, "ep1:sMBasicS:VendorID": 65521, "ep1:sMBasicS:ProductID": 32768, "ep1:sMDO:Euid": "001E5E090C0B8064", "ep1:sMDO:LeaveNetw": 0, "ep1:sMDO:NodeID": "DEDEDEDE69692570", "ep1:sMDOInfo:OnlineStatus": 1, "ep1:sPowerS:BatChargeL": 0, "ep1:sMBasicS:DMRevision": 17, "ep1:sTherS:HeatingSp": 2000, "ep1:sTherS:HeatingSp_a": 2000, "ep1:sTherS:MinHeatSp": 500, "ep1:sTherS:MaxHeatSp": 3500, "ep1:sTherS:SystemMode": 4, "ep1:sTherS:SystemMode_a": 4, "ep1:sTherS:RunningState": 0, "ep1:sMIdentiS:IdentifyTime": 0, "ep1:sTherS:LocalTemp": 2650, "ep1:sTherS:TempCalibr": 0, "ep1:sTherUIS:TempDispM": 0, "ep1:sTherUIS:LockKey": 0, "ep1:sMBasicS:SWVersionStr": "FFF1_8000_010_CT24122401", "ep1:sMBasicS:SWVersoin": 10, "ep1:sMBasicS:VendorName": "Salus", "ep1:sMBasicS:ProductName": "Thermostat", "ep1:sMBasicS:Location": "XX", "ep1:sMBasicS:HWVersion": 3, "ep1:sMBasicS:HWVersionStr": "3", "ep1:sMBasicS:SpecVersion": 16973824, "ep1:sMBasicS:MaxPTHPerIvk": 1, "ep1:sNetComm:RSSI": -25, "ep1:sMDO:DeviceName": "My Thermostat", "ep1:sTimeH:ScheduleType": 2, "ep1:sTimeH:Schedule1": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:Schedule2": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:Schedule3": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:Schedule4": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:Schedule5": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:Schedule6": "01FFFFFF08002100FFFFFFFFFFFF16302800FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:Schedule7": "01FFFFFF08002100FFFFFFFFFFFF16302800FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"}}, "createdAt": "2025-06-30T03:34:46.144Z", "cloud_metadata_config_checking_timestamp": 1751252920}, "delta": {"001E5E090C0B8064": {"properties": {"ep1:sAWSReg:SetRegistration": 11}}, "11": {"properties": {"ep1:sMDO:sDeviceName": "My Thermostat", "ep1:sNetComm:sReadRSSI": 1, "ep1:sTimeH:SetScheduleType": 2, "ep1:sTimeH:SetSchedule1": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule2": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule3": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule4": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule5": "01FFFFFF00303150FFFFFFFFFFFF08302100FFFFFFFFFFFF10302600FFFFFFFFFFFF14001650FFFFFFFFFFFF18002450FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule6": "01FFFFFF08002100FFFFFFFFFFFF16302800FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:SetSchedule7": "01FFFFFF08002100FFFFFFFFFFFF16302800FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", "ep1:sTimeH:sScheduleEn": 1}}}}, "metadata": {"desired": {"version": {"timestamp": 1751252451}, "001E5E090C0B8064": {"properties": {"ep1:sAWSReg:SetRegistration": {"timestamp": 1751252451}}}, "11": {"properties": {"ep1:sMDO:sDeviceName": {"timestamp": 1751252508}, "ep1:sNetComm:sReadRSSI": {"timestamp": 1751254479}, "ep1:sTimeH:SetScheduleType": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule1": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule2": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule3": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule4": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule5": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule6": {"timestamp": 1751254399}, "ep1:sTimeH:SetSchedule7": {"timestamp": 1751254399}, "ep1:sTimeH:sScheduleEn": {"timestamp": 1751254483}}}}, "reported": {"connected": {"timestamp": 1751254486}, "version": {"timestamp": 1751254486}, "cloud_conn": {"timestamp": 1751254486}, "node_conn": {"timestamp": 1751254486}, "11": {"model": {"timestamp": 1751254486}, "properties": {"ep1:sOTA:OTAStatus_d": {"timestamp": 1751252854}, "ep1:sTimeH:ScheduleEn": {"timestamp": 1751254485}, "ep1:sAWSReg:Registration": {"timestamp": 1751252854}, "ep1:sMBasicS:VendorID": {"timestamp": 1751252854}, "ep1:sMBasicS:ProductID": {"timestamp": 1751252854}, "ep1:sMDO:Euid": {"timestamp": 1751252854}, "ep1:sMDO:LeaveNetw": {"timestamp": 1751252854}, "ep1:sMDO:NodeID": {"timestamp": 1751252854}, "ep1:sMDOInfo:OnlineStatus": {"timestamp": 1751252910}, "ep1:sPowerS:BatChargeL": {"timestamp": 1751252919}, "ep1:sMBasicS:DMRevision": {"timestamp": 1751252854}, "ep1:sTherS:HeatingSp": {"timestamp": 1751252920}, "ep1:sTherS:HeatingSp_a": {"timestamp": 1751252920}, "ep1:sTherS:MinHeatSp": {"timestamp": 1751252920}, "ep1:sTherS:MaxHeatSp": {"timestamp": 1751252919}, "ep1:sTherS:SystemMode": {"timestamp": 1751252919}, "ep1:sTherS:SystemMode_a": {"timestamp": 1751252919}, "ep1:sTherS:RunningState": {"timestamp": 1751252920}, "ep1:sMIdentiS:IdentifyTime": {"timestamp": 1751252920}, "ep1:sTherS:LocalTemp": {"timestamp": 1751252920}, "ep1:sTherS:TempCalibr": {"timestamp": 1751252920}, "ep1:sTherUIS:TempDispM": {"timestamp": 1751252920}, "ep1:sTherUIS:LockKey": {"timestamp": 1751252920}, "ep1:sMBasicS:SWVersionStr": {"timestamp": 1751252854}, "ep1:sMBasicS:SWVersoin": {"timestamp": 1751252854}, "ep1:sMBasicS:VendorName": {"timestamp": 1751252854}, "ep1:sMBasicS:ProductName": {"timestamp": 1751252854}, "ep1:sMBasicS:Location": {"timestamp": 1751252854}, "ep1:sMBasicS:HWVersion": {"timestamp": 1751252854}, "ep1:sMBasicS:HWVersionStr": {"timestamp": 1751252854}, "ep1:sMBasicS:SpecVersion": {"timestamp": 1751252854}, "ep1:sMBasicS:MaxPTHPerIvk": {"timestamp": 1751252854}, "ep1:sNetComm:RSSI": {"timestamp": 1751254486}, "ep1:sMDO:DeviceName": {"timestamp": 1751252854}, "ep1:sTimeH:ScheduleType": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule1": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule2": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule3": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule4": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule5": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule6": {"timestamp": 1751254401}, "ep1:sTimeH:Schedule7": {"timestamp": 1751254401}}}, "createdAt": {"timestamp": 1751254486}, "cloud_metadata_config_checking_timestamp": {"timestamp": 1751252920}}}, "version": 6096, "timestamp": 1751254833}