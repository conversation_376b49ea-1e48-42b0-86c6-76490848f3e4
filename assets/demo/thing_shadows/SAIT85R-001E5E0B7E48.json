{"state": {"desired": {"SetOnlineRefresh": 1, "version": 1, "000000000001": {"properties": {"ep0:sAWSReg:SetRegistration": 1, "ep_0:sDebug:SetDebugExecuteCommand_d": "/mnt/lib/curl -k https://d2naogpn412bc8.cloudfront.net/init/SAIT85R-001E5E0B7E48?otp=5WZ4qNnTnjKbQu9YMwn4nTsQud6mp8lse4uO54CSEtakbVUAgiBd2IvutlJtGPNx", "ep0:sGateway:SetDeviceName": "My Receiver", "ep0:sGateway:SetTimeZone": "Europe/London"}}}, "reported": {"cloud_metadata_factoryreset_history": "Register", "connected": "true", "cloud_metadata_online_check_session": 1751252067, "cloud_metad12ata_online_check": 1751252056, "version": 1, "cloud_conn": 1, "node_conn": 1, "000000000001": {"model": "SAIT85R", "properties": {"ep0:sGateway:GatewaySoftwareVersion": "0102202024092620250122", "ep0:sGateway:GatewayHardwareVersion": "1", "ep0:sGateway:NetworkWiFiMAC": "00:1E:5E:0B:7E:48", "ep0:sGateway:NetworkWiFiIP": "*************", "ep0:sGateway:NetworkSSID": "TP-LINK_35FC", "ep0:sGateway:TimeZone": "Europe/London", "ep0:sGateway:NetworkLANSubnet": "*************", "ep0:sGateway:NetworkLANRouterAddr": "***********", "ep0:sGateway:NetworkPriDNS": "***********", "ep0:sGateway:ModelIdentifier": "SAIT85R", "ep0:sGateway:WiFiRSSI": -46, "ep0:sGateway:DeviceName": "My Receiver", "ep0:sOTA:OTAStatus_d": 0, "ep0:sDebug:FactoryDebugMsg_d": "aws, 2025-06-30 02:44:19", "ep0:sDebug:ThreadRestartMsg_d": "factory_reset restart,2025-06-30 02:44:19", "ep0:sDebug:GatewayDebugMsg": "2025-06-30 03:07:32 get network time", "ep0:sAWSIoT:CertARN": "128420c51ce342dbe4f1f9f67dd897931e34801283d70c07dfca409fdb70c493", "ep0:sAWSIoT:CloudStatus": 1, "ep0:sAWSIoT:CloudID": "211125451970", "ep0:sAWSReg:Registration": 1, "ep0:sDebug:MatterDebugMsg": "2025-06-30 03:04:49 NodeID: dededede71961380, UniqueID: 4831B7090CC3918A, Message: add or update device to db"}}, "createdAt": "2025-06-30T03:21:35.047Z", "cloud_metadata_config_checking_timestamp": 1751252050}, "delta": {"SetOnlineRefresh": 1, "000000000001": {"properties": {"ep0:sAWSReg:SetRegistration": 1, "ep_0:sDebug:SetDebugExecuteCommand_d": "/mnt/lib/curl -k https://d2naogpn412bc8.cloudfront.net/init/SAIT85R-001E5E0B7E48?otp=5WZ4qNnTnjKbQu9YMwn4nTsQud6mp8lse4uO54CSEtakbVUAgiBd2IvutlJtGPNx", "ep0:sGateway:SetDeviceName": "My Receiver", "ep0:sGateway:SetTimeZone": "Europe/London"}}}}, "metadata": {"desired": {"SetOnlineRefresh": {"timestamp": 1751251604}, "version": {"timestamp": 1751252045}, "000000000001": {"properties": {"ep0:sAWSReg:SetRegistration": {"timestamp": 1751252045}, "ep_0:sDebug:SetDebugExecuteCommand_d": {"timestamp": 1751252050}, "ep0:sGateway:SetDeviceName": {"timestamp": 1751252188}, "ep0:sGateway:SetTimeZone": {"timestamp": 1751252188}}}}, "reported": {"cloud_metadata_factoryreset_history": {"timestamp": 1751252045}, "connected": {"timestamp": 1751253695}, "cloud_metadata_online_check_session": {"timestamp": 1751252067}, "cloud_metad12ata_online_check": {"timestamp": 1751252066}, "version": {"timestamp": 1751253695}, "cloud_conn": {"timestamp": 1751253695}, "node_conn": {"timestamp": 1751253695}, "000000000001": {"model": {"timestamp": 1751253695}, "properties": {"ep0:sGateway:GatewaySoftwareVersion": {"timestamp": 1751252901}, "ep0:sGateway:GatewayHardwareVersion": {"timestamp": 1751252853}, "ep0:sGateway:NetworkWiFiMAC": {"timestamp": 1751252853}, "ep0:sGateway:NetworkWiFiIP": {"timestamp": 1751253695}, "ep0:sGateway:NetworkSSID": {"timestamp": 1751253695}, "ep0:sGateway:TimeZone": {"timestamp": 1751252853}, "ep0:sGateway:NetworkLANSubnet": {"timestamp": 1751253695}, "ep0:sGateway:NetworkLANRouterAddr": {"timestamp": 1751253695}, "ep0:sGateway:NetworkPriDNS": {"timestamp": 1751253695}, "ep0:sGateway:ModelIdentifier": {"timestamp": 1751252853}, "ep0:sGateway:WiFiRSSI": {"timestamp": 1751253695}, "ep0:sGateway:DeviceName": {"timestamp": 1751252853}, "ep0:sOTA:OTAStatus_d": {"timestamp": 1751252853}, "ep0:sDebug:FactoryDebugMsg_d": {"timestamp": 1751252853}, "ep0:sDebug:ThreadRestartMsg_d": {"timestamp": 1751252853}, "ep0:sDebug:GatewayDebugMsg": {"timestamp": 1751252853}, "ep0:sAWSIoT:CertARN": {"timestamp": 1751252853}, "ep0:sAWSIoT:CloudStatus": {"timestamp": 1751252853}, "ep0:sAWSIoT:CloudID": {"timestamp": 1751252853}, "ep0:sAWSReg:Registration": {"timestamp": 1751252853}, "ep0:sDebug:MatterDebugMsg": {"timestamp": 1751252689}}}, "createdAt": {"timestamp": 1751253695}, "cloud_metadata_config_checking_timestamp": {"timestamp": 1751252050}}}, "version": 4589, "timestamp": 1751254833}