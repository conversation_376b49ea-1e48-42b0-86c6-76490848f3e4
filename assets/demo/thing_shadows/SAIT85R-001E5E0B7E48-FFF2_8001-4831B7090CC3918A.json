{"state": {"desired": {"version": 1, "4831B7090CC3918A": {"properties": {"ep1:sAWSReg:SetRegistration": 11}}, "11": {"properties": {"ep1:sMDO:sDeviceName": "My TRV", "ep1:sTherS:sHeatingSp": 2250, "ep1:sTherS:sSystemMode": 4}}}, "reported": {"connected": "true", "version": 1, "cloud_conn": 1, "node_conn": 1, "11": {"model": "FFF2_8001", "properties": {"ep1:sOTA:OTAStatus_d": 0, "ep1:sTimeH:ScheduleEn": 1, "ep1:sAWSReg:Registration": 11, "ep1:sMBasicS:VendorID": 65522, "ep1:sMBasicS:ProductID": 32769, "ep1:sMDO:Euid": "4831B7090CC3918A", "ep1:sMDO:LeaveNetw": 0, "ep1:sMDO:NodeID": "DEDEDEDE71961380", "ep1:sMDOInfo:OnlineStatus": 1, "ep1:sMBasicS:DMRevision": 17, "ep1:sPowerS:BatChargeL": 0, "ep1:sTherS:SystemMode": 4, "ep1:sTherS:SystemMode_a": 4, "ep1:sTherS:RunningMode": 4, "ep1:sTherS:RunningState": 0, "ep1:sTherS:CtlSequeOpr": 2, "ep1:sTherS:HeatingSp": 500, "ep1:sTherS:HeatingSp_a": 500, "ep1:sTherS:MinHeatSp": 500, "ep1:sTherS:MaxHeatSp": 3500, "ep1:sTherS:TempCalibr": 0, "ep1:sTherS:PIHeatingD": 0, "ep1:sMIdentiS:IdentifyTime": 0, "ep1:sTherS:LocalTemp": 2614, "ep1:sTherUIS:TempDispM": 0, "ep1:sTherUIS:LockKey": 0, "ep1:sMBasicS:SWVersoin": 7, "ep1:sMBasicS:SWVersionStr": "V1.0", "ep1:sMBasicS:ProductLabel": "1527_0002-007007-CT25032401", "ep1:sMBasicS:VendorName": "espressif", "ep1:sMBasicS:ProductName": "trv", "ep1:sMBasicS:Location": "XX", "ep1:sMBasicS:HWVersion": 1, "ep1:sMBasicS:HWVersionStr": "1", "ep1:sMDO:DeviceName": "My TRV", "ep1:sMBasicS:SpecVersion": 16973824, "ep1:sMBasicS:MaxPTHPerIvk": 1, "ep1:sNetComm:RSSI": -33, "ep1:sTimeH:ScheduleType": 0}}, "createdAt": "2025-06-30T03:39:44.947Z"}, "delta": {"4831B7090CC3918A": {"properties": {"ep1:sAWSReg:SetRegistration": 11}}, "11": {"properties": {"ep1:sMDO:sDeviceName": "My TRV", "ep1:sTherS:sHeatingSp": 2250, "ep1:sTherS:sSystemMode": 4}}}}, "metadata": {"desired": {"version": {"timestamp": 1751252693}, "4831B7090CC3918A": {"properties": {"ep1:sAWSReg:SetRegistration": {"timestamp": 1751252693}}}, "11": {"properties": {"ep1:sMDO:sDeviceName": {"timestamp": 1751252706}, "ep1:sTherS:sHeatingSp": {"timestamp": 1751254503}, "ep1:sTherS:sSystemMode": {"timestamp": 1751254782}}}}, "reported": {"connected": {"timestamp": 1751254785}, "version": {"timestamp": 1751254785}, "cloud_conn": {"timestamp": 1751254785}, "node_conn": {"timestamp": 1751254785}, "11": {"model": {"timestamp": 1751254785}, "properties": {"ep1:sOTA:OTAStatus_d": {"timestamp": 1751252854}, "ep1:sTimeH:ScheduleEn": {"timestamp": 1751252854}, "ep1:sAWSReg:Registration": {"timestamp": 1751252854}, "ep1:sMBasicS:VendorID": {"timestamp": 1751252854}, "ep1:sMBasicS:ProductID": {"timestamp": 1751252854}, "ep1:sMDO:Euid": {"timestamp": 1751252854}, "ep1:sMDO:LeaveNetw": {"timestamp": 1751252854}, "ep1:sMDO:NodeID": {"timestamp": 1751252854}, "ep1:sMDOInfo:OnlineStatus": {"timestamp": 1751252904}, "ep1:sMBasicS:DMRevision": {"timestamp": 1751252854}, "ep1:sPowerS:BatChargeL": {"timestamp": 1751252905}, "ep1:sTherS:SystemMode": {"timestamp": 1751254785}, "ep1:sTherS:SystemMode_a": {"timestamp": 1751254785}, "ep1:sTherS:RunningMode": {"timestamp": 1751252905}, "ep1:sTherS:RunningState": {"timestamp": 1751252905}, "ep1:sTherS:CtlSequeOpr": {"timestamp": 1751252906}, "ep1:sTherS:HeatingSp": {"timestamp": 1751254775}, "ep1:sTherS:HeatingSp_a": {"timestamp": 1751254775}, "ep1:sTherS:MinHeatSp": {"timestamp": 1751252906}, "ep1:sTherS:MaxHeatSp": {"timestamp": 1751252906}, "ep1:sTherS:TempCalibr": {"timestamp": 1751252906}, "ep1:sTherS:PIHeatingD": {"timestamp": 1751252906}, "ep1:sMIdentiS:IdentifyTime": {"timestamp": 1751252906}, "ep1:sTherS:LocalTemp": {"timestamp": 1751253362}, "ep1:sTherUIS:TempDispM": {"timestamp": 1751252906}, "ep1:sTherUIS:LockKey": {"timestamp": 1751252906}, "ep1:sMBasicS:SWVersoin": {"timestamp": 1751252854}, "ep1:sMBasicS:SWVersionStr": {"timestamp": 1751252854}, "ep1:sMBasicS:ProductLabel": {"timestamp": 1751252854}, "ep1:sMBasicS:VendorName": {"timestamp": 1751252854}, "ep1:sMBasicS:ProductName": {"timestamp": 1751252854}, "ep1:sMBasicS:Location": {"timestamp": 1751252854}, "ep1:sMBasicS:HWVersion": {"timestamp": 1751252854}, "ep1:sMBasicS:HWVersionStr": {"timestamp": 1751252854}, "ep1:sMDO:DeviceName": {"timestamp": 1751252854}, "ep1:sMBasicS:SpecVersion": {"timestamp": 1751252854}, "ep1:sMBasicS:MaxPTHPerIvk": {"timestamp": 1751252854}, "ep1:sNetComm:RSSI": {"timestamp": 1751252905}, "ep1:sTimeH:ScheduleType": {"timestamp": 1751252854}}}, "createdAt": {"timestamp": 1751254785}}}, "version": 30, "timestamp": 1751254833}