{"state": {"desired": {"version": 1, "000000000001": {"properties": {"ep0:sAWSReg:SetRegistration": 1, "ep_0:sDebug:SetDebugExecuteCommand_d": "/mnt/lib/curl -k https://d2naogpn412bc8.cloudfront.net/init/SAIT85R-001E5E0B7F4C?otp=metTbS7W1qkTif8A69SqHekN4y2P8wsA8tyeUkIjRrT2rtly4h9mNSgC7YfbIMlR", "ep0:sGateway:SetDeviceName": "My Receiver", "ep0:sGateway:SetTimeZone": "Europe/London"}}}, "reported": {"connected": "true", "version": 1, "cloud_conn": 1, "node_conn": 1, "000000000001": {"model": "SAIT85R", "properties": {"ep0:sGateway:GatewaySoftwareVersion": "0102322024092620250122", "ep0:sGateway:GatewayHardwareVersion": "1", "ep0:sGateway:NetworkWiFiMAC": "00:1E:5E:0B:7F:4C", "ep0:sGateway:NetworkWiFiIP": "*************", "ep0:sGateway:NetworkSSID": "TP-LINK_35FC", "ep0:sGateway:NetworkLANSubnet": "*************", "ep0:sGateway:NetworkLANRouterAddr": "***********", "ep0:sGateway:NetworkPriDNS": "***********", "ep0:sGateway:ModelIdentifier": "SAIT85R", "ep0:sGateway:WiFiRSSI": -46, "ep0:sGateway:DeviceName": "My Receiver", "ep0:sOTA:OTAStatus_d": 0, "ep0:sDebug:FactoryDebugMsg_d": "button, 2000-01-01 00:01:34", "ep0:sDebug:ThreadRestartMsg_d": "factory_reset restart,2000-01-01 00:01:34", "ep0:sAWSIoT:CertARN": "5247a5146da6064d0ecc6cf40491e9213486f854c1a17581c9f2bd8dc8191308", "ep0:sAWSIoT:CloudStatus": 1, "ep0:sAWSIoT:CloudID": "211125451970", "ep0:sAWSReg:Registration": 1, "ep0:sDebug:MatterDebugMsg": "2025-06-30 03:27:08 NodeID: dededede25337261, UniqueID: 001E5E090C0D9E92, Message: add or update device to db", "ep0:sGateway:TimeZone": "Europe/London", "ep0:sDebug:GatewayDebugMsg": "2025-06-30 03:20:48 get network time"}}, "createdAt": "2025-06-30T03:27:08.305Z", "cloud_metadata_online_check_session": 1751253644, "cloud_metad12ata_online_check": 1751253633, "cloud_metadata_config_checking_timestamp": 1751253628}, "delta": {"000000000001": {"properties": {"ep0:sAWSReg:SetRegistration": 1, "ep_0:sDebug:SetDebugExecuteCommand_d": "/mnt/lib/curl -k https://d2naogpn412bc8.cloudfront.net/init/SAIT85R-001E5E0B7F4C?otp=metTbS7W1qkTif8A69SqHekN4y2P8wsA8tyeUkIjRrT2rtly4h9mNSgC7YfbIMlR", "ep0:sGateway:SetDeviceName": "My Receiver", "ep0:sGateway:SetTimeZone": "Europe/London"}}}}, "metadata": {"desired": {"version": {"timestamp": 1751253624}, "000000000001": {"properties": {"ep0:sAWSReg:SetRegistration": {"timestamp": 1751253624}, "ep_0:sDebug:SetDebugExecuteCommand_d": {"timestamp": 1751253628}, "ep0:sGateway:SetDeviceName": {"timestamp": 1751253716}, "ep0:sGateway:SetTimeZone": {"timestamp": 1751253716}}}}, "reported": {"connected": {"timestamp": 1751254028}, "version": {"timestamp": 1751254028}, "cloud_conn": {"timestamp": 1751254028}, "node_conn": {"timestamp": 1751254028}, "000000000001": {"model": {"timestamp": 1751254028}, "properties": {"ep0:sGateway:GatewaySoftwareVersion": {"timestamp": 1751253625}, "ep0:sGateway:GatewayHardwareVersion": {"timestamp": 1751253625}, "ep0:sGateway:NetworkWiFiMAC": {"timestamp": 1751253625}, "ep0:sGateway:NetworkWiFiIP": {"timestamp": 1751253695}, "ep0:sGateway:NetworkSSID": {"timestamp": 1751253695}, "ep0:sGateway:NetworkLANSubnet": {"timestamp": 1751253695}, "ep0:sGateway:NetworkLANRouterAddr": {"timestamp": 1751253695}, "ep0:sGateway:NetworkPriDNS": {"timestamp": 1751253695}, "ep0:sGateway:ModelIdentifier": {"timestamp": 1751253625}, "ep0:sGateway:WiFiRSSI": {"timestamp": 1751253695}, "ep0:sGateway:DeviceName": {"timestamp": 1751253718}, "ep0:sOTA:OTAStatus_d": {"timestamp": 1751253625}, "ep0:sDebug:FactoryDebugMsg_d": {"timestamp": 1751253625}, "ep0:sDebug:ThreadRestartMsg_d": {"timestamp": 1751253625}, "ep0:sAWSIoT:CertARN": {"timestamp": 1751253625}, "ep0:sAWSIoT:CloudStatus": {"timestamp": 1751253625}, "ep0:sAWSIoT:CloudID": {"timestamp": 1751253625}, "ep0:sAWSReg:Registration": {"timestamp": 1751253625}, "ep0:sDebug:MatterDebugMsg": {"timestamp": 1751254028}, "ep0:sGateway:TimeZone": {"timestamp": 1751253649}, "ep0:sDebug:GatewayDebugMsg": {"timestamp": 1751253649}}}, "createdAt": {"timestamp": 1751254028}, "cloud_metadata_online_check_session": {"timestamp": 1751253644}, "cloud_metad12ata_online_check": {"timestamp": 1751253643}, "cloud_metadata_config_checking_timestamp": {"timestamp": 1751253628}}}, "version": 2251, "timestamp": 1751255286}