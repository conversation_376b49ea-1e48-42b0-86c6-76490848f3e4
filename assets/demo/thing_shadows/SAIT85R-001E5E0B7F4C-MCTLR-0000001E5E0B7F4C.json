{"state": {"desired": {"version": 1, "0000001E5E0B7F4C": {"properties": {"ep0:sAWSReg:SetRegistration": 11}}, "11": {"properties": {"ep0:sMCtlr:sFabID": "FAB927874180344F", "ep0:sMCtlr:sFabRCAC": "MIIB6TCCAY+gAwIBAgIQGcr62RdCt3efxkJ/LQ6gpDAKBggqhkjOPQQDAjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwHhcNMjUwNjMwMDMyMDI4WhcNMzUwNjI4MDMyMDI4WjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAARsSwQClSUDGD4TZSyraPL2luniZXM7nPulZNICRKjxZJtmuLXsp4D9HoJjEmZNuBQiYcgsiWHIvskyCxMNMV2To2MwYTAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBSRy5QvssHrbt9Ub32cn/OOkF9RKDAOBgNVHQ8BAf8EBAMCAY4wHwYDVR0jBBgwFoAUkcuUL7LB627fVG99nJ/zjpBfUSgwCgYIKoZIzj0EAwIDSAAwRQIhAPjDbw0VQMrGZOgmUSqh2wegfMeNDZYRip4+TPoNZaVQAiARE4om7tro3MpZGL3+lcQ/Iop67/iOZyKeTDcPH9h8ZQ==", "ep0:sMCtlr:sFabIPK": "9395CCA95A27EA4C2B1949494370CB11", "ep0:sMCtlr:sFabCreTim": "1751253628", "ep0:sMCtlr:sUserNOC": "MIICIzCCAcqgAwIBAgIQDnHMUyNXJfQltEuMnrurqDAKBggqhkjOPQQDAjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwHhcNMjUwNjMwMDMyMDMxWhcNMjUwNzEwMDMyMDMxWjBeMSAwHgYKKwYBBAGConwBAQwQREVERURFREU2ODE4MTQ2OTEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYxGDAWBgorBgEEAYKifAEGDAhBQjEyMDAwMzBZMBMGByqGSM49AgEGCCqGSM49AwEHA0IABB8D4aJT95gzFfF4Zl46y6DsqqqXRdSyWlKnv2FabsQ7CkaW83J0Cu51rMrO3ms8x5Dkmg26j1mTQKYK6ugB2+WjgYMwgYAwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUkcuUL7LB627fVG99nJ/zjpBfUSgwDgYDVR0PAQH/BAQDAgGOMB8GA1UdIwQYMBaAFJHLlC+ywetu31RvfZyf846QX1EoMCAGA1UdJQEB/wQWMBQGCCsGAQUFBwMCBggrBgEFBQcDATAKBggqhkjOPQQDAgNHADBEAiAIXQlLJPzy++8OudvqPEeyTanCq2GdmYbNwqxozTh9nAIgU6WFH/IHhSR88cf+spZ4zlBJQvragH66F5VYnVR0slA=", "ep0:sMCtlr:sNodeID": "DEDEDEDE68181469", "ep0:sBoiler:sEnableDHW": 1, "ep0:sMCtlr:sNewNodeID": "DEDEDEDE25337261"}}}, "reported": {"connected": "true", "version": 1, "cloud_conn": 1, "node_conn": 1, "11": {"model": "MCTLR", "properties": {"ep0:sAWSReg:Registration": 11, "ep0:sMCtlr:CreateMatFab": -1, "ep0:sMDOInfo:OnlineStatus": 1, "ep0:sBoiler:LostThermState": 2, "ep0:sBoiler:LostOnoffState": 2, "ep0:sBoiler:CHSwitch": 2, "ep0:sBoiler:DHWSwitch": 2, "ep0:sBoiler:CHRelay": 0, "ep0:sBoiler:DHWRelay": 0, "ep0:sBoiler:HoldType": 0, "ep0:sBoiler:EnableDHW": 1, "ep0:sBoiler:BoostDHW": 0, "ep0:sMCtlr:FabIPK": "9395CCA95A27EA4C2B1949494370CB11", "ep0:sMCtlr:FabID": "FAB927874180344F", "ep0:sMCtlr:AddNOCCmpl": 1, "ep0:sMCtlr:FabRCAC": "MIIB6TCCAY+gAwIBAgIQGcr62RdCt3efxkJ/LQ6gpDAKBggqhkjOPQQDAjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwHhcNMjUwNjMwMDMyMDI4WhcNMzUwNjI4MDMyMDI4WjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAARsSwQClSUDGD4TZSyraPL2luniZXM7nPulZNICRKjxZJtmuLXsp4D9HoJjEmZNuBQiYcgsiWHIvskyCxMNMV2To2MwYTAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBSRy5QvssHrbt9Ub32cn/OOkF9RKDAOBgNVHQ8BAf8EBAMCAY4wHwYDVR0jBBgwFoAUkcuUL7LB627fVG99nJ/zjpBfUSgwCgYIKoZIzj0EAwIDSAAwRQIhAPjDbw0VQMrGZOgmUSqh2wegfMeNDZYRip4+TPoNZaVQAiARE4om7tro3MpZGL3+lcQ/Iop67/iOZyKeTDcPH9h8ZQ==", "ep0:sMCtlr:FabCreTim": "1751253628", "ep0:sMCtlr:UserCSR": "MIHJMHACAQAwDjEMMAoGA1UECgwDQ1NSMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHwPholP3mDMV8XhmXjrLoOyqqpdF1LJaUqe/YVpuxDsKRpbzcnQK7nWsys7eazzHkOSaDbqPWZNApgrq6AHb5aAAMAoGCCqGSM49BAMCA0kAMEYCIQDPWwel7sWZmTtIim8fzfNmecBcXAZJyNRD1K0g2O889QIhANCLVDYnoPqlGEuqwaTXcOfbSDp7XTUGW3aBf5RpW1wu", "ep0:sMCtlr:NodeID": "DEDEDEDE68181469", "ep0:sMCtlr:UserNOC": "MIICIzCCAcqgAwIBAgIQDnHMUyNXJfQltEuMnrurqDAKBggqhkjOPQQDAjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwHhcNMjUwNjMwMDMyMDMxWhcNMjUwNzEwMDMyMDMxWjBeMSAwHgYKKwYBBAGConwBAQwQREVERURFREU2ODE4MTQ2OTEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYxGDAWBgorBgEEAYKifAEGDAhBQjEyMDAwMzBZMBMGByqGSM49AgEGCCqGSM49AwEHA0IABB8D4aJT95gzFfF4Zl46y6DsqqqXRdSyWlKnv2FabsQ7CkaW83J0Cu51rMrO3ms8x5Dkmg26j1mTQKYK6ugB2+WjgYMwgYAwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUkcuUL7LB627fVG99nJ/zjpBfUSgwDgYDVR0PAQH/BAQDAgGOMB8GA1UdIwQYMBaAFJHLlC+ywetu31RvfZyf846QX1EoMCAGA1UdJQEB/wQWMBQGCCsGAQUFBwMCBggrBgEFBQcDATAKBggqhkjOPQQDAgNHADBEAiAIXQlLJPzy++8OudvqPEeyTanCq2GdmYbNwqxozTh9nAIgU6WFH/IHhSR88cf+spZ4zlBJQvragH66F5VYnVR0slA=", "ep0:sMCtlr:ThdDataset": "0e080000000000010000000300000b35060004001fffe00208b2c3654b0e0b63c30708fd76ddd3dd5d2d9c0510d4fa45bbaf790d853dcd9b10cfeb0484030f4f70656e5468726561642d656438640102ed8d04100667e5b990a87036cc15a74a1b5678880c0402a0f7f8", "ep0:sMCtlr:Extaddr": "4a03a34e4e8d56e0", "ep0:sMCtlr:ThdBAID": "816dc9c336cc20cf113c46ecd642b73e", "ep0:sMCtlr:ErrorCode": 0, "ep0:sTimeGH:ScheduleType": 0}}, "createdAt": "2025-06-30T03:22:08.803Z"}, "delta": {"0000001E5E0B7F4C": {"properties": {"ep0:sAWSReg:SetRegistration": 11}}, "11": {"properties": {"ep0:sMCtlr:sFabID": "FAB927874180344F", "ep0:sMCtlr:sFabRCAC": "MIIB6TCCAY+gAwIBAgIQGcr62RdCt3efxkJ/LQ6gpDAKBggqhkjOPQQDAjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwHhcNMjUwNjMwMDMyMDI4WhcNMzUwNjI4MDMyMDI4WjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAARsSwQClSUDGD4TZSyraPL2luniZXM7nPulZNICRKjxZJtmuLXsp4D9HoJjEmZNuBQiYcgsiWHIvskyCxMNMV2To2MwYTAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBSRy5QvssHrbt9Ub32cn/OOkF9RKDAOBgNVHQ8BAf8EBAMCAY4wHwYDVR0jBBgwFoAUkcuUL7LB627fVG99nJ/zjpBfUSgwCgYIKoZIzj0EAwIDSAAwRQIhAPjDbw0VQMrGZOgmUSqh2wegfMeNDZYRip4+TPoNZaVQAiARE4om7tro3MpZGL3+lcQ/Iop67/iOZyKeTDcPH9h8ZQ==", "ep0:sMCtlr:sFabIPK": "9395CCA95A27EA4C2B1949494370CB11", "ep0:sMCtlr:sFabCreTim": "1751253628", "ep0:sMCtlr:sUserNOC": "MIICIzCCAcqgAwIBAgIQDnHMUyNXJfQltEuMnrurqDAKBggqhkjOPQQDAjBEMSAwHgYKKwYBBAGConwBBAwQQ0FDQUNBQ0E0NjE0MDkzMzEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYwHhcNMjUwNjMwMDMyMDMxWhcNMjUwNzEwMDMyMDMxWjBeMSAwHgYKKwYBBAGConwBAQwQREVERURFREU2ODE4MTQ2OTEgMB4GCisGAQQBgqJ8AQUMEEZBQjkyNzg3NDE4MDM0NEYxGDAWBgorBgEEAYKifAEGDAhBQjEyMDAwMzBZMBMGByqGSM49AgEGCCqGSM49AwEHA0IABB8D4aJT95gzFfF4Zl46y6DsqqqXRdSyWlKnv2FabsQ7CkaW83J0Cu51rMrO3ms8x5Dkmg26j1mTQKYK6ugB2+WjgYMwgYAwDAYDVR0TAQH/BAIwADAdBgNVHQ4EFgQUkcuUL7LB627fVG99nJ/zjpBfUSgwDgYDVR0PAQH/BAQDAgGOMB8GA1UdIwQYMBaAFJHLlC+ywetu31RvfZyf846QX1EoMCAGA1UdJQEB/wQWMBQGCCsGAQUFBwMCBggrBgEFBQcDATAKBggqhkjOPQQDAgNHADBEAiAIXQlLJPzy++8OudvqPEeyTanCq2GdmYbNwqxozTh9nAIgU6WFH/IHhSR88cf+spZ4zlBJQvragH66F5VYnVR0slA=", "ep0:sMCtlr:sNodeID": "DEDEDEDE68181469", "ep0:sBoiler:sEnableDHW": 1, "ep0:sMCtlr:sNewNodeID": "DEDEDEDE25337261"}}}}, "metadata": {"desired": {"version": {"timestamp": 1751253627}, "0000001E5E0B7F4C": {"properties": {"ep0:sAWSReg:SetRegistration": {"timestamp": 1751253627}}}, "11": {"properties": {"ep0:sMCtlr:sFabID": {"timestamp": 1751253629}, "ep0:sMCtlr:sFabRCAC": {"timestamp": 1751253629}, "ep0:sMCtlr:sFabIPK": {"timestamp": 1751253629}, "ep0:sMCtlr:sFabCreTim": {"timestamp": 1751253629}, "ep0:sMCtlr:sUserNOC": {"timestamp": 1751253632}, "ep0:sMCtlr:sNodeID": {"timestamp": 1751253632}, "ep0:sBoiler:sEnableDHW": {"timestamp": 1751253727}, "ep0:sMCtlr:sNewNodeID": {"timestamp": 1751253985}}}}, "reported": {"connected": {"timestamp": 1751253729}, "version": {"timestamp": 1751253729}, "cloud_conn": {"timestamp": 1751253729}, "node_conn": {"timestamp": 1751253729}, "11": {"model": {"timestamp": 1751253729}, "properties": {"ep0:sAWSReg:Registration": {"timestamp": 1751253628}, "ep0:sMCtlr:CreateMatFab": {"timestamp": 1751253634}, "ep0:sMDOInfo:OnlineStatus": {"timestamp": 1751253628}, "ep0:sBoiler:LostThermState": {"timestamp": 1751253729}, "ep0:sBoiler:LostOnoffState": {"timestamp": 1751253729}, "ep0:sBoiler:CHSwitch": {"timestamp": 1751253729}, "ep0:sBoiler:DHWSwitch": {"timestamp": 1751253729}, "ep0:sBoiler:CHRelay": {"timestamp": 1751253729}, "ep0:sBoiler:DHWRelay": {"timestamp": 1751253729}, "ep0:sBoiler:HoldType": {"timestamp": 1751253729}, "ep0:sBoiler:EnableDHW": {"timestamp": 1751253729}, "ep0:sBoiler:BoostDHW": {"timestamp": 1751253729}, "ep0:sMCtlr:FabIPK": {"timestamp": 1751253630}, "ep0:sMCtlr:FabID": {"timestamp": 1751253630}, "ep0:sMCtlr:AddNOCCmpl": {"timestamp": 1751253634}, "ep0:sMCtlr:FabRCAC": {"timestamp": 1751253630}, "ep0:sMCtlr:FabCreTim": {"timestamp": 1751253630}, "ep0:sMCtlr:UserCSR": {"timestamp": 1751253631}, "ep0:sMCtlr:NodeID": {"timestamp": 1751253633}, "ep0:sMCtlr:UserNOC": {"timestamp": 1751253633}, "ep0:sMCtlr:ThdDataset": {"timestamp": 1751253634}, "ep0:sMCtlr:Extaddr": {"timestamp": 1751253634}, "ep0:sMCtlr:ThdBAID": {"timestamp": 1751253634}, "ep0:sMCtlr:ErrorCode": {"timestamp": 1751253634}, "ep0:sTimeGH:ScheduleType": {"timestamp": 1751253650}}}, "createdAt": {"timestamp": 1751253729}}}, "version": 1910, "timestamp": 1751255286}