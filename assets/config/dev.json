{"clientCode": "habi-dev", "baseUrl": "https://dev-service.ctiotsolution.com/api/v1/", "termsUrl": "https://www.habismarthome.com/app-terms-and-conditions/", "privacyUrl": "https://www.habismarthome.com/app-privacy-policy/", "supportUrl": "https://www.habismarthome.com", "baseDeviceProvisionUrl": "https://w9ijyfio43.execute-api.eu-central-1.amazonaws.com/dev", "companyCode": "salus-dev", "factoryParingMode": false, "fabric": {"scheme": "https", "host": "5fm83lh46f.execute-api.eu-central-1.amazonaws.com", "path": "/Prod"}, "aws": {"region": "eu-central-1", "endpointUrl": "apllkhw47qzr7-ats.iot.eu-central-1.amazonaws.com", "mqttScheme": "wss://", "mqttUrlPath": "/mqtt", "mqttPort": 443, "httpScheme": "https://"}, "amplify": {"UserAgent": "aws-amplify-cli/2.0", "Version": "1.0", "auth": {"plugins": {"awsCognitoAuthPlugin": {"UserAgent": "aws-amplify-cli/0.1.0", "Version": "0.1.0", "IdentityManager": {"Default": {}}, "CredentialsProvider": {"CognitoIdentity": {"Default": {"PoolId": "eu-central-1:219dc527-2947-482b-ae8a-0384fb0dfa52", "Region": "eu-central-1"}}}, "CognitoUserPool": {"Default": {"PoolId": "eu-central-1_ErlRg1onE", "AppClientId": "1llp2u2c6ep8itg3h98oemp1ki", "Region": "eu-central-1"}}, "Auth": {"Default": {"authenticationFlowType": "USER_SRP_AUTH", "OAuth": {"WebDomain": "computime-891377109635.auth.eu-central-1.amazoncognito.com", "AppClientId": "1llp2u2c6ep8itg3h98oemp1ki", "SignInRedirectURI": "myapp://callback/", "SignOutRedirectURI": "myapp://signout/", "Scopes": ["phone", "email", "openid", "profile", "aws.cognito.signin.user.admin"], "IdentityProviders": ["Google"]}}}}}}}, "supportDevices": [{"deviceType": "Thermostat", "modelId": "IT850TX", "capacity": "<PERSON><PERSON><PERSON>", "vid": 65521, "pid": 8000, "summary": "FFF1-8000，The device for testing."}, {"deviceType": "Thermostat", "modelId": "IT850TX", "capacity": "<PERSON><PERSON><PERSON>", "vid": 5415, "pid": 1, "summary": "1527-0001，The device certified under Matter."}, {"deviceType": "Thermostat", "modelId": "IT850TX", "capacity": "WiFi", "vid": 4891, "pid": 6683, "summary": "131B-1A1B，The device for testing."}, {"deviceType": "Thermostat", "modelId": "IT850TX", "capacity": "WiFi", "vid": 4216, "pid": 1, "summary": "1078-0001，The device certified under Matter."}, {"deviceType": "TRV", "modelId": "IT850TRV", "capacity": "<PERSON><PERSON><PERSON>", "vid": 65522, "pid": 8001, "summary": "FFF2-8001，The device for testing."}, {"deviceType": "TRV", "modelId": "IT850TRV", "capacity": "<PERSON><PERSON><PERSON>", "vid": 5415, "pid": 2, "summary": "1527-0002，The device certified under Matter."}]}