{"appName": "<PERSON><PERSON>", "welcomeTitle": "Welcome home", "welcomeBody": "Your home, your rules. Effortless climate control awaits.", "mainTitle": "Let's get started", "mainBody": "Effortless comfort starts here.\nLog in or create your account.", "loginTitle": "Log in", "loginBody": "Welcome back to your comfort zone!", "createAccountTitle": "Create Account", "createAccountBody": "You are now a step closer to perfect comfort", "continue": "CONTINUE", "email": "E-mail", "password": "Password", "resend": "Resend", "connectWithGoogle": "CONNECT WITH GOOGLE", "connectWithAppleId": "CONNECT WITH APPLE ID", "forgotPassword": "Forgot Password", "createAnAccount": "Create An Account", "iAlreadyHaveAnAccount": "I ALREADY HAVE AN ACCOUNT", "country": "Country", "language": "Language", "confirmPassword": "Confirm Password", "privacyPolicySpan1": "By clicking Continue you agree with our\n", "privacyPolicySpan2": "Privacy Policy", "privacyPolicySpan3": " and ", "privacyPolicySpan4": "Terms & Conditions", "login": "Log In", "demo": "Demo", "featureNotAvailableInDemoMode": "This feature isn’t available in demo mode.", "cannotDeleteRoomInDemoMode": "You can’t delete a room that contains devices in demo mode.", "sendLinkToChangeYourPassword": "We will send a link to change your password", "twoFactorAuthentication": "Two Factor Authentication", "sendConfirmationCodeToEmail": "We’ve sent a confirmation code to @email", "invalidConfirmationCodeError": "Invalid confirmation code. Please try again.", "homeManagement": "Home Management", "navigationBarDevices": "Devices", "navigationBarAutomations": "Automations", "navigationBarNotifications": "Notifications", "navigationBarSettings": "Settings", "allLocations": "All locations", "overview": "Overview", "saveDisplayedLocation": "SAVE DISPLAYED LOCATION", "location": "Location", "manage": "Manage", "delete": "Delete", "deviceCount": "@count device(s)", "createNewRoom": "CREATE NEW ROOM", "addReceiver": "Add Receiver", "createAnotherHome": "CREATE ANOTHER HOME", "skip": "<PERSON><PERSON>", "settings": "Settings", "accountSettings": "Account <PERSON><PERSON>", "appSettings": "App Settings", "deviceUpdate": "Device Update", "cloudSaving": "Cloud Saving", "aboutThisApp": "About This App", "contactSupport": "Contact Support", "privacyNotice": "Privacy Notice", "termsConditions": "Terms & Conditions", "dataCollection": "Data Collection", "dataCollectionOn": "Data Collection is <b>ON</b>", "dataCollectionOff": "Data Collection is <b>OFF</b>", "dataCollectionOnDescription": "We recommend you keep this ON. Data collected from your mobile device or computer, and equipment is used to run the application, your connected home services, and to send important alerts and notifications.", "dataCollectionOffDescription": "You always have the right to turn data collection off. Turning this off means the application will be automatically disabled and your mobile device or computer can't be used to manage your equipment or service.", "dataCollectionNote": "You can always manually manage your equipment. Refer to the equipment User Guide for instructions.", "logout": "Logout", "logouting": "Logout...", "roomManagement": "Room Management", "deviceManagement": "Device Management", "addThermostat": "Add Thermostat", "addTrv": "Add TRV", "addLight": "Add Light", "lightSetting": "Light Settings", "trvSetting": "TRV Settings", "thermostatSetting": "Thermostat Settings", "receiverSetting": "Receiver Settings", "deviceSettings": "<PERSON><PERSON>s", "matterPairingMode": "Matter Pairing Mode", "changeRoom": "Change Room", "installerSetting": "Installer Setting", "temperatureOffset": "Temperature Offset", "childLock": "Child Lock", "identifyMe": "Identify me", "batteryStatus": "Battery Status", "connectivityStatus": "Connectivity Status", "online": "Online", "offline": "Offline", "model": "Model", "deleteDevice": "DELETE DEVICE", "signalStrengthInfo": "Signal strength of the device.", "installerSettingInfo": "Installer settings are for professionals only. Unauthorized changes can impact safety & performance.", "identifyThermostatInfo": "To identify the thermostat, look for the one with the flashing LED", "identifyTrvInfo": "To identify the TRV, look for the one with the flashing LED", "addDevice": "Add <PERSON>", "networkScan": "Network Scan", "pairingCode": "Pairing Code", "macAddress": "<PERSON>dress", "qrCode": "QR Code", "lookingForDevices": "Looking for devices", "discoveredDevices": "Discovered Devices", "pairYourReceiver": "Pair your receiver", "pairYourDevice": "Pair your device", "selectNetwork": "Select Network", "rescan": "Rescan", "cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "wifiSsid": "WiFi/SSiD", "next": "NEXT", "timeZone": "Time Zone", "setTimeZone": "Set Time Zone", "selectHome": "Select Home", "addNewLocation": "Add New Location", "shareLocation": "Share Location Control Rights", "unitSettings": "Unit Settings", "modeSetting": "Mode Setting", "hourFormat": "Hour Format", "hourFormat12": "12H", "hourFormat24": "24H", "temperatureUnit": "Temperature Unit", "temperatureUnitC": "C", "temperatureUnitF": "F", "deviceLocation": "Device location", "save": "Save", "uppercaseSave": "SAVE", "tempNow": "TEMP. NOW", "fan": "Fan", "heat": "Heat", "cool": "Cool", "auto": "Auto", "manual": "Manual", "onOff": "On/Off", "deviceOnlineStatus": "Device Online Status", "currentTemp": "CURRENT TEMP", "desiredTemp": "DESIRED TEMP", "status": "Status", "schedule": "Schedule", "startTime": "Start time", "endTime": "End time", "temperature": "Temperature", "state": "State", "it850ScheduleSetting": "Setting", "addSlot": "ADD SLOT", "addSlotLimit": "You can add up to 6 slots", "addSlotLimit3": "You can add up to 3 slots", "requiredField": "This field is required", "emailAddressHasAlreadyBeenTaken": "This email address has already been taken", "incorrectEmailOrPassword": "Incorrect e-mail or password", "incorrectEmail": "Incorrect e-mail format", "incorrectMacAddress": "The MacAddress you input must be 12 characters in length and consist solely of alphanumeric characters.", "verificationCodeInvalid": "Invalid verification code provided, please try again.", "passwordTooSimple": "This password is too simple", "passwordNotMatching": "Passwords do not match", "passwordTooLong": "Passwords must not be longer than fourteen characters", "passwordTooShort": "Passwords must be at least eight characters", "passwordMustContain": "Passwords must contain at least one special symbol (^ $ * . [ ] { } ( ) ? ” ! @ # % & / \\\\ , > < ' : ; | _ ~ ` = + -)", "passwordChangedSuccessfully": "Your password has been successfully changed!", "oldPasswordIncorrect": "Please input correct old password.", "newPasswordIsSameWithOldPassword": "New password can not be same with the old password", "networkError": "Sorry, can't access internet.", "timeoutError": "Request timeout.", "requestFailed": "Request failed.", "resetPassword": "Reset Password", "changePassword": "Change Password", "oldPassword": "Old Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "repeatNewPassword": "Repeat New Password", "currentPassword": "Current Password", "incorrectPassword": "Incorrect password", "confirmationCode": "Confirmation code", "personalInformations": "Personal Informations", "changeNickname": "Change Nickname", "nameAndSurname": "Name and Surname", "emailAddress": "Email Address", "address": "Address", "region": "Region", "faceID": "Face ID", "repeat": "Repeat", "am": "AM", "pm": "PM", "desiredTemperature": "Desired temperature", "pleaseEnterName": "Please enter name", "addHome": "ADD HOME", "addRoom": "ADD ROOM", "name": "Name", "add": "ADD", "areYouSureYouWantToDeleteRoom": "Are you sure you want to delete room?", "areYouSureYouWantToDeleteThisDevice": "Are you sure you want to delete this device?", "areYouSureYouWantToDeleteYourProfile": "Are you sure you want to delete your profile?", "addNewRoom": "Add New Room", "roomNameExists": "Room name already exists", "roomNameUpdated": "Room name updated", "failedToUpdateRoomName": "Failed to update room name", "thirdPartyServices": "Third Party Services", "deleteLocation": "Delete Location", "devicePairingMode": "Device Pairing Mode", "openDevicePairingMode": "Open Device Pairing Mode", "openDevicePairingModeText": "In order to pair with a 3rd party service scan the QR Code or use the 11 digit code below", "heatSetpoint": "Heat Setpoint", "coolSetpoint": "<PERSON> Setpoint", "minValue": "Min Value", "maxValue": "Max Value", "nodeId": "Node ID", "thingName": "Thing Name", "myReceiver": "My Receiver", "myThermostat": "My Thermostat", "myTrv": "My TRV", "on": "On", "off": "Off", "currently": "Currently", "deviceOffline": "Device Offline", "unknownDevice": "Unknown Device", "hotWater": "HotWater", "clearHistory": "Clear History", "shareReceiver": "Share Receiver", "commissioning": "Commissioning", "PleaseEnterTheEmailAddressYouWantToShare": "Please enter the email address you want to share", "vacationHome": "Vacation Home", "all": "All", "workWeek": "5/2", "individual": "Individual", "sun": "Sun.", "mon": "Mon.", "tue": "<PERSON><PERSON>.", "wed": "Wed.", "thu": "<PERSON><PERSON>.", "fri": "Fri.", "sat": "Sat.", "mon-fri": "Mon.-Fri.", "sat-sun": "Sat.-Sun.", "mon-sun": "Mon.-Sun.", "validateTime": "Please enter a time", "uniqueStartTime": "You must use unique start time", "noSlot": "No slot", "editSlot": "Edit Slot", "addNewSlot": "Add", "updateSlot": "update", "deleteSlot": "DELETE Slot", "deleteAllSlots": "Delete All Slots", "invalidTimeFormat": "Invalid time format", "endTimeMustBeAfterStartTime": "End time must be after start time", "scheduleConflicts": "The new schedule overlaps with an existing schedule", "validateSetPoint": "Temperature should between @minValue and @maxValue. Decimal digits must end in 0 or 5", "validateSetPoint2": "Temperature should between @minValue and @maxValue. Decimal digits must end in 0", "time": "Time", "livingRoom": "Living Room", "noReceiver": "No receiver", "noReceiverDesc": "Please add a receiver first before adding a device", "2ndRelayFunction": "2nd Relay Function", "hotWaterControl": "Hot Water Control", "pleaseEnterTheAddress": "Please enter the address", "saving": "Saving...", "deleting": "Deleting...", "saveFailed": "Save Failed", "saveFailedPrompt": "The saving operation has failed. Please check your network connection and try the saving operation again", "deleteFailed": "Delete Failed", "deleteLocationPrompt": "Do you want to delete this location?", "deleteLocationPrompt2": "All devices associated with this location will also be permanently deleted. This action cannot be undone!", "deleteLocationFailed": "Failed to delete this location. Please try again.", "deleteLocationDevicesFailed": "Failed to delete all devices associated with this location", "unableToRemoveDeviceFromRoom": "Unable to remove device from room", "unableToAddDeviceToRoom": "Unable to add device to room", "firmwareVersion": "Firmware Version", "demandForHeat": "Heat Demand", "deleteUserProfile": "Delete User Profile", "emailNotReceived": "Didn't get the email? Try checking your spam folder or Resend the email.", "reset": "Reset", "emailCheckOrResendSpan1": "Didn't get the email? Try checking your spam folder or ", "emailCheckOrResendSpan2": " the email.", "addLocation": "Add Location", "home": "Home", "room": "Room", "controlMode": "Control Mode", "remote": "Remote", "local": "Local", "carefulIrreversibleChoice": "Please choose one of these options carefully because they cannot be undone:", "carefulIrreversibleChoiceOption1": "Keep all configuration data on receiver (along with device names)", "carefulIrreversibleChoiceOption2": "Factory reset (Not recommended because next user would have to configure the whole system again)", "deleteReceiverFailed": "Delete Receiver Failed", "deleteDeviceFailed": "Delete Device Failed", "cannotDeleteNotOwner": "You are not the owner of the receiver, so you cannot delete it", "googleHome": "Google Home", "appleHome": "Apple Home", "active": "Active", "inactive": "inactive", "wifiMismatchError": "The phone and the receiver are not on the same Wi-Fi network. \nThe phone: @phoneWiFi \nThe receiver: @receiverWiFi", "locationSettings": "Location Settings", "networkStrength": "Network Strength", "changeReceiverWiFi": "Change Receiver Wi-Fi", "areYouSureChangeReceiverWiFi": "Are you sure you want to change the Receiver's Wi-Fi?", "company": "Company", "buildDate": "Build Date", "released": "Released", "version": "Version", "battery": "Battery", "batteryNominal": "Nominal", "batteryLow": "Low", "batteryCritical": "Critical", "uncertifiedDevice": "Uncertified device", "uncertifiedDeviceSecurityRisk": "This Matter-enabled device is not certified.\n\nUncertified devices may not work reliably and are less secure.\n\nAdding an uncertified device increases the risk of unauthorized and potentially malicious access to your network.", "setUpAnyway": "Set up anyway", "receiversSetting": "Receivers Setting", "wifiRouterBoilerReceiverSignalStrength": "The signal strength between the WiFi router and the boiler receiver.", "close": "close", "deleteLocationDevices": "By deleting the locations all the devices from this location will be removed!", "locationDeleted": "Location deleted", "matterQRCodeSticker": "Locate the Matter QR code sticker on the device", "matterPairingCodeSticker": "Find the 11-digit code on the bottom of the Matter QR code sticker", "enterPairingCode": "Enter the pairing code", "pairingInProgress": "Pairing in progress", "updatingInProgress": "Updating in progress", "pleaseAllowUpTo15Minutes": "Please allow up to\n 15 minutes.", "pairing": "Pairing", "tryAgain": "Try Again", "pairingProcessFailed": "The pairing process failed!", "pairingProcessWasFinalized": "The pairing process was finalized successfully!", "loadDataTimeout": "Load data timeout.", "failedToLoadData": "Failed to load data.", "InvalidPairingCode": "Invalid pairing code.", "continuousOn": "Continuous ON", "continuousOff": "Continuous OFF", "boost": "Boost", "turnOn": "Turn ON", "turnOff": "Turn OFF", "onBoost": "Boost Mode", "onBoostUntil": "Boost Mode ON until @time", "followingSchedule": "Following Schedule", "boostDuration": "Boost Duration", "duration": "Duration", "boostHoursLimit": "You can only boost to a maximum of 6 hours at a time.", "hours": "Hours", "minutes": "Minutes", "boostHours": "Boost Hours", "cancelHours": "Cancel Hours", "boostDurationNotSet": "You have not set a Boost Duration, which should be at least one minute longer", "endBoostTimerAndResumeSchedule": "Do you wish to end the current boost timer and resume schedule?", "endScheduleAndResumeBoostTimer": "Do you wish to end the current schedule and resume boost timer?", "receiverAddLimitTitle": "Operation restricted", "receiverAddLimitMsg": "You are currently not allowed to add a Receiver. This action is limited to regular users.", "pleaseTurnOnLocation": "Please turn on location setting", "pleaseTurnOnBluetooth": "Please turn on bluetooth setting", "pleaseSelectLocationOfYourReceiver": "Please select the location of your receiver", "unableToFetchDataFromServer": "Unable to fetch data from the server.", "enablePairingModeSmallInfo": "This option is only available when the device and mobile phone are on the same WiFi network.", "failedToOpenPairingWindow": "Failed to open the pairing window.", "failedToReadVidAndPid": "Failed to read vid and pid.", "share": "Share", "shareAppLogs": "Share App Logs", "appImprovementLogsRequest": "We need your help to improve the app. Please share the app logs with us.", "pleaseConnectToAWiFiNetwork": "Please connect to a Wi-Fi network.", "updatingFirmware": "Updating Firmware", "upgrading": "Upgrading...", "firmwareUpdateSuccessful": "Firmware update successful.", "commissioningPeriodTimeoutDisconnectReconnectWifiRetry": "Commissioning period timeout, please disconncect then reconnect your phone’s WiFi and try again", "deviceNotFoundPowerOnFactoryResetPairingRetry": "Device not found, please make sure the device is powered on, factory reset the device to enter pairing mode and try again", "commissioningErrorFactoryResetAppRestartRetry": "Commissioning Error, please factory reset the device, restart the Habi app and try again", "commissioningPeriodTimeoutDisableVPNAntivirusRetry": "Commissioning period timeout, please make sure your phone’s VPN or Anti-virus is disabled and try again", "commissioningErrorFactoryResetRetry": "Commissioning Error, please factory reset the device and try again", "unableToConnectThisWiFi": "Sorry, unable to connect to this Wi-Fi.", "pleaseSelectDeviceType": "Please select the type of device to add", "matterOverWiFi": "Matter over WiFi", "matterOverThread": "Matter over Thread", "pleaseActivateTurnOnButtonFirst": "Please activate Turn On button first", "heatOnDemand": "Heat On Demand", "keepMeLoggedIn": "Keep me logged in", "switchNotInAutoMode": "The switch isn’t in auto mode right now. You should switch it to auto mode first, and then try the control operation."}